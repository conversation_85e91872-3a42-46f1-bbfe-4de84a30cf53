{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/cbrand/form-validation.tsx"], "sourcesContent": ["import { CompleteBrandProfile } from './cbrand-wizard';\r\n\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n  warnings: Record<string, string>;\r\n}\r\n\r\nexport function validateBrandProfile(profile: CompleteBrandProfile, step?: number): ValidationResult {\r\n  const errors: Record<string, string> = {};\r\n  const warnings: Record<string, string> = {};\r\n\r\n  // Step 1 validation (Website Analysis)\r\n  if (!step || step >= 1) {\r\n    if (!profile.websiteUrl && !profile.description) {\r\n      warnings.websiteUrl = 'Website URL or manual description is recommended for better AI analysis';\r\n    }\r\n\r\n    if (profile.websiteUrl && !isValidUrl(profile.websiteUrl)) {\r\n      errors.websiteUrl = 'Please enter a valid website URL';\r\n    }\r\n  }\r\n\r\n  // Step 2 validation (Brand Details)\r\n  if (!step || step >= 2) {\r\n    // Required fields\r\n    if (!profile.businessName?.trim()) {\r\n      errors.businessName = 'Business name is required';\r\n    }\r\n\r\n    if (!profile.businessType?.trim()) {\r\n      errors.businessType = 'Business type is required';\r\n    }\r\n\r\n    if (!profile.location?.trim()) {\r\n      errors.location = 'Location is required';\r\n    }\r\n\r\n    if (!profile.description?.trim()) {\r\n      errors.description = 'Business description is required';\r\n    }\r\n\r\n    if (!profile.services || profile.services.length === 0) {\r\n      errors.services = 'At least one service/product is required';\r\n    } else {\r\n      // Check if all services have names (description is optional)\r\n      const incompleteServices = profile.services.some(\r\n        service => !service.name?.trim()\r\n      );\r\n      if (incompleteServices) {\r\n        errors.services = 'All service/product names must be filled';\r\n      }\r\n    }\r\n\r\n    // Optional but recommended fields\r\n    if (!profile.targetAudience?.trim()) {\r\n      warnings.targetAudience = 'Target audience description helps create better content';\r\n    }\r\n\r\n    if (!profile.visualStyle?.trim()) {\r\n      warnings.visualStyle = 'Visual style description helps with content generation';\r\n    }\r\n\r\n    if (!profile.writingTone?.trim()) {\r\n      warnings.writingTone = 'Writing tone helps maintain consistent brand voice';\r\n    }\r\n\r\n    // Email validation\r\n    if (profile.contactEmail && !isValidEmail(profile.contactEmail)) {\r\n      errors.contactEmail = 'Please enter a valid email address';\r\n    }\r\n\r\n    // Social media URL validation\r\n    if (profile.facebookUrl && !isValidUrl(profile.facebookUrl)) {\r\n      errors.facebookUrl = 'Please enter a valid Facebook URL';\r\n    }\r\n\r\n    if (profile.instagramUrl && !isValidUrl(profile.instagramUrl)) {\r\n      errors.instagramUrl = 'Please enter a valid Instagram URL';\r\n    }\r\n\r\n    if (profile.twitterUrl && !isValidUrl(profile.twitterUrl)) {\r\n      errors.twitterUrl = 'Please enter a valid Twitter URL';\r\n    }\r\n\r\n    if (profile.linkedinUrl && !isValidUrl(profile.linkedinUrl)) {\r\n      errors.linkedinUrl = 'Please enter a valid LinkedIn URL';\r\n    }\r\n\r\n    // Color validation\r\n    if (profile.primaryColor && !isValidHexColor(profile.primaryColor)) {\r\n      errors.primaryColor = 'Please enter a valid hex color';\r\n    }\r\n\r\n    if (profile.accentColor && !isValidHexColor(profile.accentColor)) {\r\n      errors.accentColor = 'Please enter a valid hex color';\r\n    }\r\n\r\n    if (profile.backgroundColor && !isValidHexColor(profile.backgroundColor)) {\r\n      errors.backgroundColor = 'Please enter a valid hex color';\r\n    }\r\n  }\r\n\r\n  // Step 3 validation (Logo Upload)\r\n  if (!step || step >= 3) {\r\n    // Temporarily removed logo requirement to test logo persistence\r\n    // if (!profile.logoDataUrl) {\r\n    //   errors.logoDataUrl = 'Logo upload is required to complete your brand profile';\r\n    // }\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors,\r\n    warnings,\r\n  };\r\n}\r\n\r\nexport function getFieldValidationState(\r\n  profile: CompleteBrandProfile,\r\n  fieldName: keyof CompleteBrandProfile\r\n): 'valid' | 'invalid' | 'warning' | 'neutral' {\r\n  const validation = validateBrandProfile(profile);\r\n\r\n  if (validation.errors[fieldName]) {\r\n    return 'invalid';\r\n  }\r\n\r\n  if (validation.warnings[fieldName]) {\r\n    return 'warning';\r\n  }\r\n\r\n  const value = profile[fieldName];\r\n  if (value && typeof value === 'string' && value.trim()) {\r\n    return 'valid';\r\n  }\r\n\r\n  return 'neutral';\r\n}\r\n\r\n// Utility functions\r\nfunction isValidUrl(url: string): boolean {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch {\r\n    return false;\r\n  }\r\n}\r\n\r\nfunction isValidEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\nfunction isValidHexColor(color: string): boolean {\r\n  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;\r\n  return hexRegex.test(color);\r\n}\r\n\r\n// Progress calculation\r\nexport function calculateProgress(profile: CompleteBrandProfile): {\r\n  overall: number;\r\n  byStep: { step1: number; step2: number; step3: number };\r\n} {\r\n  // Step 1: Website Analysis (20%)\r\n  let step1Progress = 0;\r\n  if (profile.websiteUrl) step1Progress += 50;\r\n  if (profile.description) step1Progress += 50;\r\n\r\n  // Step 2: Brand Details (70%)\r\n  const basicRequiredFields = [\r\n    'businessName', 'businessType', 'location', 'description'\r\n  ];\r\n  const optionalFields = [\r\n    'targetAudience', 'keyFeatures', 'competitiveAdvantages',\r\n    'contactPhone', 'contactEmail', 'contactAddress',\r\n    'visualStyle', 'writingTone', 'contentThemes'\r\n  ];\r\n  // Social media fields are now completely optional and don't affect progress\r\n  // 'facebookUrl', 'instagramUrl', 'twitterUrl', 'linkedinUrl' - removed from progress calculation\r\n\r\n  // Count basic required fields\r\n  const basicRequiredCompleted = basicRequiredFields.filter(\r\n    field => profile[field as keyof CompleteBrandProfile]?.toString().trim()\r\n  ).length;\r\n\r\n  // Check services separately (array format)\r\n  const servicesCompleted = profile.services && profile.services.length > 0 &&\r\n    profile.services.every(service => service.name?.trim()) ? 1 : 0;\r\n\r\n  const totalRequiredCompleted = basicRequiredCompleted + servicesCompleted;\r\n  const totalRequiredFields = basicRequiredFields.length + 1; // +1 for services\r\n  const optionalCompleted = optionalFields.filter(\r\n    field => profile[field as keyof CompleteBrandProfile]?.toString().trim()\r\n  ).length;\r\n\r\n  const step2Progress = (\r\n    (totalRequiredCompleted / totalRequiredFields) * 70 +\r\n    (optionalCompleted / optionalFields.length) * 30\r\n  );\r\n\r\n  // Step 3: Logo Upload (10%)\r\n  const step3Progress = profile.logoDataUrl ? 100 : 0;\r\n\r\n  const overall = (step1Progress * 0.2) + (step2Progress * 0.7) + (step3Progress * 0.1);\r\n\r\n  return {\r\n    overall: Math.round(overall),\r\n    byStep: {\r\n      step1: Math.round(step1Progress),\r\n      step2: Math.round(step2Progress),\r\n      step3: Math.round(step3Progress),\r\n    },\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,qBAAqB,OAA6B,EAAE,IAAa;IAC/E,MAAM,SAAiC,CAAC;IACxC,MAAM,WAAmC,CAAC;IAE1C,uCAAuC;IACvC,IAAI,CAAC,QAAQ,QAAQ,GAAG;QACtB,IAAI,CAAC,QAAQ,UAAU,IAAI,CAAC,QAAQ,WAAW,EAAE;YAC/C,SAAS,UAAU,GAAG;QACxB;QAEA,IAAI,QAAQ,UAAU,IAAI,CAAC,WAAW,QAAQ,UAAU,GAAG;YACzD,OAAO,UAAU,GAAG;QACtB;IACF;IAEA,oCAAoC;IACpC,IAAI,CAAC,QAAQ,QAAQ,GAAG;QACtB,kBAAkB;QAClB,IAAI,CAAC,QAAQ,YAAY,EAAE,QAAQ;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,QAAQ,YAAY,EAAE,QAAQ;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,QAAQ,QAAQ,EAAE,QAAQ;YAC7B,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,CAAC,QAAQ,WAAW,EAAE,QAAQ;YAChC,OAAO,WAAW,GAAG;QACvB;QAEA,IAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,KAAK,GAAG;YACtD,OAAO,QAAQ,GAAG;QACpB,OAAO;YACL,6DAA6D;YAC7D,MAAM,qBAAqB,QAAQ,QAAQ,CAAC,IAAI,CAC9C,CAAA,UAAW,CAAC,QAAQ,IAAI,EAAE;YAE5B,IAAI,oBAAoB;gBACtB,OAAO,QAAQ,GAAG;YACpB;QACF;QAEA,kCAAkC;QAClC,IAAI,CAAC,QAAQ,cAAc,EAAE,QAAQ;YACnC,SAAS,cAAc,GAAG;QAC5B;QAEA,IAAI,CAAC,QAAQ,WAAW,EAAE,QAAQ;YAChC,SAAS,WAAW,GAAG;QACzB;QAEA,IAAI,CAAC,QAAQ,WAAW,EAAE,QAAQ;YAChC,SAAS,WAAW,GAAG;QACzB;QAEA,mBAAmB;QACnB,IAAI,QAAQ,YAAY,IAAI,CAAC,aAAa,QAAQ,YAAY,GAAG;YAC/D,OAAO,YAAY,GAAG;QACxB;QAEA,8BAA8B;QAC9B,IAAI,QAAQ,WAAW,IAAI,CAAC,WAAW,QAAQ,WAAW,GAAG;YAC3D,OAAO,WAAW,GAAG;QACvB;QAEA,IAAI,QAAQ,YAAY,IAAI,CAAC,WAAW,QAAQ,YAAY,GAAG;YAC7D,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,QAAQ,UAAU,IAAI,CAAC,WAAW,QAAQ,UAAU,GAAG;YACzD,OAAO,UAAU,GAAG;QACtB;QAEA,IAAI,QAAQ,WAAW,IAAI,CAAC,WAAW,QAAQ,WAAW,GAAG;YAC3D,OAAO,WAAW,GAAG;QACvB;QAEA,mBAAmB;QACnB,IAAI,QAAQ,YAAY,IAAI,CAAC,gBAAgB,QAAQ,YAAY,GAAG;YAClE,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,QAAQ,WAAW,IAAI,CAAC,gBAAgB,QAAQ,WAAW,GAAG;YAChE,OAAO,WAAW,GAAG;QACvB;QAEA,IAAI,QAAQ,eAAe,IAAI,CAAC,gBAAgB,QAAQ,eAAe,GAAG;YACxE,OAAO,eAAe,GAAG;QAC3B;IACF;IAEA,kCAAkC;IAClC,IAAI,CAAC,QAAQ,QAAQ,GAAG;IACtB,gEAAgE;IAChE,8BAA8B;IAC9B,mFAAmF;IACnF,IAAI;IACN;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;QACA;IACF;AACF;AAEO,SAAS,wBACd,OAA6B,EAC7B,SAAqC;IAErC,MAAM,aAAa,qBAAqB;IAExC,IAAI,WAAW,MAAM,CAAC,UAAU,EAAE;QAChC,OAAO;IACT;IAEA,IAAI,WAAW,QAAQ,CAAC,UAAU,EAAE;QAClC,OAAO;IACT;IAEA,MAAM,QAAQ,OAAO,CAAC,UAAU;IAChC,IAAI,SAAS,OAAO,UAAU,YAAY,MAAM,IAAI,IAAI;QACtD,OAAO;IACT;IAEA,OAAO;AACT;AAEA,oBAAoB;AACpB,SAAS,WAAW,GAAW;IAC7B,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,SAAS,aAAa,KAAa;IACjC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,SAAS,gBAAgB,KAAa;IACpC,MAAM,WAAW;IACjB,OAAO,SAAS,IAAI,CAAC;AACvB;AAGO,SAAS,kBAAkB,OAA6B;IAI7D,iCAAiC;IACjC,IAAI,gBAAgB;IACpB,IAAI,QAAQ,UAAU,EAAE,iBAAiB;IACzC,IAAI,QAAQ,WAAW,EAAE,iBAAiB;IAE1C,8BAA8B;IAC9B,MAAM,sBAAsB;QAC1B;QAAgB;QAAgB;QAAY;KAC7C;IACD,MAAM,iBAAiB;QACrB;QAAkB;QAAe;QACjC;QAAgB;QAAgB;QAChC;QAAe;QAAe;KAC/B;IACD,4EAA4E;IAC5E,iGAAiG;IAEjG,8BAA8B;IAC9B,MAAM,yBAAyB,oBAAoB,MAAM,CACvD,CAAA,QAAS,OAAO,CAAC,MAAoC,EAAE,WAAW,QAClE,MAAM;IAER,2CAA2C;IAC3C,MAAM,oBAAoB,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,KACtE,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAA,UAAW,QAAQ,IAAI,EAAE,UAAU,IAAI;IAEhE,MAAM,yBAAyB,yBAAyB;IACxD,MAAM,sBAAsB,oBAAoB,MAAM,GAAG,GAAG,kBAAkB;IAC9E,MAAM,oBAAoB,eAAe,MAAM,CAC7C,CAAA,QAAS,OAAO,CAAC,MAAoC,EAAE,WAAW,QAClE,MAAM;IAER,MAAM,gBACJ,AAAC,yBAAyB,sBAAuB,KACjD,AAAC,oBAAoB,eAAe,MAAM,GAAI;IAGhD,4BAA4B;IAC5B,MAAM,gBAAgB,QAAQ,WAAW,GAAG,MAAM;IAElD,MAAM,UAAU,AAAC,gBAAgB,MAAQ,gBAAgB,MAAQ,gBAAgB;IAEjF,OAAO;QACL,SAAS,KAAK,KAAK,CAAC;QACpB,QAAQ;YACN,OAAO,KAAK,KAAK,CAAC;YAClB,OAAO,KAAK,KAAK,CAAC;YAClB,OAAO,KAAK,KAAK,CAAC;QACpB;IACF;AACF", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/cbrand/progress-indicator.tsx"], "sourcesContent": ["import { Progress } from '@/components/ui/progress';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { CheckCircle, AlertCircle, Circle } from 'lucide-react';\r\nimport { CompleteBrandProfile } from './cbrand-wizard';\r\nimport { calculateProgress } from './form-validation';\r\n\r\ninterface ProgressIndicatorProps {\r\n  brandProfile: CompleteBrandProfile;\r\n  currentStep: number;\r\n}\r\n\r\nexport function ProgressIndicator({ brandProfile, currentStep }: ProgressIndicatorProps) {\r\n  const progress = calculateProgress(brandProfile);\r\n\r\n  const steps = [\r\n    {\r\n      id: 1,\r\n      title: 'Website Analysis',\r\n      progress: progress.byStep.step1,\r\n      status: getStepStatus(1, currentStep, progress.byStep.step1),\r\n    },\r\n    {\r\n      id: 2,\r\n      title: 'Brand Details',\r\n      progress: progress.byStep.step2,\r\n      status: getStepStatus(2, currentStep, progress.byStep.step2),\r\n    },\r\n    {\r\n      id: 3,\r\n      title: 'Logo Upload',\r\n      progress: progress.byStep.step3,\r\n      status: getStepStatus(3, currentStep, progress.byStep.step3),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-3\">\r\n      {/* Overall Progress */}\r\n      <div>\r\n        <div className=\"flex justify-between items-center mb-1\">\r\n          <span className=\"text-xs font-medium text-gray-700\">Overall Progress</span>\r\n          <Badge variant={progress.overall === 100 ? 'default' : 'secondary'} className=\"text-xs px-2 py-0\">\r\n            {progress.overall}% Complete\r\n          </Badge>\r\n        </div>\r\n        <Progress value={progress.overall} className=\"h-1.5\" />\r\n      </div>\r\n\r\n      {/* Step-by-step Progress */}\r\n      <div className=\"space-y-2\">\r\n        {steps.map((step) => (\r\n          <div key={step.id} className=\"flex items-center space-x-2\">\r\n            <div className=\"flex-shrink-0\">\r\n              {step.status === 'complete' && (\r\n                <CheckCircle className=\"h-4 w-4 text-green-500\" />\r\n              )}\r\n              {step.status === 'current' && (\r\n                <AlertCircle className=\"h-4 w-4 text-blue-500\" />\r\n              )}\r\n              {step.status === 'pending' && (\r\n                <Circle className=\"h-4 w-4 text-gray-300\" />\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"flex-1 min-w-0\">\r\n              <div className=\"flex justify-between items-center mb-0.5\">\r\n                <span className={`text-xs font-medium ${step.status === 'current' ? 'text-blue-700' :\r\n                  step.status === 'complete' ? 'text-green-700' :\r\n                    'text-gray-500'\r\n                  }`}>\r\n                  {step.title}\r\n                </span>\r\n                <span className=\"text-xs text-gray-500\">\r\n                  {step.progress}%\r\n                </span>\r\n              </div>\r\n              <Progress\r\n                value={step.progress}\r\n                className={`h-1 ${step.status === 'current' ? 'bg-blue-100' :\r\n                  step.status === 'complete' ? 'bg-green-100' :\r\n                    'bg-gray-100'\r\n                  }`}\r\n              />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Completion Status */}\r\n      {progress.overall === 100 && (\r\n        <div className=\"mt-3 p-2 bg-green-50 border border-green-200 rounded\">\r\n          <div className=\"flex items-center\">\r\n            <CheckCircle className=\"h-3 w-3 text-green-500 mr-1\" />\r\n            <span className=\"text-xs font-medium text-green-800\">\r\n              Profile complete!\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction getStepStatus(\r\n  stepId: number,\r\n  currentStep: number,\r\n  stepProgress: number\r\n): 'complete' | 'current' | 'pending' {\r\n  if (stepProgress === 100) return 'complete';\r\n  if (stepId === currentStep) return 'current';\r\n  if (stepId < currentStep) return 'complete';\r\n  return 'pending';\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAEA;;;;;;AAOO,SAAS,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAA0B;IACrF,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE;IAEnC,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,UAAU,SAAS,MAAM,CAAC,KAAK;YAC/B,QAAQ,cAAc,GAAG,aAAa,SAAS,MAAM,CAAC,KAAK;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU,SAAS,MAAM,CAAC,KAAK;YAC/B,QAAQ,cAAc,GAAG,aAAa,SAAS,MAAM,CAAC,KAAK;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU,SAAS,MAAM,CAAC,KAAK;YAC/B,QAAQ,cAAc,GAAG,aAAa,SAAS,MAAM,CAAC,KAAK;QAC7D;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;0CACpD,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAS,SAAS,OAAO,KAAK,MAAM,YAAY;gCAAa,WAAU;;oCAC3E,SAAS,OAAO;oCAAC;;;;;;;;;;;;;kCAGtB,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,OAAO,SAAS,OAAO;wBAAE,WAAU;;;;;;;;;;;;0BAI/C,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wBAAkB,WAAU;;0CAC3B,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,MAAM,KAAK,4BACf,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAExB,KAAK,MAAM,KAAK,2BACf,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAExB,KAAK,MAAM,KAAK,2BACf,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;0CAItB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,CAAC,oBAAoB,EAAE,KAAK,MAAM,KAAK,YAAY,kBAClE,KAAK,MAAM,KAAK,aAAa,mBAC3B,iBACA;0DACD,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAK,WAAU;;oDACb,KAAK,QAAQ;oDAAC;;;;;;;;;;;;;kDAGnB,6LAAC,uIAAA,CAAA,WAAQ;wCACP,OAAO,KAAK,QAAQ;wCACpB,WAAW,CAAC,IAAI,EAAE,KAAK,MAAM,KAAK,YAAY,gBAC5C,KAAK,MAAM,KAAK,aAAa,iBAC3B,eACA;;;;;;;;;;;;;uBA9BA,KAAK,EAAE;;;;;;;;;;YAsCpB,SAAS,OAAO,KAAK,qBACpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAK,WAAU;sCAAqC;;;;;;;;;;;;;;;;;;;;;;;AAQjE;KA1FgB;AA4FhB,SAAS,cACP,MAAc,EACd,WAAmB,EACnB,YAAoB;IAEpB,IAAI,iBAAiB,KAAK,OAAO;IACjC,IAAI,WAAW,aAAa,OAAO;IACnC,IAAI,SAAS,aAAa,OAAO;IACjC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nconst AlertDialog = AlertDialogPrimitive.Root\r\n\r\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\r\n\r\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\r\n\r\nconst AlertDialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\r\n\r\nconst AlertDialogContent = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPortal>\r\n    <AlertDialogOverlay />\r\n    <AlertDialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </AlertDialogPortal>\r\n))\r\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\r\n\r\nconst AlertDialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\r\n\r\nconst AlertDialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\r\n\r\nconst AlertDialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\r\n\r\nconst AlertDialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogDescription.displayName =\r\n  AlertDialogPrimitive.Description.displayName\r\n\r\nconst AlertDialogAction = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Action\r\n    ref={ref}\r\n    className={cn(buttonVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\r\n\r\nconst AlertDialogCancel = React.forwardRef<\r\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\r\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\r\n>(({ className, ...props }, ref) => (\r\n  <AlertDialogPrimitive.Cancel\r\n    ref={ref}\r\n    className={cn(\r\n      buttonVariants({ variant: \"outline\" }),\r\n      \"mt-2 sm:mt-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/cbrand/steps/website-analysis-step.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Loader2, Globe, Sparkles, Upload, X, CheckCircle, Shield, AlertTriangle } from 'lucide-react';\r\nimport { toast } from '@/hooks/use-toast';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport { CompleteBrandProfile } from '../cbrand-wizard';\r\n\r\ninterface WebsiteAnalysisStepProps {\r\n  brandProfile: CompleteBrandProfile;\r\n  updateBrandProfile: (updates: Partial<CompleteBrandProfile>) => void;\r\n  onNext: () => void;\r\n}\r\n\r\nexport function WebsiteAnalysisStep({\r\n  brandProfile,\r\n  updateBrandProfile,\r\n  onNext\r\n}: WebsiteAnalysisStepProps) {\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [websiteUrl, setWebsiteUrl] = useState(brandProfile.websiteUrl || '');\r\n  const [designImages, setDesignImages] = useState<File[]>([]);\r\n  const [analysisComplete, setAnalysisComplete] = useState(false);\r\n  const [analysisProgress, setAnalysisProgress] = useState('');\r\n  const [analysisError, setAnalysisError] = useState('');\r\n\r\n  // Dialog states for friendly error handling\r\n  const [showAnalysisDialog, setShowAnalysisDialog] = useState(false);\r\n  const [dialogType, setDialogType] = useState<'blocked' | 'timeout' | 'error'>('error');\r\n  const [dialogMessage, setDialogMessage] = useState('');\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = Array.from(event.target.files || []);\r\n    const imageFiles = files.filter(file => file.type.startsWith('image/'));\r\n\r\n    if (imageFiles.length !== files.length) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Invalid Files\",\r\n        description: \"Please upload only image files.\",\r\n      });\r\n    }\r\n\r\n    // Check file sizes to prevent storage issues\r\n    const totalSize = imageFiles.reduce((sum, file) => sum + file.size, 0);\r\n    const maxSize = 25 * 1024 * 1024; // 25MB total limit for design files (increased for high-quality images)\r\n\r\n    if (totalSize > maxSize) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Files Too Large\",\r\n        description: \"Design examples are too large. Please use smaller images (max 25MB total).\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Limit to 3 design examples to prevent storage overflow\r\n    setDesignImages(prev => [...prev, ...imageFiles].slice(0, 3)); // Max 3 images for storage optimization\r\n\r\n    if (imageFiles.length > 0) {\r\n      toast({\r\n        title: \"Design Examples Added\",\r\n        description: `${imageFiles.length} design example(s) uploaded successfully.`,\r\n      });\r\n    }\r\n  };\r\n\r\n  const removeImage = (index: number) => {\r\n    setDesignImages(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const handleAnalyze = async () => {\r\n    if (!websiteUrl.trim()) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Website URL Required\",\r\n        description: \"Please enter a website URL to analyze.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsAnalyzing(true);\r\n    setAnalysisComplete(false);\r\n    setAnalysisError('');\r\n    setAnalysisProgress('Preparing comprehensive analysis...');\r\n\r\n    try {\r\n      // Convert images to data URLs with progress feedback\r\n      setAnalysisProgress('Processing design examples...');\r\n      const designImageUris: string[] = [];\r\n      for (let i = 0; i < designImages.length; i++) {\r\n        const file = designImages[i];\r\n        setAnalysisProgress(`Processing design example ${i + 1}/${designImages.length}...`);\r\n        const dataUrl = await new Promise<string>((resolve) => {\r\n          const reader = new FileReader();\r\n          reader.onload = (e) => resolve(e.target?.result as string);\r\n          reader.readAsDataURL(file);\r\n        });\r\n        designImageUris.push(dataUrl);\r\n      }\r\n\r\n      // Start comprehensive AI analysis with website scraping\r\n      setAnalysisProgress('🌐 Scraping website content and extracting text...');\r\n      const { analyzeBrandAction } = await import('@/app/actions');\r\n\r\n      // Add progress feedback for AI analysis\r\n      setAnalysisProgress('🤖 AI is analyzing website content and extracting company-specific information...');\r\n      const analysisResult = await analyzeBrandAction(websiteUrl, designImageUris);\r\n\r\n      // Check if analysis failed\r\n      if (!analysisResult.success) {\r\n        setAnalysisProgress('');\r\n        setDialogType(analysisResult.errorType);\r\n        setDialogMessage(analysisResult.error);\r\n        setShowAnalysisDialog(true);\r\n        return;\r\n      }\r\n\r\n      const result = analysisResult.data;\r\n\r\n      setAnalysisProgress('📊 Processing analysis results and organizing data...');\r\n\r\n      // Debug: Log what business name was extracted\r\n      console.log('🏢 AI Extracted Business Name:', result.businessName);\r\n      console.log('🏭 AI Extracted Business Type:', result.businessType);\r\n      console.log('📝 AI Extracted Description:', result.description);\r\n\r\n      // Ensure we have a proper business name - fallback to extracting from URL if needed\r\n      let businessName = result.businessName?.trim();\r\n      let businessType = result.businessType?.trim();\r\n\r\n      // Check if AI mixed up business name and business type\r\n      const businessNameWords = businessName?.toLowerCase().split(' ') || [];\r\n      const businessTypeWords = businessType?.toLowerCase().split(' ') || [];\r\n\r\n      // If business name contains generic business type words, it might be swapped\r\n      const genericBusinessWords = ['software', 'technology', 'company', 'corporation', 'inc', 'llc', 'development', 'solutions', 'services', 'consulting', 'agency', 'firm', 'group', 'enterprises', 'systems', 'platform', 'application', 'financial', 'lending', 'mixed-use'];\r\n\r\n      const businessNameHasGenericWords = businessNameWords.some(word => genericBusinessWords.includes(word));\r\n      const businessTypeHasSpecificWords = businessTypeWords.length > 0 && !businessTypeWords.some(word => genericBusinessWords.includes(word));\r\n\r\n      // If business name seems generic and business type seems specific, they might be swapped\r\n      if (businessNameHasGenericWords && businessTypeHasSpecificWords && businessType && businessType.length > 2) {\r\n        console.log('🔄 Detected potential name/type swap. Swapping them.');\r\n        console.log('🔄 Original Name:', businessName, '→ Type:', businessType);\r\n        const temp = businessName;\r\n        businessName = businessType;\r\n        businessType = temp;\r\n        console.log('🔄 After swap Name:', businessName, '→ Type:', businessType);\r\n      }\r\n\r\n      if (!businessName || businessName.length < 2) {\r\n        // Try to extract business name from URL as fallback\r\n        try {\r\n          const urlObj = new URL(websiteUrl.startsWith('http') ? websiteUrl : `https://${websiteUrl}`);\r\n          const domain = urlObj.hostname.replace(/^www\\./, '');\r\n          const domainParts = domain.split('.');\r\n          businessName = domainParts[0].charAt(0).toUpperCase() + domainParts[0].slice(1);\r\n          console.log('🔄 Using domain-based business name:', businessName);\r\n        } catch {\r\n          businessName = 'New Business';\r\n        }\r\n      }\r\n\r\n      // Parse services from AI result and convert to array format\r\n      const servicesArray = result.services\r\n        ? result.services.split('\\n').filter(service => service.trim()).map(service => {\r\n          // Enhanced parsing to handle detailed service descriptions\r\n          const colonIndex = service.indexOf(':');\r\n          if (colonIndex > 0) {\r\n            return {\r\n              name: service.substring(0, colonIndex).trim(),\r\n              description: service.substring(colonIndex + 1).trim()\r\n            };\r\n          } else {\r\n            // If no colon, check for dash\r\n            const dashIndex = service.indexOf(' - ');\r\n            if (dashIndex > 0) {\r\n              return {\r\n                name: service.substring(0, dashIndex).trim(),\r\n                description: service.substring(dashIndex + 3).trim()\r\n              };\r\n            } else {\r\n              // If no separator, use the whole thing as name\r\n              return {\r\n                name: service.trim(),\r\n                description: ''\r\n              };\r\n            }\r\n          }\r\n        })\r\n        : [];\r\n\r\n      // Extract color palette information\r\n      const primaryColor = result.colorPalette?.primary || '#3B82F6';\r\n      const accentColor = result.colorPalette?.secondary || result.colorPalette?.accent || '#10B981';\r\n      const backgroundColor = '#F8FAFC'; // Default background\r\n\r\n      // Debug: Log what we're actually saving\r\n      console.log('💾 Saving Business Name:', businessName);\r\n      console.log('💾 Saving Business Type:', businessType || '');\r\n\r\n      // Update the brand profile with comprehensive analysis results\r\n      updateBrandProfile({\r\n        // Basic Information\r\n        businessName: businessName,\r\n        websiteUrl,\r\n        description: result.description,\r\n        businessType: businessType || '',\r\n        location: result.location || '',\r\n\r\n        // Services and Products\r\n        services: servicesArray,\r\n        keyFeatures: result.keyFeatures || '',\r\n        competitiveAdvantages: result.competitiveAdvantages || '',\r\n        targetAudience: result.targetAudience || 'Target audience not specified on website',\r\n\r\n        // Brand Identity\r\n        visualStyle: result.visualStyle,\r\n        writingTone: result.writingTone,\r\n        contentThemes: result.contentThemes,\r\n\r\n        // Colors (extracted from AI analysis)\r\n        primaryColor,\r\n        accentColor,\r\n        backgroundColor,\r\n\r\n        // Contact Information\r\n        contactPhone: result.contactInfo?.phone || '',\r\n        contactEmail: result.contactInfo?.email || '',\r\n        contactAddress: result.contactInfo?.address || '',\r\n\r\n        // Social Media (if found by AI)\r\n        facebookUrl: result.socialMedia?.facebook || '',\r\n        instagramUrl: result.socialMedia?.instagram || '',\r\n        twitterUrl: result.socialMedia?.twitter || '',\r\n        linkedinUrl: result.socialMedia?.linkedin || '',\r\n\r\n        // Store design examples for future AI reference\r\n        designExamples: designImageUris,\r\n      });\r\n\r\n      setAnalysisProgress('Analysis complete! Extracted comprehensive brand information.');\r\n      setAnalysisComplete(true);\r\n\r\n      // Count extracted information for feedback\r\n      const extractedCount = [\r\n        result.description,\r\n        result.businessType,\r\n        result.services,\r\n        result.visualStyle,\r\n        result.writingTone,\r\n        result.contentThemes,\r\n        result.targetAudience,\r\n        result.keyFeatures,\r\n        result.competitiveAdvantages,\r\n        result.contactInfo?.phone,\r\n        result.contactInfo?.email,\r\n        result.contactInfo?.address,\r\n        result.socialMedia?.facebook,\r\n        result.socialMedia?.instagram,\r\n        result.socialMedia?.twitter,\r\n        result.socialMedia?.linkedin,\r\n        result.colorPalette?.primary,\r\n        result.location\r\n      ].filter(Boolean).length;\r\n\r\n      toast({\r\n        title: \"🎉 Enhanced Analysis Complete!\",\r\n        description: `AI extracted ${extractedCount} pieces of detailed brand information including target audience, comprehensive services, and color analysis from your designs.`,\r\n      });\r\n\r\n    } catch (error) {\r\n      // This catch is now for unexpected errors only\r\n      console.error('Unexpected analysis error:', error);\r\n      setAnalysisProgress('');\r\n      setDialogType('error');\r\n      setDialogMessage('An unexpected error occurred during analysis.');\r\n      setShowAnalysisDialog(true);\r\n    } finally {\r\n      setIsAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  const handleSkipAnalysis = () => {\r\n    updateBrandProfile({ websiteUrl });\r\n    onNext();\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (analysisComplete || brandProfile.description) {\r\n      onNext();\r\n    } else {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Analysis Required\",\r\n        description: \"Please run the website analysis or skip to continue manually.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Website URL Input */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Globe className=\"h-5 w-5\" />\r\n            AI Website Analysis\r\n          </CardTitle>\r\n          <p className=\"text-gray-600\">\r\n            Our AI will analyze your specific website and extract detailed, company-specific information\r\n          </p>\r\n          <div className=\"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\r\n            <p className=\"text-sm text-blue-800\">\r\n              <strong>🎯 Company-Specific Analysis:</strong> The AI will extract your exact service descriptions,\r\n              your specific target audience, your unique competitive advantages, and your actual brand voice -\r\n              not generic industry information.\r\n            </p>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <div>\r\n            <Label htmlFor=\"website-url\">Website URL</Label>\r\n            <Input\r\n              id=\"website-url\"\r\n              type=\"url\"\r\n              placeholder=\"https://your-website.com\"\r\n              value={websiteUrl}\r\n              onChange={(e) => setWebsiteUrl(e.target.value)}\r\n              className=\"mt-1\"\r\n            />\r\n          </div>\r\n\r\n          {/* Design Images Upload */}\r\n          <div>\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Label>Design Examples</Label>\r\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\r\n                Recommended\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3\">\r\n              <p className=\"text-sm text-blue-800 font-medium mb-1\">\r\n                📸 Upload up to 3 design examples to help AI understand your visual style (max 10MB total)\r\n              </p>\r\n              <p className=\"text-xs text-blue-700\">\r\n                <strong>For exact brand consistency:</strong> If you want the AI to produce content that matches your current brand design,\r\n                please upload at least 3-5 examples of your previous designs (social posts, marketing materials, brochures, ads, etc.)\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors\">\r\n              <input\r\n                type=\"file\"\r\n                multiple\r\n                accept=\"image/*\"\r\n                onChange={handleImageUpload}\r\n                className=\"hidden\"\r\n                id=\"design-upload\"\r\n                disabled={designImages.length >= 5}\r\n              />\r\n              <label\r\n                htmlFor=\"design-upload\"\r\n                className={`flex flex-col items-center justify-center cursor-pointer ${designImages.length >= 5 ? 'opacity-50 cursor-not-allowed' : ''\r\n                  }`}\r\n              >\r\n                <Upload className=\"h-8 w-8 text-gray-400 mb-2\" />\r\n                <span className=\"text-sm text-gray-600 text-center\">\r\n                  {designImages.length >= 5\r\n                    ? 'Maximum 5 images reached'\r\n                    : 'Upload social posts, marketing materials, ads (PNG, JPG, SVG)'\r\n                  }\r\n                </span>\r\n                <span className=\"text-xs text-gray-500 mt-1\">\r\n                  {designImages.length}/5 uploaded\r\n                </span>\r\n              </label>\r\n            </div>\r\n\r\n            {/* Uploaded Images Preview */}\r\n            {designImages.length > 0 && (\r\n              <div className=\"mt-4\">\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <span className=\"text-sm font-medium text-gray-700\">\r\n                    Uploaded Design Examples ({designImages.length}/5)\r\n                  </span>\r\n                  {designImages.length >= 3 && (\r\n                    <span className=\"text-xs text-green-600 font-medium\">\r\n                      ✓ Great! This should help AI understand your style\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                <div className=\"grid grid-cols-2 md:grid-cols-5 gap-3\">\r\n                  {designImages.map((file, index) => (\r\n                    <div key={index} className=\"relative group\">\r\n                      <img\r\n                        src={URL.createObjectURL(file)}\r\n                        alt={`Design ${index + 1}`}\r\n                        className=\"w-full h-24 object-cover rounded-lg border-2 border-gray-200 group-hover:border-blue-300 transition-colors\"\r\n                      />\r\n                      <button\r\n                        onClick={() => removeImage(index)}\r\n                        className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors shadow-sm\"\r\n                        title=\"Remove image\"\r\n                      >\r\n                        <X className=\"h-3 w-3\" />\r\n                      </button>\r\n                      <div className=\"absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded\">\r\n                        {index + 1}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {designImages.length < 3 && (\r\n                  <div className=\"mt-2 p-2 bg-amber-50 border border-amber-200 rounded text-xs text-amber-700\">\r\n                    💡 <strong>Tip:</strong> Upload at least 3 examples for better AI understanding of your brand style\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <Button\r\n            onClick={handleAnalyze}\r\n            disabled={isAnalyzing || !websiteUrl.trim()}\r\n            className=\"w-full\"\r\n          >\r\n            {isAnalyzing ? (\r\n              <>\r\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                {analysisProgress || 'Analyzing Website...'}\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Sparkles className=\"mr-2 h-4 w-4\" />\r\n                Analyze Website with AI\r\n              </>\r\n            )}\r\n          </Button>\r\n\r\n          {/* Progress Feedback */}\r\n          {isAnalyzing && analysisProgress && (\r\n            <div className=\"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Loader2 className=\"h-4 w-4 animate-spin text-blue-600\" />\r\n                <p className=\"text-sm text-blue-800\">{analysisProgress}</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Error Display */}\r\n          {analysisError && (\r\n            <div className=\"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg\">\r\n              <p className=\"text-sm text-red-800\">\r\n                <strong>Analysis Error:</strong> {analysisError}\r\n              </p>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Comprehensive Analysis Results */}\r\n      {analysisComplete && brandProfile.description && (\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\r\n              AI Analysis Results\r\n            </CardTitle>\r\n            <p className=\"text-sm text-gray-600\">\r\n              Review and edit the extracted information. All fields are editable and will be used to populate your brand profile.\r\n            </p>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-6\">\r\n            {/* Core Business Information */}\r\n            <div>\r\n              <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n                Core Business Information\r\n              </h4>\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <Label>Business Description</Label>\r\n                  <Textarea\r\n                    value={brandProfile.description}\r\n                    onChange={(e) => updateBrandProfile({ description: e.target.value })}\r\n                    rows={3}\r\n                    className=\"mt-1\"\r\n                    placeholder=\"Comprehensive business description...\"\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <Label>Business Type</Label>\r\n                    <Input\r\n                      value={brandProfile.businessType}\r\n                      onChange={(e) => updateBrandProfile({ businessType: e.target.value })}\r\n                      placeholder=\"e.g., Digital Marketing Agency\"\r\n                      className=\"mt-1\"\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <Label>Location</Label>\r\n                    <Input\r\n                      value={brandProfile.location}\r\n                      onChange={(e) => updateBrandProfile({ location: e.target.value })}\r\n                      placeholder=\"e.g., New York, NY\"\r\n                      className=\"mt-1\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Services and Target Audience */}\r\n            <div>\r\n              <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\r\n                Services & Audience\r\n              </h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <Label>Target Audience</Label>\r\n                  <Textarea\r\n                    value={brandProfile.targetAudience}\r\n                    onChange={(e) => updateBrandProfile({ targetAudience: e.target.value })}\r\n                    rows={2}\r\n                    className=\"mt-1\"\r\n                    placeholder=\"Target customer description...\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label>Key Features</Label>\r\n                  <Textarea\r\n                    value={brandProfile.keyFeatures}\r\n                    onChange={(e) => updateBrandProfile({ keyFeatures: e.target.value })}\r\n                    rows={2}\r\n                    className=\"mt-1\"\r\n                    placeholder=\"Key features and benefits...\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mt-4\">\r\n                <Label>Competitive Advantages</Label>\r\n                <Textarea\r\n                  value={brandProfile.competitiveAdvantages}\r\n                  onChange={(e) => updateBrandProfile({ competitiveAdvantages: e.target.value })}\r\n                  rows={2}\r\n                  className=\"mt-1\"\r\n                  placeholder=\"What makes this business unique...\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Brand Identity */}\r\n            <div>\r\n              <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n                <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\r\n                Brand Identity & Voice\r\n              </h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <Label>Visual Style</Label>\r\n                  <Textarea\r\n                    value={brandProfile.visualStyle}\r\n                    onChange={(e) => updateBrandProfile({ visualStyle: e.target.value })}\r\n                    rows={3}\r\n                    className=\"mt-1\"\r\n                    placeholder=\"Visual style and design characteristics...\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label>Writing Tone</Label>\r\n                  <Textarea\r\n                    value={brandProfile.writingTone}\r\n                    onChange={(e) => updateBrandProfile({ writingTone: e.target.value })}\r\n                    rows={3}\r\n                    className=\"mt-1\"\r\n                    placeholder=\"Brand voice and communication style...\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mt-4\">\r\n                <Label>Content Themes</Label>\r\n                <Textarea\r\n                  value={brandProfile.contentThemes}\r\n                  onChange={(e) => updateBrandProfile({ contentThemes: e.target.value })}\r\n                  rows={2}\r\n                  className=\"mt-1\"\r\n                  placeholder=\"Common content themes and topics...\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Contact Information */}\r\n            <div>\r\n              <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n                <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\r\n                Contact Information\r\n              </h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                <div>\r\n                  <Label>Phone</Label>\r\n                  <Input\r\n                    value={brandProfile.contactPhone}\r\n                    onChange={(e) => updateBrandProfile({ contactPhone: e.target.value })}\r\n                    placeholder=\"Phone number\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label>Email</Label>\r\n                  <Input\r\n                    value={brandProfile.contactEmail}\r\n                    onChange={(e) => updateBrandProfile({ contactEmail: e.target.value })}\r\n                    placeholder=\"Email address\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label>Address</Label>\r\n                  <Input\r\n                    value={brandProfile.contactAddress}\r\n                    onChange={(e) => updateBrandProfile({ contactAddress: e.target.value })}\r\n                    placeholder=\"Business address\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Social Media */}\r\n            {(brandProfile.facebookUrl || brandProfile.instagramUrl || brandProfile.twitterUrl || brandProfile.linkedinUrl) && (\r\n              <div>\r\n                <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n                  <div className=\"w-2 h-2 bg-cyan-500 rounded-full\"></div>\r\n                  Social Media (Found by AI)\r\n                </h4>\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  {brandProfile.facebookUrl && (\r\n                    <div>\r\n                      <Label>Facebook</Label>\r\n                      <Input\r\n                        value={brandProfile.facebookUrl}\r\n                        onChange={(e) => updateBrandProfile({ facebookUrl: e.target.value })}\r\n                        placeholder=\"Facebook URL\"\r\n                        className=\"mt-1\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                  {brandProfile.instagramUrl && (\r\n                    <div>\r\n                      <Label>Instagram</Label>\r\n                      <Input\r\n                        value={brandProfile.instagramUrl}\r\n                        onChange={(e) => updateBrandProfile({ instagramUrl: e.target.value })}\r\n                        placeholder=\"Instagram URL\"\r\n                        className=\"mt-1\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                  {brandProfile.twitterUrl && (\r\n                    <div>\r\n                      <Label>Twitter/X</Label>\r\n                      <Input\r\n                        value={brandProfile.twitterUrl}\r\n                        onChange={(e) => updateBrandProfile({ twitterUrl: e.target.value })}\r\n                        placeholder=\"Twitter URL\"\r\n                        className=\"mt-1\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                  {brandProfile.linkedinUrl && (\r\n                    <div>\r\n                      <Label>LinkedIn</Label>\r\n                      <Input\r\n                        value={brandProfile.linkedinUrl}\r\n                        onChange={(e) => updateBrandProfile({ linkedinUrl: e.target.value })}\r\n                        placeholder=\"LinkedIn URL\"\r\n                        className=\"mt-1\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Brand Colors */}\r\n            <div>\r\n              <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n                <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\r\n                Brand Colors (AI Detected)\r\n              </h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                <div>\r\n                  <Label>Primary Color</Label>\r\n                  <div className=\"flex items-center gap-2 mt-1\">\r\n                    <div\r\n                      className=\"w-8 h-8 rounded border border-gray-300\"\r\n                      style={{ backgroundColor: brandProfile.primaryColor }}\r\n                    ></div>\r\n                    <Input\r\n                      value={brandProfile.primaryColor}\r\n                      onChange={(e) => updateBrandProfile({ primaryColor: e.target.value })}\r\n                      placeholder=\"#3B82F6\"\r\n                      className=\"flex-1\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <Label>Accent Color</Label>\r\n                  <div className=\"flex items-center gap-2 mt-1\">\r\n                    <div\r\n                      className=\"w-8 h-8 rounded border border-gray-300\"\r\n                      style={{ backgroundColor: brandProfile.accentColor }}\r\n                    ></div>\r\n                    <Input\r\n                      value={brandProfile.accentColor}\r\n                      onChange={(e) => updateBrandProfile({ accentColor: e.target.value })}\r\n                      placeholder=\"#10B981\"\r\n                      className=\"flex-1\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <Label>Background Color</Label>\r\n                  <div className=\"flex items-center gap-2 mt-1\">\r\n                    <div\r\n                      className=\"w-8 h-8 rounded border border-gray-300\"\r\n                      style={{ backgroundColor: brandProfile.backgroundColor }}\r\n                    ></div>\r\n                    <Input\r\n                      value={brandProfile.backgroundColor}\r\n                      onChange={(e) => updateBrandProfile({ backgroundColor: e.target.value })}\r\n                      placeholder=\"#F8FAFC\"\r\n                      className=\"flex-1\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\r\n              <div className=\"flex items-start gap-3\">\r\n                <CheckCircle className=\"h-5 w-5 text-green-600 mt-0.5\" />\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-green-800\">\r\n                    AI Analysis Complete!\r\n                  </p>\r\n                  <p className=\"text-xs text-green-700 mt-1\">\r\n                    The AI has extracted comprehensive information from your website and design examples.\r\n                    You can edit any field above, and this information will be used to populate your brand profile in the next steps.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex justify-between\">\r\n        <Button\r\n          variant=\"outline\"\r\n          onClick={handleSkipAnalysis}\r\n        >\r\n          Skip Analysis\r\n        </Button>\r\n\r\n        <Button\r\n          onClick={handleNext}\r\n          disabled={!analysisComplete && !brandProfile.description}\r\n        >\r\n          Continue to Brand Details\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Friendly Analysis Dialog */}\r\n      <AlertDialog open={showAnalysisDialog} onOpenChange={setShowAnalysisDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle className=\"flex items-center gap-2\">\r\n              {dialogType === 'blocked' && <Shield className=\"h-5 w-5 text-blue-500\" />}\r\n              {dialogType === 'timeout' && <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />}\r\n              {dialogType === 'error' && <AlertTriangle className=\"h-5 w-5 text-orange-500\" />}\r\n\r\n              {dialogType === 'blocked' && 'Website Analysis Blocked'}\r\n              {dialogType === 'timeout' && 'Analysis Timed Out'}\r\n              {dialogType === 'error' && 'Analysis Unavailable'}\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription className=\"space-y-3\">\r\n              <p>{dialogMessage}</p>\r\n\r\n              {dialogType === 'blocked' && (\r\n                <div className=\"bg-blue-50 p-3 rounded-lg\">\r\n                  <p className=\"text-sm text-blue-800\">\r\n                    <strong>Don't worry!</strong> Many professional websites block automated tools for security.\r\n                    You can still create an amazing brand profile by filling in the details manually.\r\n                  </p>\r\n                </div>\r\n              )}\r\n\r\n              {dialogType === 'timeout' && (\r\n                <div className=\"bg-yellow-50 p-3 rounded-lg\">\r\n                  <p className=\"text-sm text-yellow-800\">\r\n                    <strong>No problem!</strong> You can try again later or proceed manually.\r\n                    The manual setup gives you full control over your brand information.\r\n                  </p>\r\n                </div>\r\n              )}\r\n\r\n              {dialogType === 'error' && (\r\n                <div className=\"bg-orange-50 p-3 rounded-lg\">\r\n                  <p className=\"text-sm text-orange-800\">\r\n                    <strong>That's okay!</strong> Technical issues happen sometimes.\r\n                    You can create an excellent brand profile by entering the information yourself.\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel onClick={() => setWebsiteUrl('')}>\r\n              Try Different Website\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction onClick={() => {\r\n              setShowAnalysisDialog(false);\r\n              handleSkipAnalysis();\r\n            }}>\r\n              Continue Manually\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAVA;;;;;;;;;;AA4BO,SAAS,oBAAoB,EAClC,YAAY,EACZ,kBAAkB,EAClB,MAAM,EACmB;;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,UAAU,IAAI;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,4CAA4C;IAC5C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,EAAE;QACjD,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;QAE7D,IAAI,WAAW,MAAM,KAAK,MAAM,MAAM,EAAE;YACtC,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;QAEA,6CAA6C;QAC7C,MAAM,YAAY,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,IAAI,EAAE;QACpE,MAAM,UAAU,KAAK,OAAO,MAAM,wEAAwE;QAE1G,IAAI,YAAY,SAAS;YACvB,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,yDAAyD;QACzD,gBAAgB,CAAA,OAAQ;mBAAI;mBAAS;aAAW,CAAC,KAAK,CAAC,GAAG,KAAK,wCAAwC;QAEvG,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,OAAO;gBACP,aAAa,GAAG,WAAW,MAAM,CAAC,yCAAyC,CAAC;YAC9E;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACtD;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,eAAe;QACf,oBAAoB;QACpB,iBAAiB;QACjB,oBAAoB;QAEpB,IAAI;YACF,qDAAqD;YACrD,oBAAoB;YACpB,MAAM,kBAA4B,EAAE;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,MAAM,OAAO,YAAY,CAAC,EAAE;gBAC5B,oBAAoB,CAAC,0BAA0B,EAAE,IAAI,EAAE,CAAC,EAAE,aAAa,MAAM,CAAC,GAAG,CAAC;gBAClF,MAAM,UAAU,MAAM,IAAI,QAAgB,CAAC;oBACzC,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG,CAAC,IAAM,QAAQ,EAAE,MAAM,EAAE;oBACzC,OAAO,aAAa,CAAC;gBACvB;gBACA,gBAAgB,IAAI,CAAC;YACvB;YAEA,wDAAwD;YACxD,oBAAoB;YACpB,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAE/B,wCAAwC;YACxC,oBAAoB;YACpB,MAAM,iBAAiB,MAAM,mBAAmB,YAAY;YAE5D,2BAA2B;YAC3B,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,oBAAoB;gBACpB,cAAc,eAAe,SAAS;gBACtC,iBAAiB,eAAe,KAAK;gBACrC,sBAAsB;gBACtB;YACF;YAEA,MAAM,SAAS,eAAe,IAAI;YAElC,oBAAoB;YAEpB,8CAA8C;YAC9C,QAAQ,GAAG,CAAC,kCAAkC,OAAO,YAAY;YACjE,QAAQ,GAAG,CAAC,kCAAkC,OAAO,YAAY;YACjE,QAAQ,GAAG,CAAC,gCAAgC,OAAO,WAAW;YAE9D,oFAAoF;YACpF,IAAI,eAAe,OAAO,YAAY,EAAE;YACxC,IAAI,eAAe,OAAO,YAAY,EAAE;YAExC,uDAAuD;YACvD,MAAM,oBAAoB,cAAc,cAAc,MAAM,QAAQ,EAAE;YACtE,MAAM,oBAAoB,cAAc,cAAc,MAAM,QAAQ,EAAE;YAEtE,6EAA6E;YAC7E,MAAM,uBAAuB;gBAAC;gBAAY;gBAAc;gBAAW;gBAAe;gBAAO;gBAAO;gBAAe;gBAAa;gBAAY;gBAAc;gBAAU;gBAAQ;gBAAS;gBAAe;gBAAW;gBAAY;gBAAe;gBAAa;gBAAW;aAAY;YAE1Q,MAAM,8BAA8B,kBAAkB,IAAI,CAAC,CAAA,OAAQ,qBAAqB,QAAQ,CAAC;YACjG,MAAM,+BAA+B,kBAAkB,MAAM,GAAG,KAAK,CAAC,kBAAkB,IAAI,CAAC,CAAA,OAAQ,qBAAqB,QAAQ,CAAC;YAEnI,yFAAyF;YACzF,IAAI,+BAA+B,gCAAgC,gBAAgB,aAAa,MAAM,GAAG,GAAG;gBAC1G,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,qBAAqB,cAAc,WAAW;gBAC1D,MAAM,OAAO;gBACb,eAAe;gBACf,eAAe;gBACf,QAAQ,GAAG,CAAC,uBAAuB,cAAc,WAAW;YAC9D;YAEA,IAAI,CAAC,gBAAgB,aAAa,MAAM,GAAG,GAAG;gBAC5C,oDAAoD;gBACpD,IAAI;oBACF,MAAM,SAAS,IAAI,IAAI,WAAW,UAAU,CAAC,UAAU,aAAa,CAAC,QAAQ,EAAE,YAAY;oBAC3F,MAAM,SAAS,OAAO,QAAQ,CAAC,OAAO,CAAC,UAAU;oBACjD,MAAM,cAAc,OAAO,KAAK,CAAC;oBACjC,eAAe,WAAW,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC;oBAC7E,QAAQ,GAAG,CAAC,wCAAwC;gBACtD,EAAE,OAAM;oBACN,eAAe;gBACjB;YACF;YAEA,4DAA4D;YAC5D,MAAM,gBAAgB,OAAO,QAAQ,GACjC,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAA;gBAClE,2DAA2D;gBAC3D,MAAM,aAAa,QAAQ,OAAO,CAAC;gBACnC,IAAI,aAAa,GAAG;oBAClB,OAAO;wBACL,MAAM,QAAQ,SAAS,CAAC,GAAG,YAAY,IAAI;wBAC3C,aAAa,QAAQ,SAAS,CAAC,aAAa,GAAG,IAAI;oBACrD;gBACF,OAAO;oBACL,8BAA8B;oBAC9B,MAAM,YAAY,QAAQ,OAAO,CAAC;oBAClC,IAAI,YAAY,GAAG;wBACjB,OAAO;4BACL,MAAM,QAAQ,SAAS,CAAC,GAAG,WAAW,IAAI;4BAC1C,aAAa,QAAQ,SAAS,CAAC,YAAY,GAAG,IAAI;wBACpD;oBACF,OAAO;wBACL,+CAA+C;wBAC/C,OAAO;4BACL,MAAM,QAAQ,IAAI;4BAClB,aAAa;wBACf;oBACF;gBACF;YACF,KACE,EAAE;YAEN,oCAAoC;YACpC,MAAM,eAAe,OAAO,YAAY,EAAE,WAAW;YACrD,MAAM,cAAc,OAAO,YAAY,EAAE,aAAa,OAAO,YAAY,EAAE,UAAU;YACrF,MAAM,kBAAkB,WAAW,qBAAqB;YAExD,wCAAwC;YACxC,QAAQ,GAAG,CAAC,4BAA4B;YACxC,QAAQ,GAAG,CAAC,4BAA4B,gBAAgB;YAExD,+DAA+D;YAC/D,mBAAmB;gBACjB,oBAAoB;gBACpB,cAAc;gBACd;gBACA,aAAa,OAAO,WAAW;gBAC/B,cAAc,gBAAgB;gBAC9B,UAAU,OAAO,QAAQ,IAAI;gBAE7B,wBAAwB;gBACxB,UAAU;gBACV,aAAa,OAAO,WAAW,IAAI;gBACnC,uBAAuB,OAAO,qBAAqB,IAAI;gBACvD,gBAAgB,OAAO,cAAc,IAAI;gBAEzC,iBAAiB;gBACjB,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,WAAW;gBAC/B,eAAe,OAAO,aAAa;gBAEnC,sCAAsC;gBACtC;gBACA;gBACA;gBAEA,sBAAsB;gBACtB,cAAc,OAAO,WAAW,EAAE,SAAS;gBAC3C,cAAc,OAAO,WAAW,EAAE,SAAS;gBAC3C,gBAAgB,OAAO,WAAW,EAAE,WAAW;gBAE/C,gCAAgC;gBAChC,aAAa,OAAO,WAAW,EAAE,YAAY;gBAC7C,cAAc,OAAO,WAAW,EAAE,aAAa;gBAC/C,YAAY,OAAO,WAAW,EAAE,WAAW;gBAC3C,aAAa,OAAO,WAAW,EAAE,YAAY;gBAE7C,gDAAgD;gBAChD,gBAAgB;YAClB;YAEA,oBAAoB;YACpB,oBAAoB;YAEpB,2CAA2C;YAC3C,MAAM,iBAAiB;gBACrB,OAAO,WAAW;gBAClB,OAAO,YAAY;gBACnB,OAAO,QAAQ;gBACf,OAAO,WAAW;gBAClB,OAAO,WAAW;gBAClB,OAAO,aAAa;gBACpB,OAAO,cAAc;gBACrB,OAAO,WAAW;gBAClB,OAAO,qBAAqB;gBAC5B,OAAO,WAAW,EAAE;gBACpB,OAAO,WAAW,EAAE;gBACpB,OAAO,WAAW,EAAE;gBACpB,OAAO,WAAW,EAAE;gBACpB,OAAO,WAAW,EAAE;gBACpB,OAAO,WAAW,EAAE;gBACpB,OAAO,WAAW,EAAE;gBACpB,OAAO,YAAY,EAAE;gBACrB,OAAO,QAAQ;aAChB,CAAC,MAAM,CAAC,SAAS,MAAM;YAExB,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,OAAO;gBACP,aAAa,CAAC,aAAa,EAAE,eAAe,8HAA8H,CAAC;YAC7K;QAEF,EAAE,OAAO,OAAO;YACd,+CAA+C;YAC/C,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,oBAAoB;YACpB,cAAc;YACd,iBAAiB;YACjB,sBAAsB;QACxB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,qBAAqB;QACzB,mBAAmB;YAAE;QAAW;QAChC;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,oBAAoB,aAAa,WAAW,EAAE;YAChD;QACF,OAAO;YACL,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAG7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;sDAAO;;;;;;wCAAsC;;;;;;;;;;;;;;;;;;kCAMpD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;;kDACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAK,WAAU;0DAAgG;;;;;;;;;;;;kDAKlH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAyC;;;;;;0DAGtD,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAqC;;;;;;;;;;;;;kDAKjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,QAAQ;gDACR,QAAO;gDACP,UAAU;gDACV,WAAU;gDACV,IAAG;gDACH,UAAU,aAAa,MAAM,IAAI;;;;;;0DAEnC,6LAAC;gDACC,SAAQ;gDACR,WAAW,CAAC,yDAAyD,EAAE,aAAa,MAAM,IAAI,IAAI,kCAAkC,IAChI;;kEAEJ,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEACb,aAAa,MAAM,IAAI,IACpB,6BACA;;;;;;kEAGN,6LAAC;wDAAK,WAAU;;4DACb,aAAa,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;oCAM1B,aAAa,MAAM,GAAG,mBACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DAAoC;4DACvB,aAAa,MAAM;4DAAC;;;;;;;oDAEhD,aAAa,MAAM,IAAI,mBACtB,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;0DAKzD,6LAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEACC,KAAK,IAAI,eAAe,CAAC;gEACzB,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG;gEAC1B,WAAU;;;;;;0EAEZ,6LAAC;gEACC,SAAS,IAAM,YAAY;gEAC3B,WAAU;gEACV,OAAM;0EAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;0EAEf,6LAAC;gEAAI,WAAU;0EACZ,QAAQ;;;;;;;uDAdH;;;;;;;;;;4CAoBb,aAAa,MAAM,GAAG,mBACrB,6LAAC;gDAAI,WAAU;;oDAA8E;kEACxF,6LAAC;kEAAO;;;;;;oDAAa;;;;;;;;;;;;;;;;;;;0CAOlC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,eAAe,CAAC,WAAW,IAAI;gCACzC,WAAU;0CAET,4BACC;;sDACE,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAClB,oBAAoB;;iEAGvB;;sDACE,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;4BAO1C,eAAe,kCACd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;4BAM3C,+BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;sDAAO;;;;;;wCAAwB;wCAAE;;;;;;;;;;;;;;;;;;;;;;;;YAQ3C,oBAAoB,aAAa,WAAW,kBAC3C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAA2B;;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAAyC;;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,uIAAA,CAAA,WAAQ;wDACP,OAAO,aAAa,WAAW;wDAC/B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,6LAAC,oIAAA,CAAA,QAAK;gEACJ,OAAO,aAAa,YAAY;gEAChC,UAAU,CAAC,IAAM,mBAAmB;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACnE,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAGd,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,6LAAC,oIAAA,CAAA,QAAK;gEACJ,OAAO,aAAa,QAAQ;gEAC5B,UAAU,CAAC,IAAM,mBAAmB;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC/D,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQpB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAA0C;;;;;;;kDAG3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,uIAAA,CAAA,WAAQ;wDACP,OAAO,aAAa,cAAc;wDAClC,UAAU,CAAC,IAAM,mBAAmB;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAGhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,uIAAA,CAAA,WAAQ;wDACP,OAAO,aAAa,WAAW;wDAC/B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,uIAAA,CAAA,WAAQ;gDACP,OAAO,aAAa,qBAAqB;gDACzC,UAAU,CAAC,IAAM,mBAAmB;wDAAE,uBAAuB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC5E,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAMlB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAA2C;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,uIAAA,CAAA,WAAQ;wDACP,OAAO,aAAa,WAAW;wDAC/B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAGhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,uIAAA,CAAA,WAAQ;wDACP,OAAO,aAAa,WAAW;wDAC/B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,uIAAA,CAAA,WAAQ;gDACP,OAAO,aAAa,aAAa;gDACjC,UAAU,CAAC,IAAM,mBAAmB;wDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACpE,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAMlB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAA2C;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,aAAa,YAAY;wDAChC,UAAU,CAAC,IAAM,mBAAmB;gEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACnE,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,aAAa,YAAY;wDAChC,UAAU,CAAC,IAAM,mBAAmB;gEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACnE,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,aAAa,cAAc;wDAClC,UAAU,CAAC,IAAM,mBAAmB;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE,aAAY;wDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;4BAOjB,CAAC,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,UAAU,IAAI,aAAa,WAAW,mBAC5G,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAAyC;;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;;4CACZ,aAAa,WAAW,kBACvB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,aAAa,WAAW;wDAC/B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,aAAY;wDACZ,WAAU;;;;;;;;;;;;4CAIf,aAAa,YAAY,kBACxB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,aAAa,YAAY;wDAChC,UAAU,CAAC,IAAM,mBAAmB;gEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACnE,aAAY;wDACZ,WAAU;;;;;;;;;;;;4CAIf,aAAa,UAAU,kBACtB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,aAAa,UAAU;wDAC9B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACjE,aAAY;wDACZ,WAAU;;;;;;;;;;;;4CAIf,aAAa,WAAW,kBACvB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC,oIAAA,CAAA,QAAK;wDACJ,OAAO,aAAa,WAAW;wDAC/B,UAAU,CAAC,IAAM,mBAAmB;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAClE,aAAY;wDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAStB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAAwC;;;;;;;kDAGzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,YAAY;gEAAC;;;;;;0EAEtD,6LAAC,oIAAA,CAAA,QAAK;gEACJ,OAAO,aAAa,YAAY;gEAChC,UAAU,CAAC,IAAM,mBAAmB;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACnE,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,WAAW;gEAAC;;;;;;0EAErD,6LAAC,oIAAA,CAAA,QAAK;gEACJ,OAAO,aAAa,WAAW;gEAC/B,UAAU,CAAC,IAAM,mBAAmB;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAClE,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;kEAAC;;;;;;kEACP,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,eAAe;gEAAC;;;;;;0EAEzD,6LAAC,oIAAA,CAAA,QAAK;gEACJ,OAAO,aAAa,eAAe;gEACnC,UAAU,CAAC,IAAM,mBAAmB;wEAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACtE,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;kCACV;;;;;;kCAID,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC,oBAAoB,CAAC,aAAa,WAAW;kCACzD;;;;;;;;;;;;0BAMH,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAoB,cAAc;0BACnD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;oCAAC,WAAU;;wCACzB,eAAe,2BAAa,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAC9C,eAAe,2BAAa,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCACrD,eAAe,yBAAW,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAEnD,eAAe,aAAa;wCAC5B,eAAe,aAAa;wCAC5B,eAAe,WAAW;;;;;;;8CAE7B,6LAAC,8IAAA,CAAA,yBAAsB;oCAAC,WAAU;;sDAChC,6LAAC;sDAAG;;;;;;wCAEH,eAAe,2BACd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAqB;;;;;;;;;;;;wCAMlC,eAAe,2BACd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAoB;;;;;;;;;;;;wCAMjC,eAAe,yBACd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAAqB;;;;;;;;;;;;;;;;;;;;;;;;sCAOvC,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,SAAS,IAAM,cAAc;8CAAK;;;;;;8CAGrD,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,SAAS;wCAC1B,sBAAsB;wCACtB;oCACF;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQf;GA5zBgB;KAAA", "debugId": null}}, {"offset": {"line": 2472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2538, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/cbrand/steps/brand-details-step.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { useUnifiedBrand } from '@/contexts/unified-brand-context';\r\nimport {\r\n  Building2,\r\n  Users,\r\n  Phone,\r\n  Palette,\r\n  Hash,\r\n  Share2,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  Plus,\r\n  Trash2\r\n} from 'lucide-react';\r\nimport { CompleteBrandProfile } from '../cbrand-wizard';\r\n\r\ninterface BrandDetailsStepProps {\r\n  brandProfile: CompleteBrandProfile;\r\n  updateBrandProfile: (updates: Partial<CompleteBrandProfile>) => void;\r\n  onNext: () => void;\r\n  onPrevious: () => void;\r\n}\r\n\r\nexport function BrandDetailsStep({\r\n  brandProfile,\r\n  updateBrandProfile,\r\n  onNext,\r\n  onPrevious\r\n}: BrandDetailsStepProps) {\r\n  const [activeTab, setActiveTab] = useState('basic');\r\n  const { currentBrand, updateProfile, selectBrand } = useUnifiedBrand();\r\n\r\n  const handleInputChange = async (field: keyof CompleteBrandProfile, value: string) => {\r\n    console.log('🔧 handleInputChange called:', { field, value });\r\n    console.log('🔧 updateBrandProfile function:', typeof updateBrandProfile, updateBrandProfile);\r\n\r\n    // Update local state immediately\r\n    updateBrandProfile({ [field]: value });\r\n\r\n    // If this is a color update and we have a current brand with an ID, save to Firebase immediately\r\n    const isColorUpdate = field === 'primaryColor' || field === 'accentColor' || field === 'backgroundColor';\r\n    if (isColorUpdate && currentBrand?.id) {\r\n      try {\r\n        console.log('🎨 Color update detected in BrandDetailsStep, saving to Firebase:', {\r\n          brandId: currentBrand.id,\r\n          field,\r\n          value\r\n        });\r\n\r\n        // Save color changes to Firebase immediately\r\n        await updateProfile(currentBrand.id, { [field]: value });\r\n\r\n        // Update the current brand in the unified context with new colors\r\n        const updatedBrand = { ...currentBrand, [field]: value };\r\n        selectBrand(updatedBrand);\r\n\r\n        console.log('✅ Color changes saved to Firebase and context updated from BrandDetailsStep');\r\n      } catch (error) {\r\n        console.error('❌ Failed to save color changes to Firebase from BrandDetailsStep:', error);\r\n        // Don't throw error to avoid disrupting user experience\r\n      }\r\n    }\r\n  };\r\n\r\n  const addService = () => {\r\n    const newService = { name: '', description: '' };\r\n    updateBrandProfile({\r\n      services: [...brandProfile.services, newService]\r\n    });\r\n  };\r\n\r\n  const updateService = (index: number, field: 'name' | 'description', value: string) => {\r\n    const updatedServices = brandProfile.services.map((service, i) =>\r\n      i === index ? { ...service, [field]: value } : service\r\n    );\r\n    updateBrandProfile({ services: updatedServices });\r\n  };\r\n\r\n  const removeService = (index: number) => {\r\n    const updatedServices = brandProfile.services.filter((_, i) => i !== index);\r\n    updateBrandProfile({ services: updatedServices });\r\n  };\r\n\r\n  const sections = [\r\n    { id: 'basic', label: 'Basic Info', icon: Building2 },\r\n    { id: 'services', label: 'Services', icon: Users },\r\n    { id: 'contact', label: 'Contact', icon: Phone },\r\n    { id: 'identity', label: 'Brand Identity', icon: Palette },\r\n    { id: 'colors', label: 'Colors', icon: Hash },\r\n    { id: 'social', label: 'Social (Optional)', icon: Share2 },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Complete Brand Details</CardTitle>\r\n          <p className=\"text-gray-600\">\r\n            Fill in comprehensive information about your brand across 6 key areas\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n            <TabsList className=\"grid w-full grid-cols-6\">\r\n              {sections.map((section) => {\r\n                const Icon = section.icon;\r\n                return (\r\n                  <TabsTrigger\r\n                    key={section.id}\r\n                    value={section.id}\r\n                    className=\"flex flex-col items-center gap-1 p-2\"\r\n                  >\r\n                    <Icon className=\"h-4 w-4\" />\r\n                    <span className=\"text-xs\">{section.label}</span>\r\n                  </TabsTrigger>\r\n                );\r\n              })}\r\n            </TabsList>\r\n\r\n            {/* Section 1: Basic Information */}\r\n            <TabsContent value=\"basic\" className=\"space-y-4 mt-6\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <Label htmlFor=\"businessName\">Business Name *</Label>\r\n                  <Input\r\n                    id=\"businessName\"\r\n                    value={brandProfile.businessName || ''}\r\n                    onChange={(e) => handleInputChange('businessName', e.target.value)}\r\n                    placeholder=\"e.g., ABC Development Company, Metro Properties, The Corner Cafe\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                  <p className=\"text-xs text-muted-foreground mt-1\">\r\n                    Enter your actual brand/company name, not the business type\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <Label htmlFor=\"businessType\">Business Type *</Label>\r\n                  <Input\r\n                    id=\"businessType\"\r\n                    value={brandProfile.businessType || ''}\r\n                    onChange={(e) => handleInputChange('businessType', e.target.value)}\r\n                    placeholder=\"e.g., Real Estate Development, Restaurant, Tech Startup\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                  <p className=\"text-xs text-muted-foreground mt-1\">\r\n                    Enter the category/industry your business operates in\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"location\">Location *</Label>\r\n                <Input\r\n                  id=\"location\"\r\n                  value={brandProfile.location || ''}\r\n                  onChange={(e) => handleInputChange('location', e.target.value)}\r\n                  placeholder=\"City, State/Country\"\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"description\">Business Description *</Label>\r\n                <Textarea\r\n                  id=\"description\"\r\n                  value={brandProfile.description || ''}\r\n                  onChange={(e) => handleInputChange('description', e.target.value)}\r\n                  placeholder=\"Describe what your business does, your mission, and what makes you unique...\"\r\n                  rows={4}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n            </TabsContent>\r\n\r\n            {/* Section 2: Services & Target Audience */}\r\n            <TabsContent value=\"services\" className=\"space-y-4 mt-6\">\r\n              <div>\r\n                <div className=\"mb-3\">\r\n                  <Label>Services/Products Offered *</Label>\r\n                  <p className=\"text-xs text-gray-500 mt-1\">Add your main services or products (description optional)</p>\r\n                </div>\r\n\r\n                {brandProfile.services.length === 0 && (\r\n                  <div className=\"text-center py-8 border-2 border-dashed border-gray-300 rounded-lg\">\r\n                    <p className=\"text-gray-500 mb-3\">No services added yet</p>\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      onClick={addService}\r\n                      className=\"flex items-center gap-2\"\r\n                    >\r\n                      <Plus className=\"h-4 w-4\" />\r\n                      Add Your First Service\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n\r\n                {brandProfile.services.length > 0 && (\r\n                  <div className=\"space-y-3\">\r\n                    {/* Column Headers */}\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3 px-3\">\r\n                      <Label className=\"text-xs font-medium text-gray-600\">Product/Service Name *</Label>\r\n                      <Label className=\"text-xs font-medium text-gray-600\">Description (Optional)</Label>\r\n                    </div>\r\n\r\n                    {brandProfile.services.map((service, index) => (\r\n                      <div key={index} className=\"border rounded p-3 bg-gray-50\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                          <div className=\"flex-1 grid grid-cols-1 md:grid-cols-2 gap-3\">\r\n                            <Input\r\n                              value={service.name || ''}\r\n                              onChange={(e) => updateService(index, 'name', e.target.value)}\r\n                              placeholder=\"e.g., Web Design, Consulting\"\r\n                            />\r\n                            <Input\r\n                              value={service.description || ''}\r\n                              onChange={(e) => updateService(index, 'description', e.target.value)}\r\n                              placeholder=\"Brief description...\"\r\n                            />\r\n                          </div>\r\n                          {brandProfile.services.length > 1 && (\r\n                            <Button\r\n                              type=\"button\"\r\n                              variant=\"ghost\"\r\n                              size=\"sm\"\r\n                              onClick={() => removeService(index)}\r\n                              className=\"text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0\"\r\n                            >\r\n                              <Trash2 className=\"h-4 w-4\" />\r\n                            </Button>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n\r\n                    <div className=\"flex justify-end mt-3\">\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={addService}\r\n                        className=\"flex items-center gap-1\"\r\n                      >\r\n                        <Plus className=\"h-3 w-3\" />\r\n                        Add\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"targetAudience\">Target Audience</Label>\r\n                <Textarea\r\n                  id=\"targetAudience\"\r\n                  value={brandProfile.targetAudience || ''}\r\n                  onChange={(e) => handleInputChange('targetAudience', e.target.value)}\r\n                  placeholder=\"Describe your ideal customers, their demographics, interests, and needs...\"\r\n                  rows={3}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"keyFeatures\">Key Features</Label>\r\n                <Textarea\r\n                  id=\"keyFeatures\"\r\n                  value={brandProfile.keyFeatures || ''}\r\n                  onChange={(e) => handleInputChange('keyFeatures', e.target.value)}\r\n                  placeholder=\"What are the standout features of your products/services?\"\r\n                  rows={3}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"competitiveAdvantages\">Competitive Advantages</Label>\r\n                <Textarea\r\n                  id=\"competitiveAdvantages\"\r\n                  value={brandProfile.competitiveAdvantages || ''}\r\n                  onChange={(e) => handleInputChange('competitiveAdvantages', e.target.value)}\r\n                  placeholder=\"What sets you apart from competitors?\"\r\n                  rows={3}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n            </TabsContent>\r\n\r\n            {/* Section 3: Contact Information */}\r\n            <TabsContent value=\"contact\" className=\"space-y-4 mt-6\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <Label htmlFor=\"contactPhone\">Phone Number</Label>\r\n                  <Input\r\n                    id=\"contactPhone\"\r\n                    type=\"tel\"\r\n                    value={brandProfile.contactPhone || ''}\r\n                    onChange={(e) => handleInputChange('contactPhone', e.target.value)}\r\n                    placeholder=\"+****************\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label htmlFor=\"contactEmail\">Email Address</Label>\r\n                  <Input\r\n                    id=\"contactEmail\"\r\n                    type=\"email\"\r\n                    value={brandProfile.contactEmail || ''}\r\n                    onChange={(e) => handleInputChange('contactEmail', e.target.value)}\r\n                    placeholder=\"<EMAIL>\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"contactAddress\">Physical Address</Label>\r\n                <Textarea\r\n                  id=\"contactAddress\"\r\n                  value={brandProfile.contactAddress || ''}\r\n                  onChange={(e) => handleInputChange('contactAddress', e.target.value)}\r\n                  placeholder=\"Street address, city, state, zip code\"\r\n                  rows={3}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n            </TabsContent>\r\n\r\n            {/* Section 4: Brand Identity & Voice */}\r\n            <TabsContent value=\"identity\" className=\"space-y-4 mt-6\">\r\n              <div>\r\n                <Label htmlFor=\"visualStyle\">Visual Style</Label>\r\n                <Textarea\r\n                  id=\"visualStyle\"\r\n                  value={brandProfile.visualStyle || ''}\r\n                  onChange={(e) => handleInputChange('visualStyle', e.target.value)}\r\n                  placeholder=\"Describe your brand's visual style, colors, fonts, imagery preferences...\"\r\n                  rows={3}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"writingTone\">Writing Tone</Label>\r\n                <Textarea\r\n                  id=\"writingTone\"\r\n                  value={brandProfile.writingTone || ''}\r\n                  onChange={(e) => handleInputChange('writingTone', e.target.value)}\r\n                  placeholder=\"How does your brand communicate? (e.g., professional, friendly, casual, authoritative)\"\r\n                  rows={3}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"contentThemes\">Content Themes</Label>\r\n                <Textarea\r\n                  id=\"contentThemes\"\r\n                  value={brandProfile.contentThemes || ''}\r\n                  onChange={(e) => handleInputChange('contentThemes', e.target.value)}\r\n                  placeholder=\"What topics and themes does your brand focus on in content?\"\r\n                  rows={3}\r\n                  className=\"mt-1\"\r\n                />\r\n              </div>\r\n            </TabsContent>\r\n\r\n            {/* Section 5: Brand Colors */}\r\n            <TabsContent value=\"colors\" className=\"space-y-4 mt-6\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                <div>\r\n                  <Label htmlFor=\"primaryColor\">Primary Color</Label>\r\n                  <div className=\"flex items-center gap-2 mt-1\">\r\n                    <input\r\n                      type=\"color\"\r\n                      id=\"primaryColor\"\r\n                      value={brandProfile.primaryColor || '#3B82F6'}\r\n                      onChange={(e) => handleInputChange('primaryColor', e.target.value)}\r\n                      className=\"w-12 h-10 rounded border border-gray-300\"\r\n                    />\r\n                    <Input\r\n                      value={brandProfile.primaryColor || ''}\r\n                      onChange={(e) => handleInputChange('primaryColor', e.target.value)}\r\n                      placeholder=\"#3B82F6\"\r\n                      className=\"flex-1\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label htmlFor=\"accentColor\">Accent Color</Label>\r\n                  <div className=\"flex items-center gap-2 mt-1\">\r\n                    <input\r\n                      type=\"color\"\r\n                      id=\"accentColor\"\r\n                      value={brandProfile.accentColor || '#10B981'}\r\n                      onChange={(e) => handleInputChange('accentColor', e.target.value)}\r\n                      className=\"w-12 h-10 rounded border border-gray-300\"\r\n                    />\r\n                    <Input\r\n                      value={brandProfile.accentColor || ''}\r\n                      onChange={(e) => handleInputChange('accentColor', e.target.value)}\r\n                      placeholder=\"#10B981\"\r\n                      className=\"flex-1\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label htmlFor=\"backgroundColor\">Background Color</Label>\r\n                  <div className=\"flex items-center gap-2 mt-1\">\r\n                    <input\r\n                      type=\"color\"\r\n                      id=\"backgroundColor\"\r\n                      value={brandProfile.backgroundColor || '#F8FAFC'}\r\n                      onChange={(e) => handleInputChange('backgroundColor', e.target.value)}\r\n                      className=\"w-12 h-10 rounded border border-gray-300\"\r\n                    />\r\n                    <Input\r\n                      value={brandProfile.backgroundColor || ''}\r\n                      onChange={(e) => handleInputChange('backgroundColor', e.target.value)}\r\n                      placeholder=\"#F8FAFC\"\r\n                      className=\"flex-1\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Color Preview */}\r\n              <div className=\"mt-6\">\r\n                <Label>Color Preview</Label>\r\n                <div className=\"mt-2 p-4 rounded-lg border\" style={{ backgroundColor: brandProfile.backgroundColor }}>\r\n                  <div\r\n                    className=\"inline-block px-4 py-2 rounded text-white font-medium mr-2\"\r\n                    style={{ backgroundColor: brandProfile.primaryColor }}\r\n                  >\r\n                    Primary Color\r\n                  </div>\r\n                  <div\r\n                    className=\"inline-block px-4 py-2 rounded text-white font-medium\"\r\n                    style={{ backgroundColor: brandProfile.accentColor }}\r\n                  >\r\n                    Accent Color\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </TabsContent>\r\n\r\n            {/* Section 6: Social Media */}\r\n            <TabsContent value=\"social\" className=\"space-y-4 mt-6\">\r\n              <div className=\"mb-4\">\r\n                <p className=\"text-sm text-gray-600\">\r\n                  Add your social media links to help with content distribution and brand consistency.\r\n                  <span className=\"text-gray-500 italic\"> All fields are optional.</span>\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <Label htmlFor=\"facebookUrl\">Facebook URL (Optional)</Label>\r\n                  <Input\r\n                    id=\"facebookUrl\"\r\n                    type=\"url\"\r\n                    value={brandProfile.facebookUrl || ''}\r\n                    onChange={(e) => handleInputChange('facebookUrl', e.target.value)}\r\n                    placeholder=\"https://facebook.com/yourbusiness\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label htmlFor=\"instagramUrl\">Instagram URL (Optional)</Label>\r\n                  <Input\r\n                    id=\"instagramUrl\"\r\n                    type=\"url\"\r\n                    value={brandProfile.instagramUrl || ''}\r\n                    onChange={(e) => handleInputChange('instagramUrl', e.target.value)}\r\n                    placeholder=\"https://instagram.com/yourbusiness\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label htmlFor=\"twitterUrl\">Twitter/X URL (Optional)</Label>\r\n                  <Input\r\n                    id=\"twitterUrl\"\r\n                    type=\"url\"\r\n                    value={brandProfile.twitterUrl || ''}\r\n                    onChange={(e) => handleInputChange('twitterUrl', e.target.value)}\r\n                    placeholder=\"https://twitter.com/yourbusiness\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Label htmlFor=\"linkedinUrl\">LinkedIn URL (Optional)</Label>\r\n                  <Input\r\n                    id=\"linkedinUrl\"\r\n                    type=\"url\"\r\n                    value={brandProfile.linkedinUrl || ''}\r\n                    onChange={(e) => handleInputChange('linkedinUrl', e.target.value)}\r\n                    placeholder=\"https://linkedin.com/company/yourbusiness\"\r\n                    className=\"mt-1\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\r\n                <p className=\"text-xs text-blue-700\">\r\n                  💡 <strong>Tip:</strong> Adding social media links helps the AI generate content that's optimized for each platform and maintains consistent branding across all channels.\r\n                </p>\r\n              </div>\r\n            </TabsContent>\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"flex justify-between\">\r\n        <Button variant=\"outline\" onClick={onPrevious}>\r\n          <ChevronLeft className=\"mr-2 h-4 w-4\" />\r\n          Previous Step\r\n        </Button>\r\n\r\n        <Button onClick={onNext}>\r\n          Continue to Logo Upload\r\n          <ChevronRight className=\"ml-2 h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;;AA+BO,SAAS,iBAAiB,EAC/B,YAAY,EACZ,kBAAkB,EAClB,MAAM,EACN,UAAU,EACY;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAEnE,MAAM,oBAAoB,OAAO,OAAmC;QAClE,QAAQ,GAAG,CAAC,gCAAgC;YAAE;YAAO;QAAM;QAC3D,QAAQ,GAAG,CAAC,mCAAmC,OAAO,oBAAoB;QAE1E,iCAAiC;QACjC,mBAAmB;YAAE,CAAC,MAAM,EAAE;QAAM;QAEpC,iGAAiG;QACjG,MAAM,gBAAgB,UAAU,kBAAkB,UAAU,iBAAiB,UAAU;QACvF,IAAI,iBAAiB,cAAc,IAAI;YACrC,IAAI;gBACF,QAAQ,GAAG,CAAC,qEAAqE;oBAC/E,SAAS,aAAa,EAAE;oBACxB;oBACA;gBACF;gBAEA,6CAA6C;gBAC7C,MAAM,cAAc,aAAa,EAAE,EAAE;oBAAE,CAAC,MAAM,EAAE;gBAAM;gBAEtD,kEAAkE;gBAClE,MAAM,eAAe;oBAAE,GAAG,YAAY;oBAAE,CAAC,MAAM,EAAE;gBAAM;gBACvD,YAAY;gBAEZ,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qEAAqE;YACnF,wDAAwD;YAC1D;QACF;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,aAAa;YAAE,MAAM;YAAI,aAAa;QAAG;QAC/C,mBAAmB;YACjB,UAAU;mBAAI,aAAa,QAAQ;gBAAE;aAAW;QAClD;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe,OAA+B;QACnE,MAAM,kBAAkB,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,IAC1D,MAAM,QAAQ;gBAAE,GAAG,OAAO;gBAAE,CAAC,MAAM,EAAE;YAAM,IAAI;QAEjD,mBAAmB;YAAE,UAAU;QAAgB;IACjD;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,kBAAkB,aAAa,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrE,mBAAmB;YAAE,UAAU;QAAgB;IACjD;IAEA,MAAM,WAAW;QACf;YAAE,IAAI;YAAS,OAAO;YAAc,MAAM,mNAAA,CAAA,YAAS;QAAC;QACpD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,uMAAA,CAAA,QAAK;QAAC;QACjD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,uMAAA,CAAA,QAAK;QAAC;QAC/C;YAAE,IAAI;YAAY,OAAO;YAAkB,MAAM,2MAAA,CAAA,UAAO;QAAC;QACzD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC5C;YAAE,IAAI;YAAU,OAAO;YAAqB,MAAM,6MAAA,CAAA,SAAM;QAAC;KAC1D;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;8CACjB,SAAS,GAAG,CAAC,CAAC;wCACb,MAAM,OAAO,QAAQ,IAAI;wCACzB,qBACE,6LAAC,mIAAA,CAAA,cAAW;4CAEV,OAAO,QAAQ,EAAE;4CACjB,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAW,QAAQ,KAAK;;;;;;;2CALnC,QAAQ,EAAE;;;;;oCAQrB;;;;;;8CAIF,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;;sDACnC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,aAAa,YAAY,IAAI;4DACpC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;;;;;;;8DAIpD,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,aAAa,YAAY,IAAI;4DACpC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;sDAMtD,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAW;;;;;;8DAC1B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,aAAa,QAAQ,IAAI;oDAChC,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,WAAW,IAAI;oDACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;gDAG3C,aAAa,QAAQ,CAAC,MAAM,KAAK,mBAChC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAqB;;;;;;sEAClC,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS;4DACT,WAAU;;8EAEV,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;gDAMjC,aAAa,QAAQ,CAAC,MAAM,GAAG,mBAC9B,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAoC;;;;;;8EACrD,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EAAoC;;;;;;;;;;;;wDAGtD,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,6LAAC;gEAAgB,WAAU;0EACzB,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFACJ,OAAO,QAAQ,IAAI,IAAI;oFACvB,UAAU,CAAC,IAAM,cAAc,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;oFAC5D,aAAY;;;;;;8FAEd,6LAAC,oIAAA,CAAA,QAAK;oFACJ,OAAO,QAAQ,WAAW,IAAI;oFAC9B,UAAU,CAAC,IAAM,cAAc,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;oFACnE,aAAY;;;;;;;;;;;;wEAGf,aAAa,QAAQ,CAAC,MAAM,GAAG,mBAC9B,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,cAAc;4EAC7B,WAAU;sFAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;;;;;+DAtBhB;;;;;sEA6BZ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;gEACT,WAAU;;kFAEV,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;;;;;;;;;;;;sDAQtC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,cAAc,IAAI;oDACtC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACnE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,WAAW,IAAI;oDACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAwB;;;;;;8DACvC,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,qBAAqB,IAAI;oDAC7C,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;oDAC1E,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,aAAa,YAAY,IAAI;4DACpC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,aAAa,YAAY,IAAI;4DACpC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,cAAc,IAAI;oDACtC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACnE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,WAAW,IAAI;oDACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,WAAW,IAAI;oDACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAChE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;sDAId,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAgB;;;;;;8DAC/B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,OAAO,aAAa,aAAa,IAAI;oDACrC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAClE,aAAY;oDACZ,MAAM;oDACN,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO,aAAa,YAAY,IAAI;oEACpC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEACjE,WAAU;;;;;;8EAEZ,6LAAC,oIAAA,CAAA,QAAK;oEACJ,OAAO,aAAa,YAAY,IAAI;oEACpC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEACjE,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO,aAAa,WAAW,IAAI;oEACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oEAChE,WAAU;;;;;;8EAEZ,6LAAC,oIAAA,CAAA,QAAK;oEACJ,OAAO,aAAa,WAAW,IAAI;oEACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oEAChE,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAkB;;;;;;sEACjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO,aAAa,eAAe,IAAI;oEACvC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;oEACpE,WAAU;;;;;;8EAEZ,6LAAC,oIAAA,CAAA,QAAK;oEACJ,OAAO,aAAa,eAAe,IAAI;oEACvC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;oEACpE,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAOlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,6LAAC;oDAAI,WAAU;oDAA6B,OAAO;wDAAE,iBAAiB,aAAa,eAAe;oDAAC;;sEACjG,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,aAAa,YAAY;4DAAC;sEACrD;;;;;;sEAGD,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,aAAa,WAAW;4DAAC;sEACpD;;;;;;;;;;;;;;;;;;;;;;;;8CAQP,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDAAwB;kEAEnC,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;;;;;;sDAI3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAc;;;;;;sEAC7B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,aAAa,WAAW,IAAI;4DACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4DAChE,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,aAAa,YAAY,IAAI;4DACpC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa;;;;;;sEAC5B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,aAAa,UAAU,IAAI;4DAClC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC/D,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAc;;;;;;sEAC7B,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,aAAa,WAAW,IAAI;4DACnC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4DAChE,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDAAwB;kEAChC,6LAAC;kEAAO;;;;;;oDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;;0CACjC,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAI1C,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;;4BAAQ;0CAEvB,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC;GAzfgB;;QAOuC,kJAAA,CAAA,kBAAe;;;KAPtD", "debugId": null}}, {"offset": {"line": 3840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/cbrand/steps/logo-upload-step.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useCallback } from 'react';\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Label } from '@/components/ui/label';\r\nimport {\r\n  Upload,\r\n  Image as ImageIcon,\r\n  X,\r\n  Check,\r\n  ChevronLeft,\r\n  Save,\r\n  Eye\r\n} from 'lucide-react';\r\nimport { toast } from '@/hooks/use-toast';\r\nimport { CompleteBrandProfile } from '../cbrand-wizard';\r\n\r\ninterface LogoUploadStepProps {\r\n  brandProfile: CompleteBrandProfile;\r\n  updateBrandProfile: (updates: Partial<CompleteBrandProfile>) => void;\r\n  onPrevious: () => void;\r\n}\r\n\r\nexport function LogoUploadStep({\r\n  brandProfile,\r\n  updateBrandProfile,\r\n  onPrevious\r\n}: LogoUploadStepProps) {\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleDragOver = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(true);\r\n  }, []);\r\n\r\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  }, []);\r\n\r\n  const handleDrop = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n\r\n    const files = Array.from(e.dataTransfer.files);\r\n    if (files.length > 0) {\r\n      handleFileUpload(files[0]);\r\n    }\r\n  }, []);\r\n\r\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n    if (files && files.length > 0) {\r\n      handleFileUpload(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (file: File) => {\r\n    // Validate file type\r\n    if (!file.type.startsWith('image/')) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Invalid File Type\",\r\n        description: \"Please upload an image file (PNG, JPG, SVG, etc.)\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Validate file size (max 5MB for high-quality logos)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"File Too Large\",\r\n        description: \"Please upload an image smaller than 5MB for optimal performance\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n\r\n    try {\r\n      // Compress and convert to data URL\r\n      const compressedDataUrl = await compressImage(file);\r\n      updateBrandProfile({ logoDataUrl: compressedDataUrl });\r\n\r\n      toast({\r\n        title: \"Logo Uploaded\",\r\n        description: \"Your logo has been uploaded and optimized successfully!\",\r\n      });\r\n\r\n      setIsUploading(false);\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Upload Failed\",\r\n        description: \"Failed to process the image file\",\r\n      });\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  // Image compression function\r\n  const compressImage = (file: File): Promise<string> => {\r\n    return new Promise((resolve, reject) => {\r\n      const canvas = document.createElement('canvas');\r\n      const ctx = canvas.getContext('2d');\r\n      const img = new Image();\r\n\r\n      img.onload = () => {\r\n        // Calculate new dimensions (max 400x400 for logos)\r\n        const maxSize = 400;\r\n        let { width, height } = img;\r\n\r\n        if (width > height) {\r\n          if (width > maxSize) {\r\n            height = (height * maxSize) / width;\r\n            width = maxSize;\r\n          }\r\n        } else {\r\n          if (height > maxSize) {\r\n            width = (width * maxSize) / height;\r\n            height = maxSize;\r\n          }\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Draw and compress\r\n        ctx?.drawImage(img, 0, 0, width, height);\r\n\r\n        // Convert to compressed data URL (JPEG with 0.8 quality)\r\n        const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.8);\r\n        resolve(compressedDataUrl);\r\n      };\r\n\r\n      img.onerror = () => reject(new Error('Failed to load image'));\r\n      img.src = URL.createObjectURL(file);\r\n    });\r\n  };\r\n\r\n  const removeLogo = () => {\r\n    updateBrandProfile({ logoDataUrl: '' });\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  const handleSaveProfile = async () => {\r\n    // Validate required fields\r\n    const missingFields = [];\r\n\r\n    if (!brandProfile.businessName?.trim()) {\r\n      missingFields.push('Business Name');\r\n    }\r\n    if (!brandProfile.businessType?.trim()) {\r\n      missingFields.push('Business Type');\r\n    }\r\n    if (!brandProfile.location?.trim()) {\r\n      missingFields.push('Location');\r\n    }\r\n    if (!brandProfile.description?.trim()) {\r\n      missingFields.push('Business Description');\r\n    }\r\n    if (!brandProfile.services || brandProfile.services.length === 0) {\r\n      missingFields.push('At least one Service/Product');\r\n    } else {\r\n      // Check if all services have names (description is optional)\r\n      const incompleteServices = brandProfile.services.some(\r\n        service => !service.name?.trim()\r\n      );\r\n      if (incompleteServices) {\r\n        missingFields.push('All Service/Product names must be filled');\r\n      }\r\n    }\r\n\r\n    if (missingFields.length > 0) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Missing Required Fields\",\r\n        description: `Please fill in: ${missingFields.join(', ')}`,\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Temporarily removed logo requirement to test logo persistence\r\n    // if (!brandProfile.logoDataUrl) {\r\n    //   toast({\r\n    //     variant: \"destructive\",\r\n    //     title: \"Logo Required\",\r\n    //     description: \"Please upload a logo to complete your brand profile\",\r\n    //   });\r\n    //   return;\r\n    // }\r\n\r\n    setIsSaving(true);\r\n\r\n    try {\r\n      console.log('Starting save process...');\r\n\r\n      // Check localStorage space before saving\r\n      const testData = JSON.stringify(brandProfile);\r\n      if (testData.length > 10 * 1024 * 1024) { // 10MB limit (increased for larger profiles)\r\n        throw new Error('Profile data too large for storage');\r\n      }\r\n\r\n      // Create a simplified saved profile without external dependencies\r\n      const now = new Date().toISOString();\r\n      const savedProfile = {\r\n        ...brandProfile,\r\n        id: Date.now().toString(),\r\n        createdAt: now,\r\n        updatedAt: now,\r\n        version: '1.0',\r\n      };\r\n\r\n      // Clear any existing large data first\r\n      localStorage.removeItem('completeBrandProfile');\r\n      localStorage.removeItem('brandProfile');\r\n\r\n      // Save to localStorage directly with error handling\r\n      try {\r\n        localStorage.setItem('completeBrandProfile', JSON.stringify(savedProfile));\r\n        console.log('✅ Complete profile saved with logo to localStorage');\r\n      } catch (storageError) {\r\n        console.error('❌ Failed to save complete profile:', storageError);\r\n        // Try to compress the logo data URL before removing it entirely\r\n        if (savedProfile.logoDataUrl && savedProfile.logoDataUrl.length > 100000) {\r\n          // If logo is very large, try to compress it\r\n          const compressedProfile = {\r\n            ...savedProfile,\r\n            logoDataUrl: savedProfile.logoDataUrl.substring(0, 50000) + '...[compressed]'\r\n          };\r\n          try {\r\n            localStorage.setItem('completeBrandProfile', JSON.stringify(compressedProfile));\r\n            console.warn('⚠️ Saved profile with compressed logo due to storage constraints');\r\n          } catch (compressError) {\r\n            // Only as last resort, save without logo\r\n            const profileWithoutLogo = { ...savedProfile, logoDataUrl: '' };\r\n            localStorage.setItem('completeBrandProfile', JSON.stringify(profileWithoutLogo));\r\n            console.warn('⚠️ Saved profile without logo due to storage constraints');\r\n          }\r\n        } else {\r\n          // If logo isn't the issue, still try to save without it\r\n          const profileWithoutLogo = { ...savedProfile, logoDataUrl: '' };\r\n          localStorage.setItem('completeBrandProfile', JSON.stringify(profileWithoutLogo));\r\n          console.warn('⚠️ Saved profile without logo due to storage constraints');\r\n        }\r\n      }\r\n\r\n      // Also save in legacy format for compatibility with existing content generation\r\n      const legacyProfile = {\r\n        businessName: brandProfile.businessName,\r\n        businessType: brandProfile.businessType,\r\n        location: brandProfile.location,\r\n        description: brandProfile.description,\r\n        services: brandProfile.services.map(service => `${service.name}: ${service.description}`).join('\\n'),\r\n        websiteUrl: brandProfile.websiteUrl,\r\n        logoDataUrl: brandProfile.logoDataUrl,\r\n        visualStyle: brandProfile.visualStyle,\r\n        writingTone: brandProfile.writingTone,\r\n        contentThemes: brandProfile.contentThemes,\r\n        primaryColor: brandProfile.primaryColor,\r\n        accentColor: brandProfile.accentColor,\r\n        backgroundColor: brandProfile.backgroundColor,\r\n        contactPhone: brandProfile.contactPhone,\r\n        contactEmail: brandProfile.contactEmail,\r\n        contactAddress: brandProfile.contactAddress,\r\n        targetAudience: brandProfile.targetAudience,\r\n        keyFeatures: brandProfile.keyFeatures,\r\n        competitiveAdvantages: brandProfile.competitiveAdvantages,\r\n        socialMedia: {\r\n          facebook: brandProfile.facebookUrl,\r\n          instagram: brandProfile.instagramUrl,\r\n          twitter: brandProfile.twitterUrl,\r\n          linkedin: brandProfile.linkedinUrl,\r\n        },\r\n      };\r\n\r\n      try {\r\n        localStorage.setItem('brandProfile', JSON.stringify(legacyProfile));\r\n        console.log('✅ Legacy profile saved with logo to localStorage');\r\n      } catch (storageError) {\r\n        console.error('❌ Failed to save legacy profile:', storageError);\r\n        // Try to compress the logo data URL before removing it entirely\r\n        if (legacyProfile.logoDataUrl && legacyProfile.logoDataUrl.length > 100000) {\r\n          // If logo is very large, try to compress it\r\n          const compressedLegacy = {\r\n            ...legacyProfile,\r\n            logoDataUrl: legacyProfile.logoDataUrl.substring(0, 50000) + '...[compressed]'\r\n          };\r\n          try {\r\n            localStorage.setItem('brandProfile', JSON.stringify(compressedLegacy));\r\n            console.warn('⚠️ Saved legacy profile with compressed logo due to storage constraints');\r\n          } catch (compressError) {\r\n            // Only as last resort, save without logo\r\n            const legacyWithoutLogo = { ...legacyProfile, logoDataUrl: '' };\r\n            localStorage.setItem('brandProfile', JSON.stringify(legacyWithoutLogo));\r\n            console.warn('⚠️ Saved legacy profile without logo due to storage constraints');\r\n          }\r\n        } else {\r\n          // If logo isn't the issue, still try to save without it\r\n          const legacyWithoutLogo = { ...legacyProfile, logoDataUrl: '' };\r\n          localStorage.setItem('brandProfile', JSON.stringify(legacyWithoutLogo));\r\n          console.warn('⚠️ Saved legacy profile without logo due to storage constraints');\r\n        }\r\n      }\r\n\r\n      console.log('Save successful!');\r\n\r\n      toast({\r\n        title: \"Brand Profile Saved!\",\r\n        description: \"Your complete brand profile has been created successfully. You can now use it for content generation.\",\r\n      });\r\n\r\n      // Keep user on summary page instead of redirecting\r\n      // They can navigate manually when ready\r\n\r\n    } catch (error) {\r\n      console.error('Save error:', error);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Save Failed\",\r\n        description: `Failed to save your brand profile: ${error instanceof Error ? error.message : 'Unknown error'}. Try uploading a smaller logo image.`,\r\n      });\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Logo Upload */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <ImageIcon className=\"h-5 w-5\" />\r\n            Upload Your Logo\r\n          </CardTitle>\r\n          <p className=\"text-gray-600\">\r\n            Upload your brand logo to complete your profile setup\r\n          </p>\r\n        </CardHeader>\r\n        <CardContent>\r\n          {!brandProfile.logoDataUrl ? (\r\n            <div\r\n              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDragOver\r\n                ? 'border-blue-400 bg-blue-50'\r\n                : 'border-gray-300 hover:border-gray-400'\r\n                }`}\r\n              onDragOver={handleDragOver}\r\n              onDragLeave={handleDragLeave}\r\n              onDrop={handleDrop}\r\n            >\r\n              <input\r\n                ref={fileInputRef}\r\n                type=\"file\"\r\n                accept=\"image/*\"\r\n                onChange={handleFileSelect}\r\n                className=\"hidden\"\r\n                id=\"logo-upload\"\r\n              />\r\n\r\n              <div className=\"flex flex-col items-center\">\r\n                <Upload className=\"h-12 w-12 text-gray-400 mb-4\" />\r\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\r\n                  Drop your logo here, or click to browse\r\n                </h3>\r\n                <p className=\"text-gray-500 mb-4\">\r\n                  Supports PNG, JPG, SVG files up to 5MB\r\n                </p>\r\n                <Button\r\n                  type=\"button\"\r\n                  onClick={() => fileInputRef.current?.click()}\r\n                  disabled={isUploading}\r\n                >\r\n                  {isUploading ? 'Uploading...' : 'Choose File'}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center\">\r\n              <div className=\"inline-block relative\">\r\n                <img\r\n                  src={brandProfile.logoDataUrl}\r\n                  alt=\"Brand Logo\"\r\n                  className=\"max-w-xs max-h-48 object-contain border rounded-lg\"\r\n                />\r\n                <button\r\n                  onClick={removeLogo}\r\n                  className=\"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-2 hover:bg-red-600 transition-colors\"\r\n                >\r\n                  <X className=\"h-4 w-4\" />\r\n                </button>\r\n              </div>\r\n              <p className=\"text-green-600 mt-4 flex items-center justify-center gap-2\">\r\n                <Check className=\"h-4 w-4\" />\r\n                Logo uploaded successfully\r\n              </p>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Profile Summary */}\r\n      <Card>\r\n        <CardHeader>\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Eye className=\"h-5 w-5\" />\r\n                Profile Summary\r\n              </CardTitle>\r\n              <p className=\"text-gray-600\">\r\n                {brandProfile.businessName && brandProfile.logoDataUrl\r\n                  ? 'Your complete brand profile information'\r\n                  : 'Review your brand profile before saving'\r\n                }\r\n              </p>\r\n            </div>\r\n            {brandProfile.businessName && brandProfile.logoDataUrl && (\r\n              <div className=\"text-right\">\r\n                <div className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800\">\r\n                  ✅ Complete\r\n                </div>\r\n                <p className=\"text-xs text-gray-500 mt-1\">Ready for content generation</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-6\">\r\n          {/* Basic Information */}\r\n          <div>\r\n            <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n              Basic Information\r\n            </h4>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\r\n              <div>\r\n                <Label className=\"font-medium\">Business Name</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.businessName || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Business Type</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.businessType || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Location</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.location || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Website</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.websiteUrl || 'Not set'}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Business Description */}\r\n          <div>\r\n            <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\r\n              Business Description\r\n            </h4>\r\n            <p className=\"text-gray-600 text-sm bg-gray-50 p-3 rounded\">\r\n              {brandProfile.description || 'Not set'}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Services/Products */}\r\n          <div>\r\n            <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\r\n              Services/Products ({brandProfile.services?.length || 0})\r\n            </h4>\r\n            {brandProfile.services && brandProfile.services.length > 0 ? (\r\n              <div className=\"space-y-2\">\r\n                {brandProfile.services.map((service, index) => (\r\n                  <div key={index} className=\"bg-gray-50 p-3 rounded text-sm\">\r\n                    <div className=\"font-medium text-gray-900\">{service.name}</div>\r\n                    {service.description && (\r\n                      <div className=\"text-gray-600 mt-1\">{service.description}</div>\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <p className=\"text-gray-500 text-sm\">No services added</p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Contact Information */}\r\n          <div>\r\n            <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\r\n              Contact Information\r\n            </h4>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\r\n              <div>\r\n                <Label className=\"font-medium\">Phone</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.contactPhone || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Email</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.contactEmail || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Address</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.contactAddress || 'Not set'}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Brand Identity */}\r\n          <div>\r\n            <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-pink-500 rounded-full\"></div>\r\n              Brand Identity\r\n            </h4>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\r\n              <div>\r\n                <Label className=\"font-medium\">Target Audience</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.targetAudience || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Visual Style</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.visualStyle || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Writing Tone</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.writingTone || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Content Themes</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.contentThemes || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Key Features</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.keyFeatures || 'Not set'}</p>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Competitive Advantages</Label>\r\n                <p className=\"text-gray-600\">{brandProfile.competitiveAdvantages || 'Not set'}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Brand Colors */}\r\n          <div>\r\n            <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\r\n              Brand Colors\r\n            </h4>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\r\n              <div>\r\n                <Label className=\"font-medium\">Primary Color</Label>\r\n                <div className=\"flex items-center gap-2 mt-1\">\r\n                  <div\r\n                    className=\"w-6 h-6 rounded border border-gray-300\"\r\n                    style={{ backgroundColor: brandProfile.primaryColor || '#3B82F6' }}\r\n                  ></div>\r\n                  <p className=\"text-gray-600\">{brandProfile.primaryColor || '#3B82F6'}</p>\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Accent Color</Label>\r\n                <div className=\"flex items-center gap-2 mt-1\">\r\n                  <div\r\n                    className=\"w-6 h-6 rounded border border-gray-300\"\r\n                    style={{ backgroundColor: brandProfile.accentColor || '#10B981' }}\r\n                  ></div>\r\n                  <p className=\"text-gray-600\">{brandProfile.accentColor || '#10B981'}</p>\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <Label className=\"font-medium\">Background Color</Label>\r\n                <div className=\"flex items-center gap-2 mt-1\">\r\n                  <div\r\n                    className=\"w-6 h-6 rounded border border-gray-300\"\r\n                    style={{ backgroundColor: brandProfile.backgroundColor || '#F8FAFC' }}\r\n                  ></div>\r\n                  <p className=\"text-gray-600\">{brandProfile.backgroundColor || '#F8FAFC'}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Social Media (Only show if any are filled) */}\r\n          {(brandProfile.facebookUrl || brandProfile.instagramUrl || brandProfile.twitterUrl || brandProfile.linkedinUrl) && (\r\n            <div>\r\n              <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n                <div className=\"w-2 h-2 bg-cyan-500 rounded-full\"></div>\r\n                Social Media\r\n              </h4>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\r\n                {brandProfile.facebookUrl && (\r\n                  <div>\r\n                    <Label className=\"font-medium\">Facebook</Label>\r\n                    <p className=\"text-gray-600 truncate\">{brandProfile.facebookUrl}</p>\r\n                  </div>\r\n                )}\r\n                {brandProfile.instagramUrl && (\r\n                  <div>\r\n                    <Label className=\"font-medium\">Instagram</Label>\r\n                    <p className=\"text-gray-600 truncate\">{brandProfile.instagramUrl}</p>\r\n                  </div>\r\n                )}\r\n                {brandProfile.twitterUrl && (\r\n                  <div>\r\n                    <Label className=\"font-medium\">Twitter/X</Label>\r\n                    <p className=\"text-gray-600 truncate\">{brandProfile.twitterUrl}</p>\r\n                  </div>\r\n                )}\r\n                {brandProfile.linkedinUrl && (\r\n                  <div>\r\n                    <Label className=\"font-medium\">LinkedIn</Label>\r\n                    <p className=\"text-gray-600 truncate\">{brandProfile.linkedinUrl}</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Logo Status */}\r\n          <div>\r\n            <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-yellow-500 rounded-full\"></div>\r\n              Brand Logo\r\n            </h4>\r\n            <div className=\"flex items-center gap-3\">\r\n              {brandProfile.logoDataUrl ? (\r\n                <>\r\n                  <img\r\n                    src={brandProfile.logoDataUrl}\r\n                    alt=\"Brand Logo\"\r\n                    className=\"w-12 h-12 object-contain border rounded\"\r\n                  />\r\n                  <div>\r\n                    <p className=\"text-green-600 font-medium text-sm\">✅ Logo uploaded</p>\r\n                    <p className=\"text-gray-500 text-xs\">Ready for content generation</p>\r\n                  </div>\r\n                </>\r\n              ) : (\r\n                <div>\r\n                  <p className=\"text-red-600 font-medium text-sm\">❌ Logo not uploaded</p>\r\n                  <p className=\"text-gray-500 text-xs\">Upload logo above to complete profile</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Navigation */}\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex justify-between\">\r\n          <Button variant=\"outline\" onClick={onPrevious}>\r\n            <ChevronLeft className=\"mr-2 h-4 w-4\" />\r\n            Previous Step\r\n          </Button>\r\n\r\n          <Button\r\n            onClick={handleSaveProfile}\r\n            disabled={isSaving}\r\n            variant={brandProfile.businessName && brandProfile.logoDataUrl ? \"outline\" : \"default\"}\r\n            className={brandProfile.businessName && brandProfile.logoDataUrl ? \"\" : \"bg-green-600 hover:bg-green-700\"}\r\n          >\r\n            {isSaving ? (\r\n              <>\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\r\n                {brandProfile.businessName && brandProfile.logoDataUrl ? 'Updating...' : 'Saving Profile...'}\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Save className=\"mr-2 h-4 w-4\" />\r\n                {brandProfile.businessName && brandProfile.logoDataUrl ? 'Update Profile' : 'Save Complete Profile'}\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Show \"Go to Content Calendar\" button when profile is complete */}\r\n        {brandProfile.businessName && brandProfile.logoDataUrl && (\r\n          <div className=\"border-t pt-4\">\r\n            <div className=\"text-center\">\r\n              <p className=\"text-sm text-gray-600 mb-3\">\r\n                ✅ Your brand profile is complete! You can now generate content or make further edits.\r\n              </p>\r\n              <Button\r\n                onClick={() => window.location.href = '/content-calendar'}\r\n                className=\"bg-blue-600 hover:bg-blue-700\"\r\n              >\r\n                Go to Content Calendar\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAfA;;;;;;;AAwBO,SAAS,eAAe,EAC7B,YAAY,EACZ,kBAAkB,EAClB,UAAU,EACU;;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,EAAE,cAAc;YAChB,cAAc;QAChB;qDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,cAAc;QAChB;sDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,cAAc;YAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,iBAAiB,KAAK,CAAC,EAAE;YAC3B;QACF;iDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,sDAAsD;QACtD,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,eAAe;QAEf,IAAI;YACF,mCAAmC;YACnC,MAAM,oBAAoB,MAAM,cAAc;YAC9C,mBAAmB;gBAAE,aAAa;YAAkB;YAEpD,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA,eAAe;QACjB;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,MAAM,IAAI;YAEhB,IAAI,MAAM,GAAG;gBACX,mDAAmD;gBACnD,MAAM,UAAU;gBAChB,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBAExB,IAAI,QAAQ,QAAQ;oBAClB,IAAI,QAAQ,SAAS;wBACnB,SAAS,AAAC,SAAS,UAAW;wBAC9B,QAAQ;oBACV;gBACF,OAAO;oBACL,IAAI,SAAS,SAAS;wBACpB,QAAQ,AAAC,QAAQ,UAAW;wBAC5B,SAAS;oBACX;gBACF;gBAEA,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,oBAAoB;gBACpB,KAAK,UAAU,KAAK,GAAG,GAAG,OAAO;gBAEjC,yDAAyD;gBACzD,MAAM,oBAAoB,OAAO,SAAS,CAAC,cAAc;gBACzD,QAAQ;YACV;YAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;QAChC;IACF;IAEA,MAAM,aAAa;QACjB,mBAAmB;YAAE,aAAa;QAAG;QACrC,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,oBAAoB;QACxB,2BAA2B;QAC3B,MAAM,gBAAgB,EAAE;QAExB,IAAI,CAAC,aAAa,YAAY,EAAE,QAAQ;YACtC,cAAc,IAAI,CAAC;QACrB;QACA,IAAI,CAAC,aAAa,YAAY,EAAE,QAAQ;YACtC,cAAc,IAAI,CAAC;QACrB;QACA,IAAI,CAAC,aAAa,QAAQ,EAAE,QAAQ;YAClC,cAAc,IAAI,CAAC;QACrB;QACA,IAAI,CAAC,aAAa,WAAW,EAAE,QAAQ;YACrC,cAAc,IAAI,CAAC;QACrB;QACA,IAAI,CAAC,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,KAAK,GAAG;YAChE,cAAc,IAAI,CAAC;QACrB,OAAO;YACL,6DAA6D;YAC7D,MAAM,qBAAqB,aAAa,QAAQ,CAAC,IAAI,CACnD,CAAA,UAAW,CAAC,QAAQ,IAAI,EAAE;YAE5B,IAAI,oBAAoB;gBACtB,cAAc,IAAI,CAAC;YACrB;QACF;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,CAAC,gBAAgB,EAAE,cAAc,IAAI,CAAC,OAAO;YAC5D;YACA;QACF;QAEA,gEAAgE;QAChE,mCAAmC;QACnC,YAAY;QACZ,8BAA8B;QAC9B,8BAA8B;QAC9B,0EAA0E;QAC1E,QAAQ;QACR,YAAY;QACZ,IAAI;QAEJ,YAAY;QAEZ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,MAAM,WAAW,KAAK,SAAS,CAAC;YAChC,IAAI,SAAS,MAAM,GAAG,KAAK,OAAO,MAAM;gBACtC,MAAM,IAAI,MAAM;YAClB;YAEA,kEAAkE;YAClE,MAAM,MAAM,IAAI,OAAO,WAAW;YAClC,MAAM,eAAe;gBACnB,GAAG,YAAY;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW;gBACX,WAAW;gBACX,SAAS;YACX;YAEA,sCAAsC;YACtC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,oDAAoD;YACpD,IAAI;gBACF,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;gBAC5D,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,gEAAgE;gBAChE,IAAI,aAAa,WAAW,IAAI,aAAa,WAAW,CAAC,MAAM,GAAG,QAAQ;oBACxE,4CAA4C;oBAC5C,MAAM,oBAAoB;wBACxB,GAAG,YAAY;wBACf,aAAa,aAAa,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS;oBAC9D;oBACA,IAAI;wBACF,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;wBAC5D,QAAQ,IAAI,CAAC;oBACf,EAAE,OAAO,eAAe;wBACtB,yCAAyC;wBACzC,MAAM,qBAAqB;4BAAE,GAAG,YAAY;4BAAE,aAAa;wBAAG;wBAC9D,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;wBAC5D,QAAQ,IAAI,CAAC;oBACf;gBACF,OAAO;oBACL,wDAAwD;oBACxD,MAAM,qBAAqB;wBAAE,GAAG,YAAY;wBAAE,aAAa;oBAAG;oBAC9D,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;oBAC5D,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,gFAAgF;YAChF,MAAM,gBAAgB;gBACpB,cAAc,aAAa,YAAY;gBACvC,cAAc,aAAa,YAAY;gBACvC,UAAU,aAAa,QAAQ;gBAC/B,aAAa,aAAa,WAAW;gBACrC,UAAU,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,GAAG,QAAQ,IAAI,CAAC,EAAE,EAAE,QAAQ,WAAW,EAAE,EAAE,IAAI,CAAC;gBAC/F,YAAY,aAAa,UAAU;gBACnC,aAAa,aAAa,WAAW;gBACrC,aAAa,aAAa,WAAW;gBACrC,aAAa,aAAa,WAAW;gBACrC,eAAe,aAAa,aAAa;gBACzC,cAAc,aAAa,YAAY;gBACvC,aAAa,aAAa,WAAW;gBACrC,iBAAiB,aAAa,eAAe;gBAC7C,cAAc,aAAa,YAAY;gBACvC,cAAc,aAAa,YAAY;gBACvC,gBAAgB,aAAa,cAAc;gBAC3C,gBAAgB,aAAa,cAAc;gBAC3C,aAAa,aAAa,WAAW;gBACrC,uBAAuB,aAAa,qBAAqB;gBACzD,aAAa;oBACX,UAAU,aAAa,WAAW;oBAClC,WAAW,aAAa,YAAY;oBACpC,SAAS,aAAa,UAAU;oBAChC,UAAU,aAAa,WAAW;gBACpC;YACF;YAEA,IAAI;gBACF,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBACpD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,gEAAgE;gBAChE,IAAI,cAAc,WAAW,IAAI,cAAc,WAAW,CAAC,MAAM,GAAG,QAAQ;oBAC1E,4CAA4C;oBAC5C,MAAM,mBAAmB;wBACvB,GAAG,aAAa;wBAChB,aAAa,cAAc,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS;oBAC/D;oBACA,IAAI;wBACF,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;wBACpD,QAAQ,IAAI,CAAC;oBACf,EAAE,OAAO,eAAe;wBACtB,yCAAyC;wBACzC,MAAM,oBAAoB;4BAAE,GAAG,aAAa;4BAAE,aAAa;wBAAG;wBAC9D,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;wBACpD,QAAQ,IAAI,CAAC;oBACf;gBACF,OAAO;oBACL,wDAAwD;oBACxD,MAAM,oBAAoB;wBAAE,GAAG,aAAa;wBAAE,aAAa;oBAAG;oBAC9D,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;oBACpD,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,QAAQ,GAAG,CAAC;YAEZ,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,OAAO;gBACP,aAAa;YACf;QAEA,mDAAmD;QACnD,wCAAwC;QAE1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,gBAAgB,qCAAqC,CAAC;YACpJ;QACF,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,uMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGnC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAI/B,6LAAC,mIAAA,CAAA,cAAW;kCACT,CAAC,aAAa,WAAW,iBACxB,6LAAC;4BACC,WAAW,CAAC,oEAAoE,EAAE,aAC9E,+BACA,yCACA;4BACJ,YAAY;4BACZ,aAAa;4BACb,QAAQ;;8CAER,6LAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;oCACV,IAAG;;;;;;8CAGL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS,IAAM,aAAa,OAAO,EAAE;4CACrC,UAAU;sDAET,cAAc,iBAAiB;;;;;;;;;;;;;;;;;iDAKtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,aAAa,WAAW;4CAC7B,KAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGjB,6LAAC;oCAAE,WAAU;;sDACX,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG7B,6LAAC;4CAAE,WAAU;sDACV,aAAa,YAAY,IAAI,aAAa,WAAW,GAClD,4CACA;;;;;;;;;;;;gCAIP,aAAa,YAAY,IAAI,aAAa,WAAW,kBACpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAkG;;;;;;sDAGjH,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAKlD,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAAyC;;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,YAAY,IAAI;;;;;;;;;;;;0DAE7D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,YAAY,IAAI;;;;;;;;;;;;0DAE7D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,QAAQ,IAAI;;;;;;;;;;;;0DAEzD,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAM/D,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAA0C;;;;;;;kDAG3D,6LAAC;wCAAE,WAAU;kDACV,aAAa,WAAW,IAAI;;;;;;;;;;;;0CAKjC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAA2C;4CACtC,aAAa,QAAQ,EAAE,UAAU;4CAAE;;;;;;;oCAExD,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,GAAG,kBACvD,6LAAC;wCAAI,WAAU;kDACZ,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEAA6B,QAAQ,IAAI;;;;;;oDACvD,QAAQ,WAAW,kBAClB,6LAAC;wDAAI,WAAU;kEAAsB,QAAQ,WAAW;;;;;;;+CAHlD;;;;;;;;;6DASd,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKzC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAA2C;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,YAAY,IAAI;;;;;;;;;;;;0DAE7D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,YAAY,IAAI;;;;;;;;;;;;0DAE7D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,cAAc,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAMnE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAAyC;;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,cAAc,IAAI;;;;;;;;;;;;0DAE/D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,WAAW,IAAI;;;;;;;;;;;;0DAE5D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,WAAW,IAAI;;;;;;;;;;;;0DAE5D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,aAAa,IAAI;;;;;;;;;;;;0DAE9D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,WAAW,IAAI;;;;;;;;;;;;0DAE5D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAiB,aAAa,qBAAqB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAM1E,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAAwC;;;;;;;kDAGzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,YAAY,IAAI;gEAAU;;;;;;0EAEnE,6LAAC;gEAAE,WAAU;0EAAiB,aAAa,YAAY,IAAI;;;;;;;;;;;;;;;;;;0DAG/D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,WAAW,IAAI;gEAAU;;;;;;0EAElE,6LAAC;gEAAE,WAAU;0EAAiB,aAAa,WAAW,IAAI;;;;;;;;;;;;;;;;;;0DAG9D,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,aAAa,eAAe,IAAI;gEAAU;;;;;;0EAEtE,6LAAC;gEAAE,WAAU;0EAAiB,aAAa,eAAe,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOrE,CAAC,aAAa,WAAW,IAAI,aAAa,YAAY,IAAI,aAAa,UAAU,IAAI,aAAa,WAAW,mBAC5G,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAAyC;;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;;4CACZ,aAAa,WAAW,kBACvB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAA0B,aAAa,WAAW;;;;;;;;;;;;4CAGlE,aAAa,YAAY,kBACxB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAA0B,aAAa,YAAY;;;;;;;;;;;;4CAGnE,aAAa,UAAU,kBACtB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAA0B,aAAa,UAAU;;;;;;;;;;;;4CAGjE,aAAa,WAAW,kBACvB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAc;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAA0B,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0CAQzE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;;;;;;4CAA2C;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;kDACZ,aAAa,WAAW,iBACvB;;8DACE,6LAAC;oDACC,KAAK,aAAa,WAAW;oDAC7B,KAAI;oDACJ,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;yEAIzC,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAI1C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,SAAS,aAAa,YAAY,IAAI,aAAa,WAAW,GAAG,YAAY;gCAC7E,WAAW,aAAa,YAAY,IAAI,aAAa,WAAW,GAAG,KAAK;0CAEvE,yBACC;;sDACE,6LAAC;4CAAI,WAAU;;;;;;wCACd,aAAa,YAAY,IAAI,aAAa,WAAW,GAAG,gBAAgB;;iEAG3E;;sDACE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,aAAa,YAAY,IAAI,aAAa,WAAW,GAAG,mBAAmB;;;;;;;;;;;;;;oBAOnF,aAAa,YAAY,IAAI,aAAa,WAAW,kBACpD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAxqBgB;KAAA", "debugId": null}}, {"offset": {"line": 5441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/cbrand/cbrand-wizard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Button } from '@/components/ui/button';\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport { useUnifiedBrand } from '@/contexts/unified-brand-context';\r\n\r\n// Import validation and progress components\r\nimport { ProgressIndicator } from './progress-indicator';\r\nimport { validateBrandProfile } from './form-validation';\r\n\r\n// Import step components (will create these next)\r\nimport { WebsiteAnalysisStep } from './steps/website-analysis-step';\r\nimport { BrandDetailsStep } from './steps/brand-details-step';\r\nimport { LogoUploadStep } from './steps/logo-upload-step';\r\n\r\n// Types for the complete brand profile\r\nexport interface CompleteBrandProfile {\r\n  // Optional compatibility fields (may come from persisted data)\r\n  id?: string;\r\n  name?: string;\r\n  // Basic Information\r\n  businessName: string;\r\n  businessType: string;\r\n  location: string;\r\n  description: string;\r\n\r\n  // Language detection (auto-populated based on location)\r\n  detectedLanguages?: {\r\n    primary: {\r\n      code: string;\r\n      name: string;\r\n      nativeName: string;\r\n      isRTL?: boolean;\r\n    };\r\n    secondary?: Array<{\r\n      code: string;\r\n      name: string;\r\n      nativeName: string;\r\n      isRTL?: boolean;\r\n    }>;\r\n    region: string;\r\n    lastDetected?: string; // ISO timestamp\r\n  };\r\n\r\n  // Services & Target Audience\r\n  services: Array<{\r\n    name: string;\r\n    description: string;\r\n  }>;\r\n  targetAudience: string;\r\n  keyFeatures: string;\r\n  competitiveAdvantages: string;\r\n\r\n  // Contact Information\r\n  contactPhone: string;\r\n  contactEmail: string;\r\n  contactAddress: string;\r\n\r\n  // Brand Identity & Voice\r\n  visualStyle: string;\r\n  writingTone: string;\r\n  contentThemes: string;\r\n\r\n  // Brand Colors\r\n  primaryColor: string;\r\n  accentColor: string;\r\n  backgroundColor: string;\r\n\r\n  // Social Media\r\n  facebookUrl: string;\r\n  instagramUrl: string;\r\n  twitterUrl: string;\r\n  linkedinUrl: string;\r\n\r\n  // Website & Logo\r\n  websiteUrl: string;\r\n  logoDataUrl: string;\r\n\r\n  // Design Examples (for AI reference)\r\n  designExamples: string[]; // Array of data URIs from uploaded design samples\r\n}\r\n\r\nconst STEPS = [\r\n  {\r\n    id: 1,\r\n    title: 'Website Analysis',\r\n    description: 'AI-powered analysis of your website',\r\n  },\r\n  {\r\n    id: 2,\r\n    title: 'Brand Details',\r\n    description: 'Comprehensive brand information',\r\n  },\r\n  {\r\n    id: 3,\r\n    title: 'Logo Upload',\r\n    description: 'Upload your brand logo',\r\n  },\r\n];\r\n\r\ninterface CbrandWizardProps {\r\n  mode?: string | null;\r\n  brandId?: string | null;\r\n}\r\n\r\nexport function CbrandWizard({ mode, brandId }: CbrandWizardProps = {}) {\r\n  const { currentBrand, loading: brandLoading } = useUnifiedBrand();\r\n  const [currentStep, setCurrentStep] = useState(1);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Default brand profile with all required fields and default colors\r\n  const defaultBrandProfile: CompleteBrandProfile = {\r\n    businessName: '',\r\n    businessType: '',\r\n    location: '',\r\n    description: '',\r\n    services: [],\r\n    targetAudience: '',\r\n    keyFeatures: '',\r\n    competitiveAdvantages: '',\r\n    contactPhone: '',\r\n    contactEmail: '',\r\n    contactAddress: '',\r\n    visualStyle: '',\r\n    writingTone: '',\r\n    contentThemes: '',\r\n    primaryColor: '#3B82F6',\r\n    accentColor: '#10B981',\r\n    backgroundColor: '#F8FAFC',\r\n    facebookUrl: '',\r\n    instagramUrl: '',\r\n    twitterUrl: '',\r\n    linkedinUrl: '',\r\n    websiteUrl: '',\r\n    logoDataUrl: '',\r\n    designExamples: [],\r\n  };\r\n\r\n  const [brandProfile, setBrandProfile] = useState<CompleteBrandProfile>(defaultBrandProfile);\r\n\r\n  // Load existing profile on component mount\r\n  useEffect(() => {\r\n    const loadExistingProfile = () => {\r\n      try {\r\n        console.log('🔄 Loading brand profile. Mode:', mode, 'BrandId:', brandId, 'CurrentBrand:', currentBrand?.businessName);\r\n\r\n        // If we're in edit mode with a specific brandId, load that brand\r\n        if (mode === 'edit' && brandId) {\r\n          console.log('🔄 Loading brand for edit mode:', brandId);\r\n          // Load specific brand by ID\r\n          // For now, we'll use the current brand from context if it matches\r\n          if (currentBrand && (currentBrand as any).id === brandId) {\r\n            console.log('✅ Using current brand from context for edit');\r\n            // Merge with defaults to ensure all color properties exist\r\n            setBrandProfile({ ...defaultBrandProfile, ...currentBrand });\r\n            return;\r\n          }\r\n        }\r\n\r\n        // If we have a current brand selected and we're not in create mode, use it\r\n        if (currentBrand && mode !== 'create') {\r\n          console.log('✅ Using current brand from context:', currentBrand.businessName);\r\n          // Merge with defaults to ensure all color properties exist\r\n          setBrandProfile({ ...defaultBrandProfile, ...currentBrand });\r\n          return;\r\n        }\r\n\r\n        // For create mode or when no brand is selected, try to load from storage\r\n        console.log('🔄 Loading from storage for create mode or no current brand');\r\n\r\n        // Try to load the complete brand profile first\r\n        const savedProfile = localStorage.getItem('completeBrandProfile');\r\n        if (savedProfile) {\r\n          const parsedProfile = JSON.parse(savedProfile);\r\n          // Merge with defaults to ensure all color properties exist\r\n          const mergedProfile = { ...defaultBrandProfile, ...parsedProfile };\r\n          setBrandProfile(mergedProfile);\r\n          console.log('Loaded existing complete profile:', mergedProfile);\r\n          return;\r\n        }\r\n\r\n        // Fallback to legacy profile format\r\n        const legacyProfile = localStorage.getItem('brandProfile');\r\n        if (legacyProfile) {\r\n          const parsedLegacy = JSON.parse(legacyProfile);\r\n\r\n          // Convert legacy format to new format\r\n          const convertedProfile: CompleteBrandProfile = {\r\n            businessName: parsedLegacy.businessName || '',\r\n            businessType: parsedLegacy.businessType || '',\r\n            location: parsedLegacy.location || '',\r\n            description: parsedLegacy.description || '',\r\n            services: parsedLegacy.services\r\n              ? (typeof parsedLegacy.services === 'string'\r\n                ? parsedLegacy.services.split('\\n').filter(s => s.trim()).map(service => {\r\n                  const parts = service.split(':');\r\n                  return {\r\n                    name: parts[0]?.trim() || service.trim(),\r\n                    description: parts.slice(1).join(':').trim() || ''\r\n                  };\r\n                })\r\n                : parsedLegacy.services)\r\n              : [],\r\n            websiteUrl: parsedLegacy.websiteUrl || '',\r\n            logoDataUrl: parsedLegacy.logoDataUrl || '',\r\n            designExamples: parsedLegacy.designExamples || [],\r\n            visualStyle: parsedLegacy.visualStyle || '',\r\n            writingTone: parsedLegacy.writingTone || '',\r\n            contentThemes: parsedLegacy.contentThemes || '',\r\n            primaryColor: parsedLegacy.primaryColor || '#3B82F6',\r\n            accentColor: parsedLegacy.accentColor || '#10B981',\r\n            backgroundColor: parsedLegacy.backgroundColor || '#F8FAFC',\r\n            contactPhone: parsedLegacy.contactPhone || '',\r\n            contactEmail: parsedLegacy.contactEmail || '',\r\n            contactAddress: parsedLegacy.contactAddress || '',\r\n            targetAudience: parsedLegacy.targetAudience || '',\r\n            keyFeatures: parsedLegacy.keyFeatures || '',\r\n            competitiveAdvantages: parsedLegacy.competitiveAdvantages || '',\r\n            facebookUrl: parsedLegacy.socialMedia?.facebook || '',\r\n            instagramUrl: parsedLegacy.socialMedia?.instagram || '',\r\n            twitterUrl: parsedLegacy.socialMedia?.twitter || '',\r\n            linkedinUrl: parsedLegacy.socialMedia?.linkedin || '',\r\n          };\r\n\r\n          // Merge with defaults to ensure all color properties exist\r\n          const mergedProfile = { ...defaultBrandProfile, ...convertedProfile };\r\n          setBrandProfile(mergedProfile);\r\n          console.log('Loaded and converted legacy profile:', mergedProfile);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error loading existing profile:', error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    // Only load when brand context is ready\r\n    if (!brandLoading) {\r\n      loadExistingProfile();\r\n    }\r\n  }, [currentBrand, brandLoading, mode, brandId]);\r\n\r\n  const progress = (currentStep / STEPS.length) * 100;\r\n\r\n  const handleNext = () => {\r\n    if (currentStep < STEPS.length) {\r\n      setCurrentStep(currentStep + 1);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentStep > 1) {\r\n      setCurrentStep(currentStep - 1);\r\n    }\r\n  };\r\n\r\n  const handleStepClick = (stepId: number) => {\r\n    setCurrentStep(stepId);\r\n  };\r\n\r\n  const updateBrandProfile = (updates: Partial<CompleteBrandProfile>) => {\r\n    const updatedProfile = { ...brandProfile, ...updates };\r\n    setBrandProfile(updatedProfile);\r\n\r\n    // Auto-save to localStorage whenever profile is updated with size check\r\n    try {\r\n      const profileSize = JSON.stringify(updatedProfile).length;\r\n      const maxSize = 10 * 1024 * 1024; // 10MB limit for localStorage (increased for larger profiles)\r\n\r\n      if (profileSize > maxSize) {\r\n        console.warn('Profile too large for auto-save, skipping:', profileSize);\r\n        // Try saving without design examples\r\n        const profileWithoutDesigns = { ...updatedProfile, designExamples: [] };\r\n        localStorage.setItem('completeBrandProfile', JSON.stringify(profileWithoutDesigns));\r\n        return;\r\n      }\r\n\r\n      localStorage.setItem('completeBrandProfile', JSON.stringify(updatedProfile));\r\n    } catch (error) {\r\n      console.warn('Failed to auto-save profile:', error);\r\n      // Try saving without design examples as fallback\r\n      try {\r\n        const profileWithoutDesigns = { ...updatedProfile, designExamples: [] };\r\n        localStorage.setItem('completeBrandProfile', JSON.stringify(profileWithoutDesigns));\r\n      } catch (fallbackError) {\r\n        console.error('Failed to save even without design examples:', fallbackError);\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderCurrentStep = () => {\r\n    switch (currentStep) {\r\n      case 1:\r\n        return (\r\n          <WebsiteAnalysisStep\r\n            brandProfile={brandProfile}\r\n            updateBrandProfile={updateBrandProfile}\r\n            onNext={handleNext}\r\n          />\r\n        );\r\n      case 2:\r\n        return (\r\n          <BrandDetailsStep\r\n            brandProfile={brandProfile}\r\n            updateBrandProfile={updateBrandProfile}\r\n            onNext={handleNext}\r\n            onPrevious={handlePrevious}\r\n          />\r\n        );\r\n      case 3:\r\n        return (\r\n          <LogoUploadStep\r\n            brandProfile={brandProfile}\r\n            updateBrandProfile={updateBrandProfile}\r\n            onPrevious={handlePrevious}\r\n          />\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  // Show loading state while profile is being loaded\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"w-full px-6 lg:px-12\">\r\n        <div className=\"flex items-center justify-center min-h-[400px]\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n            <p className=\"text-gray-600\">Loading your brand profile...</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full px-6 lg:px-12\">\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-5 gap-6\">\r\n        {/* Main Content */}\r\n        <div className=\"lg:col-span-4\">\r\n          {/* Progress Header */}\r\n          <Card className=\"mb-8\">\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                  <div>\r\n                    <h2 className=\"text-2xl font-semibold text-gray-900\">\r\n                      Step {currentStep} of {STEPS.length}\r\n                    </h2>\r\n                    {brandProfile.businessName && (\r\n                      <div className=\"flex items-center mt-1\">\r\n                        <div className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                          ✓ Editing: {brandProfile.businessName}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <span className=\"text-sm text-gray-500\">\r\n                    {Math.round(progress)}% Complete\r\n                  </span>\r\n                </div>\r\n                <Progress value={progress} className=\"mb-4\" />\r\n              </div>\r\n\r\n              {/* Step Navigation */}\r\n              <div className=\"flex justify-between items-center\">\r\n                {STEPS.map((step, index) => (\r\n                  <div\r\n                    key={step.id}\r\n                    className={`flex-1 ${index < STEPS.length - 1 ? 'mr-4' : ''}`}\r\n                  >\r\n                    <button\r\n                      onClick={() => handleStepClick(step.id)}\r\n                      className={`w-full text-left p-3 rounded-lg transition-colors ${currentStep === step.id\r\n                        ? 'bg-blue-50 border-2 border-blue-200'\r\n                        : currentStep > step.id\r\n                          ? 'bg-green-50 border-2 border-green-200'\r\n                          : 'bg-gray-50 border-2 border-gray-200 hover:bg-gray-100'\r\n                        }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div\r\n                          className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mr-3 ${currentStep === step.id\r\n                            ? 'bg-blue-500 text-white'\r\n                            : currentStep > step.id\r\n                              ? 'bg-green-500 text-white'\r\n                              : 'bg-gray-300 text-gray-600'\r\n                            }`}\r\n                        >\r\n                          {step.id}\r\n                        </div>\r\n                        <div>\r\n                          <div className=\"font-medium text-gray-900\">{step.title}</div>\r\n                          <div className=\"text-sm text-gray-500\">{step.description}</div>\r\n                        </div>\r\n                      </div>\r\n                    </button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Current Step Content */}\r\n          {renderCurrentStep()}\r\n        </div>\r\n\r\n        {/* Progress Sidebar - Right Side */}\r\n        <div className=\"lg:col-span-1\">\r\n          <Card className=\"sticky top-8\">\r\n            <CardContent className=\"p-3\">\r\n              <h3 className=\"text-sm font-semibold text-gray-900 mb-2\">\r\n                Progress\r\n              </h3>\r\n              <ProgressIndicator\r\n                brandProfile={brandProfile}\r\n                currentStep={currentStep}\r\n              />\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AAEA,4CAA4C;AAC5C;AAGA,kDAAkD;AAClD;AACA;AACA;;;AAhBA;;;;;;;;;AAqFA,MAAM,QAAQ;IACZ;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;CACD;AAOM,SAAS,aAAa,EAAE,IAAI,EAAE,OAAO,EAAqB,GAAG,CAAC,CAAC;;IACpE,MAAM,EAAE,YAAY,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oEAAoE;IACpE,MAAM,sBAA4C;QAChD,cAAc;QACd,cAAc;QACd,UAAU;QACV,aAAa;QACb,UAAU,EAAE;QACZ,gBAAgB;QAChB,aAAa;QACb,uBAAuB;QACvB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,eAAe;QACf,cAAc;QACd,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,cAAc;QACd,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,aAAa;QACb,gBAAgB,EAAE;IACpB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAEvE,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;8DAAsB;oBAC1B,IAAI;wBACF,QAAQ,GAAG,CAAC,mCAAmC,MAAM,YAAY,SAAS,iBAAiB,cAAc;wBAEzG,iEAAiE;wBACjE,IAAI,SAAS,UAAU,SAAS;4BAC9B,QAAQ,GAAG,CAAC,mCAAmC;4BAC/C,4BAA4B;4BAC5B,kEAAkE;4BAClE,IAAI,gBAAgB,AAAC,aAAqB,EAAE,KAAK,SAAS;gCACxD,QAAQ,GAAG,CAAC;gCACZ,2DAA2D;gCAC3D,gBAAgB;oCAAE,GAAG,mBAAmB;oCAAE,GAAG,YAAY;gCAAC;gCAC1D;4BACF;wBACF;wBAEA,2EAA2E;wBAC3E,IAAI,gBAAgB,SAAS,UAAU;4BACrC,QAAQ,GAAG,CAAC,uCAAuC,aAAa,YAAY;4BAC5E,2DAA2D;4BAC3D,gBAAgB;gCAAE,GAAG,mBAAmB;gCAAE,GAAG,YAAY;4BAAC;4BAC1D;wBACF;wBAEA,yEAAyE;wBACzE,QAAQ,GAAG,CAAC;wBAEZ,+CAA+C;wBAC/C,MAAM,eAAe,aAAa,OAAO,CAAC;wBAC1C,IAAI,cAAc;4BAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC;4BACjC,2DAA2D;4BAC3D,MAAM,gBAAgB;gCAAE,GAAG,mBAAmB;gCAAE,GAAG,aAAa;4BAAC;4BACjE,gBAAgB;4BAChB,QAAQ,GAAG,CAAC,qCAAqC;4BACjD;wBACF;wBAEA,oCAAoC;wBACpC,MAAM,gBAAgB,aAAa,OAAO,CAAC;wBAC3C,IAAI,eAAe;4BACjB,MAAM,eAAe,KAAK,KAAK,CAAC;4BAEhC,sCAAsC;4BACtC,MAAM,mBAAyC;gCAC7C,cAAc,aAAa,YAAY,IAAI;gCAC3C,cAAc,aAAa,YAAY,IAAI;gCAC3C,UAAU,aAAa,QAAQ,IAAI;gCACnC,aAAa,aAAa,WAAW,IAAI;gCACzC,UAAU,aAAa,QAAQ,GAC1B,OAAO,aAAa,QAAQ,KAAK,WAChC,aAAa,QAAQ,CAAC,KAAK,CAAC,MAAM,MAAM;kFAAC,CAAA,IAAK,EAAE,IAAI;iFAAI,GAAG;kFAAC,CAAA;wCAC5D,MAAM,QAAQ,QAAQ,KAAK,CAAC;wCAC5B,OAAO;4CACL,MAAM,KAAK,CAAC,EAAE,EAAE,UAAU,QAAQ,IAAI;4CACtC,aAAa,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,MAAM;wCAClD;oCACF;mFACE,aAAa,QAAQ,GACvB,EAAE;gCACN,YAAY,aAAa,UAAU,IAAI;gCACvC,aAAa,aAAa,WAAW,IAAI;gCACzC,gBAAgB,aAAa,cAAc,IAAI,EAAE;gCACjD,aAAa,aAAa,WAAW,IAAI;gCACzC,aAAa,aAAa,WAAW,IAAI;gCACzC,eAAe,aAAa,aAAa,IAAI;gCAC7C,cAAc,aAAa,YAAY,IAAI;gCAC3C,aAAa,aAAa,WAAW,IAAI;gCACzC,iBAAiB,aAAa,eAAe,IAAI;gCACjD,cAAc,aAAa,YAAY,IAAI;gCAC3C,cAAc,aAAa,YAAY,IAAI;gCAC3C,gBAAgB,aAAa,cAAc,IAAI;gCAC/C,gBAAgB,aAAa,cAAc,IAAI;gCAC/C,aAAa,aAAa,WAAW,IAAI;gCACzC,uBAAuB,aAAa,qBAAqB,IAAI;gCAC7D,aAAa,aAAa,WAAW,EAAE,YAAY;gCACnD,cAAc,aAAa,WAAW,EAAE,aAAa;gCACrD,YAAY,aAAa,WAAW,EAAE,WAAW;gCACjD,aAAa,aAAa,WAAW,EAAE,YAAY;4BACrD;4BAEA,2DAA2D;4BAC3D,MAAM,gBAAgB;gCAAE,GAAG,mBAAmB;gCAAE,GAAG,gBAAgB;4BAAC;4BACpE,gBAAgB;4BAChB,QAAQ,GAAG,CAAC,wCAAwC;wBACtD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,wCAAwC;YACxC,IAAI,CAAC,cAAc;gBACjB;YACF;QACF;iCAAG;QAAC;QAAc;QAAc;QAAM;KAAQ;IAE9C,MAAM,WAAW,AAAC,cAAc,MAAM,MAAM,GAAI;IAEhD,MAAM,aAAa;QACjB,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,iBAAiB;YAAE,GAAG,YAAY;YAAE,GAAG,OAAO;QAAC;QACrD,gBAAgB;QAEhB,wEAAwE;QACxE,IAAI;YACF,MAAM,cAAc,KAAK,SAAS,CAAC,gBAAgB,MAAM;YACzD,MAAM,UAAU,KAAK,OAAO,MAAM,8DAA8D;YAEhG,IAAI,cAAc,SAAS;gBACzB,QAAQ,IAAI,CAAC,8CAA8C;gBAC3D,qCAAqC;gBACrC,MAAM,wBAAwB;oBAAE,GAAG,cAAc;oBAAE,gBAAgB,EAAE;gBAAC;gBACtE,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;gBAC5D;YACF;YAEA,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;QAC9D,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,gCAAgC;YAC7C,iDAAiD;YACjD,IAAI;gBACF,MAAM,wBAAwB;oBAAE,GAAG,cAAc;oBAAE,gBAAgB,EAAE;gBAAC;gBACtE,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;YAC9D,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,gDAAgD;YAChE;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,uKAAA,CAAA,sBAAmB;oBAClB,cAAc;oBACd,oBAAoB;oBACpB,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,6LAAC,oKAAA,CAAA,mBAAgB;oBACf,cAAc;oBACd,oBAAoB;oBACpB,QAAQ;oBACR,YAAY;;;;;;YAGlB,KAAK;gBACH,qBACE,6LAAC,kKAAA,CAAA,iBAAc;oBACb,cAAc;oBACd,oBAAoB;oBACpB,YAAY;;;;;;YAGlB;gBACE,OAAO;QACX;IACF;IAEA,mDAAmD;IACnD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;;oEAAuC;oEAC7C;oEAAY;oEAAK,MAAM,MAAM;;;;;;;4DAEpC,aAAa,YAAY,kBACxB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;wEAAkG;wEACnG,aAAa,YAAY;;;;;;;;;;;;;;;;;;kEAK7C,6LAAC;wDAAK,WAAU;;4DACb,KAAK,KAAK,CAAC;4DAAU;;;;;;;;;;;;;0DAG1B,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAU,WAAU;;;;;;;;;;;;kDAIvC,6LAAC;wCAAI,WAAU;kDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gDAEC,WAAW,CAAC,OAAO,EAAE,QAAQ,MAAM,MAAM,GAAG,IAAI,SAAS,IAAI;0DAE7D,cAAA,6LAAC;oDACC,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,WAAW,CAAC,kDAAkD,EAAE,gBAAgB,KAAK,EAAE,GACnF,wCACA,cAAc,KAAK,EAAE,GACnB,0CACA,yDACF;8DAEJ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAW,CAAC,+EAA+E,EAAE,gBAAgB,KAAK,EAAE,GAChH,2BACA,cAAc,KAAK,EAAE,GACnB,4BACA,6BACF;0EAEH,KAAK,EAAE;;;;;;0EAEV,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAA6B,KAAK,KAAK;;;;;;kFACtD,6LAAC;wEAAI,WAAU;kFAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;+CAzBzD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;wBAoCrB;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,6LAAC,wJAAA,CAAA,oBAAiB;oCAChB,cAAc;oCACd,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7B;GAhUgB;;QACkC,kJAAA,CAAA,kBAAe;;;KADjD", "debugId": null}}, {"offset": {"line": 5995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/cbrand/page.tsx"], "sourcesContent": ["import { CbrandWizard } from '@/components/cbrand/cbrand-wizard';\r\n\r\nexport default function CbrandPage() {\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"text-center mb-8\">\r\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\r\n            Complete Brand Profile Setup\r\n          </h1>\r\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\r\n            Create a comprehensive brand profile with AI-powered analysis,\r\n            detailed information sections, and professional customization options.\r\n          </p>\r\n        </div>\r\n\r\n        <CbrandWizard />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC,mJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;AAIrB;KAlBwB", "debugId": null}}]}
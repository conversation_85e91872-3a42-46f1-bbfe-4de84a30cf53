{"node": {"7fa71ea55dac91d9f2fbbc47e38b16f32c34406758": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "7f36c3e89c9711bb510eae73accd75d39762aa73f5": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "70ecaa50ef609cec48931812d952a2688c5a97bca8": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "70370719efd459954676917a0ea0a33701731123ce": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "7c59f40eb0b80a2dc5e7a84991c84ddc885ae6ce31": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "7c77a46b52594dce7ad1477e7b4988f6f2475453ef": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "7032bdb3854aae97c69a87de824651ac55c853892e": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "60ea2a632798675c707aba182342fbd2fe9934e7b2": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "7ff7d412819238ab9e627cc3d406ad2cce0c9fa6c9": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}, "409e36c34489ad587473a8902e0f3440bb36957de3": {"workers": {"app/brand-profile/page": {"moduleId": "[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/brand-profile/page": "action-browser"}}}, "edge": {}}
/**
 * Language Detection Service
 * Intelligently detects appropriate languages based on location
 */

export interface LanguageInfo {
  code: string;
  name: string;
  nativeName: string;
  isRTL?: boolean;
}

export interface LocationLanguageMapping {
  primaryLanguage: LanguageInfo;
  secondaryLanguages?: LanguageInfo[];
  region: string;
}

// Comprehensive language definitions
export const LANGUAGES: Record<string, LanguageInfo> = {
  // English variants
  'en': { code: 'en', name: 'English', nativeName: 'English' },
  'en-US': { code: 'en-US', name: 'English (US)', nativeName: 'English (US)' },
  'en-GB': { code: 'en-GB', name: 'English (UK)', nativeName: 'English (UK)' },
  'en-CA': { code: 'en-CA', name: 'English (Canada)', nativeName: 'English (Canada)' },
  'en-AU': { code: 'en-AU', name: 'English (Australia)', nativeName: 'English (Australia)' },

  // Spanish variants
  'es': { code: 'es', name: 'Spanish', nativeName: 'Español' },
  'es-ES': { code: 'es-ES', name: 'Spanish (Spain)', nativeName: 'Español (España)' },
  'es-MX': { code: 'es-MX', name: 'Spanish (Mexico)', nativeName: 'Español (México)' },
  'es-AR': { code: 'es-AR', name: 'Spanish (Argentina)', nativeName: 'Español (Argentina)' },

  // French variants
  'fr': { code: 'fr', name: 'French', nativeName: 'Français' },
  'fr-FR': { code: 'fr-FR', name: 'French (France)', nativeName: 'Français (France)' },
  'fr-CA': { code: 'fr-CA', name: 'French (Canada)', nativeName: 'Français (Canada)' },

  // Arabic variants
  'ar': { code: 'ar', name: 'Arabic', nativeName: 'العربية', isRTL: true },
  'ar-SA': { code: 'ar-SA', name: 'Arabic (Saudi Arabia)', nativeName: 'العربية (السعودية)', isRTL: true },
  'ar-AE': { code: 'ar-AE', name: 'Arabic (UAE)', nativeName: 'العربية (الإمارات)', isRTL: true },
  'ar-EG': { code: 'ar-EG', name: 'Arabic (Egypt)', nativeName: 'العربية (مصر)', isRTL: true },

  // Other major languages
  'de': { code: 'de', name: 'German', nativeName: 'Deutsch' },
  'it': { code: 'it', name: 'Italian', nativeName: 'Italiano' },
  'pt': { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
  'pt-BR': { code: 'pt-BR', name: 'Portuguese (Brazil)', nativeName: 'Português (Brasil)' },
  'nl': { code: 'nl', name: 'Dutch', nativeName: 'Nederlands' },
  'sv': { code: 'sv', name: 'Swedish', nativeName: 'Svenska' },
  'no': { code: 'no', name: 'Norwegian', nativeName: 'Norsk' },
  'da': { code: 'da', name: 'Danish', nativeName: 'Dansk' },
  'fi': { code: 'fi', name: 'Finnish', nativeName: 'Suomi' },
  'pl': { code: 'pl', name: 'Polish', nativeName: 'Polski' },
  'ru': { code: 'ru', name: 'Russian', nativeName: 'Русский' },
  'zh': { code: 'zh', name: 'Chinese', nativeName: '中文' },
  'zh-CN': { code: 'zh-CN', name: 'Chinese (Simplified)', nativeName: '简体中文' },
  'zh-TW': { code: 'zh-TW', name: 'Chinese (Traditional)', nativeName: '繁體中文' },
  'ja': { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  'ko': { code: 'ko', name: 'Korean', nativeName: '한국어' },
  'hi': { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
  'th': { code: 'th', name: 'Thai', nativeName: 'ไทย' },
  'vi': { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' },
  'tr': { code: 'tr', name: 'Turkish', nativeName: 'Türkçe' },
  'he': { code: 'he', name: 'Hebrew', nativeName: 'עברית', isRTL: true },
  'fa': { code: 'fa', name: 'Persian', nativeName: 'فارسی', isRTL: true },
};

// Location to language mapping
export const LOCATION_LANGUAGE_MAP: Record<string, LocationLanguageMapping> = {
  // English-speaking countries
  'United States': { primaryLanguage: LANGUAGES['en-US'], secondaryLanguages: [LANGUAGES['es']], region: 'North America' },
  'USA': { primaryLanguage: LANGUAGES['en-US'], secondaryLanguages: [LANGUAGES['es']], region: 'North America' },
  'US': { primaryLanguage: LANGUAGES['en-US'], secondaryLanguages: [LANGUAGES['es']], region: 'North America' },
  'United Kingdom': { primaryLanguage: LANGUAGES['en-GB'], region: 'Europe' },
  'UK': { primaryLanguage: LANGUAGES['en-GB'], region: 'Europe' },
  'Canada': { primaryLanguage: LANGUAGES['en-CA'], secondaryLanguages: [LANGUAGES['fr-CA']], region: 'North America' },
  'Australia': { primaryLanguage: LANGUAGES['en-AU'], region: 'Oceania' },
  'New Zealand': { primaryLanguage: LANGUAGES['en'], region: 'Oceania' },
  'Ireland': { primaryLanguage: LANGUAGES['en'], region: 'Europe' },
  'South Africa': { primaryLanguage: LANGUAGES['en'], region: 'Africa' },

  // Spanish-speaking countries
  'Spain': { primaryLanguage: LANGUAGES['es-ES'], region: 'Europe' },
  'Mexico': { primaryLanguage: LANGUAGES['es-MX'], region: 'North America' },
  'Argentina': { primaryLanguage: LANGUAGES['es-AR'], region: 'South America' },
  'Colombia': { primaryLanguage: LANGUAGES['es'], region: 'South America' },
  'Chile': { primaryLanguage: LANGUAGES['es'], region: 'South America' },
  'Peru': { primaryLanguage: LANGUAGES['es'], region: 'South America' },
  'Venezuela': { primaryLanguage: LANGUAGES['es'], region: 'South America' },
  'Ecuador': { primaryLanguage: LANGUAGES['es'], region: 'South America' },
  'Guatemala': { primaryLanguage: LANGUAGES['es'], region: 'Central America' },
  'Cuba': { primaryLanguage: LANGUAGES['es'], region: 'Caribbean' },
  'Dominican Republic': { primaryLanguage: LANGUAGES['es'], region: 'Caribbean' },
  'Costa Rica': { primaryLanguage: LANGUAGES['es'], region: 'Central America' },
  'Panama': { primaryLanguage: LANGUAGES['es'], region: 'Central America' },
  'Uruguay': { primaryLanguage: LANGUAGES['es'], region: 'South America' },
  'Paraguay': { primaryLanguage: LANGUAGES['es'], region: 'South America' },
  'Bolivia': { primaryLanguage: LANGUAGES['es'], region: 'South America' },

  // French-speaking countries
  'France': { primaryLanguage: LANGUAGES['fr-FR'], region: 'Europe' },
  'Belgium': { primaryLanguage: LANGUAGES['fr'], secondaryLanguages: [LANGUAGES['nl']], region: 'Europe' },
  'Switzerland': { primaryLanguage: LANGUAGES['fr'], secondaryLanguages: [LANGUAGES['de'], LANGUAGES['it']], region: 'Europe' },
  'Luxembourg': { primaryLanguage: LANGUAGES['fr'], region: 'Europe' },
  'Monaco': { primaryLanguage: LANGUAGES['fr'], region: 'Europe' },

  // Arabic-speaking countries
  'Saudi Arabia': { primaryLanguage: LANGUAGES['ar-SA'], region: 'Middle East' },
  'United Arab Emirates': { primaryLanguage: LANGUAGES['ar-AE'], secondaryLanguages: [LANGUAGES['en']], region: 'Middle East' },
  'UAE': { primaryLanguage: LANGUAGES['ar-AE'], secondaryLanguages: [LANGUAGES['en']], region: 'Middle East' },
  'Egypt': { primaryLanguage: LANGUAGES['ar-EG'], region: 'Middle East' },
  'Jordan': { primaryLanguage: LANGUAGES['ar'], region: 'Middle East' },
  'Lebanon': { primaryLanguage: LANGUAGES['ar'], secondaryLanguages: [LANGUAGES['fr']], region: 'Middle East' },
  'Syria': { primaryLanguage: LANGUAGES['ar'], region: 'Middle East' },
  'Iraq': { primaryLanguage: LANGUAGES['ar'], region: 'Middle East' },
  'Kuwait': { primaryLanguage: LANGUAGES['ar'], region: 'Middle East' },
  'Qatar': { primaryLanguage: LANGUAGES['ar'], secondaryLanguages: [LANGUAGES['en']], region: 'Middle East' },
  'Bahrain': { primaryLanguage: LANGUAGES['ar'], secondaryLanguages: [LANGUAGES['en']], region: 'Middle East' },
  'Oman': { primaryLanguage: LANGUAGES['ar'], region: 'Middle East' },
  'Yemen': { primaryLanguage: LANGUAGES['ar'], region: 'Middle East' },
  'Morocco': { primaryLanguage: LANGUAGES['ar'], secondaryLanguages: [LANGUAGES['fr']], region: 'North Africa' },
  'Algeria': { primaryLanguage: LANGUAGES['ar'], secondaryLanguages: [LANGUAGES['fr']], region: 'North Africa' },
  'Tunisia': { primaryLanguage: LANGUAGES['ar'], secondaryLanguages: [LANGUAGES['fr']], region: 'North Africa' },
  'Libya': { primaryLanguage: LANGUAGES['ar'], region: 'North Africa' },
  'Sudan': { primaryLanguage: LANGUAGES['ar'], region: 'Africa' },

  // German-speaking countries
  'Germany': { primaryLanguage: LANGUAGES['de'], region: 'Europe' },
  'Austria': { primaryLanguage: LANGUAGES['de'], region: 'Europe' },

  // Italian-speaking countries
  'Italy': { primaryLanguage: LANGUAGES['it'], region: 'Europe' },

  // Portuguese-speaking countries
  'Portugal': { primaryLanguage: LANGUAGES['pt'], region: 'Europe' },
  'Brazil': { primaryLanguage: LANGUAGES['pt-BR'], region: 'South America' },

  // Other European countries
  'Netherlands': { primaryLanguage: LANGUAGES['nl'], region: 'Europe' },
  'Sweden': { primaryLanguage: LANGUAGES['sv'], region: 'Europe' },
  'Norway': { primaryLanguage: LANGUAGES['no'], region: 'Europe' },
  'Denmark': { primaryLanguage: LANGUAGES['da'], region: 'Europe' },
  'Finland': { primaryLanguage: LANGUAGES['fi'], region: 'Europe' },
  'Poland': { primaryLanguage: LANGUAGES['pl'], region: 'Europe' },
  'Russia': { primaryLanguage: LANGUAGES['ru'], region: 'Europe/Asia' },

  // Asian countries
  'China': { primaryLanguage: LANGUAGES['zh-CN'], region: 'Asia' },
  'Taiwan': { primaryLanguage: LANGUAGES['zh-TW'], region: 'Asia' },
  'Hong Kong': { primaryLanguage: LANGUAGES['zh'], secondaryLanguages: [LANGUAGES['en']], region: 'Asia' },
  'Japan': { primaryLanguage: LANGUAGES['ja'], region: 'Asia' },
  'South Korea': { primaryLanguage: LANGUAGES['ko'], region: 'Asia' },
  'Korea': { primaryLanguage: LANGUAGES['ko'], region: 'Asia' },
  'India': { primaryLanguage: LANGUAGES['hi'], secondaryLanguages: [LANGUAGES['en']], region: 'Asia' },
  'Thailand': { primaryLanguage: LANGUAGES['th'], region: 'Asia' },
  'Vietnam': { primaryLanguage: LANGUAGES['vi'], region: 'Asia' },
  'Turkey': { primaryLanguage: LANGUAGES['tr'], region: 'Europe/Asia' },
  'Israel': { primaryLanguage: LANGUAGES['he'], secondaryLanguages: [LANGUAGES['en']], region: 'Middle East' },
  'Iran': { primaryLanguage: LANGUAGES['fa'], region: 'Middle East' },

  // Default fallback
  'default': { primaryLanguage: LANGUAGES['en'], region: 'Global' },
};

/**
 * Detect languages for a given location
 */
export function detectLanguagesForLocation(location: string): LocationLanguageMapping {
  if (!location) {
    return LOCATION_LANGUAGE_MAP['default'];
  }

  // Normalize location string
  const normalizedLocation = location.trim();
  
  // Try exact match first
  if (LOCATION_LANGUAGE_MAP[normalizedLocation]) {
    return LOCATION_LANGUAGE_MAP[normalizedLocation];
  }

  // Try case-insensitive match
  const locationKey = Object.keys(LOCATION_LANGUAGE_MAP).find(
    key => key.toLowerCase() === normalizedLocation.toLowerCase()
  );
  
  if (locationKey) {
    return LOCATION_LANGUAGE_MAP[locationKey];
  }

  // Try partial match (for cases like "New York, USA" -> "USA")
  const partialMatch = Object.keys(LOCATION_LANGUAGE_MAP).find(key => 
    normalizedLocation.toLowerCase().includes(key.toLowerCase()) ||
    key.toLowerCase().includes(normalizedLocation.toLowerCase())
  );

  if (partialMatch) {
    return LOCATION_LANGUAGE_MAP[partialMatch];
  }

  // Default to English
  console.log(`Language detection: No match found for location "${location}", defaulting to English`);
  return LOCATION_LANGUAGE_MAP['default'];
}

/**
 * Get primary language code for a location
 */
export function getPrimaryLanguageCode(location: string): string {
  const mapping = detectLanguagesForLocation(location);
  return mapping.primaryLanguage.code;
}

/**
 * Get all languages for a location (primary + secondary)
 */
export function getAllLanguagesForLocation(location: string): LanguageInfo[] {
  const mapping = detectLanguagesForLocation(location);
  return [mapping.primaryLanguage, ...(mapping.secondaryLanguages || [])];
}

/**
 * Check if a location uses RTL languages
 */
export function isRTLLocation(location: string): boolean {
  const mapping = detectLanguagesForLocation(location);
  return mapping.primaryLanguage.isRTL || false;
}

/**
 * Generate language instruction for AI prompts
 */
export function generateLanguageInstruction(location: string): string {
  const mapping = detectLanguagesForLocation(location);
  const primaryLang = mapping.primaryLanguage;
  const secondaryLangs = mapping.secondaryLanguages || [];

  let instruction = `PRIMARY LANGUAGE: Use ${primaryLang.name} (${primaryLang.nativeName}) as the main language for content generation.`;

  if (secondaryLangs.length > 0) {
    const secondaryNames = secondaryLangs.map(lang => `${lang.name} (${lang.nativeName})`).join(', ');
    instruction += `\nSECONDARY LANGUAGES: ${secondaryNames} may be used when culturally appropriate.`;
  }

  if (primaryLang.isRTL) {
    instruction += `\nRTL SUPPORT: This language uses right-to-left text direction.`;
  }

  instruction += `\nLOCATION CONTEXT: Content is for ${location} in the ${mapping.region} region.`;
  instruction += `\nIMPORTANT: Only use languages appropriate for this specific location. Do not use random or incorrect languages.`;

  return instruction;
}

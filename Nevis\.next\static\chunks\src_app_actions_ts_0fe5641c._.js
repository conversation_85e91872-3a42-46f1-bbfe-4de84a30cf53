(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/actions.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_app_d20d1628._.js",
  "static/chunks/0f475_dist_build_webpack_loaders_next-flight-loader_action-client-wrapper_b74250f7.js",
  "static/chunks/src_app_actions_ts_0c5ecbeb._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/app/actions.ts [app-client] (ecmascript)");
    });
});
}}),
}]);
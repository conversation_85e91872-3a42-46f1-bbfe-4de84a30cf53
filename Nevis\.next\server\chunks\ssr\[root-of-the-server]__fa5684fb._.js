module.exports = {

"[externals]/perf_hooks [external] (perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("perf_hooks", () => require("perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/async_hooks [external] (async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("async_hooks", () => require("async_hooks"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/process [external] (process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("process", () => require("process"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/require-in-the-middle [external] (require-in-the-middle, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("require-in-the-middle", () => require("require-in-the-middle"));

module.exports = mod;
}}),
"[externals]/import-in-the-middle [external] (import-in-the-middle, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("import-in-the-middle", () => require("import-in-the-middle"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/http2 [external] (http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http2", () => require("http2"));

module.exports = mod;
}}),
"[externals]/dns [external] (dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("dns", () => require("dns"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/express [external] (express, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("express", () => require("express"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/src/ai/genkit.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ai": (()=>ai)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$genkit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/genkit.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/googleai/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/googleai/lib/index.mjs [app-rsc] (ecmascript) <locals>");
;
;
// Get API key from environment variables
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;
if (!apiKey) {
    console.error("❌ No Google AI API key found. Please set GEMINI_API_KEY, GOOGLE_API_KEY, or GOOGLE_GENAI_API_KEY environment variable.");
}
const ai = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$genkit$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["genkit"])({
    plugins: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$googleai$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["googleAI"])({
            apiKey
        })
    ],
    model: 'googleai/gemini-2.0-flash'
});
}}),
"[project]/src/ai/flows/analyze-brand.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"409b070a66dc1c129fedbcca75604a74d27388371b":"analyzeBrand"},"",""] */ __turbopack_context__.s({
    "analyzeBrand": (()=>analyzeBrand)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview Analyzes a brand's website and design examples to extract brand voice, visual style, and other key business details.
 *
 * - analyzeBrand - A function that initiates the brand analysis process.
 * - AnalyzeBrandInput - The input type for the analyzeBrand function.
 * - AnalyzeBrandOutput - The return type for the analyzeBrand function.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/common.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
const AnalyzeBrandInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    websiteUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The URL of the brand\'s website to analyze.'),
    designImageUris: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe("A list of data URIs of previous design examples. Each must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."),
    websiteContent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The scraped content from the website for analysis.')
});
const AnalyzeBrandOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    // Core Business Information
    businessName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The EXACT business name, company name, or brand name as it appears on the website. This should be the PROPER NAME like "Apple Inc.", "Microsoft Corporation", "Joe\'s Pizza", NOT a description of what they do. Look for the company name in headers, logos, titles, "About Us" sections, or anywhere the business identifies itself. Extract the precise name they use, not their business type or industry.'),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A comprehensive, detailed summary of the business that includes: what they do, how they do it, their mission/values, their approach, their history, and what makes them unique. Combine information from multiple website sections to create a thorough description. Minimum 3-4 sentences using the company\'s own words.'),
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The specific type/category of business like "Software Company", "Restaurant", "Consulting Firm", "E-commerce Store" - this describes WHAT they do, not WHO they are. This is different from the business name.'),
    industry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The specific industry sector the business operates in using their own terminology.'),
    targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('DETAILED description of the specific target audience, customer base, client types, demographics, business types, industries, or customer characteristics this company mentions they serve. Be very specific and comprehensive. Include customer examples, business sizes, industries, or any specific customer details mentioned on the website.'),
    // Services and Products
    services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A comprehensive newline-separated list of ALL services, products, packages, plans, or offerings this specific company provides. Search the entire website content thoroughly. Format each as "Service Name: Detailed description as written on their website including features, benefits, what\'s included". Extract the company\'s own descriptions, not generic ones. Include pricing, packages, service tiers, features, or any details mentioned. Be comprehensive and don\'t miss any services.'),
    keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('ALL the SPECIFIC key features, benefits, or unique selling propositions that THIS company highlights about their offerings. Use their exact wording and claims. Be comprehensive and detailed.'),
    competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('What THIS specific company says makes them different from competitors. Extract their own competitive claims and differentiators, not generic industry advantages. Use their exact wording.'),
    // Brand Identity and Voice
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A detailed description of THIS company\'s specific visual style based on their actual design examples and website. Describe the exact colors, typography, layout patterns, imagery style, and aesthetic choices THEY use. Reference specific design elements visible in their materials.'),
    writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The SPECIFIC writing tone and voice THIS company uses in their actual website content. Analyze their actual text, headlines, and copy to describe their unique communication style. Use examples from their content.'),
    contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The SPECIFIC themes, topics, and messaging patterns THIS company focuses on in their actual content. Extract the exact topics they discuss and how they position themselves.'),
    brandPersonality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('THIS company\'s specific brand personality as expressed through their actual content and design choices. Base this on their real communications, not generic assumptions.'),
    // Visual Design Analysis
    colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        primary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Primary brand color in hex format extracted from the uploaded design examples. Look carefully at the most prominent color used in logos, headers, buttons, or main design elements in the images.'),
        secondary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Secondary brand color in hex format extracted from the uploaded design examples. Look for the second most used color in the designs.'),
        accent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Accent color in hex format extracted from the uploaded design examples. Look for colors used for highlights, calls-to-action, or accent elements in the images.'),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Detailed description of the overall color scheme and palette used in the design examples. Describe the colors you can actually see in the uploaded images and the mood/feeling they create.')
    }).optional().describe('Color palette analysis extracted from the uploaded design examples. Analyze the actual colors visible in the design images provided.'),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        style: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Typography style (e.g., modern, classic, playful, professional).'),
        characteristics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Font characteristics and typography choices observed.')
    }).optional().describe('Typography analysis from design examples and website.'),
    // Contact and Location Information
    contactInfo: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        phone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The main contact phone number.'),
        email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The main contact email address.'),
        address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The physical business address.'),
        website: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Additional website URLs or domains mentioned.'),
        hours: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Business hours if mentioned on the website.')
    }).describe('The contact information for the business, extracted from the website.'),
    // Social Media and Online Presence
    socialMedia: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Facebook page URL if found on the website.'),
        instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Instagram profile URL if found on the website.'),
        twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Twitter profile URL if found on the website.'),
        linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('LinkedIn profile URL if found on the website.'),
        youtube: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('YouTube channel URL if found on the website.'),
        other: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe('Other social media or platform URLs found.')
    }).optional().describe('Social media presence and URLs found on the website.'),
    // Additional Business Details
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Geographic location or service area of the business.'),
    establishedYear: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Year the business was established if mentioned.'),
    teamSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Information about team size or company size if mentioned.'),
    certifications: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe('Professional certifications, awards, or credentials mentioned.'),
    // Content and Marketing Insights
    contentStrategy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('Insights into their content marketing strategy based on website content.'),
    callsToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe('Common calls-to-action used throughout the website.'),
    valueProposition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The main value proposition or promise to customers.')
});
async function analyzeBrand(input) {
    return analyzeBrandFlow(input);
}
const analyzeBrandPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeBrandPrompt',
    input: {
        schema: AnalyzeBrandInputSchema
    },
    output: {
        schema: AnalyzeBrandOutputSchema
    },
    prompt: `You are an expert brand strategist, business analyst, and design consultant with deep expertise in brand identity, visual design, and digital marketing. Your task is to perform an extremely comprehensive and detailed analysis of THIS SPECIFIC BUSINESS based on its website and design examples.

  **CRITICAL INSTRUCTION: BE COMPANY-SPECIFIC, NOT GENERIC**
  - Extract ONLY information that is specifically mentioned on THIS company's website
  - Use the EXACT wording and terminology that THIS company uses
  - Do NOT provide generic industry descriptions or assumptions
  - Focus on what makes THIS specific business unique and different
  - Extract the company's OWN words about their services, not generic descriptions

  **Source Information:**
  - Website URL: {{{websiteUrl}}}
  - Website Content: {{{websiteContent}}}
  - Design Examples: These are crucial for understanding visual style, color palette, typography, and brand aesthetic.
  {{#each designImageUris}}
  Design Example: {{media url=this}}
  {{/each}}

  **COMPANY-SPECIFIC ANALYSIS REQUIREMENTS:**

  **🏢 THIS COMPANY'S BUSINESS DETAILS (Extract from Website Content Above):**
  1. **Business Name:** Extract the EXACT business name, company name, or brand name as it appears on the website. Look for the company name in headers, logos, page titles, "About Us" sections, contact information, or anywhere the business identifies itself. Extract the precise name they use - this is critical for brand identity.
  2. **Business Description:** Find and extract a COMPREHENSIVE and DETAILED description of this company from the website content. Look for "About Us", "Who We Are", "Our Story", "Mission", "Vision" sections. Combine multiple sections to create a thorough description that includes: what they do, how they do it, their mission/values, their history, their approach, and what makes them unique. Use their own words but create a complete picture. Minimum 3-4 sentences.
  3. **Business Type & Industry:** Use the SPECIFIC terms this company uses in their website content to describe their business type and industry. Be precise and specific.
  4. **Target Audience:** This is CRITICAL - Extract EXACTLY who this company says they serve from the website content. Look for "Who We Serve", "Our Clients", "Target Market", "Perfect For", "Ideal Customers", "We Help" sections. Also look for customer testimonials, case studies, or examples that indicate their target market. Include demographics, business types, industries, or specific customer characteristics they mention. Be very detailed and specific.
  5. **Services/Products:** Extract EVERY service and product this company specifically offers from the website content. Look in "Services", "What We Do", "Products", "Solutions", "Offerings", "Packages", "Plans", "Pricing" sections. Use their EXACT service names and descriptions as written. Include ALL services, packages, tiers, or offerings mentioned. Format as "Service Name: Detailed description as written on their website" on separate lines. Don't miss any services.
  6. **Key Features & Benefits:** Extract ALL the SPECIFIC features and benefits this company highlights about their offerings from the website content. Look in "Features", "Benefits", "Why Choose Us" sections. Use their exact wording and claims. Be comprehensive.
  7. **Competitive Advantages:** Extract what THIS company specifically says makes them different or better from the website content. Look for "Why Choose Us", "What Makes Us Different", "Our Advantage", "Why We're Better" sections. Use their own competitive claims and differentiators.
  8. **Value Proposition:** Extract the EXACT value proposition or promises this company makes to their customers from the website content. What do they promise to deliver?

  **🎨 VISUAL DESIGN DEEP ANALYSIS (Analyze the Design Examples Carefully):**
  8. **Visual Style:** Provide a detailed analysis of the overall visual aesthetic, design approach, imagery style, layout patterns, and visual hierarchy. Base this primarily on the design examples provided. Describe the specific design elements you can see in the uploaded images.
  9. **Color Palette Analysis - CRITICAL:**
     - CAREFULLY examine each design example image to identify the EXACT colors used
     - Extract specific colors in hex format from the designs (look at backgrounds, text, buttons, accents, logos)
     - Identify the primary brand color (most prominent color in the designs)
     - Identify secondary colors and accent colors used in the designs
     - Describe the overall color scheme and mood it creates
     - Be very specific about the colors you can see in the uploaded design examples
  10. **Typography Analysis:**
     - Examine the design examples to describe the actual font styles and typography choices used
     - Identify if fonts are modern, classic, playful, professional, etc. based on what you see in the images
     - Note any distinctive typographic elements visible in the design examples

  **✍️ BRAND VOICE & CONTENT ANALYSIS:**
  11. **Writing Tone:** Analyze the brand's communication style in detail (formal, casual, witty, professional, friendly, authoritative, conversational, etc.).
  12. **Content Themes:** Identify recurring themes, topics, and messaging patterns throughout the website and designs.
  13. **Brand Personality:** Describe the overall brand character and personality as expressed through content and design.
  14. **Content Strategy:** Analyze their approach to content marketing and communication.
  15. **Calls to Action:** Extract common CTAs and action-oriented language used.

  **📞 CONTACT & BUSINESS DETAILS:**
  16. **Complete Contact Information:** Extract phone numbers, email addresses, physical addresses, business hours, and any additional contact methods.
  17. **Location & Service Area:** Identify geographic location and areas they serve.
  18. **Business Details:** Look for establishment year, team size, company history, certifications, awards, or credentials.

  **🌐 DIGITAL PRESENCE ANALYSIS:**
  19. **Social Media Presence:** Extract ALL social media URLs found (Facebook, Instagram, Twitter, LinkedIn, YouTube, TikTok, etc.).
  20. **Additional Websites:** Note any additional domains, subdomains, or related websites mentioned.

  **CRITICAL ANALYSIS INSTRUCTIONS - COMPANY-SPECIFIC EXTRACTION:**

  **FOR SERVICES/PRODUCTS (Search ALL Sections in Website Content Above):**
  - Search the website content for "Services", "What We Do", "Our Services", "Products", "Solutions", "Offerings", "Packages", "Plans", "Pricing" sections
  - Extract EVERY service name as the company lists them in their website content - don't miss any
  - Include the company's OWN descriptions of each service from their website text
  - Look for pricing information, package details, service tiers, features included in each service
  - Look in multiple sections - services might be mentioned in different parts of the website
  - Format as: "Service Name: Company's exact description of what this service includes, features, benefits, etc."
  - Include any pricing tiers, packages, or service levels mentioned in the content
  - Be comprehensive - extract ALL services, not just the main ones

  **FOR TARGET AUDIENCE (Search ALL Sections for Customer Information):**
  - Search the website content for "Who We Serve", "Our Clients", "Target Market", "Perfect For", "Ideal Customers", "We Help", "Client Types" sections
  - Look for customer testimonials, case studies, or examples that indicate their target market
  - Extract specific demographics, business types, industries, company sizes, or customer characteristics they mention
  - Look for phrases like "small businesses", "enterprise clients", "startups", "restaurants", "healthcare providers", etc.
  - Include any specific customer examples or client types mentioned
  - Be very detailed and specific about who they serve

  **FOR BUSINESS DESCRIPTION (Create Comprehensive Description):**
  - Search the website content for "About Us", "Who We Are", "Our Story", "Mission", "Vision", "Company" sections
  - Combine information from multiple sections to create a thorough, detailed description
  - Include what they do, how they do it, their mission/values, their approach, their history, what makes them unique
  - Use their own words but create a complete, comprehensive picture
  - Make it detailed and informative - minimum 3-4 sentences

  **FOR TARGET AUDIENCE:**
  - Look for "Who We Serve", "Our Clients", "Target Market" information
  - Extract the SPECIFIC customer types they mention
  - Use their exact terminology for their customer base

  **FOR COMPETITIVE ADVANTAGES:**
  - Find "Why Choose Us", "What Makes Us Different", "Our Advantage" sections
  - Extract their SPECIFIC claims about what makes them unique
  - Use their exact competitive positioning statements

  **GENERAL EXTRACTION RULES:**
  - Be extremely thorough and detailed in your analysis
  - Extract every piece of relevant information you can find
  - For design analysis, pay close attention to the uploaded design examples
  - Look for subtle details like color codes, font choices, layout patterns
  - Extract contact information from headers, footers, contact pages, and anywhere else it appears
  - Look for social media links in headers, footers, and throughout the site
  - If information is not available, leave those fields empty rather than guessing
  - NEVER use generic industry descriptions - only use company-specific information
  - Quote the company's exact wording whenever possible

  **FINAL REQUIREMENTS:**
  - Be EXTREMELY thorough and comprehensive in your analysis
  - Extract EVERY piece of relevant information from the website content
  - Don't miss any services, features, or customer details
  - Analyze the design examples carefully for exact colors
  - Create detailed, informative descriptions using the company's own words
  - Target audience description must be specific and detailed
  - Services list must be comprehensive and complete
  - Color analysis must be based on actual colors visible in the design examples

  **OUTPUT FORMAT:**
  Provide a complete, detailed analysis in the required JSON format with all available information extracted and organized according to the schema.
  `
});
// Website scraping function with enhanced content extraction
async function scrapeWebsiteContent(url) {
    try {
        console.log('🌐 Scraping website content from:', url);
        // Import cheerio for HTML parsing
        const cheerio = await __turbopack_context__.r("[project]/node_modules/cheerio/dist/esm/index.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use fetch to get the website content
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const html = await response.text();
        const $ = cheerio.load(html);
        // Remove unwanted elements
        $('script, style, nav, footer, header, .cookie-banner, .popup, .modal').remove();
        // Extract structured content
        const extractedContent = {
            title: $('title').text().trim(),
            metaDescription: $('meta[name="description"]').attr('content') || '',
            headings: {
                h1: $('h1').map((_, el)=>$(el).text().trim()).get(),
                h2: $('h2').map((_, el)=>$(el).text().trim()).get(),
                h3: $('h3').map((_, el)=>$(el).text().trim()).get()
            },
            // Look for common business sections with more comprehensive selectors
            aboutSection: $('section:contains("About"), div:contains("About Us"), .about, #about, section:contains("Who We Are"), div:contains("Our Story"), .story, #story').text().trim(),
            servicesSection: $('section:contains("Services"), div:contains("Services"), .services, #services, section:contains("What We Do"), div:contains("What We Do"), section:contains("Solutions"), div:contains("Solutions"), .solutions, #solutions, section:contains("Offerings"), div:contains("Offerings")').text().trim(),
            contactSection: $('section:contains("Contact"), div:contains("Contact"), .contact, #contact, section:contains("Get in Touch"), div:contains("Reach Us")').text().trim(),
            // Enhanced target audience extraction
            targetAudienceSection: $('section:contains("Who We Serve"), div:contains("Who We Serve"), section:contains("Our Clients"), div:contains("Our Clients"), section:contains("Target"), div:contains("Target"), section:contains("For"), div:contains("Perfect For"), .clients, #clients, .audience, #audience').text().trim(),
            // More comprehensive service extraction
            featuresSection: $('section:contains("Features"), div:contains("Features"), .features, #features, section:contains("Benefits"), div:contains("Benefits"), .benefits, #benefits').text().trim(),
            packagesSection: $('section:contains("Packages"), div:contains("Packages"), .packages, #packages, section:contains("Plans"), div:contains("Plans"), .plans, #plans, section:contains("Pricing"), div:contains("Pricing"), .pricing, #pricing').text().trim(),
            // Extract all paragraph text
            paragraphs: $('p').map((_, el)=>$(el).text().trim()).get().filter((text)=>text.length > 20),
            // Extract list items (often contain services/features)
            listItems: $('li').map((_, el)=>$(el).text().trim()).get().filter((text)=>text.length > 10),
            // Extract any text that might contain business info
            mainContent: $('main, .main, .content, .container').text().trim()
        };
        // Combine all extracted content into a structured format
        let structuredContent = '';
        if (extractedContent.title) {
            structuredContent += `WEBSITE TITLE: ${extractedContent.title}\n\n`;
        }
        if (extractedContent.metaDescription) {
            structuredContent += `META DESCRIPTION: ${extractedContent.metaDescription}\n\n`;
        }
        if (extractedContent.headings.h1.length > 0) {
            structuredContent += `MAIN HEADINGS: ${extractedContent.headings.h1.join(' | ')}\n\n`;
        }
        if (extractedContent.aboutSection) {
            structuredContent += `ABOUT SECTION: ${extractedContent.aboutSection}\n\n`;
        }
        if (extractedContent.servicesSection) {
            structuredContent += `SERVICES SECTION: ${extractedContent.servicesSection}\n\n`;
        }
        if (extractedContent.targetAudienceSection) {
            structuredContent += `TARGET AUDIENCE SECTION: ${extractedContent.targetAudienceSection}\n\n`;
        }
        if (extractedContent.featuresSection) {
            structuredContent += `FEATURES/BENEFITS SECTION: ${extractedContent.featuresSection}\n\n`;
        }
        if (extractedContent.packagesSection) {
            structuredContent += `PACKAGES/PRICING SECTION: ${extractedContent.packagesSection}\n\n`;
        }
        if (extractedContent.contactSection) {
            structuredContent += `CONTACT SECTION: ${extractedContent.contactSection}\n\n`;
        }
        if (extractedContent.listItems.length > 0) {
            structuredContent += `KEY POINTS/SERVICES: ${extractedContent.listItems.slice(0, 20).join(' | ')}\n\n`;
        }
        if (extractedContent.paragraphs.length > 0) {
            structuredContent += `MAIN CONTENT: ${extractedContent.paragraphs.slice(0, 10).join(' ')}\n\n`;
        }
        // Fallback to main content if structured extraction didn't work well
        if (structuredContent.length < 500 && extractedContent.mainContent) {
            structuredContent += `FULL CONTENT: ${extractedContent.mainContent}`;
        }
        // Clean up and limit content length
        structuredContent = structuredContent.replace(/\s+/g, ' ').trim();
        // Limit content length to avoid token limits (increased for better analysis)
        if (structuredContent.length > 15000) {
            structuredContent = structuredContent.substring(0, 15000) + '...';
        }
        console.log('✅ Website content extracted, length:', structuredContent.length);
        console.log('📄 Content preview:', structuredContent.substring(0, 200) + '...');
        return structuredContent;
    } catch (error) {
        console.error('❌ Error scraping website:', error);
        throw new Error(`Failed to scrape website content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
const analyzeBrandFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'analyzeBrandFlow',
    inputSchema: AnalyzeBrandInputSchema,
    outputSchema: AnalyzeBrandOutputSchema
}, async (input)=>{
    // First, scrape the website content
    const websiteContent = await scrapeWebsiteContent(input.websiteUrl);
    // Create enhanced input with website content
    const enhancedInput = {
        ...input,
        websiteContent
    };
    const { output } = await analyzeBrandPrompt(enhancedInput);
    return output;
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    analyzeBrand
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(analyzeBrand, "409b070a66dc1c129fedbcca75604a74d27388371b", null);
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:https [external] (node:https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:https", () => require("node:https"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:process [external] (node:process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:process", () => require("node:process"));

module.exports = mod;
}}),
"[externals]/node:stream/web [external] (node:stream/web, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream/web", () => require("node:stream/web"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:path [external] (node:path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:path", () => require("node:path"));

module.exports = mod;
}}),
"[externals]/worker_threads [external] (worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("worker_threads", () => require("worker_threads"));

module.exports = mod;
}}),
"[project]/src/services/weather.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/services/weather.ts
__turbopack_context__.s({
    "getWeather": (()=>getWeather)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript) <locals>");
;
const API_KEY = process.env.OPENWEATHERMAP_API_KEY;
const BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';
async function getWeather(location) {
    if (!API_KEY || API_KEY === 'YOUR_OPENWEATHERMAP_API_KEY' || API_KEY.length < 20) {
        console.log('OpenWeatherMap API key is not configured or appears invalid.');
        return null;
    }
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(`${BASE_URL}?q=${location}&appid=${API_KEY}&units=imperial`);
        if (!response.ok) {
            console.error('Weather API Error:', `Status: ${response.status}`);
            // Return null to allow the flow to continue without weather data
            return `Could not retrieve weather information due to an API error (Status: ${response.status})`;
        }
        const data = await response.json();
        if (data && data.weather && data.main) {
            const description = data.weather[0].description;
            const temp = Math.round(data.main.temp);
            return `${description} with a temperature of ${temp}°F`;
        }
        return null;
    } catch (error) {
        console.error('Error fetching weather data:', error);
        return null;
    }
}
}}),
"[project]/src/services/events.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/services/events.ts
__turbopack_context__.s({
    "getEvents": (()=>getEvents)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$add$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/add.mjs [app-rsc] (ecmascript)");
;
;
const API_KEY = process.env.EVENTBRITE_PRIVATE_TOKEN;
const BASE_URL = 'https://www.eventbriteapi.com/v3/events/search/';
async function getEvents(location, date) {
    if (!API_KEY || API_KEY === 'YOUR_EVENTBRITE_PRIVATE_TOKEN' || API_KEY.length < 10) {
        console.log('Eventbrite API key is not configured or appears invalid.');
        return null;
    }
    // Eventbrite is more flexible with location strings.
    const city = location.split(',')[0].trim();
    // Search for events starting from today up to one week from now to get more results
    const startDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "yyyy-MM-dd'T'HH:mm:ss'Z'");
    const endDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$add$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["add"])(date, {
        days: 7
    }), "yyyy-MM-dd'T'HH:mm:ss'Z'");
    try {
        const url = `${BASE_URL}?location.address=${city}&start_date.range_start=${startDate}&start_date.range_end=${endDate}&sort_by=date`;
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$node$2d$fetch$2f$src$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(url, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Accept': 'application/json'
            }
        });
        if (!response.ok) {
            const errorBody = await response.text();
            console.error('Eventbrite API Error:', `Status: ${response.status}`, errorBody);
            // Return null to allow the flow to continue without event data
            return `Could not retrieve local event information due to an API error (Status: ${response.status}).`;
        }
        const data = await response.json();
        if (data.events && data.events.length > 0) {
            const eventNames = data.events.slice(0, 5).map((event)=>event.name.text);
            return `local events happening soon include: ${eventNames.join(', ')}`;
        } else {
            return 'no major local events found on Eventbrite for the upcoming week';
        }
    } catch (error) {
        console.error('Error fetching event data:', error);
        return null;
    }
}
}}),
"[project]/src/ai/tools/local-data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f006052b2e962a89122dd7c8b8cad74ef59a365ca":"getWeatherTool","7f178ddd9eaf1b9299ed47b0156091a9d602fe976e":"getEventsTool"},"",""] */ __turbopack_context__.s({
    "getEventsTool": (()=>getEventsTool),
    "getWeatherTool": (()=>getWeatherTool)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview Defines Genkit tools for fetching local weather and event data.
 * This allows the AI to dynamically decide when to pull in local information
 * to make social media posts more relevant and timely.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$weather$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/weather.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$events$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/events.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
const getWeatherTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getWeather',
    description: 'Gets the current weather for a specific location. Use this to make posts more relevant to the current conditions.',
    inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The city and state, e.g., "San Francisco, CA"')
    }),
    outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}, async (input)=>{
    const weather = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$weather$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getWeather"])(input.location);
    return weather || 'Could not retrieve weather information.';
});
const getEventsTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEvents',
    description: 'Finds local events happening on or after the current date for a specific location. Use this to create timely posts about local happenings.',
    inputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The city and state, e.g., "San Francisco, CA"')
    }),
    outputSchema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
}, async (input)=>{
    // Tools will always be called with the current date
    const events = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$events$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEvents"])(input.location, new Date());
    return events || 'Could not retrieve local event information.';
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getWeatherTool,
    getEventsTool
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getWeatherTool, "7f006052b2e962a89122dd7c8b8cad74ef59a365ca", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getEventsTool, "7f178ddd9eaf1b9299ed47b0156091a9d602fe976e", null);
}}),
"[project]/src/ai/tools/enhanced-local-data.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Enhanced Local Data Tools - Events and Weather Integration
 * 
 * This module provides real-time local events and weather data
 * for contextually aware content generation.
 */ __turbopack_context__.s({
    "getEnhancedEventsTool": (()=>getEnhancedEventsTool),
    "getEnhancedWeatherTool": (()=>getEnhancedWeatherTool)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const getEnhancedEventsTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEnhancedEvents',
    description: 'Fetch local events from Eventbrite API that are relevant to the business type and location',
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Location for events (city, country or coordinates)'),
        businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Business type to filter relevant events'),
        radius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().default('25km').describe('Search radius for events'),
        timeframe: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().default('this_week').describe('Time period: today, this_week, this_month')
    }),
    output: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        start_date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        venue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        url: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
        is_free: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean(),
        relevance_score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number()
    }))
}, async (input)=>{
    try {
        if (!process.env.EVENTBRITE_API_KEY) {
            console.log('Eventbrite API key not configured, using fallback events');
            return getEventsFallback(input.location, input.businessType);
        }
        console.log(`🎪 Fetching events from Eventbrite for ${input.location}...`);
        // Convert location to coordinates if needed
        const locationQuery = await geocodeLocation(input.location);
        // Build Eventbrite API request
        const params = new URLSearchParams({
            'location.address': input.location,
            'location.within': input.radius,
            'start_date.range_start': getDateRange(input.timeframe).start,
            'start_date.range_end': getDateRange(input.timeframe).end,
            'sort_by': 'relevance',
            'page_size': '20',
            'expand': 'venue,category'
        });
        const response = await fetch(`https://www.eventbriteapi.com/v3/events/search/?${params}`, {
            headers: {
                'Authorization': `Bearer ${process.env.EVENTBRITE_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            console.log(`Eventbrite API error: ${response.status} ${response.statusText}`);
            throw new Error(`Eventbrite API error: ${response.status}`);
        }
        const data = await response.json();
        console.log(`✅ Eventbrite returned ${data.events?.length || 0} events`);
        // Process and filter events by business relevance
        const relevantEvents = processEventbriteEvents(data.events || [], input.businessType);
        return relevantEvents.slice(0, 10);
    } catch (error) {
        console.error('Error fetching Eventbrite events:', error);
        return getEventsFallback(input.location, input.businessType);
    }
});
const getEnhancedWeatherTool = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineTool({
    name: 'getEnhancedWeather',
    description: 'Fetch current weather and forecast with business context and content opportunities',
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Location for weather (city, country)'),
        businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Business type to provide relevant weather context'),
        includeForecast: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional().default(false).describe('Include 5-day forecast')
    }),
    output: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        humidity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        feels_like: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        content_opportunities: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        business_impact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        forecast: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            business_opportunity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).optional()
    })
}, async (input)=>{
    try {
        if (!process.env.OPENWEATHER_API_KEY) {
            console.log('OpenWeather API key not configured, using fallback weather');
            return getWeatherFallback(input.location, input.businessType);
        }
        console.log(`🌤️ Fetching weather from OpenWeather for ${input.location}...`);
        // Current weather
        const currentParams = new URLSearchParams({
            q: input.location,
            appid: process.env.OPENWEATHER_API_KEY,
            units: 'metric'
        });
        const currentResponse = await fetch(`https://api.openweathermap.org/data/2.5/weather?${currentParams}`);
        if (!currentResponse.ok) {
            console.log(`OpenWeather API error: ${currentResponse.status} ${currentResponse.statusText}`);
            throw new Error(`OpenWeather API error: ${currentResponse.status}`);
        }
        const currentData = await currentResponse.json();
        console.log(`✅ OpenWeather returned current weather for ${currentData.name}`);
        // Process weather data with business context
        const weatherContext = processWeatherData(currentData, input.businessType);
        // Get forecast if requested
        if (input.includeForecast) {
            const forecastParams = new URLSearchParams({
                q: input.location,
                appid: process.env.OPENWEATHER_API_KEY,
                units: 'metric'
            });
            const forecastResponse = await fetch(`https://api.openweathermap.org/data/2.5/forecast?${forecastParams}`);
            if (forecastResponse.ok) {
                const forecastData = await forecastResponse.json();
                weatherContext.forecast = processForecastData(forecastData, input.businessType);
            }
        }
        return weatherContext;
    } catch (error) {
        console.error('Error fetching weather:', error);
        return getWeatherFallback(input.location, input.businessType);
    }
});
/**
 * Helper functions
 */ async function geocodeLocation(location) {
    try {
        if (!process.env.OPENWEATHER_API_KEY) return null;
        const params = new URLSearchParams({
            q: location,
            limit: '1',
            appid: process.env.OPENWEATHER_API_KEY
        });
        const response = await fetch(`https://api.openweathermap.org/geo/1.0/direct?${params}`);
        if (response.ok) {
            const data = await response.json();
            if (data.length > 0) {
                return {
                    lat: data[0].lat,
                    lon: data[0].lon
                };
            }
        }
    } catch (error) {
        console.error('Error geocoding location:', error);
    }
    return null;
}
function getDateRange(timeframe) {
    const now = new Date();
    const start = new Date(now);
    let end = new Date(now);
    switch(timeframe){
        case 'today':
            end.setDate(end.getDate() + 1);
            break;
        case 'this_week':
            end.setDate(end.getDate() + 7);
            break;
        case 'this_month':
            end.setMonth(end.getMonth() + 1);
            break;
        default:
            end.setDate(end.getDate() + 7);
    }
    return {
        start: start.toISOString(),
        end: end.toISOString()
    };
}
function processEventbriteEvents(events, businessType) {
    return events.map((event)=>{
        const relevanceScore = calculateEventRelevance(event, businessType);
        return {
            name: event.name?.text || 'Unnamed Event',
            description: event.description?.text?.substring(0, 200) || '',
            start_date: event.start?.local || event.start?.utc || '',
            end_date: event.end?.local || event.end?.utc,
            venue: event.venue?.name || 'Online Event',
            category: event.category?.name || 'General',
            url: event.url,
            is_free: event.is_free || false,
            relevance_score: relevanceScore
        };
    }).filter((event)=>event.relevance_score >= 5).sort((a, b)=>b.relevance_score - a.relevance_score);
}
function calculateEventRelevance(event, businessType) {
    let score = 5; // Base score
    const eventName = (event.name?.text || '').toLowerCase();
    const eventDescription = (event.description?.text || '').toLowerCase();
    const eventCategory = (event.category?.name || '').toLowerCase();
    // Business type relevance
    const businessKeywords = getBusinessKeywords(businessType);
    for (const keyword of businessKeywords){
        if (eventName.includes(keyword) || eventDescription.includes(keyword) || eventCategory.includes(keyword)) {
            score += 2;
        }
    }
    // Event category bonus
    if (eventCategory.includes('business') || eventCategory.includes('networking')) {
        score += 1;
    }
    // Free events get slight bonus for broader appeal
    if (event.is_free) {
        score += 1;
    }
    return Math.min(10, score);
}
function getBusinessKeywords(businessType) {
    const keywordMap = {
        'financial technology software': [
            'fintech',
            'finance',
            'banking',
            'payment',
            'blockchain',
            'cryptocurrency',
            'startup',
            'tech'
        ],
        'restaurant': [
            'food',
            'culinary',
            'cooking',
            'dining',
            'chef',
            'restaurant',
            'hospitality'
        ],
        'fitness': [
            'fitness',
            'health',
            'wellness',
            'gym',
            'workout',
            'nutrition',
            'sports'
        ],
        'technology': [
            'tech',
            'software',
            'programming',
            'ai',
            'digital',
            'innovation',
            'startup'
        ],
        'beauty': [
            'beauty',
            'cosmetics',
            'skincare',
            'wellness',
            'spa',
            'fashion'
        ],
        'retail': [
            'retail',
            'shopping',
            'ecommerce',
            'business',
            'sales',
            'marketing'
        ]
    };
    return keywordMap[businessType.toLowerCase()] || [
        'business',
        'networking',
        'professional'
    ];
}
function processWeatherData(weatherData, businessType) {
    const temperature = Math.round(weatherData.main.temp);
    const condition = weatherData.weather[0].main;
    const description = weatherData.weather[0].description;
    return {
        temperature,
        condition,
        description,
        humidity: weatherData.main.humidity,
        feels_like: Math.round(weatherData.main.feels_like),
        location: weatherData.name,
        content_opportunities: generateWeatherContentOpportunities(condition, temperature, businessType),
        business_impact: generateBusinessWeatherImpact(condition, temperature, businessType)
    };
}
function processForecastData(forecastData, businessType) {
    const dailyForecasts = forecastData.list.filter((_, index)=>index % 8 === 0).slice(0, 5);
    return dailyForecasts.map((forecast)=>({
            date: new Date(forecast.dt * 1000).toLocaleDateString(),
            temperature: Math.round(forecast.main.temp),
            condition: forecast.weather[0].main,
            business_opportunity: generateBusinessWeatherImpact(forecast.weather[0].main, forecast.main.temp, businessType)
        }));
}
function generateWeatherContentOpportunities(condition, temperature, businessType) {
    const opportunities = [];
    // Temperature-based opportunities
    if (temperature > 25) {
        opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');
    } else if (temperature < 10) {
        opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');
    }
    // Condition-based opportunities
    switch(condition.toLowerCase()){
        case 'rain':
            opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');
            break;
        case 'sunny':
        case 'clear':
            opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');
            break;
        case 'clouds':
            opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');
            break;
    }
    // Business-specific weather opportunities
    const businessOpportunities = getBusinessWeatherOpportunities(businessType, condition, temperature);
    opportunities.push(...businessOpportunities);
    return opportunities;
}
function generateBusinessWeatherImpact(condition, temperature, businessType) {
    const businessImpacts = {
        'restaurant': {
            'sunny': 'Perfect weather for outdoor dining and patio service',
            'rain': 'Great opportunity to promote cozy indoor dining experience',
            'hot': 'Ideal time to highlight refreshing drinks and cool dishes',
            'cold': 'Perfect weather for warm comfort food and hot beverages'
        },
        'fitness': {
            'sunny': 'Excellent conditions for outdoor workouts and activities',
            'rain': 'Great time to promote indoor fitness programs',
            'hot': 'Important to emphasize hydration and cooling strategies',
            'cold': 'Perfect for promoting warm-up routines and indoor training'
        },
        'retail': {
            'sunny': 'Great shopping weather, people are out and about',
            'rain': 'Perfect time for online shopping promotions',
            'hot': 'Opportunity to promote summer collections and cooling products',
            'cold': 'Ideal for promoting warm clothing and comfort items'
        }
    };
    const businessKey = businessType.toLowerCase();
    const impacts = businessImpacts[businessKey] || businessImpacts['retail'];
    if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';
    if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';
    return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';
}
function getBusinessWeatherOpportunities(businessType, condition, temperature) {
    // Business-specific weather content opportunities
    const opportunities = [];
    if (businessType.toLowerCase().includes('restaurant')) {
        if (condition === 'sunny') opportunities.push('Outdoor dining promotion', 'Fresh seasonal menu highlight');
        if (condition === 'rain') opportunities.push('Cozy indoor atmosphere', 'Comfort food specials');
    }
    if (businessType.toLowerCase().includes('fitness')) {
        if (condition === 'sunny') opportunities.push('Outdoor workout motivation', 'Vitamin D benefits');
        if (temperature > 25) opportunities.push('Hydration importance', 'Summer fitness tips');
    }
    return opportunities;
}
// Fallback functions
function getEventsFallback(location, businessType) {
    return [
        {
            name: `${businessType} networking event in ${location}`,
            description: `Local networking opportunity for ${businessType} professionals`,
            start_date: new Date(Date.now() + 86400000 * 3).toISOString(),
            venue: `${location} Business Center`,
            category: 'Business & Professional',
            is_free: true,
            relevance_score: 8
        }
    ];
}
function getWeatherFallback(location, businessType) {
    return {
        temperature: 22,
        condition: 'Clear',
        description: 'clear sky',
        humidity: 60,
        feels_like: 24,
        location: location,
        content_opportunities: [
            'Pleasant weather content opportunity',
            'Comfortable conditions messaging'
        ],
        business_impact: 'Current weather conditions are favorable for business activities'
    };
}
}}),
"[project]/src/ai/prompts/advanced-ai-prompt.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced AI Content Generation Prompt
 * 
 * This prompt integrates trending topics, competitor analysis, cultural optimization,
 * human-like content generation, and traffic-driving strategies.
 */ __turbopack_context__.s({
    "ADVANCED_AI_PROMPT": (()=>ADVANCED_AI_PROMPT)
});
const ADVANCED_AI_PROMPT = `You are an elite social media strategist, cultural anthropologist, and viral content creator with deep expertise in the {{{businessType}}} industry.

Your mission is to create content that:
🎯 Captures trending conversations and cultural moments
🚀 Drives maximum traffic and business results
🤝 Feels authentically human and culturally sensitive
💡 Differentiates from competitors strategically
📈 Optimizes for platform-specific viral potential
🌤️ Integrates current weather and local events naturally
🎪 Leverages local happenings for timely relevance
🌍 Uses ENGLISH ONLY for all content generation

BUSINESS INTELLIGENCE:
- Industry: {{{businessType}}}
- Location: {{{location}}}
- Brand Voice: {{{writingTone}}}
- Content Themes: {{{contentThemes}}}
- Day: {{{dayOfWeek}}}
- Date: {{{currentDate}}}
{{#if platform}}- Primary Platform: {{{platform}}}{{/if}}
{{#if services}}- Services/Products: {{{services}}}{{/if}}
{{#if targetAudience}}- Target Audience: {{{targetAudience}}}{{/if}}
{{#if keyFeatures}}- Key Features: {{{keyFeatures}}}{{/if}}
{{#if competitiveAdvantages}}- Competitive Edge: {{{competitiveAdvantages}}}{{/if}}

TRENDING TOPICS INTEGRATION:
Research and incorporate current trending topics relevant to:
- {{{businessType}}} industry developments
- {{{location}}} local events and cultural moments
- Platform-specific trending hashtags and conversations
- Seasonal relevance and timely opportunities
- News events that connect to your business value

COMPETITOR DIFFERENTIATION STRATEGY:
Analyze and differentiate from typical competitor content by:
- Avoiding generic industry messaging
- Finding unique angles on common topics
- Highlighting authentic personal/business stories
- Focusing on underserved audience needs
- Creating content gaps competitors miss
- Using authentic local cultural connections

CULTURAL & LOCATION OPTIMIZATION:
For {{{location}}}, incorporate:
- Local cultural nuances and values
- Regional language preferences and expressions
- Community customs and social norms
- Seasonal and cultural calendar awareness
- Local landmarks, events, and references
- Respectful acknowledgment of cultural diversity

INTELLIGENT CONTEXT USAGE:
{{#if contextInstructions}}
CONTEXT INSTRUCTIONS FOR THIS SPECIFIC POST:
{{{contextInstructions}}}

Follow these instructions precisely - they are based on expert analysis of what information is relevant for this specific business type and location.
{{/if}}

WEATHER & EVENTS INTEGRATION:
{{#if selectedWeather}}
- Current weather: {{{selectedWeather.temperature}}}°C, {{{selectedWeather.condition}}}
- Business impact: {{{selectedWeather.business_impact}}}
- Content opportunities: {{{selectedWeather.content_opportunities}}}
{{/if}}

{{#if selectedEvents}}
- Relevant local events:
{{#each selectedEvents}}
  * {{{this.name}}} ({{{this.category}}}) - {{{this.start_date}}}
{{/each}}
{{/if}}

Use this information ONLY if the context instructions indicate it's relevant for this business type.

HUMAN-LIKE AUTHENTICITY MARKERS:
Make content feel genuinely human by:
- Using conversational, imperfect language
- Including personal experiences and observations
- Showing vulnerability and learning moments
- Using specific details over generic statements
- Adding natural speech patterns and contractions
- Including time-specific references (today, this morning)
- Expressing genuine emotions and reactions

TRAFFIC-DRIVING OPTIMIZATION:
Maximize engagement and traffic through:
- Curiosity gaps that demand attention
- Shareability factors that encourage spreading
- Conversion triggers that drive action
- Social proof elements that build trust
- Interactive elements that boost engagement
- Viral hooks that capture trending conversations

ADVANCED COPYWRITING FRAMEWORKS:
1. **AIDA Framework**: Attention → Interest → Desire → Action
2. **PAS Framework**: Problem → Agitation → Solution  
3. **Storytelling Arc**: Setup → Conflict → Resolution → Lesson
4. **Social Proof Stack**: Testimonial → Statistics → Authority → Community
5. **Curiosity Loop**: Hook → Tension → Payoff → Next Hook

PSYCHOLOGICAL TRIGGERS FOR MAXIMUM ENGAGEMENT:
✅ **Urgency & Scarcity**: Time-sensitive opportunities
✅ **Social Proof**: Community validation and testimonials
✅ **FOMO**: Exclusive access and insider information
✅ **Curiosity Gaps**: Intriguing questions and reveals
✅ **Emotional Resonance**: Joy, surprise, inspiration, empathy
✅ **Authority**: Expert insights and industry knowledge
✅ **Reciprocity**: Valuable tips and free insights
✅ **Tribal Identity**: Community belonging and shared values

CONTENT GENERATION REQUIREMENTS:

Generate a comprehensive social media post with these components:

1. **CAPTION (content)**:
   - Start with a trending topic hook or cultural moment
   - Use authentic, conversational human language
   - Include competitor differentiation naturally
   - Apply psychological triggers strategically
   - Incorporate local cultural references appropriately
   - End with traffic-driving call-to-action
   - Length optimized for platform and engagement
   - Feel like it was written by a real person, not AI

2. **CATCHY WORDS (catchyWords)**:
   - Create relevant, business-focused catchy words (max 8 words)
   - MUST be directly related to the specific business services/products
   - Use clear, professional language that matches the business type
   - Focus on the business value proposition or key service
   - Avoid generic phrases like "Banking Made Easy" or random financial terms
   - Examples: For a restaurant: "Fresh Daily Specials", For a gym: "Transform Your Body", For a salon: "Expert Hair Care"
   - Required for ALL posts - this is the main visual text
   - Optimize for visual impact and business relevance

3. **SUBHEADLINE (subheadline)** - OPTIONAL:
   - Add only when it would make the post more effective
   - Maximum 12 words
   - Use your marketing expertise to decide when needed
   - Should complement the catchy words and enhance the message
   - Examples: When explaining a complex service, highlighting a special offer, or providing context
   - Skip if the catchy words and caption are sufficient

4. **CALL TO ACTION (callToAction)** - OPTIONAL:
   - Add only when it would drive better engagement or conversions
   - Maximum 5 words
   - Use your marketing expertise to decide when needed
   - Should be specific and actionable
   - Examples: "Book Now", "Call Today", "Visit Us", "Learn More", "Get Started"
   - Skip if the post is more about awareness or engagement rather than direct action

**CRITICAL WORD LIMIT RULE:**
- The TOTAL combined words from catchyWords + subheadline + callToAction MUST NOT exceed 25 words
- This is a hard limit that cannot be exceeded
- Prioritize: catchyWords (required) > subheadline > callToAction
- If you exceed 25 words, remove or shorten components starting with callToAction, then subheadline
- Count every single word carefully before finalizing your response

5. **HASHTAGS**:
   - Mix trending hashtags with niche industry tags
   - Include location-specific and cultural hashtags
   - Balance high-competition and low-competition tags
   - Ensure cultural sensitivity and appropriateness
   - Optimize quantity for platform (Instagram: 20-30, LinkedIn: 3-5, etc.)

6. **CONTENT VARIANTS (contentVariants)**:
   Generate 2-3 alternative approaches:

   **Variant 1 - Trending Topic Angle**:
   - Hook into current trending conversation
   - Connect trend to business value naturally
   - Use viral content patterns
   - Include shareability factors

   **Variant 2 - Cultural Connection Angle**:
   - Start with local cultural reference
   - Show deep community understanding
   - Use location-specific language naturally
   - Build authentic local connections

   **Variant 3 - Competitor Differentiation Angle**:
   - Address common industry pain points differently
   - Highlight unique business approach
   - Use contrarian but respectful positioning
   - Show authentic expertise and experience

   For each variant, provide:
   - The alternative caption content
   - The strategic approach used
   - Why this variant will drive traffic and engagement
   - Cultural sensitivity considerations

QUALITY STANDARDS:
- Every word serves engagement or conversion purpose
- Content feels authentically human, never robotic
- Cultural references are respectful and accurate
- Trending topics are naturally integrated, not forced
- Competitor differentiation is subtle but clear
- Traffic-driving elements are seamlessly woven in
- Platform optimization is invisible but effective
- Local cultural nuances are appropriately honored

TRAFFIC & CONVERSION OPTIMIZATION:
- Include clear value proposition for audience
- Create multiple engagement touchpoints
- Use psychological triggers ethically
- Provide shareable insights or entertainment
- Include conversion pathway (comment, DM, visit, etc.)
- Optimize for algorithm preferences
- Encourage community building and return visits

LANGUAGE REQUIREMENTS:
🌍 TEXT CLARITY: Generate clear, readable text
- Use proper, well-formed text in appropriate languages for the location
- Local languages are acceptable when relevant to {{{location}}}
- Do NOT use corrupted, gibberish, or unreadable character sequences
- Do NOT use random symbols or malformed text
- Ensure all text is properly formatted and legible
- Avoid character encoding issues or text corruption
- If using local language, ensure it's grammatically correct
- All text must be clear and professional, regardless of language
- Prevent any garbled or nonsensical character combinations

Your response MUST be a valid JSON object that conforms to the output schema.
Focus on creating content that real humans will love, share, and act upon.`;
}}),
"[project]/src/ai/utils/hashtag-strategy.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Hashtag Strategy Utilities
 * 
 * This module provides sophisticated hashtag generation and optimization
 * strategies for different platforms and business types.
 */ __turbopack_context__.s({
    "analyzeHashtags": (()=>analyzeHashtags),
    "generateHashtagStrategy": (()=>generateHashtagStrategy)
});
function generateHashtagStrategy(businessType, location, platform, services, targetAudience) {
    const strategy = {
        trending: generateTrendingHashtags(businessType, platform),
        niche: generateNicheHashtags(businessType, services),
        branded: generateBrandedHashtags(businessType),
        location: generateLocationHashtags(location),
        community: generateCommunityHashtags(businessType, targetAudience)
    };
    // Optimize hashtag counts based on platform
    return optimizeHashtagsForPlatform(strategy, platform);
}
/**
 * Optimizes hashtag counts based on platform best practices
 */ function optimizeHashtagsForPlatform(strategy, platform) {
    const platformLimits = {
        'instagram': {
            trending: 5,
            niche: 8,
            location: 4,
            community: 5,
            branded: 3
        },
        'linkedin': {
            trending: 2,
            niche: 2,
            location: 1,
            community: 1,
            branded: 1
        },
        'twitter': {
            trending: 2,
            niche: 1,
            location: 0,
            community: 0,
            branded: 0
        },
        'facebook': {
            trending: 2,
            niche: 3,
            location: 2,
            community: 2,
            branded: 1
        }
    };
    const limits = platformLimits[platform.toLowerCase()] || {
        trending: 3,
        niche: 5,
        location: 3,
        community: 3,
        branded: 2
    };
    return {
        trending: strategy.trending.slice(0, limits.trending),
        niche: strategy.niche.slice(0, limits.niche),
        location: strategy.location.slice(0, limits.location),
        community: strategy.community.slice(0, limits.community),
        branded: strategy.branded.slice(0, limits.branded)
    };
}
/**
 * Generates trending hashtags based on business type and platform
 */ function generateTrendingHashtags(businessType, platform) {
    const businessTypeMap = {
        'restaurant': [
            '#foodie',
            '#delicious',
            '#foodstagram',
            '#yummy',
            '#tasty'
        ],
        'fitness': [
            '#fitness',
            '#workout',
            '#health',
            '#motivation',
            '#fitlife'
        ],
        'beauty': [
            '#beauty',
            '#skincare',
            '#makeup',
            '#selfcare',
            '#glowup'
        ],
        'retail': [
            '#shopping',
            '#style',
            '#fashion',
            '#deals',
            '#newcollection'
        ],
        'technology': [
            '#tech',
            '#innovation',
            '#digital',
            '#future',
            '#startup'
        ],
        'healthcare': [
            '#health',
            '#wellness',
            '#care',
            '#medical',
            '#healthy'
        ],
        'education': [
            '#education',
            '#learning',
            '#knowledge',
            '#skills',
            '#growth'
        ],
        'real_estate': [
            '#realestate',
            '#home',
            '#property',
            '#investment',
            '#dreamhome'
        ],
        'automotive': [
            '#cars',
            '#automotive',
            '#driving',
            '#vehicle',
            '#auto'
        ],
        'travel': [
            '#travel',
            '#adventure',
            '#explore',
            '#wanderlust',
            '#vacation'
        ]
    };
    const platformTrending = {
        'instagram': [
            '#instagood',
            '#photooftheday',
            '#love',
            '#beautiful',
            '#happy',
            '#instadaily',
            '#follow',
            '#like4like'
        ],
        'linkedin': [
            '#professional',
            '#business',
            '#career',
            '#networking',
            '#success',
            '#leadership',
            '#innovation',
            '#growth'
        ],
        'twitter': [
            '#trending',
            '#viral',
            '#breaking',
            '#news',
            '#update',
            '#thread',
            '#twitterchat',
            '#follow'
        ],
        'facebook': [
            '#community',
            '#local',
            '#family',
            '#friends',
            '#share',
            '#like',
            '#comment',
            '#engage'
        ]
    };
    const businessHashtags = businessTypeMap[businessType.toLowerCase()] || [
        '#business',
        '#service',
        '#quality',
        '#professional',
        '#local'
    ];
    const platformHashtags = platformTrending[platform.toLowerCase()] || [
        '#social',
        '#content',
        '#engagement'
    ];
    return [
        ...businessHashtags.slice(0, 3),
        ...platformHashtags.slice(0, 2)
    ];
}
/**
 * Generates niche-specific hashtags
 */ function generateNicheHashtags(businessType, services) {
    const nicheMap = {
        'restaurant': [
            '#localfood',
            '#chefspecial',
            '#freshingredients',
            '#culinaryart',
            '#foodculture',
            '#diningexperience',
            '#artisanfood',
            '#farmtotable'
        ],
        'fitness': [
            '#personaltrainer',
            '#strengthtraining',
            '#cardio',
            '#nutrition',
            '#bodybuilding',
            '#crossfit',
            '#yoga',
            '#pilates'
        ],
        'beauty': [
            '#beautytips',
            '#skincareroutine',
            '#makeuptutorial',
            '#beautyproducts',
            '#antiaging',
            '#naturalbeauty',
            '#beautysalon',
            '#spa'
        ],
        'retail': [
            '#boutique',
            '#handmade',
            '#unique',
            '#quality',
            '#craftsmanship',
            '#designer',
            '#exclusive',
            '#limited'
        ],
        'technology': [
            '#software',
            '#AI',
            '#machinelearning',
            '#cybersecurity',
            '#cloudcomputing',
            '#blockchain',
            '#IoT',
            '#automation'
        ],
        'healthcare': [
            '#preventivecare',
            '#patientcare',
            '#medicaladvice',
            '#healthtips',
            '#wellness',
            '#mentalhealth',
            '#nutrition',
            '#exercise'
        ],
        'education': [
            '#onlinelearning',
            '#skillbuilding',
            '#certification',
            '#training',
            '#development',
            '#mentorship',
            '#coaching',
            '#academy'
        ],
        'real_estate': [
            '#propertyinvestment',
            '#homebuying',
            '#realtorlife',
            '#propertymanagement',
            '#commercialrealestate',
            '#luxury',
            '#firsttimehomebuyer'
        ],
        'automotive': [
            '#carcare',
            '#automotive',
            '#mechanic',
            '#carrepair',
            '#maintenance',
            '#performance',
            '#luxury',
            '#electric'
        ],
        'travel': [
            '#localtourism',
            '#hiddengems',
            '#culturalexperience',
            '#adventure',
            '#ecotourism',
            '#luxurytravel',
            '#backpacking',
            '#roadtrip'
        ]
    };
    const baseNiche = nicheMap[businessType.toLowerCase()] || [
        '#specialized',
        '#expert',
        '#professional',
        '#quality',
        '#service',
        '#local',
        '#trusted',
        '#experienced'
    ];
    // Add service-specific hashtags if services are provided
    if (services) {
        const serviceWords = services.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
        const serviceHashtags = serviceWords.slice(0, 3).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
        return [
            ...baseNiche.slice(0, 5),
            ...serviceHashtags
        ];
    }
    return baseNiche.slice(0, 8);
}
/**
 * Generates branded hashtags
 */ function generateBrandedHashtags(businessType) {
    const brandedSuffixes = [
        'experience',
        'quality',
        'service',
        'difference',
        'way',
        'style',
        'approach'
    ];
    const businessPrefix = businessType.toLowerCase().replace(/[^a-z]/g, '');
    return [
        `#${businessPrefix}${brandedSuffixes[0]}`,
        `#${businessPrefix}${brandedSuffixes[1]}`,
        `#choose${businessPrefix}`
    ];
}
/**
 * Generates location-based hashtags
 */ function generateLocationHashtags(location) {
    const locationParts = location.split(',').map((part)=>part.trim());
    const hashtags = [];
    locationParts.forEach((part)=>{
        const cleanLocation = part.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '').toLowerCase();
        if (cleanLocation.length > 2) {
            hashtags.push(`#${cleanLocation}`);
            hashtags.push(`#local${cleanLocation}`);
            hashtags.push(`#${cleanLocation}business`);
        }
    });
    // Add generic location hashtags
    hashtags.push('#local', '#community', '#neighborhood');
    return hashtags.slice(0, 5);
}
/**
 * Generates community and engagement hashtags
 */ function generateCommunityHashtags(businessType, targetAudience) {
    const communityMap = {
        'restaurant': [
            '#foodlovers',
            '#foodies',
            '#localfoodie',
            '#foodcommunity'
        ],
        'fitness': [
            '#fitnesscommunity',
            '#healthylifestyle',
            '#fitnessjourney',
            '#workoutbuddy'
        ],
        'beauty': [
            '#beautycommunity',
            '#selfcare',
            '#beautylovers',
            '#skincarecommunity'
        ],
        'retail': [
            '#shoplocal',
            '#supportlocal',
            '#shoppingcommunity',
            '#stylelovers'
        ],
        'technology': [
            '#techcommunity',
            '#developers',
            '#innovation',
            '#digitaltransformation'
        ],
        'healthcare': [
            '#healthcommunity',
            '#wellness',
            '#patientcare',
            '#healthylife'
        ],
        'education': [
            '#learningcommunity',
            '#students',
            '#educators',
            '#knowledge'
        ],
        'real_estate': [
            '#homeowners',
            '#investors',
            '#realestatecommunity',
            '#propertylovers'
        ],
        'automotive': [
            '#carenthusiasts',
            '#automotive',
            '#carlovers',
            '#drivingcommunity'
        ],
        'travel': [
            '#travelers',
            '#explorers',
            '#adventurers',
            '#wanderers'
        ]
    };
    const baseCommunity = communityMap[businessType.toLowerCase()] || [
        '#community',
        '#customers',
        '#supporters',
        '#family'
    ];
    // Add audience-specific hashtags if provided
    if (targetAudience) {
        const audienceWords = targetAudience.toLowerCase().split(/[,\s]+/).filter((word)=>word.length > 3);
        const audienceHashtags = audienceWords.slice(0, 2).map((word)=>`#${word.replace(/[^a-z0-9]/g, '')}`);
        return [
            ...baseCommunity.slice(0, 3),
            ...audienceHashtags
        ];
    }
    return baseCommunity.slice(0, 4);
}
function analyzeHashtags(hashtags) {
    return hashtags.map((hashtag)=>({
            hashtag,
            category: categorizeHashtag(hashtag),
            competitionLevel: estimateCompetition(hashtag),
            estimatedReach: estimateReach(hashtag),
            relevanceScore: Math.floor(Math.random() * 3) + 8 // Simplified scoring
        }));
}
function categorizeHashtag(hashtag) {
    const trending = [
        '#instagood',
        '#photooftheday',
        '#love',
        '#beautiful',
        '#happy',
        '#fitness',
        '#food'
    ];
    const location = hashtag.includes('local') || hashtag.includes('community');
    const branded = hashtag.includes('experience') || hashtag.includes('quality');
    if (trending.some((t)=>hashtag.includes(t.slice(1)))) return 'trending';
    if (location) return 'location';
    if (branded) return 'branded';
    return 'niche';
}
function estimateCompetition(hashtag) {
    const highCompetition = [
        '#love',
        '#instagood',
        '#photooftheday',
        '#beautiful',
        '#happy'
    ];
    const lowCompetition = hashtag.length > 15 || hashtag.includes('local');
    if (highCompetition.includes(hashtag)) return 'high';
    if (lowCompetition) return 'low';
    return 'medium';
}
function estimateReach(hashtag) {
    const highReach = [
        '#love',
        '#instagood',
        '#photooftheday',
        '#beautiful',
        '#happy'
    ];
    const lowReach = hashtag.length > 15 || hashtag.includes('local');
    if (highReach.includes(hashtag)) return 'high';
    if (lowReach) return 'low';
    return 'medium';
}
}}),
"[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Real-Time Trends Integration System
 * 
 * This module integrates multiple real-time trending topic sources
 * and provides a unified interface for getting current trends.
 */ __turbopack_context__.s({
    "TRENDING_CONFIG": (()=>TRENDING_CONFIG),
    "fetchCurrentNews": (()=>fetchCurrentNews),
    "fetchGoogleTrends": (()=>fetchGoogleTrends),
    "fetchLocalContext": (()=>fetchLocalContext),
    "fetchRedditTrends": (()=>fetchRedditTrends),
    "fetchTwitterTrends": (()=>fetchTwitterTrends)
});
const TRENDING_CONFIG = {
    sources: {
        googleTrends: {
            name: 'Google Trends RSS',
            enabled: process.env.GOOGLE_TRENDS_RSS_ENABLED === 'true',
            apiKey: undefined,
            baseUrl: 'https://trends.google.com/trends/trendingsearches/daily/rss',
            rateLimitPerHour: 1000 // RSS has higher limits
        },
        twitterApi: {
            name: 'Twitter API v1.1',
            enabled: false,
            apiKey: process.env.TWITTER_BEARER_TOKEN,
            baseUrl: 'https://api.twitter.com/1.1',
            rateLimitPerHour: 300
        },
        newsApi: {
            name: 'News API',
            enabled: false,
            apiKey: process.env.NEWS_API_KEY,
            baseUrl: 'https://newsapi.org/v2',
            rateLimitPerHour: 1000
        },
        redditApi: {
            name: 'Reddit RSS',
            enabled: process.env.REDDIT_RSS_ENABLED === 'true',
            apiKey: undefined,
            baseUrl: 'https://www.reddit.com',
            rateLimitPerHour: 1000 // RSS has higher limits
        },
        youtubeApi: {
            name: 'YouTube Data API',
            enabled: !!process.env.YOUTUBE_API_KEY,
            apiKey: process.env.YOUTUBE_API_KEY,
            baseUrl: 'https://www.googleapis.com/youtube/v3',
            rateLimitPerHour: 10000
        },
        eventbriteApi: {
            name: 'Eventbrite API',
            enabled: !!process.env.EVENTBRITE_API_KEY,
            apiKey: process.env.EVENTBRITE_API_KEY,
            baseUrl: 'https://www.eventbriteapi.com/v3',
            rateLimitPerHour: 1000
        },
        openWeatherApi: {
            name: 'OpenWeather API',
            enabled: !!process.env.OPENWEATHER_API_KEY,
            apiKey: process.env.OPENWEATHER_API_KEY,
            baseUrl: 'https://api.openweathermap.org/data/2.5',
            rateLimitPerHour: 1000
        }
    },
    fallbackToStatic: true,
    cacheTimeMinutes: 30
};
async function fetchGoogleTrends(location, category) {
    if (!TRENDING_CONFIG.sources.googleTrends.enabled) {
        console.log('Google Trends RSS not enabled, using fallback');
        return getGoogleTrendsFallback(location, category);
    }
    try {
        // Import RSS integration
        const { fetchGoogleTrendsRSS } = await __turbopack_context__.r("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use RSS feeds for Google Trends
        const geoCode = getGoogleTrendsGeoCode(location);
        const trends = await fetchGoogleTrendsRSS(geoCode, category);
        // Convert to expected format
        return trends.map((trend)=>({
                topic: trend.topic,
                relevanceScore: trend.relevanceScore,
                category: trend.category,
                timeframe: trend.timeframe,
                engagement_potential: trend.engagement_potential,
                source: 'google_trends_rss'
            }));
    } catch (error) {
        console.error('Error fetching Google Trends RSS:', error);
        return getGoogleTrendsFallback(location, category);
    }
}
async function fetchTwitterTrends(location, businessType) {
    if (!TRENDING_CONFIG.sources.twitterApi.enabled) {
        console.log('Twitter API not configured, using fallback');
        return getTwitterTrendsFallback(location, businessType);
    }
    try {
        const woeid = getTwitterWOEID(location);
        // Use Twitter API v2 trending topics endpoint
        const response = await fetch(`${TRENDING_CONFIG.sources.twitterApi.baseUrl}/trends/place.json?id=${woeid}`, {
            headers: {
                'Authorization': `Bearer ${TRENDING_CONFIG.sources.twitterApi.apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'TrendingTopicsBot/2.0'
            }
        });
        if (!response.ok) {
            console.log(`Twitter API response: ${response.status} ${response.statusText}`);
            throw new Error(`Twitter API error: ${response.status}`);
        }
        const data = await response.json();
        console.log(`✅ Twitter API returned ${data.length || 0} trend locations`);
        // Process Twitter trends data
        return processTwitterTrendsData(data, businessType);
    } catch (error) {
        console.error('Error fetching Twitter trends:', error);
        return getTwitterTrendsFallback(location, businessType);
    }
}
async function fetchCurrentNews(location, businessType, category) {
    if (!TRENDING_CONFIG.sources.newsApi.enabled) {
        console.log('News API not configured, using fallback');
        return getNewsFallback(location, businessType, category);
    }
    try {
        const params = new URLSearchParams({
            country: getNewsApiCountryCode(location),
            category: category || 'business',
            pageSize: '10',
            apiKey: TRENDING_CONFIG.sources.newsApi.apiKey
        });
        console.log(`🔍 Fetching news from News API for ${location}...`);
        const response = await fetch(`${TRENDING_CONFIG.sources.newsApi.baseUrl}/top-headlines?${params}`);
        if (!response.ok) {
            console.log(`News API response: ${response.status} ${response.statusText}`);
            const errorText = await response.text();
            console.log('News API error details:', errorText);
            throw new Error(`News API error: ${response.status}`);
        }
        const data = await response.json();
        console.log(`✅ News API returned ${data.articles?.length || 0} articles`);
        // Process news data
        return processNewsData(data, businessType);
    } catch (error) {
        console.error('Error fetching news:', error);
        return getNewsFallback(location, businessType, category);
    }
}
async function fetchRedditTrends(businessType, platform) {
    if (!TRENDING_CONFIG.sources.redditApi.enabled) {
        console.log('Reddit RSS not enabled, using fallback');
        return getRedditTrendsFallback(businessType, platform);
    }
    try {
        // Import RSS integration
        const { fetchRedditRSS } = await __turbopack_context__.r("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        // Use RSS feeds for Reddit trends
        const trends = await fetchRedditRSS(businessType);
        // Convert to expected format
        return trends.map((trend)=>({
                topic: trend.topic,
                relevanceScore: trend.relevanceScore,
                category: trend.category,
                timeframe: trend.timeframe,
                engagement_potential: trend.engagement_potential,
                source: 'reddit_rss'
            }));
    } catch (error) {
        console.error('Error fetching Reddit RSS:', error);
        return getRedditTrendsFallback(businessType, platform);
    }
}
/**
 * Helper functions for processing API data
 */ function processGoogleTrendsData(data, location, category) {
    // Process Google Trends API response
    return [
        {
            topic: `Trending in ${location}`,
            source: 'google_trends',
            relevanceScore: 9,
            category: category || 'general',
            timeframe: 'now',
            engagement_potential: 'high'
        }
    ];
}
function processTwitterTrendsData(data, businessType) {
    // Process Twitter API response
    if (data && data[0] && data[0].trends) {
        return data[0].trends.slice(0, 10).map((trend)=>({
                topic: trend.name,
                source: 'twitter',
                relevanceScore: trend.tweet_volume ? Math.min(10, Math.log10(trend.tweet_volume)) : 5,
                category: 'social',
                timeframe: 'now',
                engagement_potential: trend.tweet_volume > 10000 ? 'high' : 'medium'
            }));
    }
    return [];
}
function processNewsData(data, businessType) {
    // Process News API response
    if (data && data.articles) {
        return data.articles.slice(0, 8).map((article)=>({
                topic: article.title,
                source: 'news',
                relevanceScore: 8,
                category: 'news',
                timeframe: 'today',
                engagement_potential: 'high',
                business_angle: `How this relates to ${businessType} industry`
            }));
    }
    return [];
}
function processRedditData(data, subreddit) {
    // Process Reddit API response
    if (data && data.data && data.data.children) {
        return data.data.children.slice(0, 5).map((post)=>({
                topic: post.data.title,
                source: 'reddit',
                relevanceScore: Math.min(10, post.data.score / 100),
                category: 'community',
                timeframe: 'today',
                engagement_potential: post.data.score > 1000 ? 'high' : 'medium',
                subreddit: subreddit
            }));
    }
    return [];
}
/**
 * Helper functions for API parameters
 */ function getGoogleTrendsGeoCode(location) {
    const geoMap = {
        'kenya': 'KE',
        'united states': 'US',
        'nairobi': 'KE',
        'new york': 'US-NY',
        'london': 'GB-ENG'
    };
    return geoMap[location.toLowerCase()] || 'US';
}
function getTwitterWOEID(location) {
    const woeidMap = {
        'kenya': '23424863',
        'united states': '23424977',
        'nairobi': '1528488',
        'new york': '2459115',
        'london': '44418'
    };
    return woeidMap[location.toLowerCase()] || '1'; // Worldwide
}
function getNewsApiCountryCode(location) {
    const countryMap = {
        'kenya': 'ke',
        'united states': 'us',
        'nairobi': 'ke',
        'new york': 'us',
        'london': 'gb'
    };
    return countryMap[location.toLowerCase()] || 'us';
}
function getRelevantSubreddits(businessType) {
    const subredditMap = {
        'financial technology software': [
            'fintech',
            'personalfinance',
            'investing',
            'entrepreneur'
        ],
        'restaurant': [
            'food',
            'recipes',
            'restaurantowners',
            'smallbusiness'
        ],
        'fitness': [
            'fitness',
            'bodybuilding',
            'nutrition',
            'personaltrainer'
        ],
        'technology': [
            'technology',
            'programming',
            'startups',
            'artificial'
        ]
    };
    return subredditMap[businessType.toLowerCase()] || [
        'business',
        'entrepreneur'
    ];
}
/**
 * Fallback functions when APIs are not available
 */ function getGoogleTrendsFallback(location, category) {
    return [
        {
            topic: `Local business trends in ${location}`,
            source: 'fallback',
            relevanceScore: 7,
            category: category || 'business',
            timeframe: 'week',
            engagement_potential: 'medium'
        }
    ];
}
function getTwitterTrendsFallback(location, businessType) {
    return [
        {
            topic: '#MondayMotivation',
            source: 'fallback',
            relevanceScore: 6,
            category: 'social',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
function getNewsFallback(location, businessType, category) {
    return [
        {
            topic: `${businessType} industry updates`,
            source: 'fallback',
            relevanceScore: 6,
            category: 'news',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
function getRedditTrendsFallback(businessType, platform) {
    return [
        {
            topic: `${businessType} community discussions`,
            source: 'fallback',
            relevanceScore: 5,
            category: 'community',
            timeframe: 'today',
            engagement_potential: 'medium'
        }
    ];
}
async function fetchLocalContext(location, businessType) {
    const context = {};
    try {
        // Fetch weather context
        if (TRENDING_CONFIG.sources.openWeatherApi.enabled) {
            console.log(`🌤️ Fetching weather context for ${location}...`);
            const params = new URLSearchParams({
                q: location,
                appid: TRENDING_CONFIG.sources.openWeatherApi.apiKey,
                units: 'metric'
            });
            const response = await fetch(`${TRENDING_CONFIG.sources.openWeatherApi.baseUrl}/weather?${params}`);
            if (response.ok) {
                const weatherData = await response.json();
                context.weather = {
                    temperature: Math.round(weatherData.main.temp),
                    condition: weatherData.weather[0].main,
                    business_impact: generateBusinessWeatherImpact(weatherData.weather[0].main, weatherData.main.temp, businessType),
                    content_opportunities: generateWeatherContentOpportunities(weatherData.weather[0].main, weatherData.main.temp, businessType)
                };
                console.log(`✅ Weather: ${context.weather.temperature}°C, ${context.weather.condition}`);
            }
        }
        // Fetch events context
        if (TRENDING_CONFIG.sources.eventbriteApi.enabled) {
            console.log(`🎪 Fetching events context for ${location}...`);
            const params = new URLSearchParams({
                'location.address': location,
                'location.within': '25km',
                'start_date.range_start': new Date().toISOString(),
                'start_date.range_end': new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                'sort_by': 'relevance',
                'page_size': '10'
            });
            const response = await fetch(`${TRENDING_CONFIG.sources.eventbriteApi.baseUrl}/events/search/?${params}`, {
                headers: {
                    'Authorization': `Bearer ${TRENDING_CONFIG.sources.eventbriteApi.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.ok) {
                const eventsData = await response.json();
                context.events = (eventsData.events || []).slice(0, 5).map((event)=>({
                        name: event.name?.text || 'Event',
                        category: event.category?.name || 'General',
                        relevance_score: calculateEventRelevance(event, businessType),
                        start_date: event.start?.local || event.start?.utc
                    }));
                console.log(`✅ Found ${context.events.length} relevant events`);
            }
        }
    } catch (error) {
        console.error('Error fetching local context:', error);
    }
    return context;
}
// Helper functions for weather and events
function generateBusinessWeatherImpact(condition, temperature, businessType) {
    const businessImpacts = {
        'restaurant': {
            'sunny': 'Perfect weather for outdoor dining and patio service',
            'rain': 'Great opportunity to promote cozy indoor dining experience',
            'hot': 'Ideal time to highlight refreshing drinks and cool dishes',
            'cold': 'Perfect weather for warm comfort food and hot beverages'
        },
        'fitness': {
            'sunny': 'Excellent conditions for outdoor workouts and activities',
            'rain': 'Great time to promote indoor fitness programs',
            'hot': 'Important to emphasize hydration and cooling strategies',
            'cold': 'Perfect for promoting warm-up routines and indoor training'
        },
        'financial technology software': {
            'sunny': 'Great weather for outdoor meetings and client visits',
            'rain': 'Perfect time for indoor productivity and digital solutions',
            'hot': 'Ideal for promoting mobile solutions and remote services',
            'cold': 'Good time for cozy indoor planning and financial reviews'
        }
    };
    const businessKey = businessType.toLowerCase();
    const impacts = businessImpacts[businessKey] || businessImpacts['restaurant'];
    if (temperature > 25) return impacts['hot'] || 'Weather creates opportunities for seasonal promotions';
    if (temperature < 10) return impacts['cold'] || 'Weather creates opportunities for comfort-focused messaging';
    return impacts[condition.toLowerCase()] || impacts['sunny'] || 'Current weather conditions are favorable for business';
}
function generateWeatherContentOpportunities(condition, temperature, businessType) {
    const opportunities = [];
    // Temperature-based opportunities
    if (temperature > 25) {
        opportunities.push('Hot weather content angle', 'Summer promotion opportunity', 'Cooling solutions messaging');
    } else if (temperature < 10) {
        opportunities.push('Cold weather content angle', 'Winter comfort messaging', 'Warm-up solutions');
    }
    // Condition-based opportunities
    switch(condition.toLowerCase()){
        case 'rain':
            opportunities.push('Rainy day indoor activities', 'Weather protection messaging', 'Cozy atmosphere content');
            break;
        case 'sunny':
        case 'clear':
            opportunities.push('Beautiful weather celebration', 'Outdoor activity promotion', 'Sunshine positivity');
            break;
        case 'clouds':
            opportunities.push('Perfect weather for activities', 'Comfortable conditions messaging');
            break;
    }
    return opportunities;
}
function calculateEventRelevance(event, businessType) {
    let score = 5; // Base score
    const eventName = (event.name?.text || '').toLowerCase();
    const eventCategory = (event.category?.name || '').toLowerCase();
    // Business type relevance
    const businessKeywords = getBusinessKeywords(businessType);
    for (const keyword of businessKeywords){
        if (eventName.includes(keyword) || eventCategory.includes(keyword)) {
            score += 2;
        }
    }
    // Event category bonus
    if (eventCategory.includes('business') || eventCategory.includes('networking')) {
        score += 1;
    }
    return Math.min(10, score);
}
function getBusinessKeywords(businessType) {
    const keywordMap = {
        'financial technology software': [
            'fintech',
            'finance',
            'banking',
            'payment',
            'blockchain',
            'startup',
            'tech'
        ],
        'restaurant': [
            'food',
            'culinary',
            'cooking',
            'dining',
            'chef',
            'restaurant'
        ],
        'fitness': [
            'fitness',
            'health',
            'wellness',
            'gym',
            'workout',
            'nutrition'
        ],
        'technology': [
            'tech',
            'software',
            'programming',
            'ai',
            'digital',
            'innovation'
        ]
    };
    return keywordMap[businessType.toLowerCase()] || [
        'business',
        'networking',
        'professional'
    ];
}
}}),
"[project]/src/ai/utils/trending-topics.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Trending Topics and Market Intelligence System
 *
 * This module provides real-time trending topics, competitor analysis,
 * and market intelligence for content optimization.
 */ __turbopack_context__.s({
    "generateCompetitorInsights": (()=>generateCompetitorInsights),
    "generateCulturalContext": (()=>generateCulturalContext),
    "generateMarketIntelligence": (()=>generateMarketIntelligence),
    "generateRealTimeTrendingTopics": (()=>generateRealTimeTrendingTopics),
    "generateStaticTrendingTopics": (()=>generateStaticTrendingTopics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)");
;
async function generateRealTimeTrendingTopics(businessType, location, platform = 'general') {
    try {
        console.log(`🔍 Fetching real-time trends for ${businessType} in ${location}...`);
        // Fetch from working real-time sources (temporarily disable failing APIs)
        const [googleTrends, redditTrends] = await Promise.allSettled([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchGoogleTrends"])(location, businessType),
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchRedditTrends"])(businessType, platform)
        ]);
        // Temporarily disable failing APIs until we fix them
        const twitterTrends = {
            status: 'rejected',
            reason: 'Temporarily disabled'
        };
        const currentNews = {
            status: 'rejected',
            reason: 'Temporarily disabled'
        };
        const allTrends = [];
        // Process Google Trends
        if (googleTrends.status === 'fulfilled') {
            allTrends.push(...googleTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        }
        // Process Twitter Trends
        if (twitterTrends.status === 'fulfilled') {
            allTrends.push(...twitterTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        }
        // Process News
        if (currentNews.status === 'fulfilled') {
            allTrends.push(...currentNews.value.map((news)=>({
                    topic: news.topic,
                    relevanceScore: news.relevanceScore,
                    platform: platform,
                    category: news.category,
                    timeframe: news.timeframe,
                    engagement_potential: news.engagement_potential
                })));
        }
        // Process Reddit Trends
        if (redditTrends.status === 'fulfilled') {
            allTrends.push(...redditTrends.value.map((trend)=>({
                    topic: trend.topic,
                    relevanceScore: trend.relevanceScore,
                    platform: platform,
                    category: trend.category,
                    timeframe: trend.timeframe,
                    engagement_potential: trend.engagement_potential
                })));
        }
        // If we have real-time trends, use them
        if (allTrends.length > 0) {
            console.log(`✅ Found ${allTrends.length} real-time trends`);
            return allTrends.sort((a, b)=>b.relevanceScore - a.relevanceScore).slice(0, 10);
        }
        // Fallback to static trends
        console.log('⚠️ No real-time trends available, using static fallback');
        return generateStaticTrendingTopics(businessType, location, platform);
    } catch (error) {
        console.error('Error fetching real-time trends:', error);
        return generateStaticTrendingTopics(businessType, location, platform);
    }
}
function generateStaticTrendingTopics(businessType, location, platform = 'general') {
    const businessTrends = {
        'restaurant': [
            {
                topic: 'Sustainable dining trends',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Local food festivals',
                relevanceScore: 8,
                platform: 'facebook',
                category: 'local',
                timeframe: 'week',
                engagement_potential: 'high'
            },
            {
                topic: 'Plant-based menu innovations',
                relevanceScore: 7,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'month',
                engagement_potential: 'medium'
            }
        ],
        'fitness': [
            {
                topic: 'New Year fitness resolutions',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Mental health and exercise',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'lifestyle',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Home workout equipment trends',
                relevanceScore: 7,
                platform: 'facebook',
                category: 'lifestyle',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'technology': [
            {
                topic: 'AI in business automation',
                relevanceScore: 10,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Cybersecurity awareness',
                relevanceScore: 9,
                platform: 'twitter',
                category: 'technology',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Remote work productivity tools',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'beauty': [
            {
                topic: 'Clean beauty movement',
                relevanceScore: 9,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Skincare for different seasons',
                relevanceScore: 8,
                platform: 'instagram',
                category: 'lifestyle',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Sustainable beauty packaging',
                relevanceScore: 7,
                platform: 'facebook',
                category: 'lifestyle',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ],
        'financial technology software': [
            {
                topic: 'Digital banking adoption in Africa',
                relevanceScore: 10,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'now',
                engagement_potential: 'high'
            },
            {
                topic: 'Financial inclusion initiatives',
                relevanceScore: 9,
                platform: 'twitter',
                category: 'business',
                timeframe: 'today',
                engagement_potential: 'high'
            },
            {
                topic: 'Mobile payment security',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'technology',
                timeframe: 'week',
                engagement_potential: 'medium'
            }
        ]
    };
    // Get base trends for business type
    const baseTrends = businessTrends[businessType.toLowerCase()] || businessTrends['technology'];
    // Add location-specific trends
    const locationTrends = generateLocationTrends(location);
    // Combine and filter by platform
    const allTrends = [
        ...baseTrends,
        ...locationTrends
    ];
    return allTrends.filter((trend)=>trend.platform === platform || trend.platform === 'general').sort((a, b)=>b.relevanceScore - a.relevanceScore).slice(0, 5);
}
/**
 * Generates location-specific trending topics
 */ function generateLocationTrends(location) {
    const locationMap = {
        'nairobi': [
            {
                topic: 'Nairobi tech hub growth',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'high'
            },
            {
                topic: 'Kenyan startup ecosystem',
                relevanceScore: 7,
                platform: 'twitter',
                category: 'business',
                timeframe: 'today',
                engagement_potential: 'medium'
            }
        ],
        'new york': [
            {
                topic: 'NYC small business support',
                relevanceScore: 8,
                platform: 'facebook',
                category: 'local',
                timeframe: 'week',
                engagement_potential: 'high'
            }
        ],
        'london': [
            {
                topic: 'London fintech innovation',
                relevanceScore: 8,
                platform: 'linkedin',
                category: 'business',
                timeframe: 'week',
                engagement_potential: 'high'
            }
        ]
    };
    const locationKey = location.toLowerCase().split(',')[0].trim();
    return locationMap[locationKey] || [];
}
function generateCompetitorInsights(businessType, location, services) {
    const competitorStrategies = {
        'financial technology software': [
            {
                competitor_name: 'Traditional Banks',
                content_gap: 'Lack of educational content about digital banking benefits',
                differentiation_opportunity: 'Focus on simplicity and accessibility for everyday users',
                successful_strategy: 'Trust-building through security messaging',
                avoid_strategy: 'Overly technical jargon that confuses users'
            },
            {
                competitor_name: 'Other Fintech Apps',
                content_gap: 'Limited focus on local market needs and culture',
                differentiation_opportunity: 'Emphasize local partnerships and community impact',
                successful_strategy: 'User testimonials and success stories',
                avoid_strategy: 'Generic global messaging without local relevance'
            }
        ],
        'restaurant': [
            {
                competitor_name: 'Chain Restaurants',
                content_gap: 'Lack of personal connection and local community focus',
                differentiation_opportunity: 'Highlight local sourcing, chef personality, and community involvement',
                successful_strategy: 'Behind-the-scenes content and food preparation videos',
                avoid_strategy: 'Generic food photos without story or context'
            }
        ],
        'fitness': [
            {
                competitor_name: 'Large Gym Chains',
                content_gap: 'Impersonal approach and lack of individual attention',
                differentiation_opportunity: 'Focus on personal transformation stories and community support',
                successful_strategy: 'Client success stories and progress tracking',
                avoid_strategy: 'Intimidating fitness content that discourages beginners'
            }
        ]
    };
    return competitorStrategies[businessType.toLowerCase()] || [
        {
            competitor_name: 'Industry Leaders',
            content_gap: 'Generic messaging without personal touch',
            differentiation_opportunity: 'Focus on authentic storytelling and customer relationships',
            successful_strategy: 'Educational content that provides real value',
            avoid_strategy: 'Overly promotional content without substance'
        }
    ];
}
function generateCulturalContext(location) {
    const culturalMap = {
        'nairobi, kenya': {
            location: 'Nairobi, Kenya',
            cultural_nuances: [
                'Ubuntu philosophy - community and interconnectedness',
                'Respect for elders and traditional values',
                'Entrepreneurial spirit and innovation mindset',
                'Multilingual communication (English, Swahili, local languages)'
            ],
            local_customs: [
                'Harambee - community cooperation and fundraising',
                'Greeting customs and respect protocols',
                'Religious diversity and tolerance',
                'Family-centered decision making'
            ],
            language_preferences: [
                'Mix of English and Swahili phrases',
                'Respectful and formal tone in business contexts',
                'Storytelling and proverb usage',
                'Community-focused language'
            ],
            seasonal_relevance: [
                'Rainy seasons (March-May, October-December)',
                'School calendar considerations',
                'Agricultural seasons and harvest times',
                'Holiday seasons and celebrations'
            ],
            local_events: [
                'Nairobi Innovation Week',
                'Kenya Music Festival',
                'Nairobi Restaurant Week',
                'Local cultural festivals'
            ]
        }
    };
    const locationKey = location.toLowerCase();
    return culturalMap[locationKey] || {
        location: location,
        cultural_nuances: [
            'Local community values',
            'Regional business customs'
        ],
        local_customs: [
            'Local traditions',
            'Community practices'
        ],
        language_preferences: [
            'Local language nuances',
            'Regional communication styles'
        ],
        seasonal_relevance: [
            'Local seasons',
            'Regional events'
        ],
        local_events: [
            'Local festivals',
            'Community gatherings'
        ]
    };
}
function generateMarketIntelligence(businessType, location, platform, services) {
    return {
        trending_topics: generateStaticTrendingTopics(businessType, location, platform),
        competitor_insights: generateCompetitorInsights(businessType, location, services),
        cultural_context: generateCulturalContext(location),
        viral_content_patterns: [
            'Behind-the-scenes authentic moments',
            'User-generated content and testimonials',
            'Educational content that solves problems',
            'Emotional storytelling with clear outcomes',
            'Interactive content that encourages participation'
        ],
        engagement_triggers: [
            'Ask questions that require personal responses',
            'Share relatable struggles and solutions',
            'Use local references and cultural touchpoints',
            'Create content that people want to share with friends',
            'Provide exclusive insights or early access'
        ]
    };
}
}}),
"[project]/src/ai/utils/intelligent-context-selector.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Intelligent Context Selector
 * 
 * This module acts like a local expert who knows what information
 * is relevant for each business type, location, and content context.
 * It intelligently selects which data to use and which to ignore.
 */ __turbopack_context__.s({
    "filterContextData": (()=>filterContextData),
    "selectRelevantContext": (()=>selectRelevantContext)
});
function selectRelevantContext(businessType, location, platform, contentThemes, dayOfWeek) {
    const businessKey = businessType.toLowerCase();
    const locationKey = location.toLowerCase();
    const isWeekend = dayOfWeek === 'Saturday' || dayOfWeek === 'Sunday';
    return {
        weather: analyzeWeatherRelevance(businessKey, locationKey, platform, isWeekend),
        events: analyzeEventsRelevance(businessKey, locationKey, platform, isWeekend),
        trends: analyzeTrendsRelevance(businessKey, locationKey, platform),
        cultural: analyzeCulturalRelevance(businessKey, locationKey, platform)
    };
}
/**
 * Determines if weather information is relevant for this business/location
 */ function analyzeWeatherRelevance(businessType, location, platform, isWeekend) {
    // High weather relevance businesses
    const weatherSensitiveBusinesses = [
        'restaurant',
        'cafe',
        'food',
        'dining',
        'fitness',
        'gym',
        'sports',
        'outdoor',
        'retail',
        'shopping',
        'fashion',
        'tourism',
        'travel',
        'hotel',
        'construction',
        'agriculture',
        'delivery',
        'transportation'
    ];
    // Medium weather relevance
    const moderateWeatherBusinesses = [
        'beauty',
        'spa',
        'wellness',
        'entertainment',
        'events',
        'real estate',
        'automotive'
    ];
    // Low/No weather relevance
    const weatherIndependentBusinesses = [
        'financial technology software',
        'fintech',
        'banking',
        'software',
        'technology',
        'saas',
        'consulting',
        'legal',
        'accounting',
        'insurance',
        'healthcare',
        'education',
        'digital marketing',
        'design'
    ];
    // Check business type relevance
    const isHighRelevance = weatherSensitiveBusinesses.some((type)=>businessType.includes(type));
    const isMediumRelevance = moderateWeatherBusinesses.some((type)=>businessType.includes(type));
    const isLowRelevance = weatherIndependentBusinesses.some((type)=>businessType.includes(type));
    // Location-based adjustments
    const isExtremeWeatherLocation = location.includes('nairobi') || location.includes('kenya') || location.includes('tropical');
    if (isHighRelevance) {
        return {
            useWeather: true,
            relevanceReason: `${businessType} customers are highly influenced by weather conditions`,
            priority: 'high'
        };
    }
    if (isMediumRelevance) {
        return {
            useWeather: true,
            relevanceReason: `Weather can impact ${businessType} customer behavior`,
            priority: 'medium'
        };
    }
    if (isLowRelevance) {
        return {
            useWeather: false,
            relevanceReason: `${businessType} operates independently of weather conditions`,
            priority: 'ignore'
        };
    }
    // Default case
    return {
        useWeather: isExtremeWeatherLocation,
        relevanceReason: isExtremeWeatherLocation ? 'Local weather is culturally significant' : 'Weather has minimal business impact',
        priority: isExtremeWeatherLocation ? 'low' : 'ignore'
    };
}
/**
 * Determines if local events are relevant for this business/location
 */ function analyzeEventsRelevance(businessType, location, platform, isWeekend) {
    // Always relevant for networking/community businesses
    const networkingBusinesses = [
        'consulting',
        'marketing',
        'business services',
        'financial technology software',
        'fintech',
        'real estate',
        'insurance',
        'legal'
    ];
    // Event-dependent businesses
    const eventDependentBusinesses = [
        'restaurant',
        'entertainment',
        'retail',
        'fitness',
        'beauty',
        'tourism'
    ];
    // B2B vs B2C consideration
    const isB2B = networkingBusinesses.some((type)=>businessType.includes(type)) || businessType.includes('software') || businessType.includes('technology');
    const isB2C = eventDependentBusinesses.some((type)=>businessType.includes(type));
    // Relevant event types based on business
    let eventTypes = [];
    if (isB2B) {
        eventTypes = [
            'business',
            'networking',
            'conference',
            'workshop',
            'professional'
        ];
    }
    if (isB2C) {
        eventTypes = [
            'community',
            'festival',
            'entertainment',
            'cultural',
            'local'
        ];
    }
    // Location-based event culture
    const isEventCentricLocation = location.includes('nairobi') || location.includes('new york') || location.includes('london');
    if (isB2B && isEventCentricLocation) {
        return {
            useEvents: true,
            relevanceReason: `${businessType} benefits from professional networking events`,
            priority: 'high',
            eventTypes
        };
    }
    if (isB2C) {
        return {
            useEvents: true,
            relevanceReason: `Local events drive foot traffic for ${businessType}`,
            priority: 'medium',
            eventTypes
        };
    }
    return {
        useEvents: isEventCentricLocation,
        relevanceReason: isEventCentricLocation ? 'Local events show community engagement' : 'Events have minimal business relevance',
        priority: isEventCentricLocation ? 'low' : 'ignore',
        eventTypes: [
            'community'
        ]
    };
}
/**
 * Determines trending topics relevance
 */ function analyzeTrendsRelevance(businessType, location, platform) {
    // Always use trends for social media businesses
    const trendDependentBusinesses = [
        'marketing',
        'social media',
        'content',
        'entertainment',
        'fashion',
        'beauty',
        'technology',
        'startup'
    ];
    // Industry-specific trend types
    let trendTypes = [];
    if (businessType.includes('technology') || businessType.includes('fintech')) {
        trendTypes = [
            'technology',
            'business',
            'innovation',
            'startup'
        ];
    } else if (businessType.includes('restaurant') || businessType.includes('food')) {
        trendTypes = [
            'food',
            'lifestyle',
            'local',
            'cultural'
        ];
    } else if (businessType.includes('fitness')) {
        trendTypes = [
            'health',
            'wellness',
            'lifestyle',
            'sports'
        ];
    } else {
        trendTypes = [
            'business',
            'local',
            'community'
        ];
    }
    const isTrendSensitive = trendDependentBusinesses.some((type)=>businessType.includes(type));
    // Platform consideration
    const isSocialPlatform = platform === 'instagram' || platform === 'twitter';
    return {
        useTrends: true,
        relevanceReason: isTrendSensitive ? `${businessType} thrives on current trends and conversations` : 'Trending topics increase content relevance and engagement',
        priority: isTrendSensitive ? 'high' : 'medium',
        trendTypes
    };
}
/**
 * Determines cultural context relevance
 */ function analyzeCulturalRelevance(businessType, location, platform) {
    // Always high relevance for local businesses
    const localBusinesses = [
        'restaurant',
        'retail',
        'fitness',
        'beauty',
        'real estate',
        'healthcare',
        'education'
    ];
    // Cultural elements to emphasize
    let culturalElements = [];
    if (location.includes('nairobi') || location.includes('kenya')) {
        culturalElements = [
            'ubuntu philosophy',
            'harambee spirit',
            'swahili expressions',
            'community values'
        ];
    } else if (location.includes('new york')) {
        culturalElements = [
            'diversity',
            'hustle culture',
            'innovation',
            'fast-paced lifestyle'
        ];
    } else if (location.includes('london')) {
        culturalElements = [
            'tradition',
            'multiculturalism',
            'business etiquette',
            'dry humor'
        ];
    } else {
        culturalElements = [
            'local customs',
            'community values',
            'regional preferences'
        ];
    }
    const isLocalBusiness = localBusinesses.some((type)=>businessType.includes(type));
    const isInternationalLocation = !location.includes('united states');
    return {
        useCultural: true,
        relevanceReason: isLocalBusiness ? `Local ${businessType} must connect with community culture` : 'Cultural awareness builds authentic connections',
        priority: isLocalBusiness || isInternationalLocation ? 'high' : 'medium',
        culturalElements
    };
}
function filterContextData(relevance, availableData) {
    const result = {
        contextInstructions: generateContextInstructions(relevance)
    };
    // Filter weather data
    if (relevance.weather.useWeather && availableData.weather) {
        result.selectedWeather = availableData.weather;
    }
    // Filter events data
    if (relevance.events.useEvents && availableData.events) {
        result.selectedEvents = availableData.events.filter((event)=>relevance.events.eventTypes.some((type)=>event.category?.toLowerCase().includes(type) || event.name?.toLowerCase().includes(type))).slice(0, relevance.events.priority === 'high' ? 3 : 1);
    }
    // Filter trends data
    if (relevance.trends.useTrends && availableData.trends) {
        result.selectedTrends = availableData.trends.filter((trend)=>relevance.trends.trendTypes.some((type)=>trend.category?.toLowerCase().includes(type) || trend.topic?.toLowerCase().includes(type))).slice(0, relevance.trends.priority === 'high' ? 5 : 3);
    }
    // Filter cultural data
    if (relevance.cultural.useCultural && availableData.cultural) {
        result.selectedCultural = {
            ...availableData.cultural,
            cultural_nuances: availableData.cultural.cultural_nuances?.filter((nuance)=>relevance.cultural.culturalElements.some((element)=>nuance.toLowerCase().includes(element.toLowerCase()))).slice(0, 3)
        };
    }
    return result;
}
/**
 * Generates context-specific instructions for the AI
 */ function generateContextInstructions(relevance) {
    const instructions = [];
    if (relevance.weather.useWeather) {
        if (relevance.weather.priority === 'high') {
            instructions.push('WEATHER: Integrate weather naturally as it significantly impacts customer behavior');
        } else if (relevance.weather.priority === 'medium') {
            instructions.push('WEATHER: Mention weather subtly if it adds value to the message');
        }
    } else {
        instructions.push('WEATHER: Ignore weather data - not relevant for this business type');
    }
    if (relevance.events.useEvents) {
        if (relevance.events.priority === 'high') {
            instructions.push('EVENTS: Highlight relevant local events as key business opportunities');
        } else {
            instructions.push('EVENTS: Reference events only if they add community connection value');
        }
    } else {
        instructions.push('EVENTS: Skip event references - focus on core business value');
    }
    if (relevance.trends.priority === 'high') {
        instructions.push('TRENDS: Lead with trending topics to maximize engagement and relevance');
    } else {
        instructions.push('TRENDS: Use trends subtly to add contemporary relevance');
    }
    if (relevance.cultural.priority === 'high') {
        instructions.push('CULTURE: Deeply integrate local cultural elements for authentic connection');
    } else {
        instructions.push('CULTURE: Include respectful cultural awareness without overdoing it');
    }
    return instructions.join('\n');
}
}}),
"[project]/src/ai/utils/human-content-generator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Human-Like Content Generation System
 * 
 * This module provides techniques to make AI-generated content
 * feel authentic, human, and engaging while avoiding AI detection.
 */ __turbopack_context__.s({
    "generateContentOptimization": (()=>generateContentOptimization),
    "generateHumanizationTechniques": (()=>generateHumanizationTechniques),
    "generateTrafficDrivingElements": (()=>generateTrafficDrivingElements)
});
function generateHumanizationTechniques(businessType, brandVoice, location) {
    const basePersonality = getPersonalityMarkers(brandVoice);
    const industryAuthenticity = getIndustryAuthenticity(businessType);
    const locationConversation = getLocationConversation(location);
    return {
        personality_markers: [
            ...basePersonality,
            'Use first-person perspective occasionally',
            'Include personal opinions and preferences',
            'Show vulnerability and learning moments',
            'Express genuine excitement about successes'
        ],
        authenticity_elements: [
            ...industryAuthenticity,
            'Share behind-the-scenes moments',
            'Admit mistakes and lessons learned',
            'Use specific details instead of generalities',
            'Reference real experiences and observations',
            'Include time-specific references (today, this morning, etc.)'
        ],
        conversational_patterns: [
            ...locationConversation,
            'Start sentences with "You know what?"',
            'Use rhetorical questions naturally',
            'Include conversational fillers like "honestly" or "actually"',
            'Break up long thoughts with shorter sentences',
            'Use contractions (we\'re, don\'t, can\'t) naturally'
        ],
        storytelling_devices: [
            'Start with "I remember when..." or "Last week..."',
            'Use the "But here\'s the thing..." transition',
            'Include dialogue: "My customer said..."',
            'Paint vivid scenes with sensory details',
            'End with unexpected insights or realizations'
        ],
        emotional_connectors: [
            'Share moments of doubt and breakthrough',
            'Express genuine gratitude to customers',
            'Show empathy for customer struggles',
            'Celebrate small wins with enthusiasm',
            'Use emotional language that resonates'
        ],
        imperfection_markers: [
            'Occasional typos that feel natural (but not distracting)',
            'Slightly informal grammar in casual contexts',
            'Stream-of-consciousness moments',
            'Self-corrections: "Actually, let me rephrase that..."',
            'Honest admissions: "I\'m still figuring this out..."'
        ]
    };
}
function generateTrafficDrivingElements(businessType, platform, targetAudience) {
    return {
        viral_hooks: [
            'Controversial but respectful opinions',
            'Surprising industry statistics',
            'Before/after transformations',
            'Myth-busting content',
            'Exclusive behind-the-scenes reveals',
            'Timely reactions to trending topics',
            'Unexpected collaborations or partnerships'
        ],
        engagement_magnets: [
            'Fill-in-the-blank questions',
            'This or that choices',
            'Caption this photo challenges',
            'Share your experience prompts',
            'Prediction requests',
            'Opinion polls and surveys',
            'Challenge participation invites'
        ],
        conversion_triggers: [
            'Limited-time offers with urgency',
            'Exclusive access for followers',
            'Free valuable resources',
            'Personal consultation offers',
            'Early bird opportunities',
            'Member-only benefits',
            'Referral incentives'
        ],
        shareability_factors: [
            'Relatable everyday struggles',
            'Inspirational success stories',
            'Useful tips people want to save',
            'Funny observations about the industry',
            'Heartwarming customer stories',
            'Educational content that teaches',
            'Content that makes people look smart for sharing'
        ],
        curiosity_gaps: [
            'The one thing nobody tells you about...',
            'What happened next will surprise you...',
            'The secret that changed everything...',
            'Why everyone is wrong about...',
            'The mistake I made that taught me...',
            'What I wish I knew before...',
            'The truth about... that nobody talks about'
        ],
        social_proof_elements: [
            'Customer testimonials and reviews',
            'User-generated content features',
            'Industry recognition and awards',
            'Media mentions and press coverage',
            'Collaboration with respected figures',
            'Community size and engagement stats',
            'Success metrics and achievements'
        ]
    };
}
/**
 * Gets personality markers based on brand voice
 */ function getPersonalityMarkers(brandVoice) {
    const voiceMap = {
        'friendly': [
            'Use warm, welcoming language',
            'Include friendly greetings and sign-offs',
            'Show genuine interest in followers',
            'Use inclusive language that brings people together'
        ],
        'professional': [
            'Maintain expertise while being approachable',
            'Use industry knowledge to build authority',
            'Balance formal tone with personal touches',
            'Show competence through specific examples'
        ],
        'casual': [
            'Use everyday language and slang appropriately',
            'Be relaxed and conversational',
            'Include humor and light-hearted moments',
            'Feel like talking to a friend'
        ],
        'innovative': [
            'Show forward-thinking perspectives',
            'Challenge conventional wisdom respectfully',
            'Share cutting-edge insights',
            'Express excitement about new possibilities'
        ]
    };
    // Extract key words from brand voice description
    const lowerVoice = brandVoice.toLowerCase();
    for (const [key, markers] of Object.entries(voiceMap)){
        if (lowerVoice.includes(key)) {
            return markers;
        }
    }
    return voiceMap['friendly']; // Default fallback
}
/**
 * Gets industry-specific authenticity elements
 */ function getIndustryAuthenticity(businessType) {
    const industryMap = {
        'restaurant': [
            'Share cooking failures and successes',
            'Talk about ingredient sourcing stories',
            'Mention customer reactions and feedback',
            'Describe the sensory experience of food'
        ],
        'fitness': [
            'Share personal workout struggles',
            'Admit to having off days',
            'Celebrate client progress genuinely',
            'Talk about the mental health benefits'
        ],
        'technology': [
            'Explain complex concepts simply',
            'Share debugging stories and solutions',
            'Admit when technology isn\'t perfect',
            'Focus on human impact of technology'
        ],
        'financial technology software': [
            'Share stories about financial inclusion impact',
            'Explain complex financial concepts simply',
            'Highlight real customer success stories',
            'Address common financial fears and concerns',
            'Show the human side of financial technology'
        ],
        'beauty': [
            'Share makeup fails and learning moments',
            'Talk about skin struggles and solutions',
            'Celebrate diverse beauty standards',
            'Share product testing experiences'
        ]
    };
    return industryMap[businessType.toLowerCase()] || [
        'Share real customer interactions',
        'Talk about daily business challenges',
        'Celebrate small business wins',
        'Show the human side of your industry'
    ];
}
/**
 * Gets location-specific conversational patterns
 */ function getLocationConversation(location) {
    const locationMap = {
        'nairobi': [
            'Use occasional Swahili phrases naturally',
            'Reference local landmarks and experiences',
            'Include community-focused language',
            'Show respect for local customs and values'
        ],
        'new york': [
            'Use direct, fast-paced communication',
            'Reference city experiences and culture',
            'Include diverse perspectives',
            'Show hustle and ambition'
        ],
        'london': [
            'Use British expressions naturally',
            'Include dry humor appropriately',
            'Reference local culture and experiences',
            'Maintain polite but direct communication'
        ]
    };
    const locationKey = location.toLowerCase().split(',')[0].trim();
    return locationMap[locationKey] || [
        'Use local expressions and references',
        'Include regional cultural touchpoints',
        'Show understanding of local context',
        'Connect with community values'
    ];
}
function generateContentOptimization(platform, businessType, timeOfDay = 'morning') {
    const platformStrategies = {
        'instagram': {
            posting_strategy: [
                'Use high-quality visuals as primary hook',
                'Write captions that encourage saves and shares',
                'Include clear call-to-actions in stories',
                'Use relevant hashtags strategically'
            ],
            engagement_timing: [
                'Post when your audience is most active',
                'Respond to comments within first hour',
                'Use stories for real-time engagement',
                'Go live during peak audience times'
            ]
        },
        'linkedin': {
            posting_strategy: [
                'Lead with valuable insights or questions',
                'Use professional but personal tone',
                'Include industry-relevant hashtags',
                'Share thought leadership content'
            ],
            engagement_timing: [
                'Post during business hours for B2B',
                'Engage with comments professionally',
                'Share in relevant LinkedIn groups',
                'Connect with commenters personally'
            ]
        },
        'twitter': {
            posting_strategy: [
                'Use trending hashtags when relevant',
                'Create tweetable quotes and insights',
                'Engage in real-time conversations',
                'Share quick tips and observations'
            ],
            engagement_timing: [
                'Tweet during peak conversation times',
                'Respond quickly to mentions',
                'Join trending conversations',
                'Retweet with thoughtful comments'
            ]
        },
        'facebook': {
            posting_strategy: [
                'Create community-focused content',
                'Use longer-form storytelling',
                'Encourage group discussions',
                'Share local community content'
            ],
            engagement_timing: [
                'Post when your community is online',
                'Respond to all comments personally',
                'Share in relevant Facebook groups',
                'Use Facebook events for promotion'
            ]
        }
    };
    const strategy = platformStrategies[platform.toLowerCase()] || platformStrategies['instagram'];
    return {
        ...strategy,
        content_mix: [
            '60% educational/valuable content',
            '20% behind-the-scenes/personal',
            '15% promotional/business',
            '5% trending/entertainment'
        ],
        performance_indicators: [
            'Comments and meaningful engagement',
            'Saves and shares over likes',
            'Profile visits and follows',
            'Website clicks and conversions',
            'Direct messages and inquiries'
        ]
    };
}
}}),
"[project]/src/ai/prompts/advanced-design-prompts.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Advanced Design Generation Prompts
 * 
 * Professional-grade prompts incorporating design principles, composition rules,
 * typography best practices, color theory, and modern design trends.
 */ __turbopack_context__.s({
    "ADVANCED_DESIGN_PRINCIPLES": (()=>ADVANCED_DESIGN_PRINCIPLES),
    "BUSINESS_TYPE_DESIGN_DNA": (()=>BUSINESS_TYPE_DESIGN_DNA),
    "PLATFORM_SPECIFIC_GUIDELINES": (()=>PLATFORM_SPECIFIC_GUIDELINES),
    "QUALITY_ENHANCEMENT_INSTRUCTIONS": (()=>QUALITY_ENHANCEMENT_INSTRUCTIONS)
});
const ADVANCED_DESIGN_PRINCIPLES = `
**COMPOSITION & VISUAL HIERARCHY:**
- Apply the Rule of Thirds: Position key elements along the grid lines or intersections
- Create clear visual hierarchy using size, contrast, and positioning
- Establish a strong focal point that draws the eye immediately
- Use negative space strategically to create breathing room and emphasis
- Balance elements using symmetrical or asymmetrical composition
- Guide the viewer's eye through the design with leading lines and flow

**TYPOGRAPHY EXCELLENCE:**
- Establish clear typographic hierarchy (Primary headline, secondary text, body copy)
- Use maximum 2-3 font families with strong contrast between them
- Ensure text contrast ratio meets accessibility standards (4.5:1 minimum)
- Apply proper letter spacing, line height, and text alignment
- Scale typography appropriately for the platform and viewing distance
- Use typography as a design element, not just information delivery

**COLOR THEORY & HARMONY:**
- Apply color psychology appropriate to the business type and message
- Use complementary colors for high contrast and attention
- Apply analogous colors for harmony and cohesion
- Implement triadic color schemes for vibrant, balanced designs
- Ensure sufficient contrast between text and background
- Use the 60-30-10 rule: 60% dominant color, 30% secondary, 10% accent

**MODERN DESIGN TRENDS:**
- Embrace minimalism with purposeful use of white space
- Use bold, geometric shapes and clean lines
- Apply subtle gradients and depth effects when appropriate
- Incorporate authentic, diverse photography when using people
- Use consistent border radius and spacing throughout
- Apply subtle shadows and depth for modern dimensionality
`;
const PLATFORM_SPECIFIC_GUIDELINES = {
    instagram: `
**INSTAGRAM OPTIMIZATION:**
- Design for mobile-first viewing with bold, clear elements
- Use high contrast colors that pop on small screens
- Keep text large and readable (minimum 24px equivalent)
- Center important elements for square crop compatibility
- Use Instagram's native color palette trends
- Design for both feed and story formats
- Optimize for thumb-stopping power in fast scroll feeds
`,
    facebook: `
**FACEBOOK OPTIMIZATION:**
- Design for both desktop and mobile viewing
- Use Facebook blue (#1877F2) strategically for CTAs
- Optimize for news feed algorithm preferences
- Include clear value proposition in visual hierarchy
- Design for engagement and shareability
- Use authentic, relatable imagery
- Optimize for both organic and paid placement
`,
    twitter: `
**TWITTER/X OPTIMIZATION:**
- Design for rapid consumption and high engagement
- Use bold, contrasting colors that stand out in timeline
- Keep text minimal and impactful
- Design for retweet and quote tweet functionality
- Use trending visual styles and memes appropriately
- Optimize for both light and dark mode viewing
- Create thumb-stopping visuals for fast-scrolling feeds
`,
    linkedin: `
**LINKEDIN OPTIMIZATION:**
- Use professional, business-appropriate color schemes
- Apply corporate design standards and clean aesthetics
- Include clear value proposition for business audience
- Use professional photography and imagery
- Design for thought leadership and expertise positioning
- Apply subtle, sophisticated design elements
- Optimize for professional networking context
`
};
const BUSINESS_TYPE_DESIGN_DNA = {
    restaurant: `
**RESTAURANT DESIGN DNA:**
- Use warm, appetizing colors (reds, oranges, warm yellows)
- Include high-quality food photography with proper lighting
- Apply rustic or modern clean aesthetics based on restaurant type
- Use food-focused typography (script for upscale, bold sans for casual)
- Include appetite-triggering visual elements
- Apply golden hour lighting effects for food imagery
- Use complementary colors that enhance food appeal
`,
    fitness: `
**FITNESS DESIGN DNA:**
- Use energetic, motivational color schemes (bright blues, oranges, greens)
- Include dynamic action shots and movement
- Apply bold, strong typography with impact
- Use high-contrast designs for motivation and energy
- Include progress and achievement visual metaphors
- Apply athletic and performance-focused imagery
- Use inspiring and empowering visual language
`,
    beauty: `
**BEAUTY DESIGN DNA:**
- Use sophisticated, elegant color palettes (pastels, metallics)
- Include high-quality beauty photography with perfect lighting
- Apply clean, minimalist aesthetics with luxury touches
- Use elegant, refined typography
- Include aspirational and transformational imagery
- Apply soft, flattering lighting effects
- Use premium and luxurious visual elements
`,
    tech: `
**TECH DESIGN DNA:**
- Use modern, digital color schemes (blues, purples, teals)
- Include clean, minimalist design with geometric elements
- Apply futuristic and innovative visual metaphors
- Use modern, sans-serif typography
- Include data visualization and tech-focused imagery
- Apply gradient overlays and digital effects
- Use professional and cutting-edge visual language
`,
    default: `
**UNIVERSAL DESIGN DNA:**
- Use brand-appropriate color psychology
- Include authentic, high-quality imagery
- Apply clean, professional aesthetics
- Use readable, accessible typography
- Include relevant industry visual metaphors
- Apply consistent brand visual language
- Use trustworthy and professional design elements
`
};
const QUALITY_ENHANCEMENT_INSTRUCTIONS = `
**DESIGN QUALITY STANDARDS:**
- Ensure all text is perfectly readable with sufficient contrast
- Apply consistent spacing and alignment throughout
- Use high-resolution imagery without pixelation or artifacts
- Maintain visual balance and proper proportions
- Ensure brand elements are prominently but naturally integrated
- Apply professional color grading and visual polish
- Create designs that work across different screen sizes
- Ensure accessibility compliance for color contrast and readability

**TECHNICAL EXCELLENCE:**
- Generate crisp, high-resolution images suitable for social media
- Apply proper aspect ratios for platform requirements
- Ensure text overlay is perfectly positioned and readable
- Use consistent visual style throughout the design
- Apply professional lighting and shadow effects
- Ensure logo integration feels natural and branded
- Create designs that maintain quality when compressed for social media
`;
}}),
"[project]/src/ai/utils/design-analysis.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Analysis Utilities
 * 
 * Intelligent analysis and processing of design examples for better AI generation
 */ __turbopack_context__.s({
    "DesignAnalysisSchema": (()=>DesignAnalysisSchema),
    "analyzeDesignExample": (()=>analyzeDesignExample),
    "extractDesignDNA": (()=>extractDesignDNA),
    "selectOptimalDesignExamples": (()=>selectOptimalDesignExamples)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const DesignAnalysisSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        primary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Primary color in hex format'),
        secondary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Secondary color in hex format'),
        accent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Accent color in hex format'),
        colorHarmony: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'complementary',
            'analogous',
            'triadic',
            'monochromatic',
            'split-complementary'
        ]).describe('Type of color harmony used'),
        colorMood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Overall mood conveyed by the color scheme')
    }),
    composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        layout: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'centered',
            'left-aligned',
            'right-aligned',
            'asymmetrical',
            'grid-based'
        ]).describe('Primary layout structure'),
        visualHierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How visual hierarchy is established'),
        focalPoint: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Primary focal point and how it\'s created'),
        balance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'symmetrical',
            'asymmetrical',
            'radial'
        ]).describe('Type of visual balance'),
        whitespace: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'minimal',
            'moderate',
            'generous'
        ]).describe('Use of negative space')
    }),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        primaryFont: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Primary font style/category'),
        hierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Typographic hierarchy structure'),
        textTreatment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Special text treatments or effects'),
        readability: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'stylized'
        ]).describe('Text readability level')
    }),
    style: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        aesthetic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Overall design aesthetic (modern, vintage, minimalist, etc.)'),
        mood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Emotional mood and feeling'),
        sophistication: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'casual',
            'professional',
            'luxury',
            'playful'
        ]).describe('Level of sophistication'),
        trends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Current design trends incorporated')
    }),
    effectiveness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        attention: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Attention-grabbing potential (1-10)'),
        clarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Message clarity (1-10)'),
        brandAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Brand alignment strength (1-10)'),
        platformOptimization: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Platform optimization (1-10)')
    })
});
// Design analysis prompt
const designAnalysisPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeDesignExample',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            designContext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignAnalysisSchema
    },
    prompt: `You are an expert design analyst with deep knowledge of visual design principles, color theory, typography, and modern design trends.

Analyze the provided design image and extract detailed insights about its design elements and effectiveness.

Business Context: {{businessType}}
Platform: {{platform}}
Context: {{designContext}}

Provide a comprehensive analysis covering:

1. **Color Analysis**: Identify the color palette, harmony type, and psychological impact
2. **Composition Analysis**: Evaluate layout, hierarchy, balance, and use of space
3. **Typography Analysis**: Assess font choices, hierarchy, and text treatment
4. **Style Analysis**: Determine aesthetic approach, mood, and trend incorporation
5. **Effectiveness Analysis**: Rate attention-grabbing power, clarity, brand alignment, and platform optimization

Be specific and actionable in your analysis. Focus on elements that can be replicated or adapted for new designs.`
});
async function analyzeDesignExample(designImageUrl, businessType, platform, context) {
    try {
        // For now, return a mock analysis to avoid API issues
        // This can be replaced with actual AI analysis once the prompt system is stable
        return {
            colorPalette: {
                primary: '#FF6B6B',
                secondary: '#4ECDC4',
                accent: '#45B7D1',
                colorHarmony: 'complementary',
                colorMood: 'Energetic and modern'
            },
            composition: {
                layout: 'centered',
                visualHierarchy: 'Clear size-based hierarchy with strong focal point',
                focalPoint: 'Central logo and headline combination',
                balance: 'symmetrical',
                whitespace: 'moderate'
            },
            typography: {
                primaryFont: 'Modern sans-serif',
                hierarchy: 'Large headline, medium subtext, small details',
                textTreatment: 'Bold headlines with subtle shadows',
                readability: 'high'
            },
            style: {
                aesthetic: 'Modern minimalist',
                mood: 'Professional and approachable',
                sophistication: 'professional',
                trends: [
                    'Bold typography',
                    'Minimalist design',
                    'High contrast'
                ]
            },
            effectiveness: {
                attention: 8,
                clarity: 9,
                brandAlignment: 8,
                platformOptimization: 7
            }
        };
    } catch (error) {
        console.error('Design analysis failed:', error);
        throw new Error('Failed to analyze design example');
    }
}
function selectOptimalDesignExamples(designExamples, analyses, contentType, platform, maxExamples = 3) {
    if (!analyses.length || !designExamples.length) {
        return designExamples.slice(0, maxExamples);
    }
    // Score each design based on relevance and effectiveness
    const scoredExamples = designExamples.map((example, index)=>{
        const analysis = analyses[index];
        if (!analysis) return {
            example,
            score: 0
        };
        let score = 0;
        // Weight effectiveness metrics
        score += analysis.effectiveness.attention * 0.3;
        score += analysis.effectiveness.clarity * 0.25;
        score += analysis.effectiveness.brandAlignment * 0.25;
        score += analysis.effectiveness.platformOptimization * 0.2;
        // Bonus for sophisticated designs
        if (analysis.style.sophistication === 'professional' || analysis.style.sophistication === 'luxury') {
            score += 1;
        }
        // Bonus for modern trends
        score += analysis.style.trends.length * 0.5;
        return {
            example,
            score,
            analysis
        };
    });
    // Sort by score and return top examples
    return scoredExamples.sort((a, b)=>b.score - a.score).slice(0, maxExamples).map((item)=>item.example);
}
function extractDesignDNA(analyses) {
    if (!analyses.length) return '';
    const commonElements = {
        colors: analyses.map((a)=>a.colorPalette.colorHarmony),
        layouts: analyses.map((a)=>a.composition.layout),
        aesthetics: analyses.map((a)=>a.style.aesthetic),
        moods: analyses.map((a)=>a.style.mood)
    };
    // Find most common elements
    const mostCommonColor = getMostCommon(commonElements.colors);
    const mostCommonLayout = getMostCommon(commonElements.layouts);
    const mostCommonAesthetic = getMostCommon(commonElements.aesthetics);
    const mostCommonMood = getMostCommon(commonElements.moods);
    return `
**EXTRACTED DESIGN DNA:**
- **Color Harmony**: Primarily uses ${mostCommonColor} color schemes
- **Layout Pattern**: Favors ${mostCommonLayout} compositions
- **Aesthetic Style**: Consistent ${mostCommonAesthetic} approach
- **Emotional Tone**: Maintains ${mostCommonMood} mood throughout
- **Visual Sophistication**: ${analyses[0]?.style.sophistication} level presentation
- **Typography Approach**: ${analyses[0]?.typography.hierarchy} hierarchy structure
`;
}
/**
 * Helper function to find most common element in array
 */ function getMostCommon(arr) {
    const counts = arr.reduce((acc, item)=>{
        acc[item] = (acc[item] || 0) + 1;
        return acc;
    }, {});
    return Object.entries(counts).reduce((a, b)=>counts[a[0]] > counts[b[0]] ? a : b)[0];
}
}}),
"[project]/src/ai/utils/design-quality.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Quality Validation and Enhancement
 * 
 * System for validating, scoring, and iteratively improving generated designs
 */ __turbopack_context__.s({
    "DesignQualitySchema": (()=>DesignQualitySchema),
    "assessDesignQuality": (()=>assessDesignQuality),
    "calculateWeightedScore": (()=>calculateWeightedScore),
    "generateImprovementPrompt": (()=>generateImprovementPrompt),
    "meetsQualityStandards": (()=>meetsQualityStandards)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const DesignQualitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    overall: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Overall design quality score (1-10)'),
        grade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'A+',
            'A',
            'B+',
            'B',
            'C+',
            'C',
            'D',
            'F'
        ]).describe('Letter grade for design quality'),
        summary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Brief summary of design strengths and weaknesses')
    }),
    composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Composition and layout quality (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on composition'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested composition improvements')
    }),
    typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Typography quality and readability (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on typography'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested typography improvements')
    }),
    colorDesign: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Color usage and harmony (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on color choices'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested color improvements')
    }),
    brandAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Brand consistency and alignment (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on brand alignment'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested brand alignment improvements')
    }),
    platformOptimization: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Platform-specific optimization (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on platform optimization'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested platform optimization improvements')
    }),
    technicalQuality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10).describe('Technical execution quality (1-10)'),
        feedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific feedback on technical aspects'),
        improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Suggested technical improvements')
    }),
    recommendedActions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        priority: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'low'
        ]).describe('Priority level of the action'),
        action: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Specific action to take'),
        expectedImpact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Expected impact of the action')
    })).describe('Prioritized list of recommended improvements')
});
// Design quality assessment prompt
const designQualityPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'assessDesignQuality',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            brandColors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            designGoals: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignQualitySchema
    },
    prompt: `You are a world-class design critic and quality assessor with expertise in visual design, branding, and social media optimization.

Evaluate the provided design image with the highest professional standards.

**Context:**
- Business Type: {{businessType}}
- Platform: {{platform}}
- Visual Style Goal: {{visualStyle}}
- Brand Colors: {{brandColors}}
- Design Goals: {{designGoals}}

**Assessment Criteria:**

1. **Composition & Layout** (25%):
   - Visual hierarchy and flow
   - Balance and proportion
   - Use of negative space
   - Rule of thirds application
   - Focal point effectiveness

2. **Typography** (20%):
   - Readability and legibility
   - Hierarchy and contrast
   - Font choice appropriateness
   - Text positioning and spacing
   - Accessibility compliance

3. **Color Design** (20%):
   - Color harmony and theory
   - Brand color integration
   - Contrast and accessibility
   - Psychological impact
   - Platform appropriateness

4. **Brand Alignment** (15%):
   - Brand consistency
   - Logo integration
   - Visual style adherence
   - Brand personality expression
   - Professional presentation

5. **Platform Optimization** (10%):
   - Platform-specific best practices
   - Mobile optimization
   - Engagement potential
   - Algorithm friendliness
   - Format appropriateness

6. **Technical Quality** (10%):
   - Image resolution and clarity
   - Professional finish
   - Technical execution
   - Scalability
   - Print/digital readiness

Provide specific, actionable feedback with concrete improvement suggestions. Be thorough but constructive.`
});
async function assessDesignQuality(designImageUrl, businessType, platform, visualStyle, brandColors, designGoals) {
    try {
        // For now, return a mock quality assessment to avoid API issues
        // This provides realistic quality scores while the system is being tested
        const baseScore = 7 + Math.random() * 2; // Random score between 7-9
        return {
            overall: {
                score: Math.round(baseScore * 10) / 10,
                grade: baseScore >= 8.5 ? 'A' : baseScore >= 7.5 ? 'B+' : 'B',
                summary: `Professional ${visualStyle} design for ${businessType} with good composition and brand alignment.`
            },
            composition: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: 'Strong visual hierarchy with balanced composition',
                improvements: baseScore < 8 ? [
                    'Improve focal point clarity',
                    'Enhance visual balance'
                ] : []
            },
            typography: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: 'Clear, readable typography with appropriate hierarchy',
                improvements: baseScore < 8 ? [
                    'Increase text contrast',
                    'Improve font pairing'
                ] : []
            },
            colorDesign: {
                score: Math.round((baseScore + Math.random() * 0.5) * 10) / 10,
                feedback: brandColors ? 'Good brand color integration' : 'Appropriate color choices for business type',
                improvements: baseScore < 8 ? [
                    'Enhance color harmony',
                    'Improve contrast ratios'
                ] : []
            },
            brandAlignment: {
                score: brandColors ? Math.round((baseScore + 0.5) * 10) / 10 : Math.round((baseScore - 0.5) * 10) / 10,
                feedback: brandColors ? 'Strong brand consistency maintained' : 'Generic design approach',
                improvements: !brandColors ? [
                    'Integrate brand elements',
                    'Improve brand consistency'
                ] : []
            },
            platformOptimization: {
                score: Math.round((baseScore + Math.random() * 0.3) * 10) / 10,
                feedback: `Well optimized for ${platform} format and audience`,
                improvements: baseScore < 8 ? [
                    'Optimize for mobile viewing',
                    'Improve platform-specific elements'
                ] : []
            },
            technicalQuality: {
                score: Math.round((baseScore + 0.2) * 10) / 10,
                feedback: 'High resolution with professional finish',
                improvements: baseScore < 8 ? [
                    'Improve image resolution',
                    'Enhance visual polish'
                ] : []
            },
            recommendedActions: [
                {
                    priority: baseScore < 7.5 ? 'high' : 'medium',
                    action: 'Enhance visual impact through stronger focal points',
                    expectedImpact: 'Improved attention and engagement'
                },
                {
                    priority: 'medium',
                    action: 'Optimize typography for better readability',
                    expectedImpact: 'Clearer message communication'
                }
            ].filter((action)=>baseScore < 8.5 || action.priority === 'medium')
        };
    } catch (error) {
        console.error('Design quality assessment failed:', error);
        throw new Error('Failed to assess design quality');
    }
}
function generateImprovementPrompt(quality) {
    const highPriorityActions = quality.recommendedActions.filter((action)=>action.priority === 'high').map((action)=>action.action);
    const mediumPriorityActions = quality.recommendedActions.filter((action)=>action.priority === 'medium').map((action)=>action.action);
    let improvementPrompt = `
**DESIGN IMPROVEMENT INSTRUCTIONS:**

Based on professional design assessment (Overall Score: ${quality.overall.score}/10, Grade: ${quality.overall.grade}):

**CRITICAL IMPROVEMENTS (High Priority):**
${highPriorityActions.map((action)=>`- ${action}`).join('\n')}

**RECOMMENDED ENHANCEMENTS (Medium Priority):**
${mediumPriorityActions.map((action)=>`- ${action}`).join('\n')}

**SPECIFIC AREA FEEDBACK:**
`;
    if (quality.composition.score < 7) {
        improvementPrompt += `
**Composition Issues to Address:**
${quality.composition.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.typography.score < 7) {
        improvementPrompt += `
**Typography Issues to Address:**
${quality.typography.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.colorDesign.score < 7) {
        improvementPrompt += `
**Color Design Issues to Address:**
${quality.colorDesign.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    if (quality.brandAlignment.score < 7) {
        improvementPrompt += `
**Brand Alignment Issues to Address:**
${quality.brandAlignment.improvements.map((imp)=>`- ${imp}`).join('\n')}
`;
    }
    return improvementPrompt;
}
function meetsQualityStandards(quality, minimumScore = 7) {
    return quality.overall.score >= minimumScore && quality.composition.score >= minimumScore - 1 && quality.typography.score >= minimumScore - 1 && quality.brandAlignment.score >= minimumScore - 1;
}
function calculateWeightedScore(quality) {
    const weights = {
        composition: 0.25,
        typography: 0.20,
        colorDesign: 0.20,
        brandAlignment: 0.15,
        platformOptimization: 0.10,
        technicalQuality: 0.10
    };
    return quality.composition.score * weights.composition + quality.typography.score * weights.typography + quality.colorDesign.score * weights.colorDesign + quality.brandAlignment.score * weights.brandAlignment + quality.platformOptimization.score * weights.platformOptimization + quality.technicalQuality.score * weights.technicalQuality;
}
}}),
"[project]/src/ai/utils/design-trends.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Trends Integration System
 * 
 * Keeps design generation current with latest visual trends and best practices
 */ __turbopack_context__.s({
    "DesignTrendsSchema": (()=>DesignTrendsSchema),
    "generateTrendInstructions": (()=>generateTrendInstructions),
    "getCachedDesignTrends": (()=>getCachedDesignTrends),
    "getCurrentDesignTrends": (()=>getCurrentDesignTrends)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
;
const DesignTrendsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    currentTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Name of the design trend'),
        description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Description of the trend'),
        applicability: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
            'high',
            'medium',
            'low'
        ]).describe('How applicable this trend is to the business type'),
        implementation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How to implement this trend in the design'),
        examples: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Visual examples or descriptions of the trend')
    })).describe('Current relevant design trends'),
    colorTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        palette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending color palette in hex format'),
        mood: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Overall mood of trending colors'),
        application: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('How to apply these colors effectively')
    }),
    typographyTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        styles: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending typography styles'),
        pairings: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Popular font pairings'),
        treatments: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Special text treatments and effects')
    }),
    layoutTrends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        compositions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending layout compositions'),
        spacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Current spacing and whitespace trends'),
        hierarchy: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Visual hierarchy trends')
    }),
    platformSpecific: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Instagram-specific design trends'),
        facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Facebook-specific design trends'),
        twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Twitter/X-specific design trends'),
        linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('LinkedIn-specific design trends')
    })
});
// Design trends analysis prompt
const designTrendsPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'analyzeDesignTrends',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            industry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: DesignTrendsSchema
    },
    prompt: `You are a leading design trend analyst with deep knowledge of current visual design trends, social media best practices, and industry-specific design patterns.

Analyze and provide current design trends relevant to:
- Business Type: {{businessType}}
- Platform: {{platform}}
- Target Audience: {{targetAudience}}
- Industry: {{industry}}

Focus on trends that are:
1. Currently popular and effective (2024-2025)
2. Relevant to the specific business type and platform
3. Proven to drive engagement and conversions
4. Accessible and implementable in AI-generated designs

Provide specific, actionable trend insights that can be directly applied to design generation.`
});
async function getCurrentDesignTrends(businessType, platform, targetAudience, industry) {
    try {
        // For now, return fallback trends to avoid API issues
        // This provides current, relevant trends while the system is being tested
        return getFallbackTrends(businessType, platform);
    } catch (error) {
        console.error('Design trends analysis failed:', error);
        // Return fallback trends
        return getFallbackTrends(businessType, platform);
    }
}
function generateTrendInstructions(trends, platform) {
    const platformTrends = trends.platformSpecific[platform] || [];
    const highApplicabilityTrends = trends.currentTrends.filter((t)=>t.applicability === 'high');
    return `
**CURRENT DESIGN TRENDS INTEGRATION:**

**High-Priority Trends to Incorporate:**
${highApplicabilityTrends.map((trend)=>`
- **${trend.name}**: ${trend.description}
  Implementation: ${trend.implementation}`).join('\n')}

**Color Trends:**
- Trending Palette: ${trends.colorTrends.palette.join(', ')}
- Mood: ${trends.colorTrends.mood}
- Application: ${trends.colorTrends.application}

**Typography Trends:**
- Styles: ${trends.typographyTrends.styles.join(', ')}
- Popular Pairings: ${trends.typographyTrends.pairings.join(', ')}
- Special Treatments: ${trends.typographyTrends.treatments.join(', ')}

**Layout Trends:**
- Compositions: ${trends.layoutTrends.compositions.join(', ')}
- Spacing: ${trends.layoutTrends.spacing}
- Hierarchy: ${trends.layoutTrends.hierarchy}

**Platform-Specific Trends (${platform}):**
${platformTrends.map((trend)=>`- ${trend}`).join('\n')}

**TREND APPLICATION GUIDELINES:**
- Incorporate 2-3 relevant trends maximum to avoid overwhelming the design
- Ensure trends align with brand personality and business goals
- Prioritize trends that enhance readability and user experience
- Balance trendy elements with timeless design principles
`;
}
/**
 * Fallback trends when API fails
 */ function getFallbackTrends(businessType, platform) {
    return {
        currentTrends: [
            {
                name: "Bold Typography",
                description: "Large, impactful typography that commands attention",
                applicability: "high",
                implementation: "Use oversized headlines with strong contrast",
                examples: [
                    "Large sans-serif headers",
                    "Bold statement text",
                    "Typography as hero element"
                ]
            },
            {
                name: "Minimalist Design",
                description: "Clean, uncluttered designs with plenty of white space",
                applicability: "high",
                implementation: "Focus on essential elements, generous spacing, simple color palette",
                examples: [
                    "Clean layouts",
                    "Minimal color schemes",
                    "Focused messaging"
                ]
            },
            {
                name: "Authentic Photography",
                description: "Real, unposed photography over stock imagery",
                applicability: "medium",
                implementation: "Use candid, lifestyle photography that feels genuine",
                examples: [
                    "Behind-the-scenes shots",
                    "Real customer photos",
                    "Lifestyle imagery"
                ]
            }
        ],
        colorTrends: {
            palette: [
                "#FF6B6B",
                "#4ECDC4",
                "#45B7D1",
                "#96CEB4",
                "#FFEAA7"
            ],
            mood: "Vibrant yet calming, optimistic and approachable",
            application: "Use as accent colors against neutral backgrounds for maximum impact"
        },
        typographyTrends: {
            styles: [
                "Bold sans-serif",
                "Modern serif",
                "Custom lettering"
            ],
            pairings: [
                "Bold header + clean body",
                "Serif headline + sans-serif body"
            ],
            treatments: [
                "Gradient text",
                "Outlined text",
                "Text with shadows"
            ]
        },
        layoutTrends: {
            compositions: [
                "Asymmetrical balance",
                "Grid-based layouts",
                "Centered focal points"
            ],
            spacing: "Generous white space with intentional breathing room",
            hierarchy: "Clear size differentiation with strong contrast"
        },
        platformSpecific: {
            instagram: [
                "Square and vertical formats",
                "Story-friendly designs",
                "Carousel-optimized layouts"
            ],
            facebook: [
                "Horizontal emphasis",
                "Video-first approach",
                "Community-focused messaging"
            ],
            twitter: [
                "High contrast for timeline",
                "Text-heavy designs",
                "Trending hashtag integration"
            ],
            linkedin: [
                "Professional aesthetics",
                "Data visualization",
                "Thought leadership focus"
            ]
        }
    };
}
/**
 * Caches trends to avoid excessive API calls
 * Reduced cache duration and added randomization to prevent repetitive designs
 */ const trendsCache = new Map();
const CACHE_DURATION = 6 * 60 * 60 * 1000; // 6 hours (reduced from 24 hours)
const MAX_USAGE_COUNT = 5; // Force refresh after 5 uses to add variety
async function getCachedDesignTrends(businessType, platform, targetAudience, industry) {
    // Add randomization to cache key to create more variety
    const hourOfDay = new Date().getHours();
    const randomSeed = Math.floor(hourOfDay / 2); // Changes every 2 hours
    const cacheKey = `${businessType}-${platform}-${targetAudience}-${industry}-${randomSeed}`;
    const cached = trendsCache.get(cacheKey);
    // Check if cache is valid and not overused
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION && cached.usageCount < MAX_USAGE_COUNT) {
        cached.usageCount++;
        return cached.trends;
    }
    const trends = await getCurrentDesignTrends(businessType, platform, targetAudience, industry);
    trendsCache.set(cacheKey, {
        trends,
        timestamp: Date.now(),
        usageCount: 1
    });
    return trends;
}
}}),
"[project]/src/ai/utils/design-analytics.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Design Performance Analytics System
 * 
 * Tracks design performance, learns from successful patterns, and optimizes future generations
 */ __turbopack_context__.s({
    "DesignPerformanceSchema": (()=>DesignPerformanceSchema),
    "exportAnalyticsData": (()=>exportAnalyticsData),
    "generatePerformanceOptimizedInstructions": (()=>generatePerformanceOptimizedInstructions),
    "getPerformanceInsights": (()=>getPerformanceInsights),
    "getTopPerformingDesigns": (()=>getTopPerformingDesigns),
    "recordDesignGeneration": (()=>recordDesignGeneration),
    "updateDesignPerformance": (()=>updateDesignPerformance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
;
const DesignPerformanceSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    designId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
    generatedAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].date(),
    metrics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        qualityScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        engagementPrediction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        brandAlignmentScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        technicalQuality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10),
        trendRelevance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(10)
    }),
    designElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        colorPalette: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        typography: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        composition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        trends: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
        businessDNA: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    }),
    performance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        actualEngagement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        clickThroughRate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        conversionRate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        brandRecall: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().optional(),
        userFeedback: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number().min(1).max(5).optional()
    }).optional(),
    improvements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional(),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional()
});
// In-memory storage for design analytics (in production, use a database)
const designAnalytics = new Map();
const performancePatterns = new Map();
function recordDesignGeneration(designId, businessType, platform, visualStyle, qualityScore, designElements, predictions) {
    const record = {
        designId,
        businessType,
        platform,
        visualStyle,
        generatedAt: new Date(),
        metrics: {
            qualityScore,
            engagementPrediction: predictions.engagement,
            brandAlignmentScore: predictions.brandAlignment,
            technicalQuality: predictions.technicalQuality,
            trendRelevance: predictions.trendRelevance
        },
        designElements,
        tags: [
            businessType,
            platform,
            visualStyle
        ]
    };
    designAnalytics.set(designId, record);
    updatePerformancePatterns(record);
}
function updateDesignPerformance(designId, actualMetrics) {
    const record = designAnalytics.get(designId);
    if (!record) return;
    record.performance = {
        ...record.performance,
        ...actualMetrics
    };
    designAnalytics.set(designId, record);
    updatePerformancePatterns(record);
}
/**
 * Analyzes performance patterns to improve future designs
 */ function updatePerformancePatterns(record) {
    const patternKey = `${record.businessType}-${record.platform}-${record.visualStyle}`;
    if (!performancePatterns.has(patternKey)) {
        performancePatterns.set(patternKey, {
            count: 0,
            avgQuality: 0,
            avgEngagement: 0,
            successfulElements: new Map(),
            commonIssues: new Map(),
            bestPractices: []
        });
    }
    const pattern = performancePatterns.get(patternKey);
    pattern.count += 1;
    // Update averages
    pattern.avgQuality = (pattern.avgQuality * (pattern.count - 1) + record.metrics.qualityScore) / pattern.count;
    pattern.avgEngagement = (pattern.avgEngagement * (pattern.count - 1) + record.metrics.engagementPrediction) / pattern.count;
    // Track successful elements
    if (record.metrics.qualityScore >= 8) {
        record.designElements.trends.forEach((trend)=>{
            const count = pattern.successfulElements.get(trend) || 0;
            pattern.successfulElements.set(trend, count + 1);
        });
    }
    // Track common issues
    if (record.improvements) {
        record.improvements.forEach((issue)=>{
            const count = pattern.commonIssues.get(issue) || 0;
            pattern.commonIssues.set(issue, count + 1);
        });
    }
    performancePatterns.set(patternKey, pattern);
}
function getPerformanceInsights(businessType, platform, visualStyle) {
    const patternKey = visualStyle ? `${businessType}-${platform}-${visualStyle}` : `${businessType}-${platform}`;
    const pattern = performancePatterns.get(patternKey);
    if (!pattern) {
        return {
            averageQuality: 0,
            averageEngagement: 0,
            topSuccessfulElements: [],
            commonIssues: [],
            recommendations: [
                'Insufficient data for insights'
            ],
            sampleSize: 0
        };
    }
    // Get top successful elements
    const topElements = Array.from(pattern.successfulElements.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([element])=>element);
    // Get common issues
    const topIssues = Array.from(pattern.commonIssues.entries()).sort((a, b)=>b[1] - a[1]).slice(0, 3).map(([issue])=>issue);
    // Generate recommendations
    const recommendations = generateRecommendations(pattern, topElements, topIssues);
    return {
        averageQuality: Math.round(pattern.avgQuality * 10) / 10,
        averageEngagement: Math.round(pattern.avgEngagement * 10) / 10,
        topSuccessfulElements: topElements,
        commonIssues: topIssues,
        recommendations,
        sampleSize: pattern.count
    };
}
/**
 * Generates actionable recommendations based on performance data
 */ function generateRecommendations(pattern, successfulElements, commonIssues) {
    const recommendations = [];
    // Quality-based recommendations
    if (pattern.avgQuality < 7) {
        recommendations.push('Focus on improving overall design quality through better composition and typography');
    }
    // Engagement-based recommendations
    if (pattern.avgEngagement < 7) {
        recommendations.push('Incorporate more attention-grabbing elements and bold visual choices');
    }
    // Element-based recommendations
    if (successfulElements.length > 0) {
        recommendations.push(`Continue using successful elements: ${successfulElements.slice(0, 3).join(', ')}`);
    }
    // Issue-based recommendations
    if (commonIssues.length > 0) {
        recommendations.push(`Address common issues: ${commonIssues.slice(0, 2).join(', ')}`);
    }
    // Sample size recommendations
    if (pattern.count < 10) {
        recommendations.push('Generate more designs to improve insights accuracy');
    }
    return recommendations;
}
function getTopPerformingDesigns(businessType, platform, limit = 10) {
    let designs = Array.from(designAnalytics.values());
    // Filter by business type and platform if specified
    if (businessType) {
        designs = designs.filter((d)=>d.businessType === businessType);
    }
    if (platform) {
        designs = designs.filter((d)=>d.platform === platform);
    }
    // Sort by quality score and engagement prediction
    designs.sort((a, b)=>{
        const scoreA = (a.metrics.qualityScore + a.metrics.engagementPrediction) / 2;
        const scoreB = (b.metrics.qualityScore + b.metrics.engagementPrediction) / 2;
        return scoreB - scoreA;
    });
    return designs.slice(0, limit);
}
function generatePerformanceOptimizedInstructions(businessType, platform, visualStyle) {
    const insights = getPerformanceInsights(businessType, platform, visualStyle);
    if (insights.sampleSize === 0) {
        return ''; // No data available
    }
    let instructions = `\n**PERFORMANCE-OPTIMIZED DESIGN INSTRUCTIONS:**\n`;
    if (insights.topSuccessfulElements.length > 0) {
        instructions += `**Proven Successful Elements (${insights.sampleSize} designs analyzed):**\n`;
        insights.topSuccessfulElements.forEach((element)=>{
            instructions += `- Incorporate: ${element}\n`;
        });
    }
    if (insights.commonIssues.length > 0) {
        instructions += `\n**Avoid Common Issues:**\n`;
        insights.commonIssues.forEach((issue)=>{
            instructions += `- Prevent: ${issue}\n`;
        });
    }
    if (insights.recommendations.length > 0) {
        instructions += `\n**Performance Recommendations:**\n`;
        insights.recommendations.forEach((rec)=>{
            instructions += `- ${rec}\n`;
        });
    }
    instructions += `\n**Performance Benchmarks:**\n`;
    instructions += `- Target Quality Score: ${Math.max(insights.averageQuality + 0.5, 8)}/10\n`;
    instructions += `- Target Engagement: ${Math.max(insights.averageEngagement + 0.5, 8)}/10\n`;
    return instructions;
}
function exportAnalyticsData() {
    const designs = Array.from(designAnalytics.values());
    const patterns = Array.from(performancePatterns.entries()).map(([key, data])=>({
            key,
            data
        }));
    // Calculate summary statistics
    const totalDesigns = designs.length;
    const averageQuality = designs.reduce((sum, d)=>sum + d.metrics.qualityScore, 0) / totalDesigns;
    const businessTypeCounts = designs.reduce((acc, d)=>{
        acc[d.businessType] = (acc[d.businessType] || 0) + 1;
        return acc;
    }, {});
    const platformCounts = designs.reduce((acc, d)=>{
        acc[d.platform] = (acc[d.platform] || 0) + 1;
        return acc;
    }, {});
    const topBusinessTypes = Object.entries(businessTypeCounts).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([type])=>type);
    const topPlatforms = Object.entries(platformCounts).sort((a, b)=>b[1] - a[1]).slice(0, 5).map(([platform])=>platform);
    return {
        designs,
        patterns,
        summary: {
            totalDesigns,
            averageQuality: Math.round(averageQuality * 10) / 10,
            topBusinessTypes,
            topPlatforms
        }
    };
}
}}),
"[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"407a3fd27c1f957f774b35d49f6b2945aba9bd25f9":"generatePostFromProfile"},"",""] */ __turbopack_context__.s({
    "generatePostFromProfile": (()=>generatePostFromProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview This file defines a Genkit flow for generating a daily social media post.
 *
 * It takes into account business type, location, brand voice, current weather, and local events to create engaging content.
 * @exports generatePostFromProfile - The main function to generate a post.
 * @exports GeneratePostFromProfileInput - The input type for the generation flow.
 * @exports GeneratePostFromProfileOutput - The output type for the generation flow.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/tools/local-data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/tools/enhanced-local-data.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$ai$2d$prompt$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-ai-prompt.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$hashtag$2d$strategy$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/hashtag-strategy.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/trending-topics.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/real-time-trends-integration.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/intelligent-context-selector.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/human-content-generator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-design-prompts.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analysis.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-quality.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-trends.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analytics.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const GeneratePostFromProfileInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The type of business (e.g., restaurant, salon).'),
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The location of the business (city, state).'),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The visual style of the brand (e.g., modern, vintage).'),
    writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The brand voice of the business.'),
    contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The content themes of the business.'),
    logoDataUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe("The business logo as a data URI that must include a MIME type and use Base64 encoding. Expected format: 'data:<mimetype>;base64,<encoded_data>'."),
    designExamples: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).optional().describe("Array of design example data URIs to use as style reference for generating similar designs."),
    dayOfWeek: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The day of the week for the post.'),
    currentDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The current date for the post.'),
    variants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    })).describe('An array of platform and aspect ratio variants to generate.'),
    primaryColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The primary brand color in HSL format.'),
    accentColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The accent brand color in HSL format.'),
    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('The background brand color in HSL format.'),
    // New detailed fields for richer content
    services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of key services or products.'),
    targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A description of the target audience.'),
    keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of key features or selling points.'),
    competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional().describe('A newline-separated list of competitive advantages.'),
    // Brand consistency preferences
    brandConsistency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        strictConsistency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional(),
        followBrandColors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().optional()
    }).optional().describe('Brand consistency preferences for content generation.')
});
const GeneratePostFromProfileOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The primary generated social media post content (the caption).'),
    catchyWords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),
    subheadline: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),
    callToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),
    hashtags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Strategically selected hashtags for the post.'),
    contentVariants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Alternative caption variant.'),
        approach: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),
        rationale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Why this variant might perform well.')
    })).optional().describe('Alternative caption variants for A/B testing.'),
    hashtagAnalysis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        trending: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Trending hashtags for reach.'),
        niche: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Industry-specific hashtags.'),
        location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Location-based hashtags.'),
        community: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Community engagement hashtags.')
    }).optional().describe('Categorized hashtag strategy.'),
    marketIntelligence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        trending_topics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            topic: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            relevanceScore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            engagement_potential: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).describe('Current trending topics relevant to the business.'),
        competitor_insights: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            competitor_name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            content_gap: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            differentiation_opportunity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).describe('Competitor analysis and differentiation opportunities.'),
        cultural_context: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            cultural_nuances: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()),
            local_customs: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string())
        }).describe('Cultural and location-specific context.'),
        viral_patterns: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Content patterns that drive viral engagement.'),
        engagement_triggers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()).describe('Psychological triggers for maximum engagement.')
    }).optional().describe('Advanced market intelligence and optimization data.'),
    localContext: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        weather: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            temperature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            condition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            business_impact: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            content_opportunities: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string())
        }).optional().describe('Current weather context and business opportunities.'),
        events: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            relevance_score: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].number(),
            start_date: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
        })).optional().describe('Relevant local events for content integration.')
    }).optional().describe('Local context including weather and events.'),
    variants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
        platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
        imageUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string()
    }))
});
async function generatePostFromProfile(input) {
    return generatePostFromProfileFlow(input);
}
/**
 * Combines catchy words, subheadline, and call to action into a single text for image overlay
 */ function combineTextComponents(catchyWords, subheadline, callToAction) {
    const components = [
        catchyWords
    ];
    if (subheadline && subheadline.trim()) {
        components.push(subheadline.trim());
    }
    if (callToAction && callToAction.trim()) {
        components.push(callToAction.trim());
    }
    return components.join('\n');
}
// Define the enhanced text generation prompt
const enhancedTextGenPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
    name: 'enhancedGeneratePostTextPrompt',
    input: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            writingTone: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            contentThemes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            dayOfWeek: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            currentDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string(),
            platform: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            services: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            targetAudience: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            keyFeatures: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional(),
            competitiveAdvantages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().optional()
        })
    },
    output: {
        schema: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
            content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The primary generated social media post content (the caption).'),
            catchyWords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Catchy words for the image (max 5 words). Must be directly related to the specific business services/products, not generic phrases. Required for ALL posts.'),
            subheadline: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional subheadline (max 14 words). Add only when it would make the post more effective based on marketing strategy.'),
            callToAction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('Optional call to action. Add only when it would drive better engagement or conversions based on marketing strategy.'),
            hashtags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Strategically selected hashtags for the post.'),
            contentVariants: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
                content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Alternative caption variant.'),
                approach: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The copywriting approach used (e.g., AIDA, PAS, Storytelling).'),
                rationale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('Why this variant might perform well.')
            })).describe('2-3 alternative caption variants for A/B testing.')
        })
    },
    tools: [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getWeatherTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEventsTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnhancedWeatherTool"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$tools$2f$enhanced$2d$local$2d$data$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnhancedEventsTool"]
    ],
    prompt: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$ai$2d$prompt$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ADVANCED_AI_PROMPT"]
});
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                if (e.message && e.message.includes('429')) {
                    throw new Error("You've exceeded your request limit for the AI model. Please check your plan or try again later.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
const getMimeTypeFromDataURI = (dataURI)=>{
    const match = dataURI.match(/^data:(.*?);/);
    return match ? match[1] : 'application/octet-stream'; // Default if no match
};
// Helper function to generate an image for a single variant with advanced design principles
async function generateImageForVariant(variant, input, textOutput) {
    const colorInstructions = `The brand's color palette is: Primary HSL(${input.primaryColor}), Accent HSL(${input.accentColor}), Background HSL(${input.backgroundColor}). Please use these colors in the design.`;
    // Determine consistency level based on preferences
    const isStrictConsistency = input.brandConsistency?.strictConsistency ?? false;
    const followBrandColors = input.brandConsistency?.followBrandColors ?? true;
    // Get platform-specific guidelines
    const platformGuidelines = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLATFORM_SPECIFIC_GUIDELINES"][variant.platform] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLATFORM_SPECIFIC_GUIDELINES"].instagram;
    // Get business-specific design DNA
    const businessDNA = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"][input.businessType] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"].default;
    // Get current design trends
    let trendInstructions = '';
    try {
        const trends = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedDesignTrends"])(input.businessType, variant.platform, input.targetAudience, input.businessType);
        trendInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$trends$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateTrendInstructions"])(trends, variant.platform);
    } catch (error) {
        console.warn('Failed to get design trends, continuing without:', error);
    }
    // Get performance-optimized instructions
    const performanceInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePerformanceOptimizedInstructions"])(input.businessType, variant.platform, input.visualStyle);
    let imagePrompt = `You are a world-class creative director and visual designer with expertise in social media marketing, brand design, and visual psychology.

    **DESIGN BRIEF:**
    Create a professional, high-impact social media design for a ${input.businessType} business.

    🎯 CRITICAL ASPECT RATIO REQUIREMENT:
    - MUST generate image in exactly ${variant.aspectRatio} aspect ratio
    - Platform: ${variant.platform} (optimized for ${variant.aspectRatio} format)
    - Dimensions: ${variant.aspectRatio === '16:9' ? '1920x1080px (landscape)' : variant.aspectRatio === '9:16' ? '1080x1920px (portrait)' : '1080x1080px (square)'}
    - DO NOT generate square images unless aspect ratio is specifically 1:1
    - DO NOT generate portrait images unless aspect ratio is specifically 9:16
    - FOR FACEBOOK AND LINKEDIN: Generate landscape (16:9) format images

    Target Platform: ${variant.platform} | Aspect Ratio: ${variant.aspectRatio}
    Visual Style: ${input.visualStyle} | Location: ${input.location}

    ⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:
    - MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)
    - SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes
    - TINY TEXT PRECISION: Every character sharp and legible even when font size is very small
    - HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness
    - PERFECT ANATOMY: Complete, symmetrical faces with natural expressions
    - SHARP DETAILS: Crystal-clear textures, no blur or artifacts
    - PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows
    - PREMIUM COMPOSITION: Golden ratio layouts with perfect balance
    - ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors

    ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ADVANCED_DESIGN_PRINCIPLES"]}

    ${platformGuidelines}

    ${businessDNA}

    ${trendInstructions}

    ${performanceInstructions}

    **BRAND INTEGRATION:**
    - **Brand Colors:** ${followBrandColors && input.primaryColor ? colorInstructions : 'Use a visually appealing and appropriate palette that fits the business type.'}
    - **Logo Placement:** The provided logo must be integrated naturally into the design. It should be clearly visible but not overpower the main subject. For example, it could be on a product, a sign, or as a subtle watermark.

    **CONTENT REQUIREMENTS:**
    - **Primary Subject:** The core subject of the image should be directly inspired by: "${textOutput.catchyWords}"
    - **Text Overlay:** The following text must be overlaid on the image in a stylish, readable font: "${combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction)}". It is critical that the text is clearly readable, well-composed, and not cut off or truncated. The entire text must be visible.
    - **Cultural Representation:** If the image includes people, they should be representative of the location: ${input.location}. For example, for a post in Africa, depict Black people; for Europe, White people; for the USA, a diverse mix of ethnicities. Be thoughtful and authentic in your representation.

    ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["QUALITY_ENHANCEMENT_INSTRUCTIONS"]}`;
    // Intelligent design examples processing
    let designDNA = '';
    let selectedExamples = [];
    if (input.designExamples && input.designExamples.length > 0) {
        try {
            // Analyze design examples for intelligent processing
            const analyses = [];
            for (const example of input.designExamples.slice(0, 5)){
                try {
                    const analysis = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeDesignExample"])(example, input.businessType, variant.platform, `${input.visualStyle} design for ${textOutput.imageText}`);
                    analyses.push(analysis);
                } catch (error) {
                    console.warn('Design analysis failed for example, skipping:', error);
                }
            }
            if (analyses.length > 0) {
                // Extract design DNA from analyzed examples
                designDNA = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractDesignDNA"])(analyses);
                // Select optimal examples based on analysis
                selectedExamples = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectOptimalDesignExamples"])(input.designExamples, analyses, textOutput.imageText, variant.platform, isStrictConsistency ? 3 : 1);
            } else {
                // Fallback to original logic if analysis fails
                selectedExamples = isStrictConsistency ? input.designExamples : [
                    input.designExamples[Math.floor(Math.random() * input.designExamples.length)]
                ];
            }
        } catch (error) {
            console.warn('Design analysis system failed, using fallback:', error);
            selectedExamples = isStrictConsistency ? input.designExamples : [
                input.designExamples[Math.floor(Math.random() * input.designExamples.length)]
            ];
        }
        // Add design consistency instructions based on analysis
        if (isStrictConsistency) {
            imagePrompt += `\n    **STRICT STYLE REFERENCE:**
      Use the provided design examples as strict style reference. Closely match the visual aesthetic, color scheme, typography, layout patterns, and overall design approach of the reference designs. Create content that looks very similar to the uploaded examples while incorporating the new text and subject matter.

      ${designDNA}`;
        } else {
            imagePrompt += `\n    **STYLE INSPIRATION:**
      Use the provided design examples as loose inspiration for the overall aesthetic and mood, but feel free to create more varied and creative designs while maintaining the brand essence.

      ${designDNA}

      **CREATIVE VARIATION:** Feel free to experiment with different layouts, compositions, and design elements to create fresh, engaging content that avoids repetitive appearance while maintaining brand recognition.

      **UNIQUENESS REQUIREMENT:** This design must be visually distinct from previous generations. Use different:
      - Layout compositions (grid vs. asymmetrical vs. centered)
      - Color emphasis and gradients
      - Typography placement and hierarchy
      - Visual elements and imagery styles
      - Background treatments and textures

      **GENERATION ID:** ${Date.now()}_${Math.random().toString(36).substr(2, 9)} - Use this unique identifier to ensure no two designs are identical.`;
        }
    }
    // Build prompt parts array
    const promptParts = [
        {
            text: imagePrompt
        }
    ];
    // Add logo
    promptParts.push({
        media: {
            url: input.logoDataUrl,
            contentType: getMimeTypeFromDataURI(input.logoDataUrl)
        }
    });
    // Add selected design examples
    selectedExamples.forEach((example)=>{
        promptParts.push({
            media: {
                url: example,
                contentType: getMimeTypeFromDataURI(example)
            }
        });
    });
    // Generate initial design
    let finalImageUrl = '';
    let attempts = 0;
    const maxAttempts = 2; // Limit attempts to avoid excessive API calls
    while(attempts < maxAttempts){
        attempts++;
        try {
            const { media } = await generateWithRetry({
                model: 'googleai/gemini-2.0-flash-preview-image-generation',
                prompt: promptParts,
                config: {
                    responseModalities: [
                        'TEXT',
                        'IMAGE'
                    ]
                }
            });
            let imageUrl = media?.url ?? '';
            if (!imageUrl) {
                throw new Error('No image generated');
            }
            // Apply aspect ratio correction for non-square platforms
            const { cropImageFromUrl, needsAspectRatioCorrection } = await __turbopack_context__.r("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            if (needsAspectRatioCorrection(variant.platform)) {
                console.log(`🖼️ Applying aspect ratio correction for ${variant.platform}...`);
                try {
                    imageUrl = await cropImageFromUrl(imageUrl, variant.platform);
                    console.log(`✅ Image cropped successfully for ${variant.platform}`);
                } catch (cropError) {
                    console.warn('⚠️ Image cropping failed, using original:', cropError);
                // Continue with original image if cropping fails
                }
            }
            // Quality validation for first attempt
            if (attempts === 1) {
                try {
                    const quality = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["assessDesignQuality"])(imageUrl, input.businessType, variant.platform, input.visualStyle, followBrandColors && input.primaryColor ? colorInstructions : undefined, `Create engaging design for: ${textOutput.catchyWords}`);
                    // If quality is acceptable, use this design
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["meetsQualityStandards"])(quality, 7)) {
                        finalImageUrl = imageUrl;
                        break;
                    }
                    // If quality is poor and we have attempts left, try to improve
                    if (attempts < maxAttempts) {
                        console.log(`Design quality score: ${quality.overall.score}/10. Attempting improvement...`);
                        // Add improvement instructions to prompt
                        const improvementInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateImprovementPrompt"])(quality);
                        const improvedPrompt = `${imagePrompt}\n\n${improvementInstructions}`;
                        promptParts[0] = {
                            text: improvedPrompt
                        };
                        continue;
                    } else {
                        // Use the design even if quality is subpar (better than nothing)
                        finalImageUrl = imageUrl;
                        break;
                    }
                } catch (qualityError) {
                    console.warn('Quality assessment failed, using generated design:', qualityError);
                    finalImageUrl = imageUrl;
                    break;
                }
            } else {
                // For subsequent attempts, use the result
                finalImageUrl = imageUrl;
                break;
            }
        } catch (error) {
            console.error(`Design generation attempt ${attempts} failed:`, error);
            if (attempts === maxAttempts) {
                throw error;
            }
        }
    }
    // Record design generation for analytics
    if (finalImageUrl) {
        try {
            const designId = `design_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analytics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["recordDesignGeneration"])(designId, input.businessType, variant.platform, input.visualStyle, 9.2, {
                colorPalette: input.primaryColor ? [
                    input.primaryColor,
                    input.accentColor,
                    input.backgroundColor
                ].filter(Boolean) : [],
                typography: 'Modern social media optimized',
                composition: variant.aspectRatio,
                trends: selectedExamples.length > 0 ? [
                    'design-examples-based'
                ] : [
                    'ai-generated'
                ],
                businessDNA: businessDNA.substring(0, 100) // Truncate for storage
            }, {
                engagement: 8,
                brandAlignment: followBrandColors ? 9 : 7,
                technicalQuality: 8,
                trendRelevance: trendInstructions ? 8 : 6
            });
        } catch (analyticsError) {
            console.warn('Failed to record design analytics:', analyticsError);
        }
    }
    return {
        platform: variant.platform,
        imageUrl: finalImageUrl
    };
}
const generatePostFromProfileFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generatePostFromProfileFlow',
    inputSchema: GeneratePostFromProfileInputSchema,
    outputSchema: GeneratePostFromProfileOutputSchema
}, async (input)=>{
    // Determine the primary platform for optimization
    const primaryPlatform = input.variants[0]?.platform || 'instagram';
    // Step 1: Intelligent Context Analysis - Determine what information is relevant
    const contextRelevance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectRelevantContext"])(input.businessType, input.location, primaryPlatform, input.contentThemes, input.dayOfWeek);
    console.log(`🧠 Context Analysis for ${input.businessType} in ${input.location}:`);
    console.log(`   Weather: ${contextRelevance.weather.priority} - ${contextRelevance.weather.relevanceReason}`);
    console.log(`   Events: ${contextRelevance.events.priority} - ${contextRelevance.events.relevanceReason}`);
    console.log(`   Trends: ${contextRelevance.trends.priority} - ${contextRelevance.trends.relevanceReason}`);
    console.log(`   Culture: ${contextRelevance.cultural.priority} - ${contextRelevance.cultural.relevanceReason}`);
    // Step 2: Fetch Real-Time Trending Topics (always useful)
    const realTimeTrends = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRealTimeTrendingTopics"])(input.businessType, input.location, primaryPlatform);
    // Step 3: Fetch Local Context (Weather + Events) - but filter intelligently
    const rawLocalContext = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$real$2d$time$2d$trends$2d$integration$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchLocalContext"])(input.location, input.businessType);
    // Step 4: Generate Market Intelligence for Advanced Content
    const marketIntelligence = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$trending$2d$topics$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateMarketIntelligence"])(input.businessType, input.location, primaryPlatform, input.services);
    // Step 5: Intelligently Filter Context Data
    const filteredContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$intelligent$2d$context$2d$selector$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["filterContextData"])(contextRelevance, {
        weather: rawLocalContext.weather,
        events: rawLocalContext.events,
        trends: realTimeTrends,
        cultural: marketIntelligence.cultural_context
    });
    // Enhance market intelligence with filtered real-time trends
    marketIntelligence.trending_topics = [
        ...(filteredContext.selectedTrends || []).slice(0, 3),
        ...marketIntelligence.trending_topics.slice(0, 2)
    ];
    // Step 6: Generate Human-like Content Techniques
    const humanizationTechniques = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateHumanizationTechniques"])(input.businessType, input.writingTone, input.location);
    // Step 7: Generate Traffic-Driving Elements
    const trafficElements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$human$2d$content$2d$generator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateTrafficDrivingElements"])(input.businessType, primaryPlatform, input.targetAudience);
    // Step 8: Generate Enhanced Text Content with Intelligent Context
    const { output: textOutput } = await enhancedTextGenPrompt({
        businessType: input.businessType,
        location: input.location,
        writingTone: input.writingTone,
        contentThemes: input.contentThemes,
        dayOfWeek: input.dayOfWeek,
        currentDate: input.currentDate,
        platform: primaryPlatform,
        services: input.services,
        targetAudience: input.targetAudience,
        keyFeatures: input.keyFeatures,
        competitiveAdvantages: input.competitiveAdvantages,
        // Add intelligent context instructions
        contextInstructions: filteredContext.contextInstructions,
        selectedWeather: filteredContext.selectedWeather,
        selectedEvents: filteredContext.selectedEvents,
        selectedTrends: filteredContext.selectedTrends,
        selectedCultural: filteredContext.selectedCultural
    });
    if (!textOutput) {
        throw new Error('Failed to generate advanced AI post content.');
    }
    // Step 9: Generate Strategic Hashtag Analysis
    const hashtagStrategy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$hashtag$2d$strategy$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateHashtagStrategy"])(input.businessType, input.location, primaryPlatform, input.services, input.targetAudience);
    // Step 10: Generate Image for each variant in parallel
    const imagePromises = input.variants.map((variant)=>generateImageForVariant(variant, input, textOutput));
    const variants = await Promise.all(imagePromises);
    // Step 11: Combine text components for image overlay
    const combinedImageText = combineTextComponents(textOutput.catchyWords, textOutput.subheadline, textOutput.callToAction);
    // Step 12: Combine results with intelligently selected context
    return {
        content: textOutput.content,
        catchyWords: textOutput.catchyWords,
        subheadline: textOutput.subheadline,
        callToAction: textOutput.callToAction,
        hashtags: textOutput.hashtags,
        contentVariants: textOutput.contentVariants,
        hashtagAnalysis: {
            trending: hashtagStrategy.trending,
            niche: hashtagStrategy.niche,
            location: hashtagStrategy.location,
            community: hashtagStrategy.community
        },
        // Advanced AI features metadata (for future UI display)
        marketIntelligence: {
            trending_topics: marketIntelligence.trending_topics.slice(0, 3),
            competitor_insights: marketIntelligence.competitor_insights.slice(0, 2),
            cultural_context: marketIntelligence.cultural_context,
            viral_patterns: marketIntelligence.viral_content_patterns.slice(0, 3),
            engagement_triggers: marketIntelligence.engagement_triggers.slice(0, 3)
        },
        // Intelligently selected local context
        localContext: {
            weather: filteredContext.selectedWeather,
            events: filteredContext.selectedEvents,
            contextRelevance: {
                weather: contextRelevance.weather.priority,
                events: contextRelevance.events.priority,
                weatherReason: contextRelevance.weather.relevanceReason,
                eventsReason: contextRelevance.events.relevanceReason
            }
        },
        variants
    };
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generatePostFromProfile
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generatePostFromProfile, "407a3fd27c1f957f774b35d49f6b2945aba9bd25f9", null);
}}),
"[project]/src/ai/flows/generate-video-post.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/ai/flows/generate-video-post.ts
/* __next_internal_action_entry_do_not_use__ [{"4048656ade35017f5ce681934da6ceba194cac247b":"generateVideoPost"},"",""] */ __turbopack_context__.s({
    "generateVideoPost": (()=>generateVideoPost)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview A Genkit flow for generating a short promotional video for a social media post.
 *
 * This flow utilizes a text-to-video model to create dynamic content based on brand information,
 * local context, and specific post details.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/genkit/lib/common.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
// Define the input schema for the video generation flow.
const GenerateVideoInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    businessType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The type of business (e.g., restaurant, salon).'),
    location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The location of the business (city, state).'),
    visualStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The visual style of the brand (e.g., modern, vintage).'),
    imageText: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A brief, catchy headline for the video.'),
    postContent: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The full text content of the social media post for additional context.')
});
// Define the output schema for the video generation flow.
const GenerateVideoOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    videoUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$genkit$2f$lib$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The data URI of the generated video file.')
});
async function generateVideoPost(input) {
    return generateVideoPostFlow(input);
}
/**
 * Helper function to download video and convert to data URI
 */ async function videoToDataURI(videoPart) {
    if (!videoPart.media || !videoPart.media.url) {
        throw new Error('Media URL not found in video part.');
    }
    const fetch = (await __turbopack_context__.r("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i)).default;
    // Add API key before fetching the video.
    const videoDownloadResponse = await fetch(`${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`);
    if (!videoDownloadResponse.ok) {
        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);
    }
    const videoBuffer = await videoDownloadResponse.arrayBuffer();
    const base64Video = Buffer.from(videoBuffer).toString('base64');
    // Default to video/mp4 if contentType is not provided
    const contentType = videoPart.media.contentType || 'video/mp4';
    return `data:${contentType};base64,${base64Video}`;
}
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
/**
 * The core Genkit flow for generating a video post.
 */ const generateVideoPostFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generateVideoPostFlow',
    inputSchema: GenerateVideoInputSchema,
    outputSchema: GenerateVideoOutputSchema
}, async (input)=>{
    const videoPrompt = `Create a short, engaging promotional video with sound for a ${input.businessType} in ${input.location}.
The visual style should be ${input.visualStyle}.
The video should be visually appealing and suitable for a social media post.

The main headline for the video is: "${input.imageText}".

For additional context, here is the full post content that will accompany the video: "${input.postContent}".

Generate a video that is cinematically interesting, has relevant sound, and captures the essence of the post content.`;
    try {
        const result = await generateWithRetry({
            model: 'googleai/veo-3.0-generate-preview',
            prompt: videoPrompt
        });
        let operation = result.operation;
        if (!operation) {
            throw new Error('Expected the model to return an operation');
        }
        // Poll for completion
        while(!operation.done){
            await new Promise((resolve)=>setTimeout(resolve, 5000)); // wait 5s
            operation = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].checkOperation(operation);
        }
        if (operation.error) {
            console.error("Video generation operation failed", operation.error);
            throw new Error(`Video generation failed. Please try again. Error: ${operation.error.message}`);
        }
        // Relaxed check for the video part
        const videoPart = operation.output?.message?.content.find((p)=>!!p.media);
        if (!videoPart || !videoPart.media) {
            throw new Error('No video was generated in the operation result.');
        }
        const videoDataUrl = await videoToDataURI(videoPart);
        return {
            videoUrl: videoDataUrl
        };
    } catch (e) {
        console.error("Error during video generation:", e);
        throw new Error(e.message || "Video generation failed. The model may be overloaded. Please try again in a few moments.");
    }
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generateVideoPost
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateVideoPost, "4048656ade35017f5ce681934da6ceba194cac247b", null);
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/ai/flows/generate-creative-asset.ts
/* __next_internal_action_entry_do_not_use__ [{"409e36c34489ad587473a8902e0f3440bb36957de3":"generateCreativeAsset"},"",""] */ __turbopack_context__.s({
    "generateCreativeAsset": (()=>generateCreativeAsset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * @fileOverview A Genkit flow for generating a creative asset (image or video)
 * based on a user's prompt, an optional reference image, and brand profile settings.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/genkit.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/prompts/advanced-design-prompts.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-analysis.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/utils/design-quality.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
// Define the input schema for the creative asset generation flow.
const CreativeAssetInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    prompt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('The main text prompt describing the desired asset.'),
    outputType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
        'image',
        'video'
    ]).describe('The type of asset to generate.'),
    referenceAssetUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('An optional reference image or video as a data URI.'),
    useBrandProfile: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].boolean().describe('Whether to apply the brand profile.'),
    brandProfile: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].custom().nullable().describe('The brand profile object.'),
    maskDataUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().optional().describe('An optional mask image for inpainting as a data URI.'),
    aspectRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].enum([
        '16:9',
        '9:16'
    ]).optional().describe('The aspect ratio for video generation.')
});
// Define the output schema for the creative asset generation flow.
const CreativeAssetOutputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].object({
    imageUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('The data URI of the generated image, if applicable.'),
    videoUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().nullable().describe('The data URI of the generated video, if applicable.'),
    aiExplanation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"].string().describe('A brief explanation from the AI about what it created.')
});
async function generateCreativeAsset(input) {
    return generateCreativeAssetFlow(input);
}
/**
 * Helper function to download video and convert to data URI
 */ async function videoToDataURI(videoPart) {
    if (!videoPart.media || !videoPart.media.url) {
        throw new Error('Media URL not found in video part.');
    }
    const fetch = (await __turbopack_context__.r("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i)).default;
    const videoDownloadResponse = await fetch(`${videoPart.media.url}&key=${process.env.GEMINI_API_KEY}`);
    if (!videoDownloadResponse.ok) {
        throw new Error(`Failed to download video: ${videoDownloadResponse.statusText}`);
    }
    const videoBuffer = await videoDownloadResponse.arrayBuffer();
    const base64Video = Buffer.from(videoBuffer).toString('base64');
    const contentType = videoPart.media.contentType || 'video/mp4';
    return `data:${contentType};base64,${base64Video}`;
}
/**
 * Extracts text in quotes and the remaining prompt.
 */ const extractQuotedText = (prompt)=>{
    const quoteRegex = /"([^"]*)"/;
    const match = prompt.match(quoteRegex);
    if (match) {
        return {
            imageText: match[1],
            remainingPrompt: prompt.replace(quoteRegex, '').trim()
        };
    }
    return {
        imageText: null,
        remainingPrompt: prompt
    };
};
/**
 * Wraps ai.generate with retry logic for 503 errors.
 */ async function generateWithRetry(request, retries = 3, delay = 1000) {
    for(let i = 0; i < retries; i++){
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].generate(request);
            return result;
        } catch (e) {
            if (e.message && e.message.includes('503') && i < retries - 1) {
                console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            } else {
                if (e.message && e.message.includes('503')) {
                    throw new Error("The AI model is currently overloaded. Please try again in a few moments.");
                }
                if (e.message && e.message.includes('429')) {
                    throw new Error("You've exceeded your request limit for the AI model. Please check your plan or try again later.");
                }
                throw e; // Rethrow other errors immediately
            }
        }
    }
    // This line should not be reachable if retries are configured, but as a fallback:
    throw new Error("The AI model is currently overloaded after multiple retries. Please try again later.");
}
const getMimeTypeFromDataURI = (dataURI)=>{
    const match = dataURI.match(/^data:(.*?);/);
    return match ? match[1] : 'application/octet-stream'; // Default if no match
};
/**
 * The core Genkit flow for generating a creative asset.
 */ const generateCreativeAssetFlow = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].defineFlow({
    name: 'generateCreativeAssetFlow',
    inputSchema: CreativeAssetInputSchema,
    outputSchema: CreativeAssetOutputSchema
}, async (input)=>{
    const promptParts = [];
    let textPrompt = '';
    const { imageText, remainingPrompt } = extractQuotedText(input.prompt);
    if (input.maskDataUrl && input.referenceAssetUrl) {
        // This is an inpainting request.
        textPrompt = `You are an expert image editor performing a precise inpainting task.
You will be given an original image, a mask, and a text prompt.
Your task is to modify the original image *only* in the areas designated by the black region of the mask.
The rest of the image must remain absolutely unchanged.
If the prompt is a "remove" or "delete" instruction, perform a seamless, content-aware fill to replace the masked object with a photorealistic background that matches the surrounding area.
The user's instruction for the masked area is: "${remainingPrompt}".
Recreate the content within the black-masked region based on this instruction, ensuring a seamless and photorealistic blend with the surrounding, untouched areas of the image.`;
        promptParts.push({
            text: textPrompt
        });
        promptParts.push({
            media: {
                url: input.referenceAssetUrl,
                contentType: getMimeTypeFromDataURI(input.referenceAssetUrl)
            }
        });
        promptParts.push({
            media: {
                url: input.maskDataUrl,
                contentType: getMimeTypeFromDataURI(input.maskDataUrl)
            }
        });
    } else if (input.referenceAssetUrl) {
        // This is a generation prompt with a reference asset (image or video).
        let referencePrompt = `You are an expert creative director specializing in high-end advertisements. You will be given a reference asset and a text prompt with instructions.
Your task is to generate a new asset that is inspired by the reference asset and follows the new instructions.

Your primary goal is to intelligently interpret the user's request, considering the provided reference asset. Do not just copy the reference.
Analyze the user's prompt for common editing terminology and apply it creatively. For example:
- If asked to "change the background," intelligently isolate the main subject and replace the background with a new one that matches the prompt, preserving the foreground subject.
- If asked to "make the logo bigger" or "change the text color," perform those specific edits while maintaining the overall composition.
- If the prompt is more general, use the reference asset for style, color, and subject inspiration to create a new, distinct asset.

The user's instruction is: "${remainingPrompt}"`;
        if (imageText) {
            referencePrompt += `\n\n**Explicit Text Overlay:** The user has provided specific text in quotes: "${imageText}". You MUST overlay this text on the image. If there was existing text, replace it. Ensure the new text is readable and well-composed.`;
        }
        if (input.outputType === 'video') {
            referencePrompt += `\n\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (imageText) {
                referencePrompt += `\n\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
        }
        if (input.useBrandProfile && input.brandProfile) {
            const bp = input.brandProfile;
            let brandGuidelines = '\n\n**Brand Guidelines:**';
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
                brandGuidelines += ` A logo has also been provided. Integrate it naturally into the new design.`;
            } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {
                brandGuidelines += ` Create a design that represents the brand identity (SVG logo format not supported by AI model).`;
            }
            referencePrompt += brandGuidelines;
        }
        textPrompt = referencePrompt;
        if (textPrompt) {
            promptParts.push({
                text: textPrompt
            });
        }
        promptParts.push({
            media: {
                url: input.referenceAssetUrl,
                contentType: getMimeTypeFromDataURI(input.referenceAssetUrl)
            }
        });
    } else if (input.useBrandProfile && input.brandProfile) {
        // This is a new, on-brand asset generation with advanced design principles.
        const bp = input.brandProfile;
        // Get business-specific design DNA
        const businessDNA = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"][bp.businessType] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BUSINESS_TYPE_DESIGN_DNA"].default;
        let onBrandPrompt = `You are a world-class creative director and visual designer with expertise in social media marketing, brand design, and visual psychology.

**CREATIVE BRIEF:**
Create a professional, high-impact social media ${input.outputType} for a ${bp.businessType} business.

🎯 CRITICAL ASPECT RATIO REQUIREMENT:
${input.aspectRatio ? `- MUST generate ${input.outputType} in exactly ${input.aspectRatio} aspect ratio
- Dimensions: ${input.aspectRatio === '16:9' ? '1920x1080px (landscape)' : input.aspectRatio === '9:16' ? '1080x1920px (portrait)' : '1080x1080px (square)'}
- DO NOT generate square images unless aspect ratio is specifically 1:1
- DO NOT generate portrait images unless aspect ratio is specifically 9:16` : '- Generate in appropriate aspect ratio for the content'}

Visual Style: ${bp.visualStyle} | Writing Tone: ${bp.writingTone}
Content Themes: ${bp.contentThemes}

${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ADVANCED_DESIGN_PRINCIPLES"]}

${businessDNA}

**BRAND INTEGRATION:**
- **Visual Style:** The design must be ${bp.visualStyle}. The writing tone is ${bp.writingTone} and content should align with these themes: ${bp.contentThemes}.
- **Subject/Theme:** The core subject of the ${input.outputType} should be: "${remainingPrompt}".

**MANDATORY BRAND COLORS:**
${bp.primaryColor ? `- **Primary Color: ${bp.primaryColor}** - Use this as the dominant color (60% of design)` : ''}
${bp.accentColor ? `- **Accent Color: ${bp.accentColor}** - Use for highlights and important elements (30% of design)` : ''}
${bp.backgroundColor ? `- **Background Color: ${bp.backgroundColor}** - Use as base background color (10% of design)` : ''}
${bp.primaryColor || bp.accentColor || bp.backgroundColor ? '- **CRITICAL:** These brand colors MUST be prominently featured and used exactly as specified' : '- Use colors appropriate for the business type and visual style'}

${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$prompts$2f$advanced$2d$design$2d$prompts$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["QUALITY_ENHANCEMENT_INSTRUCTIONS"]}`;
        // Intelligent design examples processing
        let designDNA = '';
        let selectedExamples = [];
        if (bp.designExamples && bp.designExamples.length > 0) {
            try {
                // Analyze design examples for intelligent processing
                const analyses = [];
                for (const example of bp.designExamples.slice(0, 3)){
                    try {
                        const analysis = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeDesignExample"])(example, bp.businessType, 'creative-studio', `${bp.visualStyle} ${input.outputType} for ${remainingPrompt}`);
                        analyses.push(analysis);
                    } catch (error) {
                        console.warn('Design analysis failed for example, skipping:', error);
                    }
                }
                if (analyses.length > 0) {
                    // Extract design DNA from analyzed examples
                    designDNA = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractDesignDNA"])(analyses);
                    // Select optimal examples based on analysis
                    selectedExamples = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$analysis$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["selectOptimalDesignExamples"])(bp.designExamples, analyses, remainingPrompt, 'creative-studio', 2);
                } else {
                    selectedExamples = bp.designExamples.slice(0, 2);
                }
            } catch (error) {
                console.warn('Design analysis system failed, using fallback:', error);
                selectedExamples = bp.designExamples.slice(0, 2);
            }
            onBrandPrompt += `\n**STYLE REFERENCE:**
Use the provided design examples as style reference to create a similar visual aesthetic, color scheme, typography, and overall design approach. Match the style, mood, and visual characteristics of the reference designs while creating new content.

${designDNA}`;
        }
        if (input.outputType === 'image') {
            onBrandPrompt += `\n- **Text Overlay Requirements:** ${imageText ? `
                  * Display this EXACT text: "${imageText}"
                  * Use ENGLISH ONLY - no foreign languages, symbols, or corrupted characters
                  * Make text LARGE and BOLD for mobile readability
                  * Apply high contrast (minimum 4.5:1 ratio) between text and background
                  * Add text shadows, outlines, or semi-transparent backgrounds for readability
                  * Position text using rule of thirds for optimal composition
                  * Ensure text is the primary focal point of the design` : 'No text should be added to the asset.'}`;
            onBrandPrompt += `\n- **Logo Placement:** The provided logo must be integrated naturally into the design (e.g., on a product, a sign, or as a subtle watermark).`;
            onBrandPrompt += `\n- **Critical Language Rule:** ALL text must be in clear, readable ENGLISH only. Never use foreign languages, corrupted text, or unreadable symbols.`;
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
            }
            textPrompt = onBrandPrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        } else {
            onBrandPrompt += `\n- **Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (input.aspectRatio === '16:9') {
                onBrandPrompt += ' The video should have relevant sound.';
            }
            if (imageText) {
                onBrandPrompt += `\n- **Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
            if (bp.logoDataUrl && !bp.logoDataUrl.includes('image/svg+xml')) {
                onBrandPrompt += `\n- **Logo Placement:** The provided logo must be integrated naturally into the design.`;
                promptParts.push({
                    media: {
                        url: bp.logoDataUrl,
                        contentType: getMimeTypeFromDataURI(bp.logoDataUrl)
                    }
                });
            } else if (bp.logoDataUrl && bp.logoDataUrl.includes('image/svg+xml')) {
                onBrandPrompt += `\n- **Brand Identity:** Create a design that represents the brand identity and style.`;
            }
            // Add selected design examples as reference
            selectedExamples.forEach((designExample)=>{
                promptParts.push({
                    media: {
                        url: designExample,
                        contentType: getMimeTypeFromDataURI(designExample)
                    }
                });
            });
            textPrompt = onBrandPrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        }
    } else {
        // This is a new, un-branded, creative prompt.
        let creativePrompt = `You are an expert creative director specializing in high-end advertisements. Generate a compelling, high-quality social media advertisement ${input.outputType} based on the following instruction: "${remainingPrompt}".

⚡ GEMINI 2.0 FLASH HD QUALITY ENHANCEMENTS:
- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)
- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes
- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small
- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness
- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions
- SHARP DETAILS: Crystal-clear textures, no blur or artifacts
- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows
- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance
- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors`;
        if (input.outputType === 'image' && imageText) {
            creativePrompt += `

🚨🚨🚨 EMERGENCY OVERRIDE - CRITICAL TEXT CONTROL 🚨🚨🚨

⛔ ABSOLUTE PROHIBITION - NO EXCEPTIONS:
- NEVER add "Flex Your Finances" or any financial terms
- NEVER add "Payroll Banking Simplified" or banking phrases
- NEVER add "Banking Made Easy" or similar taglines
- NEVER add company descriptions or service explanations
- NEVER add marketing copy or promotional text
- NEVER add placeholder text or sample content
- NEVER create fake headlines or taglines
- NEVER add descriptive text about the business
- NEVER add ANY text except what is specified below

🎯 ONLY THIS TEXT IS ALLOWED: "${imageText}"
🎯 REPEAT: ONLY THIS TEXT: "${imageText}"
🎯 NO OTHER TEXT PERMITTED: "${imageText}"

🌍 ENGLISH ONLY REQUIREMENT:
- ALL text must be in clear, readable English
- NO foreign languages (Arabic, Chinese, Hindi, etc.)
- NO special characters, symbols, or corrupted text
- NO accents or diacritical marks

Overlay ONLY the following text onto the asset: "${imageText}".
DO NOT ADD ANY OTHER TEXT.
Ensure the text is readable and well-composed.`;
            textPrompt = creativePrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        } else {
            creativePrompt += `\n\n**Video Specifics:** Generate a video that is cinematically interesting, well-composed, and has a sense of completeness. Create a well-composed shot with a clear beginning, middle, and end, even within a short duration. Avoid abrupt cuts or unfinished scenes.`;
            if (input.aspectRatio === '16:9') {
                creativePrompt += ' The video should have relevant sound.';
            }
            if (imageText) {
                creativePrompt += `\n\n**Text Overlay:** The following text MUST be overlaid on the video in a stylish, readable font: "${imageText}". It is critical that the text is clearly readable, well-composed, and not cut off. The entire text must be visible.`;
            }
            textPrompt = creativePrompt;
            if (textPrompt) {
                promptParts.unshift({
                    text: textPrompt
                });
            }
        }
    }
    const aiExplanationPrompt = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].definePrompt({
        name: 'creativeAssetExplanationPrompt',
        prompt: `Based on the generated ${input.outputType}, write a very brief, one-sentence explanation of the creative choices made. For example: "I created a modern, vibrant image of a coffee shop, using your brand's primary color for the logo."`
    });
    const explanationResult = await aiExplanationPrompt();
    try {
        if (input.outputType === 'image') {
            // Generate image with quality validation
            let finalImageUrl = null;
            let attempts = 0;
            const maxAttempts = 2;
            while(attempts < maxAttempts && !finalImageUrl){
                attempts++;
                const { media } = await generateWithRetry({
                    model: 'googleai/gemini-2.0-flash-preview-image-generation',
                    prompt: promptParts,
                    config: {
                        responseModalities: [
                            'TEXT',
                            'IMAGE'
                        ]
                    }
                });
                let imageUrl = media?.url ?? null;
                if (!imageUrl) {
                    if (attempts === maxAttempts) {
                        throw new Error('Failed to generate image');
                    }
                    continue;
                }
                // Apply aspect ratio correction if needed
                if (input.aspectRatio && input.aspectRatio !== '1:1') {
                    console.log(`🖼️ Applying aspect ratio correction for ${input.aspectRatio}...`);
                    try {
                        const { cropImageFromUrl } = await __turbopack_context__.r("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                        // Map aspect ratio to platform for cropping
                        const platformForCropping = input.aspectRatio === '16:9' ? 'linkedin' : input.aspectRatio === '9:16' ? 'story' : 'instagram';
                        imageUrl = await cropImageFromUrl(imageUrl, platformForCropping);
                        console.log(`✅ Image cropped successfully for ${input.aspectRatio}`);
                    } catch (cropError) {
                        console.warn('⚠️ Image cropping failed, using original:', cropError);
                    // Continue with original image if cropping fails
                    }
                }
                // Quality validation for brand profile designs
                if (input.useBrandProfile && input.brandProfile && attempts === 1) {
                    try {
                        const quality = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["assessDesignQuality"])(imageUrl, input.brandProfile.businessType, 'creative-studio', input.brandProfile.visualStyle, undefined, `Creative asset: ${remainingPrompt}`);
                        // If quality is acceptable, use this design
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["meetsQualityStandards"])(quality, 6)) {
                            finalImageUrl = imageUrl;
                            break;
                        }
                        // If quality is poor and we have attempts left, try to improve
                        if (attempts < maxAttempts) {
                            console.log(`Creative asset quality score: ${quality.overall.score}/10. Attempting improvement...`);
                            // Add improvement instructions to prompt
                            const improvementInstructions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$utils$2f$design$2d$quality$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateImprovementPrompt"])(quality);
                            const improvedPrompt = `${promptParts[0].text}\n\n${improvementInstructions}`;
                            promptParts[0] = {
                                text: improvedPrompt
                            };
                            continue;
                        } else {
                            finalImageUrl = imageUrl;
                            break;
                        }
                    } catch (qualityError) {
                        console.warn('Quality assessment failed for creative asset, using generated design:', qualityError);
                        finalImageUrl = imageUrl;
                        break;
                    }
                } else {
                    finalImageUrl = imageUrl;
                    break;
                }
            }
            return {
                imageUrl: finalImageUrl,
                videoUrl: null,
                aiExplanation: explanationResult.output ?? "Here is the generated image based on your prompt."
            };
        } else {
            const isVertical = input.aspectRatio === '9:16';
            const model = isVertical ? 'googleai/veo-2.0-generate-001' : 'googleai/veo-3.0-generate-preview';
            const config = {};
            if (isVertical) {
                config.aspectRatio = '9:16';
                config.durationSeconds = 8;
            }
            const result = await generateWithRetry({
                model,
                prompt: promptParts,
                config
            });
            let operation = result.operation;
            if (!operation) {
                throw new Error('The video generation process did not start correctly. Please try again.');
            }
            // Poll for completion
            while(!operation.done){
                await new Promise((resolve)=>setTimeout(resolve, 5000)); // wait 5s
                operation = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$genkit$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ai"].checkOperation(operation);
            }
            if (operation.error) {
                console.error("Video generation operation failed", operation.error);
                throw new Error(`Video generation failed: ${operation.error.message}. Please try again.`);
            }
            const videoPart = operation.output?.message?.content.find((p)=>!!p.media);
            if (!videoPart || !videoPart.media) {
                throw new Error('Video generation completed, but the final video file could not be found.');
            }
            const videoDataUrl = await videoToDataURI(videoPart);
            return {
                imageUrl: null,
                videoUrl: videoDataUrl,
                aiExplanation: explanationResult.output ?? "Here is the generated video based on your prompt."
            };
        }
    } catch (e) {
        console.error("Error during creative asset generation:", e);
        // Ensure a user-friendly error is thrown
        const message = e.message || "An unknown error occurred during asset generation.";
        throw new Error(message);
    }
});
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generateCreativeAsset
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateCreativeAsset, "409e36c34489ad587473a8902e0f3440bb36957de3", null);
}}),
"[project]/src/lib/services/artifacts-service.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/lib/services/artifacts-service.ts
/**
 * Service for managing artifacts - upload, storage, retrieval, and metadata management
 */ __turbopack_context__.s({
    "artifactsService": (()=>artifactsService),
    "default": (()=>__TURBOPACK__default__export__)
});
// Default upload configuration
const DEFAULT_UPLOAD_CONFIG = {
    maxFileSize: 20 * 1024 * 1024,
    allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/gif',
        'image/svg+xml',
        'application/pdf',
        'text/plain'
    ],
    generateThumbnails: true,
    extractMetadata: true,
    performImageAnalysis: true,
    storage: {
        provider: 'local',
        basePath: '/uploads/artifacts',
        publicUrl: '/api/artifacts'
    }
};
class ArtifactsService {
    artifacts = new Map();
    folders = new Map();
    config = DEFAULT_UPLOAD_CONFIG;
    // Temporary file storage for previews (not persisted to avoid quota issues)
    fileCache = new Map();
    constructor(config){
        if (config) {
            this.config = {
                ...DEFAULT_UPLOAD_CONFIG,
                ...config
            };
        }
        this.loadArtifactsFromStorage();
        this.initializeDefaultFolders();
    }
    /**
   * Upload and process new artifacts with enhanced configuration
   */ async uploadArtifacts(files, category, options) {
        const uploadedArtifacts = [];
        for (const file of files){
            try {
                // Validate file
                this.validateFile(file);
                // Generate unique ID
                const id = this.generateId();
                // Process file and extract metadata
                const metadata = await this.extractMetadata(file);
                // Generate file path
                const filePath = await this.saveFile(file, id);
                // Generate thumbnail path (don't store actual data to avoid quota issues)
                const thumbnailPath = metadata.mimeType.startsWith('image/') ? `/uploads/artifacts/thumbnails/${id}_thumb.jpg` : undefined;
                // Auto-generate directives based on file analysis
                const directives = this.config.performImageAnalysis ? await this.generateDirectives(file, metadata) : [];
                // Create artifact with enhanced configuration
                const artifact = {
                    id,
                    name: options?.customName?.trim() || file.name,
                    type: this.determineArtifactType(file),
                    category: category || this.determineCategoryFromFile(file),
                    usageType: options?.usageType || 'reference',
                    uploadType: options?.uploadType || this.determineUploadType(file),
                    folderId: options?.folderId || this.getDefaultFolderId(file),
                    isActive: options?.isActive || false,
                    instructions: options?.instructions,
                    textOverlay: options?.textOverlay,
                    filePath,
                    thumbnailPath,
                    metadata,
                    directives,
                    tags: this.generateTags(file, metadata),
                    usage: {
                        usageCount: 0,
                        usedInContexts: []
                    },
                    timestamps: {
                        created: new Date(),
                        modified: new Date(),
                        uploaded: new Date()
                    }
                };
                // Add artifact to folder if specified
                if (artifact.folderId) {
                    const folder = this.folders.get(artifact.folderId);
                    if (folder) {
                        folder.artifactIds.push(id);
                        folder.metadata.modified = new Date();
                        this.folders.set(artifact.folderId, folder);
                    }
                }
                // Store artifact
                this.artifacts.set(id, artifact);
                uploadedArtifacts.push(artifact);
                // Cache the file temporarily for preview generation (not persisted)
                if (file.type.startsWith('image/')) {
                    this.fileCache.set(id, file);
                }
            } catch (error) {
                console.error(`Failed to upload ${file.name}:`, error);
                throw new Error(`Failed to upload ${file.name}: ${error.message}`);
            }
        }
        // Save to persistent storage
        await this.saveArtifactsToStorage();
        return uploadedArtifacts;
    }
    /**
   * Search artifacts with filters
   */ searchArtifacts(filters) {
        const startTime = Date.now();
        let results = Array.from(this.artifacts.values());
        // Apply filters
        if (filters.types?.length) {
            results = results.filter((a)=>filters.types.includes(a.type));
        }
        if (filters.categories?.length) {
            results = results.filter((a)=>filters.categories.includes(a.category));
        }
        if (filters.tags?.length) {
            results = results.filter((a)=>filters.tags.some((tag)=>a.tags.includes(tag)));
        }
        if (filters.searchText) {
            const searchLower = filters.searchText.toLowerCase();
            results = results.filter((a)=>a.name.toLowerCase().includes(searchLower) || a.description?.toLowerCase().includes(searchLower) || a.tags.some((tag)=>tag.toLowerCase().includes(searchLower)));
        }
        if (filters.usageContext) {
            results = results.filter((a)=>a.usage.usedInContexts.includes(filters.usageContext));
        }
        if (filters.dateRange) {
            results = results.filter((a)=>a.timestamps.created >= filters.dateRange.start && a.timestamps.created <= filters.dateRange.end);
        }
        if (filters.fileSizeRange) {
            results = results.filter((a)=>a.metadata.fileSize >= filters.fileSizeRange.min && a.metadata.fileSize <= filters.fileSizeRange.max);
        }
        if (filters.dimensionsRange && filters.dimensionsRange.minWidth) {
            results = results.filter((a)=>a.metadata.dimensions && a.metadata.dimensions.width >= filters.dimensionsRange.minWidth && (!filters.dimensionsRange.maxWidth || a.metadata.dimensions.width <= filters.dimensionsRange.maxWidth) && (!filters.dimensionsRange.minHeight || a.metadata.dimensions.height >= filters.dimensionsRange.minHeight) && (!filters.dimensionsRange.maxHeight || a.metadata.dimensions.height <= filters.dimensionsRange.maxHeight));
        }
        const executionTime = Date.now() - startTime;
        return {
            artifacts: results,
            totalCount: results.length,
            searchMetadata: {
                query: filters,
                executionTime,
                suggestions: this.generateSearchSuggestions(filters, results)
            }
        };
    }
    /**
   * Get artifact by ID
   */ getArtifact(id) {
        return this.artifacts.get(id);
    }
    /**
   * Update artifact
   */ async updateArtifact(id, updates) {
        const artifact = this.artifacts.get(id);
        if (!artifact) {
            throw new Error(`Artifact ${id} not found`);
        }
        const updatedArtifact = {
            ...artifact,
            ...updates,
            timestamps: {
                ...artifact.timestamps,
                modified: new Date()
            }
        };
        this.artifacts.set(id, updatedArtifact);
        await this.saveArtifactsToStorage();
        return updatedArtifact;
    }
    /**
   * Delete artifact
   */ async deleteArtifact(id) {
        const artifact = this.artifacts.get(id);
        if (!artifact) {
            throw new Error(`Artifact ${id} not found`);
        }
        // Delete files
        await this.deleteFile(artifact.filePath);
        if (artifact.thumbnailPath) {
            await this.deleteFile(artifact.thumbnailPath);
        }
        // Remove from memory
        this.artifacts.delete(id);
        // Save to storage
        await this.saveArtifactsToStorage();
    }
    /**
   * Track artifact usage
   */ async trackUsage(id, context) {
        const artifact = this.artifacts.get(id);
        if (!artifact) return;
        artifact.usage.usageCount++;
        artifact.usage.lastUsed = new Date();
        if (!artifact.usage.usedInContexts.includes(context)) {
            artifact.usage.usedInContexts.push(context);
        }
        await this.saveArtifactsToStorage();
    }
    /**
   * Get all artifacts
   */ getAllArtifacts() {
        return Array.from(this.artifacts.values());
    }
    /**
   * Get artifacts by category
   */ getArtifactsByCategory(category) {
        return Array.from(this.artifacts.values()).filter((a)=>a.category === category);
    }
    /**
   * Get recently used artifacts
   */ getRecentlyUsed(limit = 10) {
        return Array.from(this.artifacts.values()).filter((a)=>a.usage.lastUsed).sort((a, b)=>b.usage.lastUsed.getTime() - a.usage.lastUsed.getTime()).slice(0, limit);
    }
    /**
   * Create a text-only artifact
   */ async createTextArtifact(options) {
        const id = this.generateId();
        // Parse structured text content if it's JSON
        let textOverlay;
        let instructions;
        try {
            const parsedContent = JSON.parse(options.content);
            if (parsedContent.headline || parsedContent.message) {
                textOverlay = {
                    headline: parsedContent.headline,
                    message: parsedContent.message,
                    cta: parsedContent.cta,
                    contact: parsedContent.contact,
                    discount: parsedContent.discount,
                    instructions: parsedContent.instructions
                };
                // Use provided instructions or auto-generate from content
                instructions = parsedContent.instructions?.trim() || this.generateInstructionsFromTextOverlay(parsedContent);
            }
        } catch  {
        // Not JSON, treat as plain text
        }
        const artifact = {
            id,
            name: options.name,
            type: 'text',
            category: options.category || 'uncategorized',
            usageType: options.usageType,
            uploadType: 'text',
            folderId: options.folderId || '',
            isActive: options.isActive || false,
            instructions,
            textOverlay,
            filePath: '',
            metadata: {
                fileSize: new Blob([
                    options.content
                ]).size,
                mimeType: 'text/plain',
                extractedText: options.content
            },
            directives: [],
            tags: this.generateTagsFromText(options.content),
            usage: {
                usageCount: 0,
                usedInContexts: []
            },
            timestamps: {
                created: new Date(),
                modified: new Date(),
                uploaded: new Date()
            }
        };
        this.artifacts.set(id, artifact);
        await this.saveArtifactsToStorage();
        return artifact;
    }
    // Private helper methods
    validateFile(file) {
        if (file.size > this.config.maxFileSize) {
            throw new Error(`File size exceeds maximum allowed size of ${this.config.maxFileSize} bytes`);
        }
        if (!this.config.allowedTypes.includes(file.type)) {
            throw new Error(`File type ${file.type} is not allowed`);
        }
    }
    generateId() {
        return `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async extractMetadata(file) {
        const metadata = {
            fileSize: file.size,
            mimeType: file.type
        };
        // Extract image dimensions and colors for images
        if (file.type.startsWith('image/')) {
            const dimensions = await this.getImageDimensions(file);
            metadata.dimensions = dimensions;
            if (this.config.performImageAnalysis) {
                metadata.colorPalette = await this.extractColorPalette(file);
                metadata.imageAnalysis = await this.analyzeImage(file);
            }
        }
        // Extract text for text files or OCR for images
        if (file.type === 'text/plain') {
            metadata.extractedText = await file.text();
        } else if (file.type.startsWith('image/') && this.config.performImageAnalysis) {
            metadata.extractedText = await this.performOCR(file);
        }
        return metadata;
    }
    async getImageDimensions(file) {
        return new Promise((resolve, reject)=>{
            const img = new Image();
            img.onload = ()=>{
                resolve({
                    width: img.width,
                    height: img.height
                });
            };
            img.onerror = reject;
            img.src = URL.createObjectURL(file);
        });
    }
    async extractColorPalette(file) {
        // Simplified color extraction - in production, use a proper library
        return [
            '#FF6B6B',
            '#4ECDC4',
            '#45B7D1',
            '#96CEB4',
            '#FFEAA7'
        ];
    }
    async analyzeImage(file) {
        // Simplified image analysis - in production, use AI vision APIs
        return {
            hasText: Math.random() > 0.5,
            hasPeople: Math.random() > 0.7,
            hasProducts: Math.random() > 0.6,
            style: 'modern',
            mood: 'professional'
        };
    }
    async performOCR(file) {
        // Placeholder for OCR functionality
        return '';
    }
    async saveFile(file, id) {
        // In production, implement actual file saving logic
        return `/uploads/artifacts/${id}_${file.name}`;
    }
    async generateThumbnail(file, id) {
        // Return a placeholder path - actual thumbnails will be generated on-demand
        return `/uploads/artifacts/thumbnails/${id}_thumb.jpg`;
    }
    /**
   * Generate a thumbnail data URL from a file (for display purposes)
   * This is called on-demand to avoid localStorage quota issues
   */ async generateThumbnailDataUrl(file) {
        try {
            return new Promise((resolve, reject)=>{
                const reader = new FileReader();
                reader.onload = (e)=>{
                    const result = e.target?.result;
                    resolve(result);
                };
                reader.onerror = ()=>reject(new Error('Failed to read file'));
                reader.readAsDataURL(file);
            });
        } catch (error) {
            console.error('Failed to generate thumbnail data URL:', error);
            throw error;
        }
    }
    /**
   * Get thumbnail data URL for an artifact (on-demand generation)
   */ async getArtifactThumbnail(artifactId) {
        const file = this.fileCache.get(artifactId);
        if (!file) {
            return null;
        }
        try {
            return await this.generateThumbnailDataUrl(file);
        } catch (error) {
            console.error('Failed to generate thumbnail for artifact:', artifactId, error);
            return null;
        }
    }
    /**
   * Generate instructions from text overlay content
   */ generateInstructionsFromTextOverlay(content) {
        const instructions = [];
        if (content.headline?.trim()) {
            instructions.push(`Use "${content.headline.trim()}" as the main headline with large, bold text`);
        }
        if (content.message?.trim()) {
            instructions.push(`Include the message "${content.message.trim()}" as supporting text`);
        }
        if (content.cta?.trim()) {
            instructions.push(`Add a prominent call-to-action button with "${content.cta.trim()}"`);
        }
        if (content.contact?.trim()) {
            instructions.push(`Display contact information "${content.contact.trim()}" clearly`);
        }
        if (content.discount?.trim()) {
            instructions.push(`Highlight the discount offer "${content.discount.trim()}" prominently`);
        }
        return instructions.length > 0 ? instructions.join(', ') : 'Use this text content in the design as appropriate';
    }
    /**
   * Fix existing artifacts that don't have instructions
   */ fixMissingInstructions() {
        console.log('🔧 Fixing artifacts with missing instructions...');
        let fixedCount = 0;
        for (const [id, artifact] of this.artifacts.entries()){
            if (artifact.type === 'text' && artifact.textOverlay && (!artifact.instructions || artifact.instructions.trim() === '')) {
                const generatedInstructions = this.generateInstructionsFromTextOverlay(artifact.textOverlay);
                artifact.instructions = generatedInstructions;
                this.artifacts.set(id, artifact);
                fixedCount++;
                console.log(`✅ Fixed instructions for artifact: ${artifact.name} -> "${generatedInstructions}"`);
            }
        }
        if (fixedCount > 0) {
            this.saveArtifactsToStorage();
            console.log(`🎯 Fixed ${fixedCount} artifacts with missing instructions`);
        } else {
            console.log('📋 No artifacts needed instruction fixes');
        }
    }
    /**
   * Set an artifact as active or inactive
   */ setArtifactActive(artifactId, isActive) {
        console.log(`🔧 setArtifactActive called: ${artifactId} -> ${isActive}`);
        const artifact = this.artifacts.get(artifactId);
        if (artifact) {
            console.log(`✅ Artifact found: ${artifact.name}, current isActive: ${artifact.isActive}`);
            artifact.isActive = isActive;
            this.artifacts.set(artifactId, artifact);
            this.saveArtifactsToStorage();
            console.log(`💾 Artifact updated and saved: ${artifact.name} isActive: ${artifact.isActive}`);
        } else {
            console.error(`❌ Artifact not found: ${artifactId}`);
        }
    }
    async deleteFile(filePath) {
        // In production, implement file deletion
        console.log(`Would delete file: ${filePath}`);
    }
    determineArtifactType(file) {
        if (file.type.startsWith('image/')) {
            if (file.name.toLowerCase().includes('logo')) return 'logo';
            if (file.name.toLowerCase().includes('screenshot')) return 'screenshot';
            return 'image';
        }
        return 'document';
    }
    determineCategoryFromFile(file) {
        const name = file.name.toLowerCase();
        if (name.includes('logo')) return 'logos';
        if (name.includes('screenshot')) return 'screenshots';
        if (name.includes('template')) return 'templates';
        if (name.includes('product')) return 'product-images';
        if (name.includes('brand')) return 'brand-assets';
        return 'uncategorized';
    }
    generateTags(file, metadata) {
        const tags = [];
        // Add type-based tags
        if (file.type.startsWith('image/')) tags.push('image');
        if (metadata.dimensions) {
            if (metadata.dimensions.width > metadata.dimensions.height) tags.push('landscape');
            else if (metadata.dimensions.height > metadata.dimensions.width) tags.push('portrait');
            else tags.push('square');
        }
        // Add size-based tags
        if (metadata.fileSize > 5 * 1024 * 1024) tags.push('large');
        else if (metadata.fileSize > 1024 * 1024) tags.push('medium');
        else tags.push('small');
        return tags;
    }
    generateTagsFromText(content) {
        const tags = [
            'text'
        ];
        // Try to parse structured content
        try {
            const parsed = JSON.parse(content);
            if (parsed.headline) tags.push('headline');
            if (parsed.message) tags.push('message');
            if (parsed.cta) tags.push('call-to-action');
            if (parsed.contact) tags.push('contact-info');
            if (parsed.discount) tags.push('discount');
        } catch  {
            // Plain text
            tags.push('plain-text');
        }
        return tags;
    }
    getDefaultFolderId(file) {
        // Return the ID of the default folder for the artifact type
        const defaultFolder = Array.from(this.folders.values()).find((f)=>f.isDefault);
        return defaultFolder?.id || '';
    }
    async generateDirectives(file, metadata) {
        const directives = [];
        // Generate style reference directive for images
        if (file.type.startsWith('image/')) {
            const styleDirective = {
                id: `style_${this.generateId()}`,
                type: 'style-reference',
                label: 'Use as style reference',
                instruction: `Use this image as a style reference for layout, color scheme, and overall aesthetic`,
                priority: 7,
                active: true,
                styleElements: {
                    layout: true,
                    colorScheme: true,
                    composition: true,
                    mood: true
                },
                adaptationLevel: 'moderate'
            };
            directives.push(styleDirective);
        }
        // Generate text overlay directive if text is detected
        if (metadata.extractedText && metadata.extractedText.trim()) {
            const textDirective = {
                id: `text_${this.generateId()}`,
                type: 'text-overlay',
                label: 'Include extracted text',
                instruction: `Include this exact text in the generated design: "${metadata.extractedText.trim()}"`,
                priority: 9,
                active: false,
                exactText: metadata.extractedText.trim(),
                positioning: {
                    horizontal: 'center',
                    vertical: 'center'
                },
                styling: {
                    fontSize: 'large',
                    fontWeight: 'bold'
                }
            };
            directives.push(textDirective);
        }
        return directives;
    }
    generateSearchSuggestions(filters, results) {
        // Generate helpful search suggestions based on current results
        const suggestions = [];
        if (results.length === 0) {
            suggestions.push('Try removing some filters');
            suggestions.push('Check different categories');
        } else if (results.length > 50) {
            suggestions.push('Add more specific filters');
            suggestions.push('Filter by category or type');
        }
        return suggestions;
    }
    loadArtifactsFromStorage() {
        try {
            // Check if we're in a browser environment
            if ("TURBOPACK compile-time truthy", 1) {
                console.log('🚫 Not in browser environment, skipping artifact loading');
                return;
            }
            "TURBOPACK unreachable";
            const stored = undefined;
        } catch (error) {
            console.error('Failed to load artifacts from storage:', error);
        }
    }
    async saveArtifactsToStorage() {
        try {
            // Check if we're in a browser environment
            if ("TURBOPACK compile-time truthy", 1) {
                console.log('🚫 Not in browser environment, skipping artifact saving');
                return;
            }
            "TURBOPACK unreachable";
            const artifacts = undefined;
            const folders = undefined;
        } catch (error) {
            console.error('Failed to save artifacts to storage:', error);
        }
    }
    // === NEW ENHANCED METHODS ===
    /**
   * Initialize default folders
   */ initializeDefaultFolders() {
        const defaultFolders = [
            {
                id: 'previous-posts',
                name: 'Previous Posts',
                description: 'Previously created social media posts for reference',
                color: '#3B82F6'
            },
            {
                id: 'products',
                name: 'Products',
                description: 'Product images and screenshots for exact use',
                color: '#10B981'
            },
            {
                id: 'discounts',
                name: 'Discounts',
                description: 'Discount and promotional materials',
                color: '#F59E0B'
            },
            {
                id: 'references',
                name: 'References',
                description: 'Style references and inspiration materials',
                color: '#8B5CF6'
            }
        ];
        defaultFolders.forEach((folderData)=>{
            if (!this.folders.has(folderData.id)) {
                const folder = {
                    ...folderData,
                    type: 'default',
                    artifactIds: [],
                    metadata: {
                        created: new Date(),
                        modified: new Date()
                    },
                    isDefault: true
                };
                this.folders.set(folder.id, folder);
            }
        });
        this.loadFoldersFromStorage();
    }
    /**
   * Load folders from storage
   */ loadFoldersFromStorage() {
        try {
            // Check if we're in a browser environment
            if ("TURBOPACK compile-time truthy", 1) {
                return;
            }
            "TURBOPACK unreachable";
            const stored = undefined;
        } catch (error) {
            console.error('Failed to load folders from storage:', error);
        }
    }
    /**
   * Determine upload type from file
   */ determineUploadType(file) {
        if (file.type.startsWith('image/')) return 'image';
        if (file.type === 'text/plain') return 'text';
        return 'document';
    }
    /**
   * Get default folder ID based on file type
   */ getDefaultFolderId(file) {
        if (file.name.toLowerCase().includes('product')) return 'products';
        if (file.name.toLowerCase().includes('discount') || file.name.toLowerCase().includes('sale')) return 'discounts';
        if (file.name.toLowerCase().includes('post')) return 'previous-posts';
        return 'references';
    }
    // === FOLDER MANAGEMENT METHODS ===
    /**
   * Create a new folder
   */ async createFolder(request) {
        const id = this.generateId();
        const folder = {
            id,
            name: request.name,
            description: request.description,
            type: request.type,
            color: request.color,
            artifactIds: [],
            metadata: {
                created: new Date(),
                modified: new Date()
            },
            isDefault: false
        };
        this.folders.set(id, folder);
        await this.saveArtifactsToStorage();
        return folder;
    }
    /**
   * Update folder
   */ async updateFolder(folderId, request) {
        const folder = this.folders.get(folderId);
        if (!folder) return null;
        if (request.name) folder.name = request.name;
        if (request.description !== undefined) folder.description = request.description;
        if (request.color) folder.color = request.color;
        folder.metadata.modified = new Date();
        this.folders.set(folderId, folder);
        await this.saveArtifactsToStorage();
        return folder;
    }
    /**
   * Delete folder (only custom folders)
   */ async deleteFolder(folderId) {
        const folder = this.folders.get(folderId);
        if (!folder || folder.isDefault) return false;
        // Move artifacts to references folder
        const referencesFolder = this.folders.get('references');
        if (referencesFolder) {
            folder.artifactIds.forEach((artifactId)=>{
                const artifact = this.artifacts.get(artifactId);
                if (artifact) {
                    artifact.folderId = 'references';
                    this.artifacts.set(artifactId, artifact);
                }
                referencesFolder.artifactIds.push(artifactId);
            });
            this.folders.set('references', referencesFolder);
        }
        this.folders.delete(folderId);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Get all folders
   */ getFolders() {
        return Array.from(this.folders.values());
    }
    /**
   * Get folder by ID
   */ getFolder(folderId) {
        return this.folders.get(folderId) || null;
    }
    /**
   * Move artifact to folder
   */ async moveArtifactToFolder(artifactId, folderId) {
        const artifact = this.artifacts.get(artifactId);
        const folder = this.folders.get(folderId);
        if (!artifact || !folder) return false;
        // Remove from old folder
        if (artifact.folderId) {
            const oldFolder = this.folders.get(artifact.folderId);
            if (oldFolder) {
                oldFolder.artifactIds = oldFolder.artifactIds.filter((id)=>id !== artifactId);
                this.folders.set(artifact.folderId, oldFolder);
            }
        }
        // Add to new folder
        artifact.folderId = folderId;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        if (!folder.artifactIds.includes(artifactId)) {
            folder.artifactIds.push(artifactId);
            folder.metadata.modified = new Date();
            this.folders.set(folderId, folder);
        }
        await this.saveArtifactsToStorage();
        return true;
    }
    // === ACTIVATION MANAGEMENT METHODS ===
    /**
   * Activate artifact for content generation
   */ async activateArtifact(artifactId, context) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.isActive = true;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Deactivate artifact
   */ async deactivateArtifact(artifactId) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.isActive = false;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Toggle artifact activation
   */ async toggleArtifactActivation(artifactId) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.isActive = !artifact.isActive;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return artifact.isActive;
    }
    /**
   * Get all active artifacts
   */ getActiveArtifacts() {
        const allArtifacts = Array.from(this.artifacts.values());
        const activeArtifacts = allArtifacts.filter((artifact)=>artifact.isActive);
        console.log(`🔍 getActiveArtifacts called:`);
        console.log(`📊 Total artifacts: ${allArtifacts.length}`);
        console.log(`✅ Active artifacts: ${activeArtifacts.length}`);
        console.log(`📋 All artifacts status:`, allArtifacts.map((a)=>({
                id: a.id,
                name: a.name,
                isActive: a.isActive,
                usageType: a.usageType
            })));
        return activeArtifacts;
    }
    /**
   * Get active artifacts by usage type
   */ getActiveArtifactsByUsageType(usageType) {
        return this.getActiveArtifacts().filter((artifact)=>artifact.usageType === usageType);
    }
    /**
   * Deactivate all artifacts
   */ async deactivateAllArtifacts() {
        const artifacts = Array.from(this.artifacts.values());
        for (const artifact of artifacts){
            if (artifact.isActive) {
                artifact.isActive = false;
                artifact.timestamps.modified = new Date();
                this.artifacts.set(artifact.id, artifact);
            }
        }
        await this.saveArtifactsToStorage();
    }
    // === TEXT OVERLAY MANAGEMENT ===
    /**
   * Update artifact text overlay
   */ async updateArtifactTextOverlay(artifactId, textOverlay) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.textOverlay = textOverlay;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Remove artifact text overlay
   */ async removeArtifactTextOverlay(artifactId) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.textOverlay = undefined;
        artifact.timestamps.modified = new Date();
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    /**
   * Update artifact usage type
   */ async updateArtifactUsageType(artifactId, usageType) {
        const artifact = this.artifacts.get(artifactId);
        if (!artifact) return false;
        artifact.usageType = usageType;
        artifact.timestamps.modified = new Date();
        // Clear text overlay if changing from exact-use to reference
        if (usageType === 'reference') {
            artifact.textOverlay = undefined;
        }
        this.artifacts.set(artifactId, artifact);
        await this.saveArtifactsToStorage();
        return true;
    }
    // === ENHANCED SEARCH METHODS ===
    /**
   * Enhanced search with new filters
   */ async searchArtifactsEnhanced(filters) {
        const startTime = Date.now();
        let artifacts = Array.from(this.artifacts.values());
        // Apply existing filters (from base search)
        if (filters.query) {
            const query = filters.query.toLowerCase();
            artifacts = artifacts.filter((artifact)=>artifact.name.toLowerCase().includes(query) || artifact.description?.toLowerCase().includes(query) || artifact.tags.some((tag)=>tag.toLowerCase().includes(query)));
        }
        if (filters.type && filters.type !== 'all') {
            artifacts = artifacts.filter((artifact)=>artifact.type === filters.type);
        }
        if (filters.category && filters.category !== 'all') {
            artifacts = artifacts.filter((artifact)=>artifact.category === filters.category);
        }
        // Apply new enhanced filters
        if (filters.usageType) {
            artifacts = artifacts.filter((artifact)=>artifact.usageType === filters.usageType);
        }
        if (filters.uploadType) {
            artifacts = artifacts.filter((artifact)=>artifact.uploadType === filters.uploadType);
        }
        if (filters.folderId) {
            artifacts = artifacts.filter((artifact)=>artifact.folderId === filters.folderId);
        }
        if (filters.isActive !== undefined) {
            artifacts = artifacts.filter((artifact)=>artifact.isActive === filters.isActive);
        }
        if (filters.hasTextOverlay !== undefined) {
            artifacts = artifacts.filter((artifact)=>filters.hasTextOverlay ? !!artifact.textOverlay : !artifact.textOverlay);
        }
        // Apply pagination
        const totalCount = artifacts.length;
        if (filters.limit) {
            const offset = filters.offset || 0;
            artifacts = artifacts.slice(offset, offset + filters.limit);
        }
        const executionTime = Date.now() - startTime;
        return {
            artifacts,
            totalCount,
            searchMetadata: {
                query: filters,
                executionTime,
                suggestions: [] // Could implement search suggestions here
            }
        };
    }
    /**
   * Get artifacts by folder
   */ getArtifactsByFolder(folderId) {
        return Array.from(this.artifacts.values()).filter((artifact)=>artifact.folderId === folderId);
    }
}
const artifactsService = new ArtifactsService();
const __TURBOPACK__default__export__ = ArtifactsService;
}}),
"[project]/src/app/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/app/actions.ts
/* __next_internal_action_entry_do_not_use__ [{"60ea2a632798675c707aba182342fbd2fe9934e7b2":"analyzeBrandAction","7032bdb3854aae97c69a87de824651ac55c853892e":"generateVideoContentAction","70370719efd459954676917a0ea0a33701731123ce":"generateContentAction","7c59f40eb0b80a2dc5e7a84991c84ddc885ae6ce31":"generateContentWithArtifactsAction","7c77a46b52594dce7ad1477e7b4988f6f2475453ef":"generateContentWithRevoModelAction","7f36c3e89c9711bb510eae73accd75d39762aa73f5":"generateEnhancedDesignAction","7fa71ea55dac91d9f2fbbc47e38b16f32c34406758":"generateCreativeAssetAction","7ff7d412819238ab9e627cc3d406ad2cce0c9fa6c9":"generateGeminiHDDesignAction"},"",""] */ __turbopack_context__.s({
    "analyzeBrandAction": (()=>analyzeBrandAction),
    "generateContentAction": (()=>generateContentAction),
    "generateContentWithArtifactsAction": (()=>generateContentWithArtifactsAction),
    "generateContentWithRevoModelAction": (()=>generateContentWithRevoModelAction),
    "generateCreativeAssetAction": (()=>generateCreativeAssetAction),
    "generateEnhancedDesignAction": (()=>generateEnhancedDesignAction),
    "generateGeminiHDDesignAction": (()=>generateGeminiHDDesignAction),
    "generateVideoContentAction": (()=>generateVideoContentAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$analyze$2d$brand$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/analyze-brand.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$post$2d$from$2d$profile$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-post-from-profile.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$video$2d$post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-video-post.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/artifacts-service.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
async function analyzeBrandAction(websiteUrl, designImageUris) {
    try {
        console.log("🔍 Starting brand analysis for URL:", websiteUrl);
        console.log("🖼️ Design images count:", designImageUris.length);
        // Validate URL format
        if (!websiteUrl || !websiteUrl.trim()) {
            return {
                success: false,
                error: "Website URL is required",
                errorType: 'error'
            };
        }
        // Ensure URL has protocol
        let validUrl = websiteUrl.trim();
        if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {
            validUrl = 'https://' + validUrl;
        }
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$analyze$2d$brand$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeBrand"])({
            websiteUrl: validUrl,
            designImageUris: designImageUris || []
        });
        console.log("✅ Brand analysis result:", JSON.stringify(result, null, 2));
        console.log("🔍 Result type:", typeof result);
        console.log("🔍 Result keys:", result ? Object.keys(result) : "No result");
        if (!result) {
            return {
                success: false,
                error: "Analysis returned empty result",
                errorType: 'error'
            };
        }
        return {
            success: true,
            data: result
        };
    } catch (error) {
        console.error("❌ Error analyzing brand:", error);
        // Return structured error response instead of throwing
        const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
        if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {
            return {
                success: false,
                error: "Website blocks automated access. This is common for security reasons.",
                errorType: 'blocked'
            };
        } else if (errorMessage.includes('timeout')) {
            return {
                success: false,
                error: "Website analysis timed out. Please try again or check if the website is accessible.",
                errorType: 'timeout'
            };
        } else if (errorMessage.includes('CORS')) {
            return {
                success: false,
                error: "Website blocks automated access. This is common for security reasons.",
                errorType: 'blocked'
            };
        } else {
            return {
                success: false,
                error: `Analysis failed: ${errorMessage}`,
                errorType: 'error'
            };
        }
    }
}
const getAspectRatioForPlatform = (platform)=>{
    switch(platform){
        case 'Instagram':
            return '1:1'; // Square
        case 'Facebook':
            return '16:9'; // Landscape - Facebook posts are landscape format
        case 'Twitter':
            return '16:9'; // Landscape
        case 'LinkedIn':
            return '16:9'; // Landscape - LinkedIn posts are landscape format
        default:
            return '1:1';
    }
};
async function generateContentAction(profile, platform, brandConsistency) {
    try {
        const today = new Date();
        const dayOfWeek = today.toLocaleDateString('en-US', {
            weekday: 'long'
        });
        const currentDate = today.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        // Apply brand consistency logic
        const effectiveDesignExamples = brandConsistency?.strictConsistency ? profile.designExamples || [] : []; // Don't use design examples if not strict consistency
        // Convert arrays to newline-separated strings for AI processing
        const keyFeaturesString = Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '';
        const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '';
        // Convert services array to newline-separated string
        const servicesString = Array.isArray(profile.services) ? profile.services.map((service)=>typeof service === 'object' && service.name ? `${service.name}: ${service.description || ''}` : service).join('\n') : profile.services || '';
        const postDetails = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$post$2d$from$2d$profile$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePostFromProfile"])({
            businessType: profile.businessType,
            location: profile.location,
            writingTone: profile.writingTone,
            contentThemes: profile.contentThemes,
            visualStyle: profile.visualStyle,
            logoDataUrl: profile.logoDataUrl,
            designExamples: effectiveDesignExamples,
            primaryColor: profile.primaryColor,
            accentColor: profile.accentColor,
            backgroundColor: profile.backgroundColor,
            dayOfWeek,
            currentDate,
            variants: [
                {
                    platform: platform,
                    aspectRatio: getAspectRatioForPlatform(platform)
                }
            ],
            // Pass new detailed fields
            services: servicesString,
            targetAudience: profile.targetAudience,
            keyFeatures: keyFeaturesString,
            competitiveAdvantages: competitiveAdvantagesString,
            // Pass brand consistency preferences
            brandConsistency: brandConsistency || {
                strictConsistency: false,
                followBrandColors: true
            }
        });
        const newPost = {
            id: new Date().toISOString(),
            date: today.toISOString(),
            content: postDetails.content,
            hashtags: postDetails.hashtags,
            status: 'generated',
            variants: postDetails.variants,
            catchyWords: postDetails.catchyWords,
            subheadline: postDetails.subheadline,
            callToAction: postDetails.callToAction,
            // Include enhanced AI features
            contentVariants: postDetails.contentVariants,
            hashtagAnalysis: postDetails.hashtagAnalysis,
            // Include advanced AI features
            marketIntelligence: postDetails.marketIntelligence,
            // Include local context features
            localContext: postDetails.localContext
        };
        return newPost;
    } catch (error) {
        console.error("Error generating content:", error);
        throw new Error("Failed to generate content. Please try again later.");
    }
}
async function generateVideoContentAction(profile, catchyWords, postContent) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$video$2d$post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateVideoPost"])({
            businessType: profile.businessType,
            location: profile.location,
            visualStyle: profile.visualStyle,
            imageText: catchyWords,
            postContent: postContent
        });
        return {
            videoUrl: result.videoUrl
        };
    } catch (error) {
        console.error("Error generating video content:", error);
        // Pass the specific error message from the flow to the client
        throw new Error(error.message);
    }
}
async function generateCreativeAssetAction(prompt, outputType, referenceAssetUrl, useBrandProfile, brandProfile, maskDataUrl, aspectRatio) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateCreativeAsset"])({
            prompt,
            outputType,
            referenceAssetUrl,
            useBrandProfile,
            brandProfile: useBrandProfile ? brandProfile : null,
            maskDataUrl,
            aspectRatio
        });
        return result;
    } catch (error) {
        console.error("Error generating creative asset:", error);
        // Always pass the specific error message from the flow to the client.
        throw new Error(error.message);
    }
}
async function generateEnhancedDesignAction(businessType, platform, visualStyle, imageText, brandProfile, enableEnhancements = true, brandConsistency, artifactInstructions) {
    const startTime = Date.now();
    const enhancementsApplied = [];
    try {
        if (!brandProfile) {
            throw new Error('Brand profile is required for enhanced design generation');
        }
        // Handle both old string format and new object format
        let finalImageText;
        if (typeof imageText === 'string') {
            finalImageText = imageText;
        } else {
            // Combine catchy words, subheadline, and call-to-action
            const components = [
                imageText.catchyWords
            ];
            if (imageText.subheadline && imageText.subheadline.trim()) {
                components.push(imageText.subheadline.trim());
            }
            if (imageText.callToAction && imageText.callToAction.trim()) {
                components.push(imageText.callToAction.trim());
            }
            finalImageText = components.join('\n');
        }
        console.log('🎨 Enhanced Design Generation Started');
        console.log('- Business Type:', businessType);
        console.log('- Platform:', platform);
        console.log('- Visual Style:', visualStyle);
        console.log('- Image Text:', finalImageText);
        console.log('- Brand Profile:', brandProfile.businessName);
        console.log('- Enhancements Enabled:', enableEnhancements);
        // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD
        let result;
        try {
            console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');
            result = await generateEnhancedDesign({
                businessType,
                platform,
                visualStyle,
                imageText: finalImageText,
                brandProfile,
                brandConsistency,
                artifactInstructions
            });
            console.log('✅ Gemini 2.5 enhanced design generated successfully');
            console.log(`🎯 Quality Score: ${result.qualityScore}/10`);
            console.log(`⚡ Processing Time: ${result.processingTime}ms`);
        } catch (gemini25Error) {
            console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);
            try {
                console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');
                const { generateEnhancedDesignWithFallback } = await __turbopack_context__.r("[project]/src/ai/openai-enhanced-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                result = await generateEnhancedDesignWithFallback({
                    businessType,
                    platform,
                    visualStyle,
                    imageText: finalImageText,
                    brandProfile,
                    brandConsistency,
                    artifactInstructions
                });
                console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');
            } catch (openaiError) {
                console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);
                console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');
                const { generateGeminiHDEnhancedDesignWithFallback } = await __turbopack_context__.r("[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                result = await generateGeminiHDEnhancedDesignWithFallback({
                    businessType,
                    platform,
                    visualStyle,
                    imageText: finalImageText,
                    brandProfile,
                    brandConsistency,
                    artifactInstructions
                });
                console.log('✅ Gemini 2.0 HD enhanced design generated successfully');
            }
        }
        console.log('🔗 Image URL:', result.imageUrl);
        console.log('⭐ Quality Score:', result.qualityScore);
        console.log('🎯 Enhancements Applied:', result.enhancementsApplied);
        return {
            imageUrl: result.imageUrl,
            qualityScore: result.qualityScore,
            enhancementsApplied: result.enhancementsApplied,
            processingTime: result.processingTime
        };
    } catch (error) {
        console.error("Error generating enhanced design:", error);
        throw new Error(error.message);
    }
}
async function generateGeminiHDDesignAction(businessType, platform, visualStyle, imageText, brandProfile, brandConsistency, artifactInstructions) {
    try {
        if (!brandProfile) {
            throw new Error('Brand profile is required for Gemini HD design generation');
        }
        console.log('🎨 Gemini HD Design Generation Started');
        console.log('- Business Type:', businessType);
        console.log('- Platform:', platform);
        console.log('- Visual Style:', visualStyle);
        console.log('- Image Text:', imageText);
        console.log('- Brand Profile:', brandProfile.businessName);
        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');
        const { generateGeminiHDEnhancedDesignWithFallback } = await __turbopack_context__.r("[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        const result = await generateGeminiHDEnhancedDesignWithFallback({
            businessType,
            platform,
            visualStyle,
            imageText,
            brandProfile,
            brandConsistency,
            artifactInstructions
        });
        console.log('✅ Gemini HD enhanced design generated successfully');
        console.log('🔗 Image URL:', result.imageUrl);
        console.log('⭐ Quality Score:', result.qualityScore);
        console.log('🎯 Enhancements Applied:', result.enhancementsApplied);
        return {
            platform,
            imageUrl: result.imageUrl,
            caption: imageText,
            hashtags: []
        };
    } catch (error) {
        console.error('❌ Error in Gemini HD design generation:', error);
        throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateContentWithArtifactsAction(profile, platform, brandConsistency, artifactIds = [], useEnhancedDesign = true) {
    try {
        console.log('🎨 Generating content with artifacts...');
        console.log('- Platform:', platform);
        console.log('- Artifacts:', artifactIds.length);
        console.log('- Enhanced Design:', useEnhancedDesign);
        // Get active artifacts if no specific artifacts provided
        let targetArtifacts = [];
        if (artifactIds.length > 0) {
            // Use specified artifacts
            for (const artifactId of artifactIds){
                const artifact = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].getArtifact(artifactId);
                if (artifact) {
                    targetArtifacts.push(artifact);
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].trackUsage(artifactId, 'quick-content');
                }
            }
        } else {
            // Use active artifacts, prioritizing exact-use
            const activeArtifacts = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].getActiveArtifacts();
            console.log('🔍 Active artifacts found:', activeArtifacts.length);
            console.log('📋 Active artifacts details:', activeArtifacts.map((a)=>({
                    id: a.id,
                    name: a.name,
                    type: a.type,
                    usageType: a.usageType,
                    isActive: a.isActive,
                    instructions: a.instructions
                })));
            const exactUseArtifacts = activeArtifacts.filter((a)=>a.usageType === 'exact-use');
            const referenceArtifacts = activeArtifacts.filter((a)=>a.usageType === 'reference');
            // Prioritize exact-use artifacts
            targetArtifacts = [
                ...exactUseArtifacts,
                ...referenceArtifacts.slice(0, 3)
            ];
            // Track usage for active artifacts
            for (const artifact of targetArtifacts){
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].trackUsage(artifact.id, 'quick-content');
            }
        }
        console.log('📎 Using artifacts:', targetArtifacts.map((a)=>`${a.name} (${a.usageType})`));
        // Generate base content first
        const basePost = await generateContentAction(profile, platform, brandConsistency);
        // If enhanced design is disabled, return base content
        if (!useEnhancedDesign) {
            console.log('🔄 Enhanced design disabled, using base content generation');
            return basePost;
        }
        // Enhanced design is enabled - always use enhanced generation regardless of artifacts
        console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');
        console.log(`📊 Artifacts available: ${targetArtifacts.length}`);
        if (targetArtifacts.length === 0) {
            console.log('✨ No artifacts provided - using enhanced design without artifact context');
        } else {
            console.log('🎯 Using enhanced design with artifact context');
        }
        // Separate exact-use and reference artifacts
        const exactUseArtifacts = targetArtifacts.filter((a)=>a.usageType === 'exact-use');
        const referenceArtifacts = targetArtifacts.filter((a)=>a.usageType === 'reference');
        // Create enhanced image text structure from post components
        let enhancedImageText = {
            catchyWords: basePost.catchyWords || 'Engaging Content',
            subheadline: basePost.subheadline,
            callToAction: basePost.callToAction
        };
        let enhancedContent = basePost.content;
        // Collect usage instructions from artifacts
        const artifactInstructions = targetArtifacts.filter((a)=>a.instructions && a.instructions.trim()).map((a)=>`- ${a.name}: ${a.instructions}`).join('\n');
        // Collect text overlay instructions from text artifacts
        const textOverlayInstructions = exactUseArtifacts.filter((a)=>a.textOverlay?.instructions && a.textOverlay.instructions.trim()).map((a)=>`- ${a.name}: ${a.textOverlay.instructions}`).join('\n');
        // Process exact-use artifacts first (higher priority)
        if (exactUseArtifacts.length > 0) {
            const primaryExactUse = exactUseArtifacts[0];
            // Use text overlay if available
            if (primaryExactUse.textOverlay) {
                if (primaryExactUse.textOverlay.headline) {
                    enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;
                    console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);
                }
                if (primaryExactUse.textOverlay.message) {
                    enhancedContent = primaryExactUse.textOverlay.message;
                    console.log('📝 Using message from exact-use artifact');
                }
                // Use CTA from artifact if available
                if (primaryExactUse.textOverlay.cta) {
                    enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;
                    console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);
                }
            }
        }
        // Process reference artifacts for style guidance
        const activeDirectives = referenceArtifacts.flatMap((artifact)=>artifact.directives.filter((directive)=>directive.active));
        // Apply style reference directives
        const styleDirectives = activeDirectives.filter((d)=>d.type === 'style-reference');
        let visualStyleOverride = profile.visualStyle || 'modern';
        if (styleDirectives.length > 0) {
            console.log('🎨 Applying style references from artifacts');
            const primaryStyleDirective = styleDirectives.find((d)=>d.priority >= 7);
            if (primaryStyleDirective) {
                visualStyleOverride = 'artifact-inspired';
                console.log('🎨 Using artifact-inspired visual style');
            }
        }
        // Combine all instructions
        const allInstructions = [
            artifactInstructions,
            textOverlayInstructions
        ].filter(Boolean).join('\n');
        // Generate enhanced design with artifact context
        const enhancedResult = await generateEnhancedDesignAction(profile.businessType || 'business', platform.toLowerCase(), visualStyleOverride, enhancedImageText, profile, true, brandConsistency, allInstructions || undefined);
        // Create enhanced post with artifact metadata
        const enhancedPost = {
            ...basePost,
            id: Date.now().toString(),
            variants: [
                {
                    platform: platform,
                    imageUrl: enhancedResult.imageUrl
                }
            ],
            content: targetArtifacts.length > 0 ? `${enhancedContent}\n\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)` : `${enhancedContent}\n\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,
            date: new Date().toISOString(),
            // Add artifact metadata
            metadata: {
                ...basePost.metadata,
                referencedArtifacts: targetArtifacts.map((a)=>({
                        id: a.id,
                        name: a.name,
                        type: a.type,
                        category: a.category
                    })),
                activeDirectives: activeDirectives.map((d)=>({
                        id: d.id,
                        type: d.type,
                        label: d.label,
                        priority: d.priority
                    }))
            }
        };
        console.log('✅ Enhanced content with artifacts generated successfully');
        return enhancedPost;
    } catch (error) {
        console.error("Error generating content with artifacts:", error);
        throw new Error(error.message);
    }
}
async function generateContentWithRevoModelAction(profile, platform, revoModel, brandConsistency, artifactIds = []) {
    try {
        console.log(`🎨 Generating content with ${revoModel} model...`);
        console.log('- Platform:', platform);
        console.log('- Business Type:', profile.businessType);
        console.log('- Artifacts:', artifactIds.length);
        // Handle artifacts if provided
        let targetArtifacts = [];
        if (artifactIds.length > 0) {
            for (const artifactId of artifactIds){
                const artifact = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].getArtifact(artifactId);
                if (artifact) {
                    targetArtifacts.push(artifact);
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].trackUsage(artifactId, 'quick-content');
                }
            }
        } else {
            // Use active artifacts
            const activeArtifacts = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].getActiveArtifacts();
            const exactUseArtifacts = activeArtifacts.filter((a)=>a.usageType === 'exact-use');
            const referenceArtifacts = activeArtifacts.filter((a)=>a.usageType === 'reference');
            targetArtifacts = [
                ...exactUseArtifacts,
                ...referenceArtifacts.slice(0, 3)
            ];
            for (const artifact of targetArtifacts){
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$artifacts$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["artifactsService"].trackUsage(artifact.id, 'quick-content');
            }
        }
        // Prepare artifact instructions
        let artifactInstructions = '';
        if (targetArtifacts.length > 0) {
            const exactUseArtifacts = targetArtifacts.filter((a)=>a.usageType === 'exact-use');
            const referenceArtifacts = targetArtifacts.filter((a)=>a.usageType === 'reference');
            if (exactUseArtifacts.length > 0) {
                artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\n';
                exactUseArtifacts.forEach((artifact)=>{
                    artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\n`;
                });
            }
            if (referenceArtifacts.length > 0) {
                artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\n';
                referenceArtifacts.forEach((artifact)=>{
                    artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\n`;
                });
            }
        }
        // Use simplified Revo model generation with text validation
        console.log(`🎨 Using Revo ${revoModel} with text validation...`);
        console.log('🔧 DEBUG: This is the SIMPLIFIED code path');
        // Generate dynamic and varied text for each design
        const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);
        let imageText = textVariations.selectedText;
        if (revoModel === 'revo-1.0') {
            console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');
            console.log('🔍 Original text:', imageText);
            // Simple text validation for Revo 1.0
            const words = imageText.split(' ').filter((word)=>word.length > 0);
            if (words.length > 25) {
                console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);
                imageText = words.slice(0, 25).join(' ');
            }
            console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);
        }
        // Use sophisticated design prompt created by 20-year veteran designer + marketer
        const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);
        const designResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateCreativeAsset"])({
            prompt: designPrompt,
            outputType: 'image',
            referenceAssetUrl: null,
            useBrandProfile: true,
            brandProfile: profile,
            maskDataUrl: null
        });
        if (!designResult.imageUrl) {
            throw new Error('Design generation failed: No image URL returned');
        }
        // Generate content using the standard flow for caption and hashtags
        const contentResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$post$2d$from$2d$profile$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generatePostFromProfile"])({
            businessType: profile.businessType,
            location: profile.location,
            writingTone: profile.writingTone,
            contentThemes: profile.contentThemes,
            visualStyle: profile.visualStyle,
            logoDataUrl: profile.logoDataUrl,
            designExamples: brandConsistency?.strictConsistency ? profile.designExamples || [] : [],
            primaryColor: profile.primaryColor,
            accentColor: profile.accentColor,
            backgroundColor: profile.backgroundColor,
            dayOfWeek: new Date().toLocaleDateString('en-US', {
                weekday: 'long'
            }),
            currentDate: new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            variants: [
                {
                    platform: platform,
                    aspectRatio: getAspectRatioForPlatform(platform)
                }
            ],
            services: Array.isArray(profile.services) ? profile.services.map((s)=>typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\n') : profile.services || '',
            targetAudience: profile.targetAudience,
            keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '',
            competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '',
            brandConsistency: brandConsistency || {
                strictConsistency: false,
                followBrandColors: true
            }
        });
        // Combine the design result with content result
        const newPost = {
            id: new Date().toISOString(),
            date: new Date().toISOString(),
            content: contentResult.content,
            hashtags: contentResult.hashtags,
            status: 'generated',
            variants: [
                {
                    platform,
                    imageUrl: designResult.imageUrl || '',
                    caption: contentResult.content,
                    hashtags: contentResult.hashtags
                }
            ],
            catchyWords: contentResult.catchyWords,
            subheadline: contentResult.subheadline,
            callToAction: contentResult.callToAction,
            contentVariants: contentResult.contentVariants,
            hashtagAnalysis: contentResult.hashtagAnalysis,
            marketIntelligence: contentResult.marketIntelligence,
            localContext: contentResult.localContext,
            // Add Revo model metadata
            revoModelUsed: revoModel,
            qualityScore: 8,
            processingTime: Date.now() - Date.now(),
            creditsUsed: 1,
            enhancementsApplied: [
                `Revo ${revoModel} Generation`,
                'Text Validation',
                'Professional Design'
            ]
        };
        console.log(`✅ Content generated successfully with ${revoModel}`);
        console.log(`⭐ Quality Score: 8/10`);
        console.log(`⚡ Processing Time: Fast generation`);
        console.log(`💰 Credits Used: 1`);
        return newPost;
    } catch (error) {
        console.error(`❌ Error generating content with ${revoModel}:`, error);
        throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Generate professional marketing-driven text with cultural awareness
 * Designed by a 20-year veteran designer + 20-year marketing expert
 * Now deeply connected to actual brand profile information
 */ function generateDynamicTextForRevo(profile, revoModel, platform) {
    const businessName = profile.businessName || 'Your Business';
    const businessType = profile.businessType || 'Professional Services';
    const services = Array.isArray(profile.services) ? profile.services : [];
    const location = profile.location || '';
    const description = profile.description || '';
    // Generate sophisticated marketing copy using actual brand profile data
    const marketingCopy = generateMarketingCopy(profile, platform);
    console.log(`🎯 Generated personalized marketing copy for ${businessName}: "${marketingCopy}"`);
    return {
        selectedText: marketingCopy,
        allVariations: [
            marketingCopy
        ],
        variationIndex: 0
    };
}
/**
 * Generate sophisticated marketing copy that sells
 * Combines 20 years of design + marketing expertise with actual brand profile data
 * Now deeply personalized using real business information
 */ function generateMarketingCopy(profile, platform) {
    const businessName = profile.businessName || 'Your Business';
    const businessType = profile.businessType || 'Professional Services';
    const location = profile.location || '';
    const description = profile.description || '';
    const services = Array.isArray(profile.services) ? profile.services : [];
    // Extract real business intelligence from profile
    const businessIntelligence = extractBusinessIntelligence(profile);
    // Cultural and regional insights
    const culturalContext = getCulturalContext(location);
    // Generate catchy headline using actual business strengths (max 5 words)
    const catchyHeadlines = [
        `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,
        `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,
        `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,
        `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,
        `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`
    ];
    // Generate subheadline using real competitive advantages (max 14 words)
    const subheadlines = [
        `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,
        `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,
        `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,
        `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,
        `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`
    ];
    // Generate call-to-action using actual business context
    const callToActions = [
        `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,
        `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,
        `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,
        `${getRandomElement([
            'Book',
            'Schedule',
            'Request'
        ])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,
        `${getRandomElement([
            'Start',
            'Begin',
            'Launch'
        ])} Your ${businessIntelligence.journeyType} Today`
    ];
    // Randomly select components
    const catchyWords = getRandomElement(catchyHeadlines);
    const subheadline = getRandomElement(subheadlines);
    const cta = getRandomElement(callToActions);
    // Combine based on marketing best practices and business context
    const marketingFormats = [
        `${catchyWords}\n${subheadline}\n${cta}`,
        `${catchyWords}\n${subheadline}`,
        `${catchyWords}\n${cta}`,
        `${subheadline}\n${cta}`,
        catchyWords
    ];
    return getRandomElement(marketingFormats);
}
/**
 * Get cultural context and local market insights
 */ function getCulturalContext(location) {
    // Default context
    let context = {
        localTerms: [
            'Premium',
            'Professional',
            'Expert'
        ],
        marketingStyle: 'Professional',
        targetAudience: 'businesses',
        localMarket: 'modern',
        socialProof: 'thousands of clients',
        valueProposition: 'Industry-leading',
        competitiveAdvantage: 'proven expertise',
        urgencyTrigger: 'Don\'t miss out on',
        actionWords: 'Connect with',
        localCTA: 'Get Started',
        ctaUrgency: 'Now'
    };
    // Location-specific cultural adaptations
    if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {
        context = {
            localTerms: [
                'Premium',
                'Luxury',
                'Elite',
                'Exclusive'
            ],
            marketingStyle: 'Luxury-focused',
            targetAudience: 'discerning clients',
            localMarket: 'Dubai\'s dynamic',
            socialProof: 'leading UAE businesses',
            valueProposition: 'World-class',
            competitiveAdvantage: 'international excellence',
            urgencyTrigger: 'Seize the opportunity for',
            actionWords: 'Experience',
            localCTA: 'Book Your Exclusive Consultation',
            ctaUrgency: 'Today'
        };
    } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {
        context = {
            localTerms: [
                'Bespoke',
                'Tailored',
                'Refined'
            ],
            marketingStyle: 'Sophisticated',
            targetAudience: 'discerning professionals',
            localMarket: 'London\'s competitive',
            socialProof: 'established UK enterprises',
            valueProposition: 'Expertly crafted',
            competitiveAdvantage: 'British excellence',
            urgencyTrigger: 'Secure your',
            actionWords: 'Discover',
            localCTA: 'Arrange Your Consultation',
            ctaUrgency: 'Promptly'
        };
    } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {
        context = {
            localTerms: [
                'Cutting-edge',
                'Innovative',
                'Game-changing'
            ],
            marketingStyle: 'Bold and direct',
            targetAudience: 'ambitious professionals',
            localMarket: 'NYC\'s fast-paced',
            socialProof: 'successful New York businesses',
            valueProposition: 'Results-driven',
            competitiveAdvantage: 'New York hustle',
            urgencyTrigger: 'Don\'t let competitors get',
            actionWords: 'Dominate with',
            localCTA: 'Schedule Your Strategy Session',
            ctaUrgency: 'ASAP'
        };
    }
    return context;
}
/**
 * Extract business intelligence from brand profile for personalized marketing
 * Analyzes actual business data to create relevant marketing copy
 */ function extractBusinessIntelligence(profile) {
    const businessName = profile.businessName || 'Your Business';
    const businessType = profile.businessType || 'Professional Services';
    const description = profile.description || '';
    const services = Array.isArray(profile.services) ? profile.services : [];
    const location = profile.location || '';
    // Extract primary service information
    const primaryService = services[0]?.name || services[0] || businessType;
    const serviceDescription = services[0]?.description || '';
    const targetAudience = services[0]?.targetAudience || 'businesses';
    const keyFeatures = services[0]?.keyFeatures || '';
    const competitiveAdvantages = services[0]?.competitiveAdvantages || '';
    // Analyze description for key terms
    const descriptionWords = description.toLowerCase().split(/\s+/);
    const strengthWords = extractStrengthWords(description, businessType);
    const valueWords = extractValueWords(description, keyFeatures);
    const benefitWords = extractBenefitWords(description, competitiveAdvantages);
    // Extract competitive advantages
    const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);
    const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);
    const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);
    // Extract target audience specifics
    const actualTargetAudience = extractTargetAudience(targetAudience, businessType);
    // Generate contextual elements
    const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);
    const mainValue = extractMainValue(description, keyFeatures);
    const offerType = generateOfferType(businessType, services);
    const consultationType = generateConsultationType(businessType);
    const journeyType = generateJourneyType(businessType, primaryService);
    const urgencyTrigger = generateUrgencyTrigger(businessType, location);
    // Extract outcome words from business context
    const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);
    const differentiators = extractDifferentiators(competitiveAdvantages, businessType);
    return {
        primaryService,
        strengthWords,
        valueWords,
        benefitWords,
        realCompetitiveAdvantage,
        uniqueFeature,
        realDifferentiator,
        actualTargetAudience,
        keyBenefit,
        mainValue,
        offerType,
        consultationType,
        journeyType,
        urgencyTrigger,
        outcomeWords,
        differentiators
    };
}
/**
 * Extract strength words from business description and type
 */ function extractStrengthWords(description, businessType) {
    const strengthKeywords = [
        'leading',
        'premium',
        'expert',
        'professional',
        'trusted',
        'innovative',
        'cutting-edge',
        'award-winning',
        'certified',
        'proven',
        'reliable',
        'secure',
        'fast',
        'efficient',
        'quality',
        'excellence',
        'superior',
        'advanced',
        'specialized'
    ];
    const found = strengthKeywords.filter((word)=>description.toLowerCase().includes(word));
    // Add business type specific strengths
    const typeStrengths = getBusinessTypeStrengths(businessType);
    return found.length > 0 ? found : typeStrengths;
}
/**
 * Extract value words from description and features
 */ function extractValueWords(description, keyFeatures) {
    const valueKeywords = [
        'value',
        'results',
        'success',
        'growth',
        'efficiency',
        'savings',
        'profit',
        'revenue',
        'performance',
        'productivity',
        'quality',
        'excellence',
        'innovation',
        'solutions',
        'benefits'
    ];
    const text = `${description} ${keyFeatures}`.toLowerCase();
    const found = valueKeywords.filter((word)=>text.includes(word));
    return found.length > 0 ? found : [
        'Excellence',
        'Results',
        'Success'
    ];
}
/**
 * Extract benefit words from description and competitive advantages
 */ function extractBenefitWords(description, competitiveAdvantages) {
    const benefitKeywords = [
        'success',
        'growth',
        'efficiency',
        'savings',
        'results',
        'performance',
        'quality',
        'reliability',
        'security',
        'speed',
        'convenience',
        'expertise',
        'support',
        'innovation',
        'excellence'
    ];
    const text = `${description} ${competitiveAdvantages}`.toLowerCase();
    const found = benefitKeywords.filter((word)=>text.includes(word));
    return found.length > 0 ? found : [
        'Success',
        'Quality',
        'Results'
    ];
}
/**
 * Extract competitive advantage from actual business data
 */ function extractCompetitiveAdvantage(competitiveAdvantages, businessType) {
    if (competitiveAdvantages && competitiveAdvantages.length > 10) {
        // Extract first meaningful advantage
        const advantages = competitiveAdvantages.split(',').map((s)=>s.trim());
        return advantages[0] || getDefaultAdvantage(businessType);
    }
    return getDefaultAdvantage(businessType);
}
/**
 * Extract unique feature from key features
 */ function extractUniqueFeature(keyFeatures, businessType) {
    if (keyFeatures && keyFeatures.length > 10) {
        const features = keyFeatures.split(',').map((s)=>s.trim());
        return features[0] || getDefaultFeature(businessType);
    }
    return getDefaultFeature(businessType);
}
/**
 * Extract differentiator from competitive advantages and description
 */ function extractDifferentiator(competitiveAdvantages, description) {
    const text = `${competitiveAdvantages} ${description}`.toLowerCase();
    if (text.includes('24/7') || text.includes('24-7')) return '24/7 availability';
    if (text.includes('fastest') || text.includes('quick') || text.includes('speed')) return 'fastest service';
    if (text.includes('secure') || text.includes('security')) return 'advanced security';
    if (text.includes('expert') || text.includes('experience')) return 'expert knowledge';
    if (text.includes('custom') || text.includes('tailored')) return 'customized solutions';
    if (text.includes('award') || text.includes('certified')) return 'award-winning service';
    return 'professional excellence';
}
/**
 * Extract target audience from service data
 */ function extractTargetAudience(targetAudience, businessType) {
    if (targetAudience && targetAudience.length > 5) {
        return targetAudience.split(',')[0].trim() || getDefaultAudience(businessType);
    }
    return getDefaultAudience(businessType);
}
/**
 * Extract key benefit from service description and advantages
 */ function extractKeyBenefit(serviceDescription, competitiveAdvantages) {
    const text = `${serviceDescription} ${competitiveAdvantages}`.toLowerCase();
    if (text.includes('save') || text.includes('cost')) return 'cost savings';
    if (text.includes('fast') || text.includes('quick') || text.includes('speed')) return 'faster results';
    if (text.includes('secure') || text.includes('safety')) return 'enhanced security';
    if (text.includes('grow') || text.includes('increase')) return 'business growth';
    if (text.includes('efficient') || text.includes('optimize')) return 'improved efficiency';
    if (text.includes('quality') || text.includes('premium')) return 'superior quality';
    return 'exceptional results';
}
/**
 * Extract main value proposition
 */ function extractMainValue(description, keyFeatures) {
    const text = `${description} ${keyFeatures}`.toLowerCase();
    if (text.includes('transform') || text.includes('revolutionize')) return 'transforms your business';
    if (text.includes('maximize') || text.includes('optimize')) return 'maximizes your potential';
    if (text.includes('accelerate') || text.includes('boost')) return 'accelerates your growth';
    if (text.includes('streamline') || text.includes('simplify')) return 'streamlines your operations';
    if (text.includes('enhance') || text.includes('improve')) return 'enhances your performance';
    return 'delivers exceptional value';
}
/**
 * Generate business type-specific offer types
 */ function generateOfferType(businessType, services) {
    const type = businessType.toLowerCase();
    if (type.includes('restaurant') || type.includes('food')) return 'Free Tasting';
    if (type.includes('tech') || type.includes('software')) return 'Free Demo';
    if (type.includes('health') || type.includes('medical')) return 'Free Consultation';
    if (type.includes('finance') || type.includes('banking')) return 'Free Assessment';
    if (type.includes('real estate')) return 'Free Valuation';
    if (type.includes('legal')) return 'Free Consultation';
    if (type.includes('education') || type.includes('training')) return 'Free Trial';
    return 'Free Consultation';
}
/**
 * Generate consultation types based on business
 */ function generateConsultationType(businessType) {
    const type = businessType.toLowerCase();
    if (type.includes('tech') || type.includes('software')) return 'Strategy Session';
    if (type.includes('health') || type.includes('medical')) return 'Health Assessment';
    if (type.includes('finance') || type.includes('banking')) return 'Financial Review';
    if (type.includes('real estate')) return 'Property Consultation';
    if (type.includes('legal')) return 'Legal Consultation';
    if (type.includes('marketing')) return 'Marketing Audit';
    return 'Business Consultation';
}
/**
 * Generate journey types
 */ function generateJourneyType(businessType, primaryService) {
    const type = businessType.toLowerCase();
    if (type.includes('tech') || type.includes('digital')) return 'Digital Transformation';
    if (type.includes('health') || type.includes('wellness')) return 'Wellness Journey';
    if (type.includes('finance') || type.includes('investment')) return 'Financial Success';
    if (type.includes('real estate')) return 'Property Investment';
    if (type.includes('education')) return 'Learning Journey';
    return 'Success Journey';
}
/**
 * Generate urgency triggers based on business and location
 */ function generateUrgencyTrigger(businessType, location) {
    const triggers = [
        'Limited Time Offer',
        'Act Now',
        'Don\'t Wait',
        'Book Today',
        'Available Now'
    ];
    if (location.toLowerCase().includes('dubai')) return 'Exclusive Dubai Offer';
    if (location.toLowerCase().includes('london')) return 'Limited London Availability';
    if (location.toLowerCase().includes('new york')) return 'NYC Exclusive Deal';
    return getRandomElement(triggers);
}
/**
 * Get business type specific strengths
 */ function getBusinessTypeStrengths(businessType) {
    const type = businessType.toLowerCase();
    if (type.includes('restaurant') || type.includes('food')) return [
        'Premium',
        'Fresh',
        'Authentic'
    ];
    if (type.includes('tech') || type.includes('software')) return [
        'Innovative',
        'Cutting-edge',
        'Advanced'
    ];
    if (type.includes('health') || type.includes('medical')) return [
        'Trusted',
        'Professional',
        'Expert'
    ];
    if (type.includes('finance') || type.includes('banking')) return [
        'Secure',
        'Reliable',
        'Trusted'
    ];
    if (type.includes('real estate')) return [
        'Premium',
        'Exclusive',
        'Professional'
    ];
    if (type.includes('legal')) return [
        'Expert',
        'Trusted',
        'Professional'
    ];
    if (type.includes('education')) return [
        'Expert',
        'Certified',
        'Professional'
    ];
    return [
        'Professional',
        'Quality',
        'Trusted'
    ];
}
/**
 * Get default competitive advantage by business type
 */ function getDefaultAdvantage(businessType) {
    const type = businessType.toLowerCase();
    if (type.includes('restaurant') || type.includes('food')) return 'Fresh, authentic ingredients';
    if (type.includes('tech') || type.includes('software')) return 'Cutting-edge technology';
    if (type.includes('health') || type.includes('medical')) return 'Expert medical care';
    if (type.includes('finance') || type.includes('banking')) return 'Secure financial solutions';
    if (type.includes('real estate')) return 'Premium property expertise';
    if (type.includes('legal')) return 'Expert legal guidance';
    return 'Professional excellence';
}
/**
 * Get default feature by business type
 */ function getDefaultFeature(businessType) {
    const type = businessType.toLowerCase();
    if (type.includes('restaurant') || type.includes('food')) return 'farm-to-table freshness';
    if (type.includes('tech') || type.includes('software')) return 'advanced automation';
    if (type.includes('health') || type.includes('medical')) return 'personalized care';
    if (type.includes('finance') || type.includes('banking')) return 'secure transactions';
    if (type.includes('real estate')) return 'market expertise';
    return 'personalized service';
}
/**
 * Get default audience by business type
 */ function getDefaultAudience(businessType) {
    const type = businessType.toLowerCase();
    if (type.includes('restaurant') || type.includes('food')) return 'food enthusiasts';
    if (type.includes('tech') || type.includes('software')) return 'forward-thinking businesses';
    if (type.includes('health') || type.includes('medical')) return 'health-conscious individuals';
    if (type.includes('finance') || type.includes('banking')) return 'smart investors';
    if (type.includes('real estate')) return 'property investors';
    return 'discerning clients';
}
/**
 * Extract outcome words from business context
 */ function extractOutcomeWords(description, competitiveAdvantages) {
    const text = `${description} ${competitiveAdvantages}`.toLowerCase();
    const outcomes = [
        'success',
        'growth',
        'results',
        'performance',
        'efficiency',
        'savings',
        'profit',
        'revenue'
    ];
    const found = outcomes.filter((word)=>text.includes(word));
    return found.length > 0 ? found : [
        'success',
        'results',
        'growth'
    ];
}
/**
 * Extract differentiators from competitive advantages
 */ function extractDifferentiators(competitiveAdvantages, businessType) {
    if (competitiveAdvantages && competitiveAdvantages.length > 10) {
        const advantages = competitiveAdvantages.split(',').map((s)=>s.trim().split(' ')[0]);
        return advantages.slice(0, 3);
    }
    return getBusinessTypeStrengths(businessType);
}
/**
 * Get random element from array
 */ function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}
/**
 * Create professional design prompt with 20 years of design + marketing expertise
 * Combines cultural awareness, psychology, and visual design mastery
 */ function createProfessionalDesignPrompt(imageText, platform, profile, revoModel) {
    const businessName = profile.businessName || 'Your Business';
    const businessType = profile.businessType || 'Professional Services';
    const location = profile.location || '';
    const description = profile.description || '';
    const services = Array.isArray(profile.services) ? profile.services : [];
    // Extract business intelligence for design context
    const businessIntelligence = extractBusinessIntelligence(profile);
    // Get cultural context for design decisions
    const culturalContext = getCulturalContext(location);
    // Industry-specific design psychology
    const industryDesignPsychology = getIndustryDesignPsychology(businessType);
    // Platform-specific design requirements
    const platformRequirements = getPlatformDesignRequirements(platform);
    // Color psychology based on business type and culture
    const colorPsychology = getColorPsychology(businessType, location);
    // Typography psychology for conversion
    const typographyStrategy = getTypographyStrategy(businessType, platform);
    return `Create an exceptional, conversion-focused ${platform} design for ${businessName} that embodies 20 years of professional design and marketing expertise.

BUSINESS INTELLIGENCE & CONTEXT:
- Company: ${businessName} (${businessType})
- Primary Service: ${businessIntelligence.primaryService}
- Location: ${location}
- Target Audience: ${businessIntelligence.actualTargetAudience}
- Key Differentiator: ${businessIntelligence.realDifferentiator}
- Unique Value: ${businessIntelligence.mainValue}
- Competitive Advantage: ${businessIntelligence.realCompetitiveAdvantage}

TEXT TO INTEGRATE: "${imageText}"

BRAND-SPECIFIC DESIGN REQUIREMENTS:
- Must communicate: ${businessIntelligence.realCompetitiveAdvantage}
- Must highlight: ${businessIntelligence.uniqueFeature}
- Must appeal to: ${businessIntelligence.actualTargetAudience}
- Must convey: ${businessIntelligence.keyBenefit}

DESIGN PSYCHOLOGY & STRATEGY:
${industryDesignPsychology}

VISUAL HIERARCHY & COMPOSITION:
- Apply the golden ratio and rule of thirds for optimal visual flow
- Create clear focal points that guide the eye to key conversion elements
- Use strategic white space to enhance readability and premium feel
- Implement Z-pattern or F-pattern layout for maximum engagement

COLOR STRATEGY:
${colorPsychology}

TYPOGRAPHY MASTERY:
${typographyStrategy}

CULTURAL DESIGN ADAPTATION:
- ${culturalContext.localMarket} aesthetic preferences
- ${culturalContext.targetAudience} visual expectations
- Regional design trends and cultural symbols
- Local color associations and meanings

CONVERSION OPTIMIZATION:
- Design elements that create urgency and desire
- Visual cues that guide toward call-to-action
- Trust signals through professional presentation
- Emotional triggers through strategic imagery and layout

PLATFORM OPTIMIZATION:
${platformRequirements}

TECHNICAL EXCELLENCE:
- Aspect Ratio: 1:1 (perfect square)
- Resolution: Ultra-high quality, print-ready standards
- Text Clarity: Crystal clear, perfectly readable at all sizes
- Brand Consistency: Align with professional brand standards
- Mobile Optimization: Ensure perfect display on all devices

FINAL QUALITY STANDARDS:
This design must look like it was created by a top-tier creative agency specifically for ${businessName}. Every element should reflect their unique value proposition: "${businessIntelligence.realCompetitiveAdvantage}". The design should immediately communicate their expertise in ${businessIntelligence.primaryService} while appealing directly to ${businessIntelligence.actualTargetAudience}.

The final result should be a sophisticated, professional design that drives ${businessIntelligence.actualTargetAudience} to choose ${businessName} over competitors and take immediate action.

Make it absolutely irresistible for ${businessIntelligence.actualTargetAudience} and perfectly aligned with ${businessName}'s brand identity.`;
}
/**
 * Get industry-specific design psychology
 */ function getIndustryDesignPsychology(businessType) {
    const type = businessType.toLowerCase();
    if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {
        return `- Use warm, appetizing colors that stimulate hunger and comfort
- Incorporate food photography principles with rich textures
- Create cozy, inviting atmosphere through design elements
- Focus on sensory appeal and mouth-watering visual presentation`;
    }
    if (type.includes('tech') || type.includes('software') || type.includes('digital')) {
        return `- Employ clean, minimalist design with high-tech aesthetics
- Use gradients and modern geometric shapes
- Incorporate subtle tech-inspired elements and icons
- Focus on innovation, efficiency, and cutting-edge appeal`;
    }
    if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {
        return `- Use calming, trustworthy colors that convey safety and care
- Incorporate clean, sterile design elements
- Focus on professionalism, expertise, and patient comfort
- Use imagery that suggests health, vitality, and well-being`;
    }
    if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {
        return `- Employ sophisticated, conservative design elements
- Use colors that convey stability, trust, and prosperity
- Incorporate subtle luxury elements and professional imagery
- Focus on security, growth, and financial success`;
    }
    if (type.includes('real estate') || type.includes('property')) {
        return `- Use aspirational imagery and luxury design elements
- Incorporate architectural lines and premium materials
- Focus on lifestyle, investment, and dream fulfillment
- Use colors that suggest stability, growth, and success`;
    }
    // Default professional services
    return `- Use professional, trustworthy design elements
- Incorporate subtle premium touches and quality indicators
- Focus on expertise, reliability, and professional excellence
- Use colors and imagery that convey competence and success`;
}
/**
 * Get platform-specific design requirements
 */ function getPlatformDesignRequirements(platform) {
    switch(platform){
        case 'Instagram':
            return `- Optimize for Instagram's visual-first environment
- Use bold, eye-catching elements that stand out in feeds
- Incorporate Instagram-native design trends and aesthetics
- Ensure design works perfectly in both feed and story formats`;
        case 'Facebook':
            return `- Design for Facebook's diverse, multi-generational audience
- Use clear, readable elements that work across age groups
- Incorporate social proof and community-focused elements
- Ensure design is engaging but not overwhelming`;
        case 'LinkedIn':
            return `- Employ professional, business-focused design elements
- Use conservative colors and sophisticated typography
- Incorporate industry-specific imagery and professional symbols
- Focus on credibility, expertise, and business value`;
        case 'Twitter':
            return `- Create concise, impactful design that communicates quickly
- Use bold typography and clear visual hierarchy
- Incorporate trending design elements and current aesthetics
- Ensure design is optimized for rapid consumption`;
        default:
            return `- Create versatile design that works across multiple platforms
- Use universal design principles and broad appeal
- Ensure scalability and readability across different contexts
- Focus on timeless, professional aesthetics`;
    }
}
/**
 * Get color psychology based on business type and location
 */ function getColorPsychology(businessType, location) {
    const type = businessType.toLowerCase();
    const loc = location.toLowerCase();
    let baseColors = '';
    let culturalColors = '';
    // Business type color psychology
    if (type.includes('restaurant') || type.includes('food')) {
        baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';
    } else if (type.includes('tech') || type.includes('digital')) {
        baseColors = 'modern blues, teals, and purples to convey innovation and trust';
    } else if (type.includes('health') || type.includes('medical')) {
        baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';
    } else if (type.includes('finance') || type.includes('banking')) {
        baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';
    } else {
        baseColors = 'professional blues, grays, and accent colors to convey trust and competence';
    }
    // Cultural color adaptations
    if (loc.includes('dubai') || loc.includes('uae')) {
        culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\'s premium market expectations';
    } else if (loc.includes('london') || loc.includes('uk')) {
        culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';
    } else if (loc.includes('new york') || loc.includes('nyc')) {
        culturalColors = 'Employ bold, confident colors that match New York\'s dynamic business environment';
    } else {
        culturalColors = 'Use universally appealing professional color combinations';
    }
    return `- Primary Strategy: ${baseColors}
- Cultural Adaptation: ${culturalColors}
- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors
- Contrast Optimization: Ensure maximum readability and visual impact`;
}
/**
 * Get typography strategy for conversion
 */ function getTypographyStrategy(businessType, platform) {
    const type = businessType.toLowerCase();
    let fontStrategy = '';
    let hierarchyStrategy = '';
    if (type.includes('luxury') || type.includes('premium')) {
        fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';
    } else if (type.includes('tech') || type.includes('digital')) {
        fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';
    } else if (type.includes('creative') || type.includes('design')) {
        fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';
    } else {
        fontStrategy = 'Professional, highly readable fonts that convey trust and competence';
    }
    hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact
- Secondary Text: Clear, readable subheadings that support the main message
- Call-to-Action: Distinctive typography that stands out and drives action
- Supporting Text: Clean, professional fonts for additional information`;
    return `- Font Selection: ${fontStrategy}
- Visual Hierarchy: ${hierarchyStrategy}
- Readability: Optimized for ${platform} viewing conditions and mobile devices
- Conversion Focus: Typography choices designed to guide the eye and encourage action`;
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    analyzeBrandAction,
    generateContentAction,
    generateVideoContentAction,
    generateCreativeAssetAction,
    generateEnhancedDesignAction,
    generateGeminiHDDesignAction,
    generateContentWithArtifactsAction,
    generateContentWithRevoModelAction
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(analyzeBrandAction, "60ea2a632798675c707aba182342fbd2fe9934e7b2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateContentAction, "70370719efd459954676917a0ea0a33701731123ce", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateVideoContentAction, "7032bdb3854aae97c69a87de824651ac55c853892e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateCreativeAssetAction, "7fa71ea55dac91d9f2fbbc47e38b16f32c34406758", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateEnhancedDesignAction, "7f36c3e89c9711bb510eae73accd75d39762aa73f5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateGeminiHDDesignAction, "7ff7d412819238ab9e627cc3d406ad2cce0c9fa6c9", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateContentWithArtifactsAction, "7c59f40eb0b80a2dc5e7a84991c84ddc885ae6ce31", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateContentWithRevoModelAction, "7c77a46b52594dce7ad1477e7b4988f6f2475453ef", null);
}}),
"[project]/src/ai/imagen-4-service.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Imagen 4 Service for Revo 2.0
 * Next-generation AI image generation with advanced capabilities
 */ __turbopack_context__.s({
    "IMAGEN_4_MODELS": (()=>IMAGEN_4_MODELS),
    "REVO_2_ASPECT_RATIOS": (()=>REVO_2_ASPECT_RATIOS),
    "generateImagen4Image": (()=>generateImagen4Image),
    "getImagen4Capabilities": (()=>getImagen4Capabilities)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-rsc] (ecmascript)");
;
// Initialize Google AI with API key
const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || process.env.GOOGLE_GENAI_API_KEY;
if (!apiKey) {
    console.error("❌ No Google AI API key found for Imagen 4. Please set GEMINI_API_KEY, GOOGLE_API_KEY, or GOOGLE_GENAI_API_KEY environment variable.");
}
const genAI = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](apiKey);
const IMAGEN_4_MODELS = {
    STANDARD: 'imagen-4',
    ULTRA: 'imagen-4-ultra'
};
const REVO_2_ASPECT_RATIOS = {
    SQUARE: '1:1',
    LANDSCAPE: '16:9',
    PORTRAIT: '9:16',
    WIDE: '21:9',
    TALL: '4:5'
};
/**
 * Convert aspect ratio to actual pixel dimensions for Revo 2.0
 */ function getAspectRatioDimensions(aspectRatio) {
    const dimensionMap = {
        '1:1': {
            width: 1080,
            height: 1080
        },
        '16:9': {
            width: 1200,
            height: 675
        },
        '9:16': {
            width: 675,
            height: 1200
        },
        '21:9': {
            width: 1400,
            height: 600
        },
        '4:5': {
            width: 1080,
            height: 1350
        }
    };
    return dimensionMap[aspectRatio] || dimensionMap['1:1'];
}
/**
 * Map Revo 2.0 aspect ratios to Imagen 4 API format
 */ function mapToImagen4AspectRatio(revo2AspectRatio) {
    const aspectRatioMap = {
        '1:1': '1:1',
        '16:9': '16:9',
        '9:16': '9:16',
        '4:5': '3:4',
        '21:9': '16:9'
    };
    return aspectRatioMap[revo2AspectRatio] || '1:1';
}
/**
 * Generate Google Cloud access token using service account
 */ async function getGoogleAccessToken(serviceAccount) {
    const jwt = __turbopack_context__.r("[project]/node_modules/jsonwebtoken/index.js [app-rsc] (ecmascript)");
    const now = Math.floor(Date.now() / 1000);
    const payload = {
        iss: serviceAccount.client_email,
        scope: 'https://www.googleapis.com/auth/cloud-platform',
        aud: 'https://oauth2.googleapis.com/token',
        exp: now + 3600,
        iat: now
    };
    const token = jwt.sign(payload, serviceAccount.private_key, {
        algorithm: 'RS256'
    });
    const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
            grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            assertion: token
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to get access token: ${response.statusText}`);
    }
    const data = await response.json();
    return data.access_token;
}
/**
 * Build comprehensive AI prompt for Revo 2.0 image generation
 */ function buildRevo2Prompt(basePrompt, config) {
    const platformContext = getPlatformContext(config.platform);
    const styleEnhancements = getStyleEnhancements(config.style);
    const qualityModifiers = getQualityModifiers(config.quality);
    return `
🎨 REVO 2.0 AI DESIGN GENERATION 🎨

${basePrompt}

📱 PLATFORM: ${config.platform?.toUpperCase()}
${platformContext}

🎭 VISUAL STYLE: ${config.style}
${styleEnhancements}

⚡ QUALITY LEVEL: ${config.quality}
${qualityModifiers}

📐 ASPECT RATIO: ${config.aspectRatio}
- Generate image in exact ${config.aspectRatio} format
- Platform-optimized dimensions for ${config.platform}

🚀 REVO 2.0 SPECIFICATIONS:
- Ultra-high quality AI generation
- Professional social media design
- Modern, engaging visual composition
- Brand-appropriate aesthetic
- Platform-native optimization
- Next-generation AI enhancement

Generate a stunning, professional social media design that captures attention and drives engagement.
`.trim();
}
/**
 * Get platform-specific context for AI generation
 */ function getPlatformContext(platform) {
    const contexts = {
        'instagram': '- Square format optimized for Instagram feed\n- Visually striking and scroll-stopping\n- Mobile-first design approach',
        'facebook': '- Landscape format for Facebook news feed\n- Community-focused and shareable\n- Engaging for social interaction',
        'linkedin': '- Professional landscape format\n- Business-appropriate and polished\n- Career and industry focused',
        'twitter': '- Landscape format for Twitter timeline\n- Attention-grabbing and concise\n- News-worthy and trending'
    };
    return contexts[platform?.toLowerCase() || ''] || '- Social media optimized design\n- Platform-appropriate formatting';
}
/**
 * Get style-specific enhancements for AI generation
 */ function getStyleEnhancements(style) {
    const enhancements = {
        'photographic': '- Realistic photography style\n- Natural lighting and shadows\n- High-resolution details',
        'artistic': '- Creative and expressive design\n- Artistic composition and colors\n- Unique visual elements',
        'digital_art': '- Modern digital art style\n- Clean vector graphics\n- Contemporary design elements',
        'cinematic': '- Dramatic lighting and composition\n- Movie-like visual quality\n- Epic and engaging atmosphere',
        'anime': '- Vibrant anime-inspired style\n- Colorful and dynamic design\n- Playful visual elements',
        'sketch': '- Hand-drawn sketch aesthetic\n- Minimal and clean lines\n- Artistic simplicity'
    };
    return enhancements[style || ''] || '- Professional visual style\n- High-quality design elements';
}
/**
 * Get quality-specific modifiers for AI generation
 */ function getQualityModifiers(quality) {
    const modifiers = {
        'ultra': '- Maximum detail and resolution\n- Ultra-high quality rendering\n- Professional-grade output',
        'high': '- High-quality generation\n- Detailed and polished\n- Premium visual quality',
        'standard': '- Standard quality output\n- Good visual quality\n- Reliable generation'
    };
    return modifiers[quality || ''] || '- Quality AI generation\n- Professional output';
}
/**
 * Generate enhanced prompt for Imagen 4
 */ function enhancePromptForImagen4(basePrompt, options, brandColors) {
    const enhancements = [];
    // Add style enhancement
    if (options.style) {
        const styleMap = {
            photographic: 'professional photography style, high resolution, sharp details',
            artistic: 'artistic composition, creative interpretation, expressive style',
            digital_art: 'digital art style, modern aesthetic, clean lines',
            cinematic: 'cinematic composition, dramatic framing, movie-like quality',
            anime: 'anime art style, vibrant colors, stylized characters',
            sketch: 'hand-drawn sketch style, artistic lines, creative interpretation'
        };
        enhancements.push(styleMap[options.style]);
    }
    // Add lighting enhancement
    if (options.lighting) {
        const lightingMap = {
            natural: 'natural lighting, soft shadows, realistic illumination',
            studio: 'professional studio lighting, even illumination, clean shadows',
            dramatic: 'dramatic lighting, strong contrast, moody atmosphere',
            soft: 'soft diffused lighting, gentle shadows, warm ambiance',
            golden_hour: 'golden hour lighting, warm tones, beautiful natural glow',
            neon: 'neon lighting effects, vibrant colors, modern urban aesthetic'
        };
        enhancements.push(lightingMap[options.lighting]);
    }
    // Add mood enhancement
    if (options.mood) {
        const moodMap = {
            professional: 'professional atmosphere, clean composition, business-appropriate',
            energetic: 'dynamic energy, vibrant composition, active feeling',
            calm: 'peaceful atmosphere, serene composition, relaxing mood',
            vibrant: 'bright vibrant colors, lively composition, energetic feel',
            elegant: 'elegant sophistication, refined composition, luxury aesthetic',
            bold: 'bold statement, strong visual impact, confident composition'
        };
        enhancements.push(moodMap[options.mood]);
    }
    // Add brand colors if provided
    if (brandColors && brandColors.length > 0) {
        enhancements.push(`incorporating brand colors: ${brandColors.join(', ')}`);
    }
    // Add quality enhancement
    if (options.quality === 'ultra') {
        enhancements.push('ultra-high quality, maximum detail, professional grade');
    } else if (options.quality === 'high') {
        enhancements.push('high quality, detailed rendering, professional standard');
    }
    // Combine base prompt with enhancements
    const enhancedPrompt = `${basePrompt}. ${enhancements.join(', ')}.`;
    return enhancedPrompt;
}
async function generateImagen4Image(prompt, options = {}) {
    const startTime = Date.now();
    try {
        console.log('🌟 Starting Revo 2.0 Imagen 4 generation...');
        // Set defaults
        const config = {
            model: options.model || IMAGEN_4_MODELS.STANDARD,
            aspectRatio: options.aspectRatio || REVO_2_ASPECT_RATIOS.SQUARE,
            quality: options.quality || 'high',
            style: options.style || 'photographic',
            lighting: options.lighting || 'natural',
            mood: options.mood || 'professional',
            enhancePrompt: options.enhancePrompt !== false,
            safetyLevel: options.safetyLevel || 'moderate',
            // Revo 2.0 specific options
            brandProfile: options.brandProfile,
            brandConsistency: options.brandConsistency,
            platform: options.platform
        };
        // Enhance prompt if requested
        let finalPrompt = prompt;
        let enhancedPrompt;
        if (config.enhancePrompt) {
            enhancedPrompt = enhancePromptForImagen4(prompt, config, options.colorScheme);
            finalPrompt = enhancedPrompt;
        }
        console.log(`🎨 Using ${config.model} with ${config.aspectRatio} aspect ratio`);
        console.log(`🎯 Style: ${config.style}, Quality: ${config.quality}`);
        // Get the Imagen 4 model
        const model = genAI.getGenerativeModel({
            model: config.model,
            generationConfig: {
                // Imagen 4 specific configuration
                temperature: 0.7,
                topP: 0.9,
                topK: 40
            }
        });
        // Generate actual social media design with Revo 2.0 enhanced capabilities
        console.log('🎨 Revo 2.0 Imagen 4 generating social media design...');
        console.log(`- Prompt: ${finalPrompt}`);
        console.log(`- Aspect Ratio: ${config.aspectRatio}`);
        console.log(`- Style: ${config.style}`);
        console.log(`- Quality: ${config.quality}`);
        console.log(`- Brand Profile: ${config.brandProfile ? 'PROVIDED' : 'MISSING'}`);
        console.log(`- Platform: ${config.platform || 'MISSING'}`);
        let imageUrl;
        try {
            // Pure Revo 2.0 generation - no mixing with enhanced design system
            console.log('🚀 Using pure Revo 2.0 Imagen 4 generation...');
            // Get platform-specific dimensions for Revo 2.0
            const dimensions = getAspectRatioDimensions(config.aspectRatio);
            console.log(`📐 Platform dimensions: ${dimensions.width}x${dimensions.height} (${config.aspectRatio})`);
            // Build comprehensive prompt for Revo 2.0 AI generation
            const revo2Prompt = buildRevo2Prompt(finalPrompt, config);
            console.log(`🎨 Revo 2.0 AI prompt: ${revo2Prompt.substring(0, 100)}...`);
            // Generate actual AI image with Revo 2.0 specifications
            console.log('🤖 Generating AI design with Revo 2.0 specifications...');
            try {
                // Use actual Imagen 4 API with proper format
                console.log('🚀 Calling Imagen 4 API with proper format...');
                // Map Revo 2.0 aspect ratios to Imagen 4 format
                const imagen4AspectRatio = mapToImagen4AspectRatio(config.aspectRatio);
                // Determine sample image size based on quality
                const sampleImageSize = config.quality === 'ultra' ? '2K' : '1K';
                // Build Imagen 4 API request body
                const imagen4Request = {
                    instances: [
                        {
                            prompt: revo2Prompt,
                            aspectRatio: imagen4AspectRatio,
                            sampleImageSize: sampleImageSize
                        }
                    ]
                };
                console.log(`📐 Imagen 4 Request: aspectRatio=${imagen4AspectRatio}, sampleImageSize=${sampleImageSize}`);
                console.log(`🎨 Imagen 4 Prompt: ${revo2Prompt.substring(0, 150)}...`);
                // Make actual Imagen 4 API call using Firebase service account
                console.log('🚀 Making actual Imagen 4 API call...');
                // Get project ID from Firebase config
                const projectId = ("TURBOPACK compile-time value", "localbuzz-mpkuv");
                const serviceAccountKey = process.env.FIREBASE_SERVICE_ACCOUNT_KEY;
                if (!projectId || !serviceAccountKey) {
                    throw new Error('❌ IMAGEN 4 NOT CONFIGURED: Missing Firebase project ID or service account key');
                }
                // Parse service account key
                const serviceAccount = JSON.parse(serviceAccountKey);
                // Generate access token using service account
                const accessToken = await getGoogleAccessToken(serviceAccount);
                const response = await fetch(`https://us-central1-aiplatform.googleapis.com/v1/projects/${projectId}/locations/us-central1/publishers/google/models/imagen-4.0-generate-001:predict`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(imagen4Request)
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`❌ IMAGEN 4 API FAILED: ${response.status} ${response.statusText} - ${errorText}`);
                }
                const result = await response.json();
                console.log('✅ Imagen 4 API response received');
                // Extract image from Imagen 4 response
                if (!result.predictions || !result.predictions[0] || !result.predictions[0].bytesBase64Encoded) {
                    throw new Error('❌ IMAGEN 4 RESPONSE INVALID: No image data in response');
                }
                imageUrl = `data:image/png;base64,${result.predictions[0].bytesBase64Encoded}`;
                console.log('✅ Revo 2.0 Imagen 4 generation completed successfully');
            } catch (aiError) {
                console.error('❌ IMAGEN 4 GENERATION FAILED:', aiError.message);
                throw new Error(`Imagen 4 generation failed: ${aiError.message}`);
            }
        } catch (error) {
            console.error('❌ REVO 2.0 IMAGEN 4 GENERATION FAILED:', error);
            throw new Error(`Revo 2.0 Imagen 4 generation failed: ${error.message}`);
        }
        const processingTime = Date.now() - startTime;
        // Calculate quality score based on configuration
        let qualityScore = 7.0; // Base score
        if (config.quality === 'ultra') qualityScore += 2.0;
        else if (config.quality === 'high') qualityScore += 1.0;
        if (config.model === IMAGEN_4_MODELS.ULTRA) qualityScore += 1.0;
        if (config.enhancePrompt) qualityScore += 0.5;
        qualityScore = Math.min(10.0, qualityScore);
        // Track enhancements applied
        const enhancementsApplied = [];
        if (config.enhancePrompt) enhancementsApplied.push('Enhanced Prompting');
        if (config.quality === 'ultra') enhancementsApplied.push('Ultra Quality');
        if (config.model === IMAGEN_4_MODELS.ULTRA) enhancementsApplied.push('Imagen 4 Ultra');
        enhancementsApplied.push(`${config.style} Style`);
        enhancementsApplied.push(`${config.lighting} Lighting`);
        enhancementsApplied.push(`${config.mood} Mood`);
        console.log(`✅ Revo 2.0 Imagen 4 generation completed in ${processingTime}ms`);
        console.log(`🎯 Quality Score: ${qualityScore}/10`);
        console.log(`🚀 Enhancements: ${enhancementsApplied.join(', ')}`);
        return {
            imageUrl,
            model: config.model,
            aspectRatio: config.aspectRatio,
            qualityScore,
            processingTime,
            enhancementsApplied,
            metadata: {
                prompt,
                enhancedPrompt,
                style: config.style,
                quality: config.quality,
                safetyLevel: config.safetyLevel
            }
        };
    } catch (error) {
        console.error('❌ Revo 2.0 Imagen 4 generation failed:', error);
        throw new Error(`Imagen 4 generation failed: ${error.message}`);
    }
}
function getImagen4Capabilities() {
    return {
        models: IMAGEN_4_MODELS,
        aspectRatios: REVO_2_ASPECT_RATIOS,
        styles: [
            'photographic',
            'artistic',
            'digital_art',
            'cinematic',
            'anime',
            'sketch'
        ],
        lighting: [
            'natural',
            'studio',
            'dramatic',
            'soft',
            'golden_hour',
            'neon'
        ],
        moods: [
            'professional',
            'energetic',
            'calm',
            'vibrant',
            'elegant',
            'bold'
        ],
        qualities: [
            'standard',
            'high',
            'ultra'
        ],
        features: [
            'Multi-aspect ratio support',
            'Advanced style control',
            'Professional lighting options',
            'Mood-based generation',
            'Brand color integration',
            'Enhanced prompt engineering',
            'Ultra-high quality output'
        ]
    };
}
}}),
"[project]/src/ai/flows/revo-2-generation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Revo 2.0 Generation Flow
 * Next-generation content creation with Imagen 4 integration
 */ __turbopack_context__.s({
    "generateRevo2Content": (()=>generateRevo2Content),
    "getRevo2Capabilities": (()=>getRevo2Capabilities)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$imagen$2d$4$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/imagen-4-service.ts [app-rsc] (ecmascript)");
;
/**
 * Map platform to optimal aspect ratio for Revo 2.0
 */ function getPlatformAspectRatio(platform) {
    const platformMap = {
        Instagram: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$imagen$2d$4$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REVO_2_ASPECT_RATIOS"].SQUARE,
        Facebook: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$imagen$2d$4$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REVO_2_ASPECT_RATIOS"].LANDSCAPE,
        Twitter: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$imagen$2d$4$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REVO_2_ASPECT_RATIOS"].LANDSCAPE,
        LinkedIn: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$imagen$2d$4$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REVO_2_ASPECT_RATIOS"].LANDSCAPE
    };
    return platformMap[platform] || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$imagen$2d$4$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REVO_2_ASPECT_RATIOS"].SQUARE;
}
/**
 * Generate enhanced prompt for Revo 2.0
 */ function generateRevo2Prompt(basePrompt, platform, brandProfile, style) {
    const platformContext = {
        Instagram: 'Instagram post, visually striking, social media optimized',
        Facebook: 'Facebook post, engaging and shareable, community-focused',
        Twitter: 'Twitter post, attention-grabbing, news-worthy',
        LinkedIn: 'LinkedIn post, professional and business-focused'
    };
    let enhancedPrompt = `🎨 REVO 2.0 AI DESIGN GENERATION 🎨

${basePrompt}. ${platformContext[platform] || platformContext.Instagram}.

**TEXT READABILITY REQUIREMENTS:**
- NO unreadable, blurry, or distorted text
- Any text in the image must be clear, legible, and professional
- Use high contrast between text and background
- Avoid text that appears corrupted, pixelated, or illegible
- If text is included, ensure it's crisp and readable at all sizes`;
    // Add brand context if available
    if (brandProfile) {
        if (brandProfile.businessType) {
            enhancedPrompt += ` ${brandProfile.businessType} business context.`;
        }
        if (brandProfile.visualStyle) {
            enhancedPrompt += ` ${brandProfile.visualStyle} visual style.`;
        }
        if (brandProfile.targetAudience) {
            enhancedPrompt += ` Targeting ${brandProfile.targetAudience}.`;
        }
    }
    // Add style-specific enhancements
    if (style) {
        const styleEnhancements = {
            photographic: 'Professional photography, realistic, high-quality, ultra-sharp details, professional lighting',
            artistic: 'Creative artistic interpretation, expressive, unique, vibrant colors',
            digital_art: 'Modern digital art, clean, contemporary, crisp graphics',
            cinematic: 'Movie-like quality, dramatic, cinematic composition, professional grade',
            anime: 'Anime art style, vibrant, stylized, clean lines',
            sketch: 'Hand-drawn aesthetic, artistic, creative, clear strokes'
        };
        enhancedPrompt += ` ${styleEnhancements[style]}.`;
    }
    return enhancedPrompt;
}
/**
 * Generate caption for Revo 2.0 content
 */ function generateRevo2Caption(prompt, platform, brandProfile) {
    // This is a simplified caption generation
    // In a real implementation, you might use another AI model for this
    const platformStyles = {
        Instagram: '✨ ',
        Facebook: '🚀 ',
        Twitter: '💡 ',
        LinkedIn: '📈 '
    };
    const emoji = platformStyles[platform] || '✨ ';
    let caption = `${emoji}${prompt}`;
    if (brandProfile?.businessName) {
        caption += ` #${brandProfile.businessName.replace(/\s+/g, '')}`;
    }
    return caption;
}
/**
 * Generate hashtags for Revo 2.0 content
 */ function generateRevo2Hashtags(platform, brandProfile) {
    console.log('🏷️ Generating hashtags for platform:', platform, 'type:', typeof platform);
    const baseHashtags = {
        instagram: [
            '#instagram',
            '#content',
            '#creative',
            '#design'
        ],
        facebook: [
            '#facebook',
            '#social',
            '#community',
            '#engagement'
        ],
        twitter: [
            '#twitter',
            '#trending',
            '#news',
            '#update'
        ],
        linkedin: [
            '#linkedin',
            '#professional',
            '#business',
            '#networking'
        ],
        tiktok: [
            '#tiktok',
            '#viral',
            '#trending',
            '#fyp'
        ],
        youtube: [
            '#youtube',
            '#video',
            '#content',
            '#creator'
        ],
        pinterest: [
            '#pinterest',
            '#inspiration',
            '#ideas',
            '#discover'
        ]
    };
    // Normalize platform to lowercase and handle edge cases
    const normalizedPlatform = String(platform || 'instagram').toLowerCase().trim();
    console.log('🏷️ Normalized platform:', normalizedPlatform);
    const selectedHashtags = baseHashtags[normalizedPlatform] || baseHashtags.instagram;
    console.log('🏷️ Selected hashtags:', selectedHashtags);
    if (!Array.isArray(selectedHashtags)) {
        console.error('❌ Selected hashtags is not an array:', selectedHashtags);
        return [
            ...baseHashtags.instagram
        ];
    }
    let hashtags = [
        ...selectedHashtags
    ];
    // Add brand-specific hashtags
    if (brandProfile) {
        if (brandProfile.businessType) {
            hashtags.push(`#${brandProfile.businessType.replace(/\s+/g, '')}`);
        }
        if (brandProfile.businessName) {
            hashtags.push(`#${brandProfile.businessName.replace(/\s+/g, '')}`);
        }
    }
    // Add Revo 2.0 signature
    hashtags.push('#Revo2', '#NextGenAI');
    return hashtags.slice(0, 8); // Limit to 8 hashtags
}
async function generateRevo2Content(input) {
    const startTime = Date.now();
    try {
        console.log('🌟 Starting Revo 2.0 next-generation content creation...');
        console.log(`📱 Platform: ${input.platform}`);
        console.log(`🎨 Style: ${input.style || 'photographic'}`);
        console.log(`⚡ Quality: ${input.quality || 'high'}`);
        // Determine optimal aspect ratio
        const aspectRatio = input.aspectRatio || getPlatformAspectRatio(input.platform);
        // Generate enhanced prompt
        const enhancedPrompt = generateRevo2Prompt(input.prompt, input.platform, input.brandProfile, input.style);
        // Prepare Imagen 4 options with Revo 2.0 enhancements
        const imagen4Options = {
            aspectRatio,
            quality: input.quality || 'high',
            style: input.style || 'photographic',
            mood: input.mood || 'professional',
            enhancePrompt: input.enhancePrompt !== false,
            colorScheme: input.brandProfile?.primaryColor ? [
                input.brandProfile.primaryColor
            ] : undefined,
            safetyLevel: 'moderate',
            // Revo 2.0 specific options for design generation
            brandProfile: input.brandProfile,
            brandConsistency: input.brandConsistency,
            platform: input.platform
        };
        // Generate image with Imagen 4
        console.log('🎯 Generating image with Imagen 4...');
        const imageResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$imagen$2d$4$2d$service$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateImagen4Image"])(enhancedPrompt, imagen4Options);
        // Generate caption and hashtags
        const caption = generateRevo2Caption(input.prompt, input.platform, input.brandProfile);
        const hashtags = generateRevo2Hashtags(input.platform, input.brandProfile);
        const totalProcessingTime = Date.now() - startTime;
        // Combine enhancements
        const enhancementsApplied = [
            ...imageResult.enhancementsApplied,
            'Revo 2.0 Platform Optimization',
            'Smart Aspect Ratio Selection',
            'Advanced Caption Generation',
            'Intelligent Hashtag Strategy'
        ];
        console.log(`✅ Revo 2.0 content generation completed in ${totalProcessingTime}ms`);
        console.log(`🎯 Final Quality Score: ${imageResult.qualityScore}/10`);
        console.log(`🚀 Total Enhancements: ${enhancementsApplied.length}`);
        return {
            imageUrl: imageResult.imageUrl,
            caption,
            hashtags,
            platform: input.platform,
            aspectRatio,
            qualityScore: imageResult.qualityScore,
            processingTime: totalProcessingTime,
            enhancementsApplied,
            metadata: {
                model: imageResult.model,
                style: input.style || 'photographic',
                quality: input.quality || 'high',
                brandIntegration: !!input.brandProfile
            }
        };
    } catch (error) {
        console.error('❌ Revo 2.0 content generation failed:', error);
        throw new Error(`Revo 2.0 generation failed: ${error.message}`);
    }
}
function getRevo2Capabilities() {
    return {
        name: 'Revo 2.0',
        description: 'Next-generation AI content creation with Imagen 4',
        features: [
            'Imagen 4 Ultra integration',
            'Multi-aspect ratio support (1:1, 16:9, 9:16, 21:9, 4:5)',
            'Advanced style control (6 styles)',
            'Professional mood settings',
            'Brand color integration',
            'Platform-optimized generation',
            'Enhanced prompt engineering',
            'Ultra-high quality output',
            'Smart caption generation',
            'Intelligent hashtag strategy'
        ],
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        qualityRange: '8.0-10.0/10',
        status: 'Next-Generation'
    };
}
}}),
"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"009528f95601e6332a1f76b6c581dc57776b12dc46":"getRevo2CapabilitiesAction","70ecaa50ef609cec48931812d952a2688c5a97bca8":"generateRevo2CreativeAssetAction","7c4958387ae4634086778aea63574415427db832a7":"generateRevo2ContentAction"},"",""] */ __turbopack_context__.s({
    "generateRevo2ContentAction": (()=>generateRevo2ContentAction),
    "generateRevo2CreativeAssetAction": (()=>generateRevo2CreativeAssetAction),
    "getRevo2CapabilitiesAction": (()=>getRevo2CapabilitiesAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
/**
 * Revo 2.0 Server Actions
 * Next-generation content creation with Imagen 4 integration
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$revo$2d$2$2d$generation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/revo-2-generation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function generateRevo2ContentAction(brandProfile, platform, brandConsistency, prompt, options) {
    try {
        console.log('🌟 Starting Revo 2.0 content generation...');
        console.log(`📱 Platform: ${platform}`);
        console.log(`🎨 Style: ${options?.style || 'photographic'}`);
        console.log(`⚡ Quality: ${options?.quality || 'ultra'}`);
        // Generate content prompt if not provided
        const contentPrompt = prompt || generateContentPrompt(brandProfile, platform);
        // Prepare Revo 2.0 input
        const revo2Input = {
            prompt: contentPrompt,
            platform,
            aspectRatio: options?.aspectRatio,
            brandProfile,
            brandConsistency,
            style: options?.style || 'photographic',
            quality: options?.quality || 'ultra',
            mood: options?.mood || 'professional',
            enhancePrompt: true
        };
        // Generate with Revo 2.0
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$revo$2d$2$2d$generation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRevo2Content"])(revo2Input);
        console.log(`✅ Revo 2.0 generation completed!`);
        console.log(`🎯 Quality Score: ${result.qualityScore}/10`);
        console.log(`🚀 Enhancements: ${result.enhancementsApplied.length}`);
        // Convert to GeneratedPost format
        const generatedPost = {
            id: `revo2-${Date.now()}`,
            date: new Date().toISOString(),
            platform: platform.toLowerCase(),
            postType: 'post',
            imageUrl: result.imageUrl,
            content: result.caption,
            hashtags: result.hashtags,
            status: 'generated',
            variants: [
                {
                    platform: platform.toLowerCase(),
                    imageUrl: result.imageUrl
                }
            ],
            catchyWords: '',
            createdAt: new Date(),
            brandProfileId: brandProfile.id || 'unknown',
            qualityScore: result.qualityScore,
            metadata: {
                model: 'revo-2.0',
                qualityScore: result.qualityScore,
                processingTime: result.processingTime,
                enhancementsApplied: result.enhancementsApplied,
                aspectRatio: result.aspectRatio,
                ...result.metadata
            }
        };
        return generatedPost;
    } catch (error) {
        console.error('❌ Revo 2.0 content generation failed:', error);
        throw new Error(`Revo 2.0 generation failed: ${error.message}`);
    }
}
async function generateRevo2CreativeAssetAction(prompt, brandProfile, options) {
    try {
        console.log('🎨 Starting Revo 2.0 creative asset generation...');
        console.log(`🎯 Prompt: ${prompt}`);
        console.log(`🎨 Style: ${options?.style || 'photographic'}`);
        // Prepare Revo 2.0 input
        const revo2Input = {
            prompt,
            platform: options?.platform || 'Instagram',
            aspectRatio: options?.aspectRatio,
            brandProfile,
            style: options?.style || 'photographic',
            quality: options?.quality || 'ultra',
            mood: options?.mood || 'professional',
            enhancePrompt: true
        };
        // Generate with Revo 2.0
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$revo$2d$2$2d$generation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRevo2Content"])(revo2Input);
        // Create AI explanation
        const aiExplanation = `🌟 Revo 2.0 Ultra Generation Complete!

🎯 Quality Score: ${result.qualityScore}/10
⚡ Processing Time: ${result.processingTime}ms
🎨 Style: ${result.metadata.style}
📐 Aspect Ratio: ${result.aspectRatio}
🔧 Model: ${result.metadata.model}

🚀 Enhancements Applied:
${result.enhancementsApplied.map((enhancement)=>`• ${enhancement}`).join('\n')}

This image was created using our next-generation AI engine with revolutionary capabilities, delivering ultra-high quality results with advanced style control and professional optimization.`;
        console.log(`✅ Revo 2.0 creative asset completed!`);
        console.log(`🎯 Quality Score: ${result.qualityScore}/10`);
        return {
            imageUrl: result.imageUrl,
            aiExplanation,
            qualityScore: result.qualityScore,
            processingTime: result.processingTime,
            enhancementsApplied: result.enhancementsApplied
        };
    } catch (error) {
        console.error('❌ Revo 2.0 creative asset generation failed:', error);
        throw new Error(`Revo 2.0 creative asset generation failed: ${error.message}`);
    }
}
/**
 * Generate content prompt based on brand profile and platform
 */ function generateContentPrompt(brandProfile, platform) {
    const businessType = brandProfile.businessType || 'business';
    const visualStyle = brandProfile.visualStyle || 'modern';
    const platformPrompts = {
        Instagram: `Create an engaging Instagram post for a ${businessType} with ${visualStyle} style`,
        Facebook: `Design a shareable Facebook post for a ${businessType} with ${visualStyle} aesthetic`,
        Twitter: `Generate a Twitter-optimized image for a ${businessType} with ${visualStyle} design`,
        LinkedIn: `Create a professional LinkedIn post for a ${businessType} with ${visualStyle} style`
    };
    let prompt = platformPrompts[platform] || platformPrompts.Instagram;
    // Add brand-specific context
    if (brandProfile.businessName) {
        prompt += ` for ${brandProfile.businessName}`;
    }
    if (brandProfile.targetAudience) {
        prompt += `, targeting ${brandProfile.targetAudience}`;
    }
    if (brandProfile.primaryColor) {
        prompt += `, incorporating ${brandProfile.primaryColor} brand color`;
    }
    return prompt;
}
async function getRevo2CapabilitiesAction() {
    return {
        name: 'Revo 2.0',
        description: 'Next-generation AI content creation with revolutionary capabilities',
        features: [
            'Ultra-high quality image generation',
            'Multi-aspect ratio support (1:1, 16:9, 9:16, 21:9, 4:5)',
            'Advanced style control (6 professional styles)',
            'Professional mood settings',
            'Brand color integration',
            'Platform-optimized generation',
            'Enhanced prompt engineering',
            'Revolutionary AI engine',
            'Smart caption generation',
            'Intelligent hashtag strategy'
        ],
        styles: [
            {
                id: 'photographic',
                name: 'Photographic',
                description: 'Professional photography style'
            },
            {
                id: 'artistic',
                name: 'Artistic',
                description: 'Creative artistic interpretation'
            },
            {
                id: 'digital_art',
                name: 'Digital Art',
                description: 'Modern digital art style'
            },
            {
                id: 'cinematic',
                name: 'Cinematic',
                description: 'Movie-like cinematic quality'
            },
            {
                id: 'anime',
                name: 'Anime',
                description: 'Anime art style'
            },
            {
                id: 'sketch',
                name: 'Sketch',
                description: 'Hand-drawn sketch aesthetic'
            }
        ],
        qualities: [
            {
                id: 'standard',
                name: 'Standard',
                description: 'Good quality, fast generation'
            },
            {
                id: 'high',
                name: 'High',
                description: 'High quality, balanced performance'
            },
            {
                id: 'ultra',
                name: 'Ultra',
                description: 'Maximum quality, premium results'
            }
        ],
        aspectRatios: [
            {
                id: '1:1',
                name: 'Square',
                description: 'Perfect for Instagram posts'
            },
            {
                id: '16:9',
                name: 'Landscape',
                description: 'Great for Facebook, YouTube'
            },
            {
                id: '9:16',
                name: 'Portrait',
                description: 'Ideal for TikTok, Stories'
            },
            {
                id: '21:9',
                name: 'Ultra Wide',
                description: 'Cinematic wide format'
            },
            {
                id: '4:5',
                name: 'Tall',
                description: 'Perfect for Pinterest'
            }
        ],
        supportedPlatforms: [
            'Instagram',
            'Facebook',
            'Twitter',
            'LinkedIn'
        ],
        qualityRange: '8.5-10.0/10',
        status: 'Revolutionary'
    };
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    generateRevo2ContentAction,
    generateRevo2CreativeAssetAction,
    getRevo2CapabilitiesAction
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateRevo2ContentAction, "7c4958387ae4634086778aea63574415427db832a7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateRevo2CreativeAssetAction, "70ecaa50ef609cec48931812d952a2688c5a97bca8", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getRevo2CapabilitiesAction, "009528f95601e6332a1f76b6c581dc57776b12dc46", null);
}}),
"[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "409e36c34489ad587473a8902e0f3440bb36957de3": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateCreativeAsset"]),
    "60ea2a632798675c707aba182342fbd2fe9934e7b2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["analyzeBrandAction"]),
    "7032bdb3854aae97c69a87de824651ac55c853892e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateVideoContentAction"]),
    "70370719efd459954676917a0ea0a33701731123ce": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateContentAction"]),
    "70ecaa50ef609cec48931812d952a2688c5a97bca8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateRevo2CreativeAssetAction"]),
    "7c59f40eb0b80a2dc5e7a84991c84ddc885ae6ce31": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateContentWithArtifactsAction"]),
    "7c77a46b52594dce7ad1477e7b4988f6f2475453ef": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateContentWithRevoModelAction"]),
    "7f36c3e89c9711bb510eae73accd75d39762aa73f5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateEnhancedDesignAction"]),
    "7fa71ea55dac91d9f2fbbc47e38b16f32c34406758": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateCreativeAssetAction"]),
    "7ff7d412819238ab9e627cc3d406ad2cce0c9fa6c9": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateGeminiHDDesignAction"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "409e36c34489ad587473a8902e0f3440bb36957de3": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["409e36c34489ad587473a8902e0f3440bb36957de3"]),
    "60ea2a632798675c707aba182342fbd2fe9934e7b2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60ea2a632798675c707aba182342fbd2fe9934e7b2"]),
    "7032bdb3854aae97c69a87de824651ac55c853892e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7032bdb3854aae97c69a87de824651ac55c853892e"]),
    "70370719efd459954676917a0ea0a33701731123ce": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["70370719efd459954676917a0ea0a33701731123ce"]),
    "70ecaa50ef609cec48931812d952a2688c5a97bca8": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["70ecaa50ef609cec48931812d952a2688c5a97bca8"]),
    "7c59f40eb0b80a2dc5e7a84991c84ddc885ae6ce31": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7c59f40eb0b80a2dc5e7a84991c84ddc885ae6ce31"]),
    "7c77a46b52594dce7ad1477e7b4988f6f2475453ef": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7c77a46b52594dce7ad1477e7b4988f6f2475453ef"]),
    "7f36c3e89c9711bb510eae73accd75d39762aa73f5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f36c3e89c9711bb510eae73accd75d39762aa73f5"]),
    "7fa71ea55dac91d9f2fbbc47e38b16f32c34406758": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7fa71ea55dac91d9f2fbbc47e38b16f32c34406758"]),
    "7ff7d412819238ab9e627cc3d406ad2cce0c9fa6c9": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7ff7d412819238ab9e627cc3d406ad2cce0c9fa6c9"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$brand$2d$profile$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$actions$2f$revo$2d$2$2d$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => "[project]/src/app/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/src/app/icon--metadata.js [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/icon--metadata.js [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/brand-profile/page.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/brand-profile/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/brand-profile/page.tsx <module evaluation>", "default");
}}),
"[project]/src/app/brand-profile/page.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/brand-profile/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/brand-profile/page.tsx", "default");
}}),
"[project]/src/app/brand-profile/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$brand$2d$profile$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/brand-profile/page.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$brand$2d$profile$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/brand-profile/page.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$brand$2d$profile$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/brand-profile/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/brand-profile/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__fa5684fb._.js.map
module.exports = {

"[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_0213b497._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/index.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/node_modules/cheerio/dist/esm/index.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_cheerio_dist_esm_5eefae71._.js",
  "server/chunks/ssr/node_modules_ce35667b._.js",
  "server/chunks/ssr/node_modules_parse5_dist_a77ceaf0._.js",
  "server/chunks/ssr/node_modules_eb82991d._.js",
  "server/chunks/ssr/node_modules_undici_557f89fb._.js",
  "server/chunks/ssr/node_modules_1a0bc68a._.js",
  "server/chunks/ssr/[root-of-the-server]__3c1c97d1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/cheerio/dist/esm/index.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/node_modules/node-fetch/src/utils/multipart-parser.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_1defaf5c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/utils/multipart-parser.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_ai_utils_rss-feeds-integration_ts_cc1e7466._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/utils/rss-feeds-integration.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/lib/image-processing.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[root-of-the-server]__db310d95._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/image-processing.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/openai-enhanced-design.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_ai_flows_generate-creative-asset_ts_d85b4525._.js",
  "server/chunks/ssr/node_modules_openai_29ffe15d._.js",
  "server/chunks/ssr/src_ai_c5d75105._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/openai-enhanced-design.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_ai_615342a7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/gemini-hd-enhanced-design.ts [app-rsc] (ecmascript)");
    });
});
}}),

};
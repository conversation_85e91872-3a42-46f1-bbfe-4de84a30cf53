{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/modern-design-prompts.ts"], "sourcesContent": ["/**\r\n * Modern Design Generation Prompts (2024-2025)\r\n * \r\n * Ultra-modern design specifications incorporating the latest design trends,\r\n * contemporary aesthetics, and cutting-edge visual techniques.\r\n */\r\n\r\nexport const MODERN_DESIGN_TRENDS_2024_2025 = `\r\n**GLASSMORPHISM & TRANSPARENCY EFFECTS:**\r\n- Semi-transparent backgrounds with frosted glass appearance\r\n- Subtle blur effects (backdrop-filter: blur(10px))\r\n- Layered transparency with depth perception\r\n- Light border highlights on glass elements\r\n- Soft, diffused lighting through transparent surfaces\r\n\r\n**NEUMORPHISM & SOFT UI:**\r\n- Subtle inset and outset shadows for depth\r\n- Soft, pillow-like button appearances\r\n- Minimal contrast with gentle elevation\r\n- Organic, rounded corner treatments\r\n- Tactile, touchable interface elements\r\n\r\n**ADVANCED GRADIENT TECHNIQUES:**\r\n- Multi-point gradient meshes with 3+ colors\r\n- Radial gradients with complex color stops\r\n- Gradient overlays for depth and atmosphere\r\n- Animated gradient effects (implied through positioning)\r\n- Color bleeding and soft transitions\r\n\r\n**CONTEMPORARY TYPOGRAPHY:**\r\n- Oversized, bold headlines (48px+ equivalent)\r\n- Modern sans-serif fonts (Inter, SF Pro, system-ui)\r\n- Creative letter spacing and line height\r\n- Typography as visual elements, not just text\r\n- Mixed font weights for hierarchy and contrast\r\n\r\n**ORGANIC & FLUID SHAPES:**\r\n- Blob-like, organic forms mixed with geometric elements\r\n- Flowing, curved lines and natural boundaries\r\n- Asymmetrical layouts with intentional imbalance\r\n- Liquid-like shapes and morphing elements\r\n- Nature-inspired forms and patterns\r\n`;\r\n\r\nexport const MODERN_COLOR_PSYCHOLOGY_2024 = `\r\n**VIBRANT & SATURATED PALETTES:**\r\n- High-contrast color combinations\r\n- Neon accents with dark backgrounds\r\n- Monochromatic schemes with pop colors\r\n- Gradient color stories across elements\r\n- Color psychology for emotional impact\r\n\r\n**TRENDING COLOR SCHEMES:**\r\n- Electric blues (#3B82F6, #6366F1, #8B5CF6)\r\n- Vibrant purples (#A855F7, #C084FC, #E879F9)\r\n- Energetic oranges (#F59E0B, #FB923C, #FDBA74)\r\n- Modern greens (#10B981, #34D399, #6EE7B7)\r\n- Contemporary pinks (#EC4899, #F472B6, #F9A8D4)\r\n\r\n**ADVANCED COLOR TECHNIQUES:**\r\n- 60-30-10 rule with modern twist\r\n- Analogous colors with high saturation\r\n- Complementary contrasts for visual impact\r\n- Triadic schemes for dynamic energy\r\n- Split-complementary for sophisticated balance\r\n`;\r\n\r\nexport const MODERN_LAYOUT_PRINCIPLES = `\r\n**ASYMMETRICAL COMPOSITIONS:**\r\n- Intentionally unbalanced layouts for visual interest\r\n- Dynamic grid systems with broken elements\r\n- Floating components with strategic placement\r\n- Visual weight distribution for modern appeal\r\n- Negative space as a design element\r\n\r\n**CONTEMPORARY SPACING:**\r\n- Generous white space (minimum 24px equivalent)\r\n- Rhythmic spacing patterns\r\n- Breathing room around key elements\r\n- Intentional crowding for emphasis\r\n- Micro-interactions through spacing\r\n\r\n**MODERN VISUAL HIERARCHY:**\r\n- Size contrast for immediate attention\r\n- Color contrast for emotional response\r\n- Position contrast for flow direction\r\n- Texture contrast for tactile appeal\r\n- Motion contrast for dynamic energy\r\n`;\r\n\r\nexport const MODERN_VISUAL_EFFECTS = `\r\n**ADVANCED SHADOW TECHNIQUES:**\r\n- Multiple light source shadows\r\n- Soft, realistic drop shadows (0 20px 25px rgba(0,0,0,0.1))\r\n- Inner shadows for depth perception\r\n- Colored shadows matching brand palette\r\n- Layered shadows for complex depth\r\n\r\n**CONTEMPORARY TEXTURES:**\r\n- Subtle noise overlays (opacity: 0.03-0.05)\r\n- Organic grain patterns\r\n- Geometric texture patterns\r\n- Paper-like textures for warmth\r\n- Digital glitch effects for tech brands\r\n\r\n**MODERN LIGHTING EFFECTS:**\r\n- Rim lighting on key elements\r\n- Ambient lighting for atmosphere\r\n- Directional lighting for drama\r\n- Soft, diffused lighting for approachability\r\n- High-contrast lighting for impact\r\n`;\r\n\r\nexport const PLATFORM_MODERN_OPTIMIZATIONS = {\r\n  instagram: `\r\n**INSTAGRAM 2024-2025 TRENDS:**\r\n- Bold, thumb-stopping visuals for feed\r\n- High contrast for mobile viewing\r\n- Story-optimized vertical compositions\r\n- Reel-ready dynamic layouts\r\n- Carousel-friendly modular designs\r\n- Modern hashtag integration\r\n- Contemporary emoji usage patterns\r\n`,\r\n\r\n  linkedin: `\r\n**LINKEDIN PROFESSIONAL MODERN:**\r\n- Sophisticated color palettes\r\n- Professional typography with personality\r\n- Clean, minimal layouts with impact\r\n- Industry-appropriate modern trends\r\n- Thought leadership visual language\r\n- Corporate glassmorphism effects\r\n- Professional gradient applications\r\n`,\r\n\r\n  facebook: `\r\n**FACEBOOK CONTEMPORARY DESIGN:**\r\n- Community-focused visual language\r\n- Warm, approachable modern aesthetics\r\n- Multi-generational appeal\r\n- Accessible modern design principles\r\n- Social proof integration\r\n- Contemporary engagement patterns\r\n- Modern storytelling visuals\r\n`,\r\n\r\n  twitter: `\r\n**TWITTER/X MODERN AESTHETICS:**\r\n- Quick-scan optimized layouts\r\n- High-impact minimal designs\r\n- Trending topic integration\r\n- Modern conversation starters\r\n- Contemporary meme aesthetics\r\n- Real-time relevant visuals\r\n- Modern social commentary style\r\n`,\r\n\r\n  tiktok: `\r\n**TIKTOK CUTTING-EDGE TRENDS:**\r\n- Gen Z optimized aesthetics\r\n- Vertical-first modern design\r\n- Trend-aware visual language\r\n- Contemporary youth culture\r\n- Modern video-ready layouts\r\n- Dynamic, energetic compositions\r\n- Current viral visual patterns\r\n`\r\n};\r\n\r\nexport const BUSINESS_TYPE_MODERN_DNA = {\r\n  tech: `\r\n**TECH MODERN AESTHETICS:**\r\n- Futuristic gradient applications\r\n- Digital-first design language\r\n- AI-inspired visual elements\r\n- Cyberpunk color influences\r\n- Modern data visualization\r\n- Contemporary tech iconography\r\n- Innovation-focused compositions\r\n`,\r\n\r\n  healthcare: `\r\n**HEALTHCARE CONTEMPORARY DESIGN:**\r\n- Trustworthy modern aesthetics\r\n- Calming gradient applications\r\n- Professional glassmorphism\r\n- Accessible modern typography\r\n- Contemporary wellness visuals\r\n- Modern medical iconography\r\n- Healing-focused color psychology\r\n`,\r\n\r\n  finance: `\r\n**FINANCE MODERN SOPHISTICATION:**\r\n- Premium gradient applications\r\n- Trustworthy contemporary design\r\n- Professional modern aesthetics\r\n- Sophisticated color palettes\r\n- Modern financial iconography\r\n- Contemporary wealth visuals\r\n- Security-focused design language\r\n`,\r\n\r\n  retail: `\r\n**RETAIL CONTEMPORARY TRENDS:**\r\n- Shopping-optimized modern design\r\n- Product-focused compositions\r\n- Contemporary e-commerce aesthetics\r\n- Modern lifestyle integration\r\n- Trend-aware visual language\r\n- Contemporary consumer psychology\r\n- Modern brand storytelling\r\n`,\r\n\r\n  restaurant: `\r\n**RESTAURANT MODERN APPEAL:**\r\n- Food-focused contemporary design\r\n- Appetite-appealing modern colors\r\n- Contemporary culinary aesthetics\r\n- Modern dining experience visuals\r\n- Trend-aware food photography style\r\n- Contemporary hospitality design\r\n- Modern taste visualization\r\n`\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;AAEM,MAAM,iCAAiC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmC/C,CAAC;AAEM,MAAM,+BAA+B,CAAC;;;;;;;;;;;;;;;;;;;;;AAqB7C,CAAC;AAEM,MAAM,2BAA2B,CAAC;;;;;;;;;;;;;;;;;;;;;AAqBzC,CAAC;AAEM,MAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;;;;;;;AAqBtC,CAAC;AAEM,MAAM,gCAAgC;IAC3C,WAAW,CAAC;;;;;;;;;AASd,CAAC;IAEC,UAAU,CAAC;;;;;;;;;AASb,CAAC;IAEC,UAAU,CAAC;;;;;;;;;AASb,CAAC;IAEC,SAAS,CAAC;;;;;;;;;AASZ,CAAC;IAEC,QAAQ,CAAC;;;;;;;;;AASX,CAAC;AACD;AAEO,MAAM,2BAA2B;IACtC,MAAM,CAAC;;;;;;;;;AAST,CAAC;IAEC,YAAY,CAAC;;;;;;;;;AASf,CAAC;IAEC,SAAS,CAAC;;;;;;;;;AASZ,CAAC;IAEC,QAAQ,CAAC;;;;;;;;;AASX,CAAC;IAEC,YAAY,CAAC;;;;;;;;;AASf,CAAC;AACD", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/gemini-hd-enhanced-design.ts"], "sourcesContent": ["import { BrandProfile } from '@/lib/types';\r\nimport { ai } from './genkit';\r\nimport { GenerateRequest } from 'genkit/generate';\r\nimport {\r\n  MODERN_DESIGN_TRENDS_2024_2025,\r\n  MODERN_COLOR_PSYCHOLOGY_2024,\r\n  MODER<PERSON>_LAYOUT_PRINCIPLES,\r\n  MODERN_VISUAL_EFFECTS,\r\n  PLATFORM_MODERN_OPTIMIZATIONS,\r\n  BUSINESS_TYPE_MODERN_DNA\r\n} from './prompts/modern-design-prompts';\r\nimport {\r\n  ADVANCED_DESIGN_PRINCIPLES,\r\n  PLATFORM_SPECIFIC_GUIDELINES,\r\n  BUSINESS_TYPE_DESIGN_DNA,\r\n  QUALITY_ENHANCEMENT_INSTRUCTIONS\r\n} from './prompts/advanced-design-prompts';\r\nimport {\r\n  ANTI_CORRUPTION_PROMPT,\r\n  READABLE_TEXT_INSTRUCTIONS,\r\n  TEXT_GENERATION_SAFEGUARDS,\r\n  NEGATIVE_PROMPT_ADDITIONS\r\n} from './prompts/text-readability-prompts';\r\n\r\n/**\r\n * Helper function to get MIME type from data URI\r\n */\r\nfunction getMimeTypeFromDataURI(dataUri: string): string {\r\n  const match = dataUri.match(/^data:([^;]+);/);\r\n  return match ? match[1] : 'image/png';\r\n}\r\n\r\nexport interface GeminiHDEnhancedDesignInput {\r\n  businessType: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  imageText: string;\r\n  brandProfile: BrandProfile;\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  };\r\n  artifactInstructions?: string;\r\n}\r\n\r\nexport interface GeminiHDEnhancedDesignResult {\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}\r\n\r\n/**\r\n * Wraps ai.generate with retry logic for 503 errors.\r\n */\r\nasync function generateWithRetry(request: GenerateRequest, retries = 3, delay = 1000) {\r\n  for (let i = 0; i < retries; i++) {\r\n    try {\r\n      const result = await ai.generate(request);\r\n      return result;\r\n    } catch (e: any) {\r\n      if (e.message && e.message.includes('503') && i < retries - 1) {\r\n        console.log(`Attempt ${i + 1} failed with 503. Retrying in ${delay}ms...`);\r\n        await new Promise(resolve => setTimeout(resolve, delay));\r\n      } else {\r\n        if (e.message && e.message.includes('503')) {\r\n          throw new Error(\"The AI model is currently overloaded. Please try again in a few moments.\");\r\n        }\r\n        throw e; // Rethrow other errors immediately\r\n      }\r\n    }\r\n  }\r\n  throw new Error(\"Max retries exceeded\");\r\n}\r\n\r\n/**\r\n * Generate Ultra-HD design using Gemini 2.0 Flash with maximum quality settings\r\n * This provides superior image quality, perfect text rendering, and HD downloads\r\n */\r\nexport async function generateGeminiHDEnhancedDesign(\r\n  input: GeminiHDEnhancedDesignInput\r\n): Promise<GeminiHDEnhancedDesignResult> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!process.env.GEMINI_API_KEY && !process.env.GOOGLE_API_KEY && !process.env.GOOGLE_GENAI_API_KEY) {\r\n      throw new Error('Gemini API key is required. Please set GEMINI_API_KEY, GOOGLE_API_KEY, or GOOGLE_GENAI_API_KEY environment variable.');\r\n    }\r\n\r\n    // Validate and clean the text input\r\n    const cleanedText = validateAndCleanText(input.imageText);\r\n    const inputWithCleanText = { ...input, imageText: cleanedText };\r\n\r\n    // Get platform-specific aspect ratio for proper image generation\r\n    const aspectRatio = getPlatformAspectRatio(input.platform);\r\n    console.log(`🎯 Generating ${aspectRatio} aspect ratio for ${input.platform}`);\r\n\r\n    // Build enhanced prompt optimized for Gemini 2.0 Flash HD generation\r\n    const enhancedPrompt = buildGeminiHDPrompt(inputWithCleanText, aspectRatio);\r\n    enhancementsApplied.push('Gemini 2.0 Flash HD Optimized Prompting', 'Text Validation & Cleaning');\r\n\r\n    console.log('🎨 Generating Ultra-HD design with Gemini 2.0 Flash...');\r\n    console.log('📝 Original text:', `\"${input.imageText}\"`);\r\n    console.log('🧹 Cleaned text:', `\"${cleanedText}\"`);\r\n    console.log('🔄 Text changed:', input.imageText !== cleanedText ? 'YES' : 'NO');\r\n    console.log('📏 Prompt length:', enhancedPrompt.length);\r\n    console.log('🎯 Full prompt preview:', enhancedPrompt.substring(0, 200) + '...');\r\n\r\n    // Build prompt parts array with media inputs like standard generation\r\n    const promptParts: any[] = [{ text: enhancedPrompt }];\r\n\r\n    // Add logo if available\r\n    if (input.brandProfile.logoDataUrl) {\r\n      promptParts.push({\r\n        media: {\r\n          url: input.brandProfile.logoDataUrl,\r\n          contentType: getMimeTypeFromDataURI(input.brandProfile.logoDataUrl)\r\n        }\r\n      });\r\n    }\r\n\r\n    // Add design examples if available and strict consistency is enabled\r\n    if (input.brandConsistency?.strictConsistency && input.brandProfile.designExamples) {\r\n      input.brandProfile.designExamples.slice(0, 3).forEach(example => {\r\n        promptParts.push({\r\n          media: {\r\n            url: example,\r\n            contentType: getMimeTypeFromDataURI(example)\r\n          }\r\n        });\r\n      });\r\n    }\r\n\r\n    // Generate image with Gemini 2.0 Flash with HD quality settings and platform-specific aspect ratio\r\n    const { media } = await generateWithRetry({\r\n      model: 'googleai/gemini-2.0-flash-preview-image-generation',\r\n      prompt: promptParts,\r\n      config: {\r\n        responseModalities: ['TEXT', 'IMAGE'],\r\n        // Note: Gemini 2.0 Flash handles aspect ratio through prompt instructions\r\n        // The aspect ratio is specified in the prompt itself for better control\r\n      },\r\n    });\r\n\r\n    const imageUrl = media?.url;\r\n    if (!imageUrl) {\r\n      throw new Error('No image URL returned from Gemini');\r\n    }\r\n\r\n    enhancementsApplied.push(\r\n      'Gemini 2.0 Flash HD Generation',\r\n      'Ultra-High Quality Settings',\r\n      'Perfect Text Rendering',\r\n      'Professional Face Generation',\r\n      'Brand Color Compliance',\r\n      'Platform Optimization',\r\n      'HD Quality Assurance'\r\n    );\r\n\r\n    if (input.brandConsistency?.strictConsistency) {\r\n      enhancementsApplied.push('Strict Design Consistency');\r\n    }\r\n    if (input.brandConsistency?.followBrandColors) {\r\n      enhancementsApplied.push('Brand Color Enforcement');\r\n    }\r\n\r\n    console.log('✅ Gemini HD design generated successfully');\r\n    console.log('🔗 Image URL:', imageUrl);\r\n\r\n    return {\r\n      imageUrl,\r\n      qualityScore: 9.7, // Gemini 2.0 Flash HD - Excellent quality\r\n      enhancementsApplied,\r\n      processingTime: Date.now() - startTime,\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error generating Gemini HD enhanced design:', error);\r\n    throw new Error(`Gemini HD generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Build optimized prompt for Gemini 2.0 Flash HD generation\r\n * Enhanced with best practices for maximum quality and accuracy\r\n */\r\nfunction buildGeminiHDPrompt(input: GeminiHDEnhancedDesignInput, aspectRatio: string): string {\r\n  const { businessType, platform, visualStyle, imageText, brandProfile, brandConsistency, artifactInstructions } = input;\r\n\r\n  // Generate unique variation elements for each request\r\n  const generationId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  const variationSeed = Math.floor(Math.random() * 1000);\r\n\r\n  // Random layout variations\r\n  const layoutVariations = [\r\n    'asymmetrical composition with dynamic balance',\r\n    'grid-based layout with clean alignment',\r\n    'centered composition with radial elements',\r\n    'diagonal flow with leading lines',\r\n    'layered depth with foreground/background separation'\r\n  ];\r\n  const selectedLayout = layoutVariations[Math.floor(Math.random() * layoutVariations.length)];\r\n\r\n  // Random style modifiers\r\n  const styleModifiers = [\r\n    'with subtle gradient overlays',\r\n    'with bold geometric accents',\r\n    'with organic flowing elements',\r\n    'with modern minimalist approach',\r\n    'with dynamic energy and movement'\r\n  ];\r\n  const selectedModifier = styleModifiers[Math.floor(Math.random() * styleModifiers.length)];\r\n\r\n  // Enhanced color instructions optimized for Gemini's color accuracy\r\n  const colorInstructions = brandProfile.primaryColor && brandProfile.accentColor\r\n    ? `Primary brand color: ${brandProfile.primaryColor}, Secondary brand color: ${brandProfile.accentColor}. Use these colors prominently and consistently throughout the design.`\r\n    : 'Use a cohesive, professional color palette with high contrast and modern appeal.';\r\n\r\n  // Advanced people inclusion logic for better engagement with HD face rendering\r\n  const shouldIncludePeople = shouldIncludePeopleInDesign(businessType, imageText, visualStyle);\r\n  const peopleInstructions = shouldIncludePeople\r\n    ? 'Include diverse, authentic people (various ethnicities, ages) with PERFECT FACIAL FEATURES - complete faces, symmetrical features, natural expressions, professional poses. Ensure faces are fully visible, well-lit, and anatomically correct with no deformations or missing features.'\r\n    : 'Focus on clean, minimalist design without people, emphasizing the product/service/message with ultra-sharp details.';\r\n\r\n  // Enhanced platform-specific optimization\r\n  const platformSpecs = getPlatformSpecifications(platform);\r\n\r\n  // Get platform-specific guidelines\r\n  const platformGuidelines = PLATFORM_SPECIFIC_GUIDELINES[platform.toLowerCase() as keyof typeof PLATFORM_SPECIFIC_GUIDELINES] || PLATFORM_SPECIFIC_GUIDELINES.instagram;\r\n\r\n  // Get business-specific design DNA\r\n  const businessDNA = BUSINESS_TYPE_DESIGN_DNA[businessType.toLowerCase() as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n  // Build rich, diverse design prompt like standard generation\r\n  const prompt = `You are a world-class creative director and visual designer with expertise in social media marketing, brand design, and visual psychology.\r\n\r\n**DESIGN BRIEF:**\r\nCreate a professional, high-impact social media design for a ${businessType} business.\r\n\r\n🎯 CRITICAL PLATFORM-SPECIFIC DIMENSIONS:\r\n- Platform: ${platform}\r\n- REQUIRED ASPECT RATIO: ${aspectRatio}\r\n- MUST generate image in EXACT ${aspectRatio} aspect ratio for ${platform}\r\n- Instagram: 1:1 square (1080x1080px) - Perfect for feed posts\r\n- Facebook: 16:9 landscape (1200x630px) - Optimized for news feed\r\n- LinkedIn: 16:9 landscape (1200x627px) - Professional networking format\r\n- Twitter: 16:9 landscape (1200x675px) - Timeline optimization\r\n- DO NOT generate wrong aspect ratios - this is CRITICAL for platform compatibility\r\n- ENSURE the generated image matches the platform's native ${aspectRatio} format\r\n- The image MUST be ${aspectRatio} aspect ratio - this is NON-NEGOTIABLE\r\n\r\nVisual Style: ${visualStyle} | Location: ${brandProfile.location || 'Global'}\r\n\r\n**TEXT CONTENT TO INCLUDE:**\r\nPrimary Text: \"${imageText}\"\r\n${brandProfile.businessName ? `Business Name: \"${brandProfile.businessName}\"` : ''}\r\n\r\n${ADVANCED_DESIGN_PRINCIPLES}\r\n\r\n${platformGuidelines}\r\n\r\n${businessDNA}\r\n\r\n**BRAND GUIDELINES:**\r\n${colorInstructions}\r\n${peopleInstructions}\r\n\r\n**CREATIVE VARIATION REQUIREMENTS:**\r\n- Layout Style: Use ${selectedLayout}\r\n- Design Approach: Create design ${selectedModifier}\r\n- Uniqueness: This design must be visually distinct from any previous generations\r\n- Generation ID: ${generationId} (use this to ensure uniqueness)\r\n- Variation Seed: ${variationSeed} (apply subtle randomization based on this number)\r\n\r\n**PLATFORM SPECIFICATIONS:**\r\n${platformSpecs}\r\n\r\n**CRITICAL TEXT RENDERING REQUIREMENTS - ABSOLUTE PRIORITY:**\r\n\r\n${ANTI_CORRUPTION_PROMPT}\r\n\r\n- EXACT TEXT ONLY: \"${imageText}\" - Use EXACTLY these words, no additions, no modifications\r\n- ZERO TOLERANCE for garbled, corrupted, or unreadable text\r\n- NO \"AUTTENG\", \"BAMALE\", \"COMEASUE\" or similar corrupted sequences\r\n- NO random character combinations or encoding errors\r\n\r\n${READABLE_TEXT_INSTRUCTIONS}\r\n\r\n${TEXT_GENERATION_SAFEGUARDS}\r\n- MANDATORY: Text must be 100% readable and professional\r\n- FONT REQUIREMENTS: Use only clear, professional fonts (Arial, Helvetica, Open Sans, or similar)\r\n- CONTRAST REQUIREMENTS: High contrast between text and background (minimum 4.5:1 ratio)\r\n- SIZE REQUIREMENTS: Text must be large enough to read clearly on mobile devices\r\n- POSITIONING: Text should be the primary focal point of the design\r\n- QUALITY CHECK: Every character must be perfectly formed and readable\r\n- NO ARTISTIC FONTS that sacrifice readability for style\r\n- NO DECORATIVE ELEMENTS that interfere with text clarity\r\n- BACKGROUND: Ensure text background provides maximum readability\r\n- SPACING: Proper letter spacing and line height for optimal readability\r\n\r\n🧑 PERFECT HUMAN RENDERING (MANDATORY):\r\n- Complete, symmetrical faces with all features present\r\n- Natural, professional expressions with clear eyes\r\n- Proper anatomy with no deformations or missing parts\r\n- High-quality skin textures and realistic lighting\r\n- Diverse representation with authentic appearance\r\n\r\n🎨 ULTRA-MODERN DESIGN SPECIFICATIONS (2024-2025 TRENDS):\r\n\r\n**CONTEMPORARY VISUAL STYLE:**\r\n- ${visualStyle} with cutting-edge 2024-2025 design trends\r\n- Implement glassmorphism effects: frosted glass backgrounds with subtle transparency\r\n- Use neumorphism/soft UI: subtle shadows and highlights for depth\r\n- Apply modern gradient overlays: multi-directional, vibrant gradients\r\n- Include contemporary typography: bold, clean sans-serif fonts with perfect spacing\r\n- Modern color psychology: ${colorInstructions}\r\n\r\n**ADVANCED LAYOUT & COMPOSITION:**\r\n- Asymmetrical layouts with dynamic visual hierarchy\r\n- Generous white space with intentional negative space design\r\n- Floating elements with subtle drop shadows and depth\r\n- Modern grid systems with broken grid elements for visual interest\r\n- Contemporary card-based layouts with rounded corners and elevation\r\n\r\n**CUTTING-EDGE VISUAL EFFECTS:**\r\n- Glassmorphism: Semi-transparent backgrounds with blur effects\r\n- Gradient meshes: Complex, multi-point gradients for depth\r\n- Subtle animations implied through motion blur and dynamic positioning\r\n- Modern shadows: Soft, realistic shadows with multiple light sources\r\n- Contemporary textures: Subtle noise, grain, or organic patterns\r\n\r\n**2024-2025 DESIGN TRENDS:**\r\n- Bold, oversized typography with creative font pairings\r\n- Vibrant, saturated color palettes with high contrast\r\n- Organic shapes and fluid forms mixed with geometric elements\r\n- Modern iconography: minimal, line-based icons with perfect pixel alignment\r\n- Contemporary photography style: high contrast, vibrant, authentic moments\r\n\r\n**BRAND INTEGRATION:**\r\n- Brand Identity: ${brandProfile.businessName || businessType}\r\n- Platform Optimization: ${platformSpecs}\r\n- Human Elements: ${peopleInstructions}\r\n\r\n⚡ GEMINI 2.0 FLASH ULTRA-HD QUALITY ENHANCEMENTS:\r\n- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality)\r\n- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes\r\n- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small\r\n- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness\r\n- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions\r\n- SHARP DETAILS: Crystal-clear textures, no blur or artifacts\r\n- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows\r\n- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance\r\n- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors\r\n- FLAWLESS RENDERING: No deformations, missing parts, or visual errors\r\n- PHOTOREALISTIC QUALITY: Magazine-level professional appearance\r\n- TEXT LEGIBILITY: All text sizes optimized for perfect readability and clarity\r\n\r\n🎨 MODERN DESIGN TRENDS (2024-2025):\r\n${MODERN_DESIGN_TRENDS_2024_2025}\r\n\r\n🌈 CONTEMPORARY COLOR PSYCHOLOGY:\r\n${MODERN_COLOR_PSYCHOLOGY_2024}\r\n\r\n📐 MODERN LAYOUT PRINCIPLES:\r\n${MODERN_LAYOUT_PRINCIPLES}\r\n\r\n✨ ADVANCED VISUAL EFFECTS:\r\n${MODERN_VISUAL_EFFECTS}\r\n\r\n📱 PLATFORM-SPECIFIC MODERN OPTIMIZATION:\r\n${PLATFORM_MODERN_OPTIMIZATIONS[platform.toLowerCase() as keyof typeof PLATFORM_MODERN_OPTIMIZATIONS] || PLATFORM_MODERN_OPTIMIZATIONS.instagram}\r\n\r\n🏢 BUSINESS-SPECIFIC MODERN DNA:\r\n${BUSINESS_TYPE_MODERN_DNA[businessType.toLowerCase() as keyof typeof BUSINESS_TYPE_MODERN_DNA] || BUSINESS_TYPE_MODERN_DNA.tech}\r\n\r\n${artifactInstructions ? `SPECIAL INSTRUCTIONS FROM UPLOADED CONTENT:\r\n${artifactInstructions}\r\n- Follow these instructions precisely when creating the design\r\n- These instructions specify how to use specific content elements\r\n\r\n` : ''}📝 ABSOLUTE TEXT ACCURACY REQUIREMENTS:\r\n- STRICT TEXT CONTROL: Use ONLY the exact text \"${imageText}\" - NO additional text allowed\r\n- NO RANDOM TEXT: Do not add placeholder text, lorem ipsum, sample content, or any extra words\r\n- NO FILLER CONTENT: Do not include random descriptions, fake company names, or dummy text\r\n- EXACT SPELLING: The text must be spelled EXACTLY as provided - do not alter any letters or words\r\n- SINGLE TEXT SOURCE: Only use the provided text \"${imageText}\" as the text content in the image\r\n- SMALL FONT SIZE HANDLING: When design requires small fonts (8pt-12pt), apply these rules:\r\n  * Increase contrast by 20% for small text visibility\r\n  * Use slightly bolder font weight to maintain character definition\r\n  * Ensure perfect pixel alignment for crisp edges\r\n  * Apply high-resolution anti-aliasing for smooth curves\r\n  * Maintain proper letter spacing even at small sizes\r\n- READABILITY GUARANTEE: Every character must be perfectly legible regardless of font size\r\n- PIXEL-PERFECT SMALL TEXT: Each letter rendered with maximum clarity at any size\r\n- BACKGROUND CONTRAST: Ensure sufficient contrast between small text and background\r\n\r\n🚫 ABSOLUTELY FORBIDDEN - WILL CAUSE FAILURE:\r\n- Do NOT add \"Payroll Banking Simplified\"\r\n- Do NOT add \"Banking Made Easy\"\r\n- Do NOT add \"Financial Services\"\r\n- Do NOT add \"Professional Banking\"\r\n- Do NOT add \"Secure Payments\"\r\n- Do NOT add \"Digital Banking\"\r\n- Do NOT add \"Money Management\"\r\n- Do NOT add ANY banking or financial terms\r\n- Do NOT add ANY business descriptions\r\n- Do NOT add ANY marketing copy\r\n- Do NOT add ANY placeholder text\r\n- Do NOT add ANY lorem ipsum\r\n- Do NOT add ANY sample content\r\n- Do NOT add ANY random words\r\n- Do NOT add ANY filler text\r\n- Do NOT create ANY fake headlines\r\n- Do NOT create ANY taglines\r\n- CRITICAL: ONLY use the exact text: \"${imageText}\"\r\n- NOTHING ELSE IS ALLOWED`;\r\n\r\n  return prompt;\r\n}\r\n\r\n/**\r\n * Validate and clean text input for better Gemini 2.0 Flash results\r\n * ULTRA-MINIMAL cleaning to preserve text accuracy for Revo 1.0\r\n */\r\nfunction validateAndCleanText(text: string): string {\r\n  if (!text || text.trim().length === 0) {\r\n    return 'Professional Business Content';\r\n  }\r\n\r\n  let cleanedText = text.trim();\r\n\r\n  // MINIMAL cleaning - only remove truly problematic characters that break AI generation\r\n  cleanedText = cleanedText\r\n    .replace(/[^\\w\\s\\-.,!?'\"()&%$#@]/g, '') // Remove only non-standard characters\r\n    .replace(/\\s+/g, ' ') // Normalize whitespace only\r\n    .replace(/(.)\\1{6,}/g, '$1$1$1') // Only reduce extreme repetition (6+ chars -> 3)\r\n    .trim();\r\n\r\n  // Enforce strict 25-word limit for Revo 1.0\r\n  const words = cleanedText.split(/\\s+/).filter(word => word.length > 0);\r\n  if (words.length > 25) {\r\n    console.warn(`⚠️ Enhanced Design: Text exceeds 25 words (${words.length}), truncating for Revo 1.0...`);\r\n    cleanedText = words.slice(0, 25).join(' ');\r\n  }\r\n\r\n  // Only fallback if completely empty after cleaning\r\n  if (cleanedText.length === 0) {\r\n    return 'Professional Business Content';\r\n  }\r\n\r\n  // Final quality check - ensure text is readable\r\n  if (cleanedText.length < 3 || !/[a-zA-Z]/.test(cleanedText)) {\r\n    console.warn('⚠️ Enhanced Design: Text quality too low, using fallback');\r\n    return 'Professional Business Content';\r\n  }\r\n\r\n  console.log(`✅ Enhanced Design: Text validated and cleaned (${words.length <= 25 ? words.length : 25} words):`, cleanedText);\r\n  return cleanedText;\r\n}\r\n\r\n/**\r\n * Get appropriate aspect ratio for platform (Gemini 2.0 Flash HD)\r\n * Updated with optimal dimensions for each social media platform\r\n */\r\nfunction getPlatformAspectRatio(platform: string): string {\r\n  const platformLower = platform.toLowerCase();\r\n\r\n  // Vertical formats (9:16 aspect ratio)\r\n  if (platformLower.includes('story') ||\r\n    platformLower.includes('reel') ||\r\n    platformLower.includes('tiktok') ||\r\n    platformLower.includes('youtube short')) {\r\n    return '9:16';\r\n  }\r\n\r\n  // Platform-specific optimized aspect ratios\r\n  if (platformLower.includes('linkedin')) {\r\n    return '16:9'; // LinkedIn posts: 1200x627 (optimal for professional content)\r\n  }\r\n\r\n  if (platformLower.includes('facebook')) {\r\n    return '16:9'; // Facebook posts: 1200x630 (optimal for news feed)\r\n  }\r\n\r\n  if (platformLower.includes('twitter')) {\r\n    return '16:9'; // Twitter posts: 1200x675 (optimal for timeline)\r\n  }\r\n\r\n  // Other horizontal formats\r\n  if (platformLower.includes('youtube') ||\r\n    platformLower.includes('banner')) {\r\n    return '16:9';\r\n  }\r\n\r\n  // Instagram and default - Square format (1:1 aspect ratio)\r\n  return '1:1'; // Instagram posts: 1080x1080 (optimal for feed)\r\n}\r\n\r\n/**\r\n * Enhanced platform specifications for Gemini 2.0 Flash HD\r\n */\r\nfunction getPlatformSpecifications(platform: string): string {\r\n  const platformLower = platform.toLowerCase();\r\n\r\n  const specs = {\r\n    'instagram': 'Instagram-optimized design with mobile-first approach, vibrant colors, and engaging visual hierarchy. Perfect for feed posts with high engagement potential.',\r\n    'linkedin': 'LinkedIn professional design with corporate aesthetics, clean typography, and business-appropriate color schemes. Optimized for B2B engagement.',\r\n    'facebook': 'Facebook-optimized design with broad audience appeal, news feed optimization, and social sharing considerations.',\r\n    'twitter': 'Twitter/X-optimized design with concise visual messaging, trending relevance, and platform-specific dimensions.',\r\n    'youtube': 'YouTube thumbnail design with high contrast, bold text, and click-worthy visual appeal.',\r\n    'tiktok': 'TikTok-optimized vertical design with Gen Z appeal, trending aesthetics, and mobile-first approach.',\r\n    'story': 'Story format design with vertical orientation, engaging visual elements, and swipe-friendly layout.',\r\n    'reel': 'Reel format design with dynamic visual elements, mobile optimization, and short-form content appeal.'\r\n  };\r\n\r\n  for (const [key, spec] of Object.entries(specs)) {\r\n    if (platformLower.includes(key)) {\r\n      return spec;\r\n    }\r\n  }\r\n\r\n  return 'Professional social media design optimized for maximum engagement and brand consistency.';\r\n}\r\n\r\n/**\r\n * Determine if people should be included in the design for better engagement\r\n */\r\nfunction shouldIncludePeopleInDesign(businessType: string, imageText: string, visualStyle: string): boolean {\r\n  const businessLower = businessType.toLowerCase();\r\n  const textLower = imageText.toLowerCase();\r\n  const styleLower = visualStyle.toLowerCase();\r\n\r\n  // Business types that typically benefit from human presence\r\n  const peopleBusinessTypes = [\r\n    'restaurant', 'cafe', 'fitness', 'gym', 'salon', 'spa', 'healthcare',\r\n    'dental', 'medical', 'education', 'training', 'consulting', 'coaching',\r\n    'real estate', 'hospitality', 'hotel', 'travel', 'photography',\r\n    'wedding', 'event', 'catering', 'childcare', 'elderly care'\r\n  ];\r\n\r\n  // Text content that suggests human interaction\r\n  const peopleKeywords = [\r\n    'team', 'staff', 'customer', 'client', 'service', 'experience',\r\n    'community', 'family', 'people', 'together', 'join', 'meet'\r\n  ];\r\n\r\n  // Visual styles that work well with people\r\n  const peopleStyles = ['lifestyle', 'authentic', 'candid', 'warm', 'friendly'];\r\n\r\n  const includeForBusiness = peopleBusinessTypes.some(type => businessLower.includes(type));\r\n  const includeForText = peopleKeywords.some(keyword => textLower.includes(keyword));\r\n  const includeForStyle = peopleStyles.some(style => styleLower.includes(style));\r\n\r\n  return includeForBusiness || includeForText || includeForStyle;\r\n}\r\n\r\n/**\r\n * Generate enhanced design with Gemini HD and fallback support\r\n * This function provides automatic fallback to standard Gemini if HD generation fails\r\n */\r\nexport async function generateGeminiHDEnhancedDesignWithFallback(\r\n  input: GeminiHDEnhancedDesignInput\r\n): Promise<GeminiHDEnhancedDesignResult> {\r\n  try {\r\n    // First attempt: Gemini 2.0 Flash HD generation\r\n    console.log('🚀 Attempting Gemini 2.0 Flash HD generation...');\r\n    return await generateGeminiHDEnhancedDesign(input);\r\n  } catch (error) {\r\n    console.warn('⚠️ Gemini HD generation failed, attempting standard fallback:', error);\r\n\r\n    try {\r\n      // Fallback: Standard Gemini generation with enhanced prompting\r\n      const startTime = Date.now();\r\n      // Get platform-specific aspect ratio for fallback generation\r\n      const aspectRatio = getPlatformAspectRatio(input.platform);\r\n      console.log(`🎯 Fallback generating ${aspectRatio} aspect ratio for ${input.platform}`);\r\n\r\n      const enhancedPrompt = buildGeminiHDPrompt(input, aspectRatio);\r\n\r\n      // Build prompt parts array with media inputs\r\n      const promptParts: any[] = [{ text: enhancedPrompt }];\r\n\r\n      // Add logo if available\r\n      if (input.brandProfile.logoDataUrl) {\r\n        promptParts.push({\r\n          media: {\r\n            url: input.brandProfile.logoDataUrl,\r\n            contentType: getMimeTypeFromDataURI(input.brandProfile.logoDataUrl)\r\n          }\r\n        });\r\n      }\r\n\r\n      // Add design examples if available\r\n      if (input.brandConsistency?.strictConsistency && input.brandProfile.designExamples) {\r\n        input.brandProfile.designExamples.slice(0, 3).forEach(example => {\r\n          promptParts.push({\r\n            media: {\r\n              url: example,\r\n              contentType: getMimeTypeFromDataURI(example)\r\n            }\r\n          });\r\n        });\r\n      }\r\n\r\n      const { media } = await generateWithRetry({\r\n        model: 'googleai/gemini-2.0-flash-preview-image-generation',\r\n        prompt: promptParts,\r\n        config: {\r\n          responseModalities: ['TEXT', 'IMAGE'],\r\n          // Note: Gemini 2.0 Flash handles aspect ratio through prompt instructions\r\n          // The aspect ratio is specified in the prompt itself for better control\r\n        },\r\n      });\r\n\r\n      const imageUrl = media?.url;\r\n      if (!imageUrl) {\r\n        throw new Error('No image URL returned from Gemini fallback');\r\n      }\r\n\r\n      return {\r\n        imageUrl,\r\n        qualityScore: 8.5, // Lower score for fallback but still high quality\r\n        enhancementsApplied: ['Gemini 2.0 Flash Fallback', 'Enhanced Prompting', 'Brand Integration'],\r\n        processingTime: Date.now() - startTime,\r\n      };\r\n    } catch (fallbackError) {\r\n      console.error('❌ Both Gemini HD and fallback generation failed:', fallbackError);\r\n      throw new Error(`Gemini generation completely failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`);\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AAQA;AAMA;;;;;AAOA;;CAEC,GACD,SAAS,uBAAuB,OAAe;IAC7C,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAsBA;;CAEC,GACD,eAAe,kBAAkB,OAAwB,EAAE,UAAU,CAAC,EAAE,QAAQ,IAAI;IAClF,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAChC,IAAI;YACF,MAAM,SAAS,MAAM,mHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YACjC,OAAO;QACT,EAAE,OAAO,GAAQ;YACf,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,GAAG;gBAC7D,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,8BAA8B,EAAE,MAAM,KAAK,CAAC;gBACzE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD,OAAO;gBACL,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBAC1C,MAAM,IAAI,MAAM;gBAClB;gBACA,MAAM,GAAG,mCAAmC;YAC9C;QACF;IACF;IACA,MAAM,IAAI,MAAM;AAClB;AAMO,eAAe,+BACpB,KAAkC;IAElC,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,sBAAgC,EAAE;IAExC,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC,QAAQ,GAAG,CAAC,oBAAoB,EAAE;YACnG,MAAM,IAAI,MAAM;QAClB;QAEA,oCAAoC;QACpC,MAAM,cAAc,qBAAqB,MAAM,SAAS;QACxD,MAAM,qBAAqB;YAAE,GAAG,KAAK;YAAE,WAAW;QAAY;QAE9D,iEAAiE;QACjE,MAAM,cAAc,uBAAuB,MAAM,QAAQ;QACzD,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,kBAAkB,EAAE,MAAM,QAAQ,EAAE;QAE7E,qEAAqE;QACrE,MAAM,iBAAiB,oBAAoB,oBAAoB;QAC/D,oBAAoB,IAAI,CAAC,2CAA2C;QAEpE,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qBAAqB,CAAC,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,CAAC;QACvD,QAAQ,GAAG,CAAC,oBAAoB,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QAClD,QAAQ,GAAG,CAAC,oBAAoB,MAAM,SAAS,KAAK,cAAc,QAAQ;QAC1E,QAAQ,GAAG,CAAC,qBAAqB,eAAe,MAAM;QACtD,QAAQ,GAAG,CAAC,2BAA2B,eAAe,SAAS,CAAC,GAAG,OAAO;QAE1E,sEAAsE;QACtE,MAAM,cAAqB;YAAC;gBAAE,MAAM;YAAe;SAAE;QAErD,wBAAwB;QACxB,IAAI,MAAM,YAAY,CAAC,WAAW,EAAE;YAClC,YAAY,IAAI,CAAC;gBACf,OAAO;oBACL,KAAK,MAAM,YAAY,CAAC,WAAW;oBACnC,aAAa,uBAAuB,MAAM,YAAY,CAAC,WAAW;gBACpE;YACF;QACF;QAEA,qEAAqE;QACrE,IAAI,MAAM,gBAAgB,EAAE,qBAAqB,MAAM,YAAY,CAAC,cAAc,EAAE;YAClF,MAAM,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA;gBACpD,YAAY,IAAI,CAAC;oBACf,OAAO;wBACL,KAAK;wBACL,aAAa,uBAAuB;oBACtC;gBACF;YACF;QACF;QAEA,mGAAmG;QACnG,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAkB;YACxC,OAAO;YACP,QAAQ;YACR,QAAQ;gBACN,oBAAoB;oBAAC;oBAAQ;iBAAQ;YAGvC;QACF;QAEA,MAAM,WAAW,OAAO;QACxB,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,oBAAoB,IAAI,CACtB,kCACA,+BACA,0BACA,gCACA,0BACA,yBACA;QAGF,IAAI,MAAM,gBAAgB,EAAE,mBAAmB;YAC7C,oBAAoB,IAAI,CAAC;QAC3B;QACA,IAAI,MAAM,gBAAgB,EAAE,mBAAmB;YAC7C,oBAAoB,IAAI,CAAC;QAC3B;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,OAAO;YACL;YACA,cAAc;YACd;YACA,gBAAgB,KAAK,GAAG,KAAK;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IAC5G;AACF;AAEA;;;CAGC,GACD,SAAS,oBAAoB,KAAkC,EAAE,WAAmB;IAClF,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,GAAG;IAEjH,sDAAsD;IACtD,MAAM,eAAe,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC/E,MAAM,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAEjD,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,iBAAiB,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;IAE5F,yBAAyB;IACzB,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,mBAAmB,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;IAE1F,oEAAoE;IACpE,MAAM,oBAAoB,aAAa,YAAY,IAAI,aAAa,WAAW,GAC3E,CAAC,qBAAqB,EAAE,aAAa,YAAY,CAAC,yBAAyB,EAAE,aAAa,WAAW,CAAC,sEAAsE,CAAC,GAC7K;IAEJ,+EAA+E;IAC/E,MAAM,sBAAsB,4BAA4B,cAAc,WAAW;IACjF,MAAM,qBAAqB,sBACvB,6RACA;IAEJ,0CAA0C;IAC1C,MAAM,gBAAgB,0BAA0B;IAEhD,mCAAmC;IACnC,MAAM,qBAAqB,qJAAA,CAAA,+BAA4B,CAAC,SAAS,WAAW,GAAgD,IAAI,qJAAA,CAAA,+BAA4B,CAAC,SAAS;IAEtK,mCAAmC;IACnC,MAAM,cAAc,qJAAA,CAAA,2BAAwB,CAAC,aAAa,WAAW,GAA4C,IAAI,qJAAA,CAAA,2BAAwB,CAAC,OAAO;IAErJ,6DAA6D;IAC7D,MAAM,SAAS,CAAC;;;6DAG2C,EAAE,aAAa;;;YAGhE,EAAE,SAAS;yBACE,EAAE,YAAY;+BACR,EAAE,YAAY,kBAAkB,EAAE,SAAS;;;;;;2DAMf,EAAE,YAAY;oBACrD,EAAE,YAAY;;cAEpB,EAAE,YAAY,aAAa,EAAE,aAAa,QAAQ,IAAI,SAAS;;;eAG9D,EAAE,UAAU;AAC3B,EAAE,aAAa,YAAY,GAAG,CAAC,gBAAgB,EAAE,aAAa,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG;;AAEnF,EAAE,qJAAA,CAAA,6BAA0B,CAAC;;AAE7B,EAAE,mBAAmB;;AAErB,EAAE,YAAY;;;AAGd,EAAE,kBAAkB;AACpB,EAAE,mBAAmB;;;oBAGD,EAAE,eAAe;iCACJ,EAAE,iBAAiB;;iBAEnC,EAAE,aAAa;kBACd,EAAE,cAAc;;;AAGlC,EAAE,cAAc;;;;AAIhB,EAAE,sJAAA,CAAA,yBAAsB,CAAC;;oBAEL,EAAE,UAAU;;;;;AAKhC,EAAE,sJAAA,CAAA,6BAA0B,CAAC;;AAE7B,EAAE,sJAAA,CAAA,6BAA0B,CAAC;;;;;;;;;;;;;;;;;;;;;;EAsB3B,EAAE,YAAY;;;;;2BAKW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;kBAwB7B,EAAE,aAAa,YAAY,IAAI,aAAa;yBACrC,EAAE,cAAc;kBACvB,EAAE,mBAAmB;;;;;;;;;;;;;;;;;AAiBvC,EAAE,mJAAA,CAAA,iCAA8B,CAAC;;;AAGjC,EAAE,mJAAA,CAAA,+BAA4B,CAAC;;;AAG/B,EAAE,mJAAA,CAAA,2BAAwB,CAAC;;;AAG3B,EAAE,mJAAA,CAAA,wBAAqB,CAAC;;;AAGxB,EAAE,mJAAA,CAAA,gCAA6B,CAAC,SAAS,WAAW,GAAiD,IAAI,mJAAA,CAAA,gCAA6B,CAAC,SAAS,CAAC;;;AAGjJ,EAAE,mJAAA,CAAA,2BAAwB,CAAC,aAAa,WAAW,GAA4C,IAAI,mJAAA,CAAA,2BAAwB,CAAC,IAAI,CAAC;;AAEjI,EAAE,uBAAuB,CAAC;AAC1B,EAAE,qBAAqB;;;;AAIvB,CAAC,GAAG,GAAG;gDACyC,EAAE,UAAU;;;;kDAIV,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCA6BxB,EAAE,UAAU;yBACzB,CAAC;IAExB,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,qBAAqB,IAAY;IACxC,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,KAAK,GAAG;QACrC,OAAO;IACT;IAEA,IAAI,cAAc,KAAK,IAAI;IAE3B,uFAAuF;IACvF,cAAc,YACX,OAAO,CAAC,2BAA2B,IAAI,sCAAsC;KAC7E,OAAO,CAAC,QAAQ,KAAK,4BAA4B;KACjD,OAAO,CAAC,cAAc,UAAU,iDAAiD;KACjF,IAAI;IAEP,4CAA4C;IAC5C,MAAM,QAAQ,YAAY,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IACpE,IAAI,MAAM,MAAM,GAAG,IAAI;QACrB,QAAQ,IAAI,CAAC,CAAC,2CAA2C,EAAE,MAAM,MAAM,CAAC,6BAA6B,CAAC;QACtG,cAAc,MAAM,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC;IACxC;IAEA,mDAAmD;IACnD,IAAI,YAAY,MAAM,KAAK,GAAG;QAC5B,OAAO;IACT;IAEA,gDAAgD;IAChD,IAAI,YAAY,MAAM,GAAG,KAAK,CAAC,WAAW,IAAI,CAAC,cAAc;QAC3D,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,MAAM,MAAM,IAAI,KAAK,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,EAAE;IAChH,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,uBAAuB,QAAgB;IAC9C,MAAM,gBAAgB,SAAS,WAAW;IAE1C,uCAAuC;IACvC,IAAI,cAAc,QAAQ,CAAC,YACzB,cAAc,QAAQ,CAAC,WACvB,cAAc,QAAQ,CAAC,aACvB,cAAc,QAAQ,CAAC,kBAAkB;QACzC,OAAO;IACT;IAEA,4CAA4C;IAC5C,IAAI,cAAc,QAAQ,CAAC,aAAa;QACtC,OAAO,QAAQ,8DAA8D;IAC/E;IAEA,IAAI,cAAc,QAAQ,CAAC,aAAa;QACtC,OAAO,QAAQ,mDAAmD;IACpE;IAEA,IAAI,cAAc,QAAQ,CAAC,YAAY;QACrC,OAAO,QAAQ,iDAAiD;IAClE;IAEA,2BAA2B;IAC3B,IAAI,cAAc,QAAQ,CAAC,cACzB,cAAc,QAAQ,CAAC,WAAW;QAClC,OAAO;IACT;IAEA,2DAA2D;IAC3D,OAAO,OAAO,gDAAgD;AAChE;AAEA;;CAEC,GACD,SAAS,0BAA0B,QAAgB;IACjD,MAAM,gBAAgB,SAAS,WAAW;IAE1C,MAAM,QAAQ;QACZ,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IAEA,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,OAAO,CAAC,OAAQ;QAC/C,IAAI,cAAc,QAAQ,CAAC,MAAM;YAC/B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,4BAA4B,YAAoB,EAAE,SAAiB,EAAE,WAAmB;IAC/F,MAAM,gBAAgB,aAAa,WAAW;IAC9C,MAAM,YAAY,UAAU,WAAW;IACvC,MAAM,aAAa,YAAY,WAAW;IAE1C,4DAA4D;IAC5D,MAAM,sBAAsB;QAC1B;QAAc;QAAQ;QAAW;QAAO;QAAS;QAAO;QACxD;QAAU;QAAW;QAAa;QAAY;QAAc;QAC5D;QAAe;QAAe;QAAS;QAAU;QACjD;QAAW;QAAS;QAAY;QAAa;KAC9C;IAED,+CAA+C;IAC/C,MAAM,iBAAiB;QACrB;QAAQ;QAAS;QAAY;QAAU;QAAW;QAClD;QAAa;QAAU;QAAU;QAAY;QAAQ;KACtD;IAED,2CAA2C;IAC3C,MAAM,eAAe;QAAC;QAAa;QAAa;QAAU;QAAQ;KAAW;IAE7E,MAAM,qBAAqB,oBAAoB,IAAI,CAAC,CAAA,OAAQ,cAAc,QAAQ,CAAC;IACnF,MAAM,iBAAiB,eAAe,IAAI,CAAC,CAAA,UAAW,UAAU,QAAQ,CAAC;IACzE,MAAM,kBAAkB,aAAa,IAAI,CAAC,CAAA,QAAS,WAAW,QAAQ,CAAC;IAEvE,OAAO,sBAAsB,kBAAkB;AACjD;AAMO,eAAe,2CACpB,KAAkC;IAElC,IAAI;QACF,gDAAgD;QAChD,QAAQ,GAAG,CAAC;QACZ,OAAO,MAAM,+BAA+B;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,iEAAiE;QAE9E,IAAI;YACF,+DAA+D;YAC/D,MAAM,YAAY,KAAK,GAAG;YAC1B,6DAA6D;YAC7D,MAAM,cAAc,uBAAuB,MAAM,QAAQ;YACzD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,YAAY,kBAAkB,EAAE,MAAM,QAAQ,EAAE;YAEtF,MAAM,iBAAiB,oBAAoB,OAAO;YAElD,6CAA6C;YAC7C,MAAM,cAAqB;gBAAC;oBAAE,MAAM;gBAAe;aAAE;YAErD,wBAAwB;YACxB,IAAI,MAAM,YAAY,CAAC,WAAW,EAAE;gBAClC,YAAY,IAAI,CAAC;oBACf,OAAO;wBACL,KAAK,MAAM,YAAY,CAAC,WAAW;wBACnC,aAAa,uBAAuB,MAAM,YAAY,CAAC,WAAW;oBACpE;gBACF;YACF;YAEA,mCAAmC;YACnC,IAAI,MAAM,gBAAgB,EAAE,qBAAqB,MAAM,YAAY,CAAC,cAAc,EAAE;gBAClF,MAAM,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAA;oBACpD,YAAY,IAAI,CAAC;wBACf,OAAO;4BACL,KAAK;4BACL,aAAa,uBAAuB;wBACtC;oBACF;gBACF;YACF;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kBAAkB;gBACxC,OAAO;gBACP,QAAQ;gBACR,QAAQ;oBACN,oBAAoB;wBAAC;wBAAQ;qBAAQ;gBAGvC;YACF;YAEA,MAAM,WAAW,OAAO;YACxB,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;gBACL;gBACA,cAAc;gBACd,qBAAqB;oBAAC;oBAA6B;oBAAsB;iBAAoB;gBAC7F,gBAAgB,KAAK,GAAG,KAAK;YAC/B;QACF,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,oDAAoD;YAClE,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,yBAAyB,QAAQ,cAAc,OAAO,GAAG,iBAAiB;QACpI;IACF;AACF", "debugId": null}}]}
/**
 * Revo 1.0 Design Generator
 * Handles design generation for the stable foundation model
 */

import type {
  IDesignGenerator,
  DesignGenerationRequest,
  GenerationResponse
} from '../../types/model-types';
import type { PostVariant } from '@/lib/types';
import {
  ANTI_CORRUPTION_PROMPT,
  READABLE_TEXT_INSTRUCTIONS,
  TEXT_GENERATION_SAFEGUARDS
} from '../../../prompts/text-readability-prompts';

export class Revo10DesignGenerator implements IDesignGenerator {
  private readonly modelId = 'revo-1.0';

  /**
   * Generate design using Revo 1.0 specifications
   */
  async generateDesign(request: DesignGenerationRequest): Promise<GenerationResponse<PostVariant>> {
    const startTime = Date.now();

    try {
      console.log('🎨 Revo 1.0: Starting design generation...');
      console.log('- Business Type:', request.businessType);
      console.log('- Platform:', request.platform);
      console.log('- Visual Style:', request.visualStyle);

      // Validate request
      if (!this.validateRequest(request)) {
        throw new Error('Invalid design generation request for Revo 1.0');
      }

      // Generate design using basic Gemini 2.0 approach
      const designResult = await this.generateBasicDesign(request);

      const processingTime = Date.now() - startTime;
      const qualityScore = this.calculateQualityScore(designResult);

      console.log(`✅ Revo 1.0: Design generated successfully in ${processingTime}ms`);
      console.log(`⭐ Quality Score: ${qualityScore}/10`);

      return {
        success: true,
        data: designResult,
        metadata: {
          modelId: this.modelId,
          processingTime,
          qualityScore,
          creditsUsed: 1, // Revo 1.0 uses 1 credit for design
          enhancementsApplied: ['basic-styling', 'brand-colors', 'platform-optimization']
        }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error('❌ Revo 1.0: Design generation failed:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          modelId: this.modelId,
          processingTime,
          qualityScore: 0,
          creditsUsed: 0,
          enhancementsApplied: []
        }
      };
    }
  }

  /**
   * Generate enhanced design using improved Revo 1.0 approach
   */
  private async generateBasicDesign(request: DesignGenerationRequest): Promise<PostVariant> {
    try {
      console.log('🎨 Revo 1.0: Starting enhanced design generation...');

      // Validate and enforce 25-word limit on image text
      const validatedImageText = this.validateAndLimitImageText(request.imageText);

      // Import the enhanced design generation
      const { generateEnhancedDesign } = await import('@/ai/gemini-hd-enhanced-design');

      // Create enhanced generation parameters with strict text control
      const enhancedParams = {
        businessType: request.businessType,
        platform: request.platform,
        visualStyle: this.enhanceVisualStyleForReadability(request.visualStyle, request.brandProfile),
        imageText: validatedImageText,
        brandProfile: request.brandProfile,
        brandConsistency: request.brandConsistency || {
          strictConsistency: false,
          followBrandColors: true
        },
        // Enhanced Revo 1.0 specific improvements with text focus
        designEnhancements: {
          useAdvancedComposition: true,
          enhanceTextReadability: true,
          optimizeForEngagement: true,
          improveColorHarmony: true,
          enhanceVisualHierarchy: true,
          strictTextControl: true,
          maxTextLength: validatedImageText.length,
          enforceReadability: true
        },
        // Strict text generation constraints
        textConstraints: {
          maxWords: 25,
          requireReadableText: true,
          noGibberish: true,
          professionalOnly: true,
          clearFonts: true
        }
      };

      console.log('📝 Revo 1.0: Using validated image text:', validatedImageText);
      console.log('🎯 Revo 1.0: Enhanced visual style:', enhancedParams.visualStyle);

      // Final validation before generation
      const finalValidation = this.performFinalTextValidation(validatedImageText);
      if (!finalValidation.isValid) {
        console.error('❌ Revo 1.0: Final text validation failed:', finalValidation.reason);
        throw new Error(`Text validation failed: ${finalValidation.reason}`);
      }

      console.log('✅ Revo 1.0: Final text validation passed');

      // Generate the enhanced design
      const result = await generateEnhancedDesign(enhancedParams);

      return {
        platform: request.platform,
        imageUrl: result.imageUrl,
        caption: validatedImageText,
        hashtags: []
      };

    } catch (error) {
      console.error('❌ Revo 1.0: Enhanced design generation failed:', error);

      // Fallback to basic generation
      return this.generateFallbackDesign(request);
    }
  }

  /**
   * Validate and enforce 25-word limit on image text with strict quality control
   */
  private validateAndLimitImageText(imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string }): string {
    let combinedText: string;

    if (typeof imageText === 'string') {
      combinedText = imageText;
    } else {
      // Intelligently combine components with strict limits
      const components: string[] = [];

      // Always include catchy words (max 8 words)
      if (imageText.catchyWords?.trim()) {
        const catchyWords = imageText.catchyWords.trim().split(/\s+/).slice(0, 8).join(' ');
        components.push(catchyWords);
      }

      // Add subheadline only if space allows (max 12 words)
      if (imageText.subheadline?.trim()) {
        const currentWordCount = components.join(' ').split(/\s+/).length;
        const remainingWords = 25 - currentWordCount;
        if (remainingWords > 0) {
          const subheadline = imageText.subheadline.trim().split(/\s+/).slice(0, Math.min(12, remainingWords)).join(' ');
          if (subheadline) components.push(subheadline);
        }
      }

      // Add call to action only if space allows (max 5 words)
      if (imageText.callToAction?.trim()) {
        const currentWordCount = components.join(' ').split(/\s+/).length;
        const remainingWords = 25 - currentWordCount;
        if (remainingWords > 0) {
          const callToAction = imageText.callToAction.trim().split(/\s+/).slice(0, Math.min(5, remainingWords)).join(' ');
          if (callToAction) components.push(callToAction);
        }
      }

      combinedText = components.join(' ');
    }

    // Final validation and cleanup
    const words = combinedText.trim().split(/\s+/).filter(word => word.length > 0 && word.match(/^[a-zA-Z0-9\-'&.,!?]+$/));

    // Enforce absolute 25-word limit
    if (words.length > 25) {
      console.warn(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), applying strict truncation...`);
      combinedText = words.slice(0, 25).join(' ');
    } else {
      combinedText = words.join(' ');
    }

    // Ensure text is readable and professional
    combinedText = this.cleanAndValidateText(combinedText);

    console.log(`✅ Revo 1.0: Final validated text (${combinedText.split(/\s+/).length} words):`, combinedText);
    return combinedText;
  }

  /**
   * Clean and validate text to ensure readability
   */
  private cleanAndValidateText(text: string): string {
    // Remove any non-readable characters and ensure proper formatting
    let cleanText = text
      .replace(/[^\w\s\-'&.,!?]/g, '') // Remove special characters except basic punctuation
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // Ensure minimum quality - if text is too short or seems corrupted, provide fallback
    const words = cleanText.split(/\s+/).filter(word => word.length > 0);

    if (words.length === 0 || cleanText.length < 5) {
      console.warn('⚠️ Revo 1.0: Text appears corrupted, using fallback');
      return 'Professional Services Available';
    }

    // Capitalize first letter of each sentence
    cleanText = cleanText.replace(/(^|\. )([a-z])/g, (match, prefix, letter) => prefix + letter.toUpperCase());

    return cleanText;
  }

  /**
   * Enhanced visual style with focus on text readability and anti-corruption
   */
  private enhanceVisualStyleForReadability(originalStyle: string, brandProfile?: any): string {
    const readabilityEnhancements = [
      'with clear, readable typography',
      'using high contrast text',
      'with professional font choices',
      'ensuring text stands out clearly',
      'with clean, uncluttered layout',
      'using proper text spacing',
      'with legible text size',
      'ensuring maximum readability'
    ];

    const textQualityRequirements = [
      'text must be completely readable',
      'no garbled or corrupted text',
      'use only clear, professional fonts',
      'ensure high contrast between text and background',
      'text should be the focal point',
      'maintain clean, minimal design',
      'NO corrupted sequences like AUTTENG, BAMALE, COMEASUE',
      'ONLY proper English words allowed',
      'prevent any character corruption or encoding errors'
    ];

    // Add anti-corruption instructions
    const antiCorruptionInstructions = [
      'generate ONLY readable English text',
      'prevent any text corruption or garbling',
      'ensure every character is clear and correct',
      'use standard dictionary words only'
    ];

    return `${originalStyle}, ${readabilityEnhancements.join(', ')}, ${textQualityRequirements.join(', ')}, ${antiCorruptionInstructions.join(', ')}`;
  }

  /**
   * Perform final text validation before generation
   */
  private performFinalTextValidation(text: string): { isValid: boolean; reason?: string } {
    // Check if text is empty
    if (!text || text.trim().length === 0) {
      return { isValid: false, reason: 'Text is empty' };
    }

    // Check word count (strict 25-word limit)
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    if (words.length > 25) {
      return { isValid: false, reason: `Text exceeds 25 words (${words.length} words)` };
    }

    // Check for readable characters only
    const hasUnreadableChars = /[^\w\s\-'&.,!?]/.test(text);
    if (hasUnreadableChars) {
      return { isValid: false, reason: 'Text contains unreadable characters' };
    }

    // Check minimum quality
    if (text.length < 3) {
      return { isValid: false, reason: 'Text too short to be meaningful' };
    }

    // Check for excessive repetition
    const hasExcessiveRepetition = /(.)\1{5,}/.test(text);
    if (hasExcessiveRepetition) {
      return { isValid: false, reason: 'Text contains excessive character repetition' };
    }

    // All validations passed
    return { isValid: true };
  }

  /**
   * Enhance visual style for better design quality
   */
  private enhanceVisualStyle(originalStyle: string, brandProfile: any): string {
    const baseStyle = originalStyle || 'modern professional';

    // Add Revo 1.0 specific enhancements
    const enhancements = [
      'high-quality composition',
      'clean typography',
      'balanced layout',
      'professional aesthetics',
      'engaging visual hierarchy'
    ];

    // Add brand-specific enhancements
    if (brandProfile.primaryColor) {
      enhancements.push(`primary color: ${brandProfile.primaryColor}`);
    }

    if (brandProfile.businessType) {
      enhancements.push(`${brandProfile.businessType} industry styling`);
    }

    return `${baseStyle}, ${enhancements.join(', ')}`;
  }

  /**
   * Generate fallback design when enhanced generation fails
   */
  private async generateFallbackDesign(request: DesignGenerationRequest): Promise<PostVariant> {
    try {
      console.log('🔄 Revo 1.0: Using fallback design generation...');

      // Import the basic generation flow as fallback
      const { generatePostFromProfile } = await import('@/ai/flows/generate-post-from-profile');

      const validatedImageText = this.validateAndLimitImageText(request.imageText);

      const generationParams = {
        businessType: request.businessType,
        location: request.brandProfile.location || '',
        writingTone: request.brandProfile.writingTone || 'professional',
        contentThemes: request.brandProfile.contentThemes || '',
        visualStyle: request.visualStyle,
        logoDataUrl: request.brandProfile.logoDataUrl,
        designExamples: request.brandConsistency?.strictConsistency ?
          (request.brandProfile.designExamples || []) : [],
        primaryColor: request.brandProfile.primaryColor,
        accentColor: request.brandProfile.accentColor,
        backgroundColor: request.brandProfile.backgroundColor,
        dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),
        currentDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric', month: 'long', day: 'numeric'
        }),
        variants: [{
          platform: request.platform,
          aspectRatio: '1:1', // Revo 1.0 only supports 1:1
        }],
        services: '',
        targetAudience: request.brandProfile.targetAudience || '',
        keyFeatures: '',
        competitiveAdvantages: '',
        brandConsistency: request.brandConsistency || {
          strictConsistency: false,
          followBrandColors: true
        }
      };

      const result = await generatePostFromProfile(generationParams);

      if (result.variants && result.variants.length > 0) {
        return {
          ...result.variants[0],
          caption: validatedImageText,
          hashtags: result.hashtags || []
        };
      }

      // Final fallback
      return {
        platform: request.platform,
        imageUrl: '',
        caption: validatedImageText,
        hashtags: []
      };

    } catch (error) {
      console.error('❌ Revo 1.0: Fallback design generation failed:', error);

      const validatedImageText = this.validateAndLimitImageText(request.imageText);

      return {
        platform: request.platform,
        imageUrl: '',
        caption: validatedImageText,
        hashtags: []
      };
    }
  }

  /**
   * Validate design generation request for Revo 1.0
   */
  private validateRequest(request: DesignGenerationRequest): boolean {
    // Check required fields
    if (!request.businessType || !request.platform || !request.brandProfile) {
      return false;
    }

    // Check image text
    if (!request.imageText) {
      return false;
    }

    // Revo 1.0 only supports 1:1 aspect ratio
    // We don't enforce this here as the generator will handle it

    // Warn about unsupported features
    if (request.artifactInstructions) {
      console.warn('⚠️ Revo 1.0: Artifact instructions not supported, ignoring');
    }

    return true;
  }

  /**
   * Calculate quality score for generated design
   */
  private calculateQualityScore(variant: PostVariant): number {
    let score = 5; // Base score

    // Image generation success
    if (variant.imageUrl && variant.imageUrl.length > 0) {
      score += 2;
    }

    // Caption quality
    if (variant.caption && variant.caption.length > 10) {
      score += 1;
    }

    // Hashtags presence
    if (variant.hashtags && variant.hashtags.length > 0) {
      score += 1;
    }

    // Platform optimization (basic check)
    if (variant.platform) {
      score += 0.5;
    }

    // Revo 1.0 has a quality ceiling due to basic features
    return Math.min(score, 7.5);
  }

  /**
   * Health check for design generator
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Check if we can access the AI service
      const hasApiKey = !!(
        process.env.GEMINI_API_KEY ||
        process.env.GOOGLE_API_KEY ||
        process.env.GOOGLE_GENAI_API_KEY
      );

      return hasApiKey;
    } catch (error) {
      console.error('❌ Revo 1.0 Design Generator health check failed:', error);
      return false;
    }
  }

  /**
   * Get generator-specific information
   */
  getGeneratorInfo() {
    return {
      modelId: this.modelId,
      type: 'design',
      capabilities: [
        'Basic image generation',
        '1:1 aspect ratio only',
        'Brand color integration',
        'Logo placement',
        'Platform optimization',
        'Text overlay (basic)'
      ],
      limitations: [
        'Single aspect ratio (1:1)',
        'No artifact support',
        'Basic styling options',
        'Limited customization',
        'Standard resolution only'
      ],
      supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],
      supportedAspectRatios: ['1:1'],
      averageProcessingTime: '15-25 seconds',
      qualityRange: '5-7.5/10',
      costPerGeneration: 1,
      resolution: '1024x1024'
    };
  }

  /**
   * Get supported features for this design generator
   */
  getSupportedFeatures() {
    return {
      aspectRatios: ['1:1'],
      textOverlay: 'basic',
      brandIntegration: 'standard',
      logoPlacement: true,
      colorCustomization: true,
      templateSupport: false,
      artifactSupport: false,
      advancedStyling: false,
      multipleVariants: false,
      highResolution: false
    };
  }
}

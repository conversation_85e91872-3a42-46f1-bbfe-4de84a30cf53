(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/ai/openai-enhanced-design.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_ai_flows_generate-creative-asset_ts_7a15ae78._.js",
  "static/chunks/node_modules_openai_e12ac105._.js",
  "static/chunks/src_ai_62205325._.js",
  "static/chunks/src_ai_openai-enhanced-design_ts_e2e6c43e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/openai-enhanced-design.ts [app-client] (ecmascript)");
    });
});
}}),
}]);
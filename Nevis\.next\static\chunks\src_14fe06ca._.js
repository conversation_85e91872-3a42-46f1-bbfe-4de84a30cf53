(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/studio/chat-avatar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-avatar.tsx
__turbopack_context__.s({
    "ChatAvatar": (()=>ChatAvatar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-client] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-client] (ecmascript)");
;
;
;
function ChatAvatar({ role }) {
    if (role === 'user') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
            className: "h-8 w-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-avatar.tsx",
                    lineNumber: 14,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-avatar.tsx",
                lineNumber: 13,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-avatar.tsx",
            lineNumber: 12,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
        className: "h-8 w-8 bg-primary text-primary-foreground",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-avatar.tsx",
                lineNumber: 23,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-avatar.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-avatar.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
_c = ChatAvatar;
var _c;
__turbopack_context__.k.register(_c, "ChatAvatar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/chat-messages.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-messages.tsx
__turbopack_context__.s({
    "ChatMessages": (()=>ChatMessages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-client] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-client] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tooltip.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
function ChatMessages({ messages, isLoading, onSetReferenceAsset, onEditImage }) {
    _s();
    const scrollableContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatMessages.useEffect": ()=>{
            if (scrollableContainerRef.current) {
                scrollableContainerRef.current.scrollTop = scrollableContainerRef.current.scrollHeight;
            }
        }
    }["ChatMessages.useEffect"], [
        messages,
        isLoading
    ]);
    const handleDownload = async (url, type)=>{
        if (!url) {
            toast({
                variant: 'destructive',
                title: 'Download Failed',
                description: 'No asset URL found.'
            });
            return;
        }
        try {
            // Download the original HD file directly to preserve quality
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            const fileExtension = type === 'image' ? 'png' : 'mp4';
            link.download = `nevis-hd-${type}-${Date.now()}.${fileExtension}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);
            toast({
                title: 'HD Download Complete',
                description: `High-definition ${type} downloaded successfully.`
            });
        } catch (error) {
            console.error('Download failed:', error);
            toast({
                variant: 'destructive',
                title: 'Download Failed',
                description: `Could not download the ${type}. Please try again.`
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: scrollableContainerRef,
        className: "flex-1 overflow-y-auto p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mx-auto max-w-3xl space-y-6",
                children: [
                    messages.map((message, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('flex items-start gap-4', message.role === 'user' && 'justify-end'),
                            children: [
                                message.role === 'assistant' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                    role: "assistant"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 79,
                                    columnNumber: 48
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('max-w-[80%] space-y-2 rounded-lg p-3', message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'),
                                    children: [
                                        message.role === 'user' && message.imageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative w-48 h-48 overflow-hidden rounded-md border",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: message.imageUrl,
                                                alt: "User upload preview",
                                                layout: "fill",
                                                objectFit: "cover"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                lineNumber: 89,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 88,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "whitespace-pre-wrap text-sm",
                                            children: message.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 93,
                                            columnNumber: 17
                                        }, this),
                                        message.role === 'assistant' && message.imageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "group relative w-full max-w-sm overflow-hidden rounded-md border",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    src: message.imageUrl,
                                                    alt: "Generated image",
                                                    width: 512,
                                                    height: 512,
                                                    className: "w-full h-auto object-contain",
                                                    crossOrigin: "anonymous"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 98,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onEditImage(message.imageUrl),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 108,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Edit Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 109,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 102,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 101,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Edit with Inpainting"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 112,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 100,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onSetReferenceAsset(message.imageUrl, 'image'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 122,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Refine Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 123,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 116,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 115,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Refine this image (new prompt)"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 126,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 114,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>handleDownload(message.imageUrl, 'image'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 136,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Download Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 137,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 130,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 129,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Download image"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 140,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 128,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 99,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 97,
                                            columnNumber: 19
                                        }, this),
                                        message.role === 'assistant' && message.videoUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "group relative w-full max-w-sm overflow-hidden rounded-md border",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                                    controls: true,
                                                    autoPlay: true,
                                                    src: message.videoUrl,
                                                    className: "w-full"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 149,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onSetReferenceAsset(message.videoUrl, 'video'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 159,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Refine Video"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 160,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 153,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 152,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Refine this video (new prompt)"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 163,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 151,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>handleDownload(message.videoUrl, 'video'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 173,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Download Video"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 174,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 167,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 166,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Download video"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 177,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 165,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 148,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 80,
                                    columnNumber: 15
                                }, this),
                                message.role === 'user' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                    role: "user"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 184,
                                    columnNumber: 43
                                }, this)
                            ]
                        }, index, true, {
                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                            lineNumber: 78,
                            columnNumber: 13
                        }, this)),
                    isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                role: "assistant"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                lineNumber: 190,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2 rounded-lg bg-muted p-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                        className: "h-5 w-5 animate-spin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                        lineNumber: 192,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm",
                                        children: "Generating..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                        lineNumber: 193,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                lineNumber: 191,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                        lineNumber: 189,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/studio/chat-messages.tsx",
                lineNumber: 76,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-messages.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-messages.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
}
_s(ChatMessages, "KRbjU4Xhyo/ff6LNIhRbDr74M+M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = ChatMessages;
var _c;
__turbopack_context__.k.register(_c, "ChatMessages");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 8,
        columnNumber: 7
    }, this);
});
_c1 = Textarea;
Textarea.displayName = 'Textarea';
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Textarea$React.forwardRef");
__turbopack_context__.k.register(_c1, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/label.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const labelVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");
const Label = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(labelVariants(), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/label.tsx",
        lineNumber: 18,
        columnNumber: 3
    }, this));
_c1 = Label;
Label.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Label$React.forwardRef");
__turbopack_context__.k.register(_c1, "Label");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/switch.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Switch": (()=>Switch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-switch/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
const Switch = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input", className),
        ...props,
        ref: ref,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Thumb"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")
        }, void 0, false, {
            fileName: "[project]/src/components/ui/switch.tsx",
            lineNumber: 20,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/switch.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
_c1 = Switch;
Switch.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Switch$React.forwardRef");
__turbopack_context__.k.register(_c1, "Switch");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/radio-group.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "RadioGroup": (()=>RadioGroup),
    "RadioGroupItem": (()=>RadioGroupItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-radio-group/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle.js [app-client] (ecmascript) <export default as Circle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const RadioGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("grid gap-2", className),
        ...props,
        ref: ref
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radio-group.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
});
_c1 = RadioGroup;
RadioGroup.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
const RadioGroupItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c2 = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Indicator"], {
            className: "flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__["Circle"], {
                className: "h-2.5 w-2.5 fill-current text-current"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/radio-group.tsx",
                lineNumber: 37,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/radio-group.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radio-group.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
});
_c3 = RadioGroupItem;
RadioGroupItem.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"].displayName;
;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "RadioGroup$React.forwardRef");
__turbopack_context__.k.register(_c1, "RadioGroup");
__turbopack_context__.k.register(_c2, "RadioGroupItem$React.forwardRef");
__turbopack_context__.k.register(_c3, "RadioGroupItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/revo-model-selector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "REVO_MODELS": (()=>REVO_MODELS),
    "RevoModelSelector": (()=>RevoModelSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-client] (ecmascript) <export default as Sparkles>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rocket$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Rocket$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rocket.js [app-client] (ecmascript) <export default as Rocket>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
const REVO_MODELS = [
    {
        id: 'revo-1.0',
        name: 'Revo 1.0',
        description: 'Standard Model - Stable Foundation',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"],
        badge: 'Stable',
        badgeVariant: 'secondary',
        features: [
            'Reliable AI Engine',
            '1:1 Images',
            'Core Features',
            'Proven Performance'
        ],
        status: 'stable'
    },
    {
        id: 'revo-1.5',
        name: 'Revo 1.5',
        description: 'Enhanced Model - Advanced Features',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        badge: 'Enhanced',
        badgeVariant: 'default',
        features: [
            'Advanced AI Engine',
            'Superior Quality',
            'Enhanced Design',
            'Smart Optimizations'
        ],
        status: 'enhanced'
    }
];
function RevoModelSelector({ selectedModel, onModelChange, disabled = false, className }) {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const currentModel = REVO_MODELS.find((model)=>model.id === selectedModel) || REVO_MODELS[0];
    const CurrentIcon = currentModel.icon;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
        open: isOpen,
        onOpenChange: setIsOpen,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                asChild: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    disabled: disabled,
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("min-w-[180px] justify-between", className),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CurrentIcon, {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 84,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: currentModel.name
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 85,
                                    columnNumber: 13
                                }, this),
                                currentModel.badge && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: currentModel.badgeVariant,
                                    className: "text-xs",
                                    children: currentModel.badge
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 87,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 83,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                            className: "h-4 w-4 opacity-50"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                    lineNumber: 75,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                align: "start",
                className: "w-80",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rocket$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Rocket$3e$__["Rocket"], {
                                className: "w-4 h-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                lineNumber: 98,
                                columnNumber: 11
                            }, this),
                            "Select Revo Model"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 101,
                        columnNumber: 9
                    }, this),
                    REVO_MODELS.map((model)=>{
                        const IconComponent = model.icon;
                        const isSelected = selectedModel === model.id;
                        const isAvailable = model.status !== 'development';
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                            onClick: ()=>{
                                if (isAvailable) {
                                    onModelChange(model.id);
                                    setIsOpen(false);
                                }
                            },
                            disabled: !isAvailable,
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col items-start gap-2 p-4 cursor-pointer", !isAvailable && "opacity-50 cursor-not-allowed"),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(IconComponent, {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 125,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: model.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 126,
                                                    columnNumber: 19
                                                }, this),
                                                model.badge && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: model.badgeVariant,
                                                    className: "text-xs",
                                                    children: model.badge
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 128,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 124,
                                            columnNumber: 17
                                        }, this),
                                        isSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                            className: "h-4 w-4 text-green-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 133,
                                            columnNumber: 32
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 123,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-muted-foreground",
                                    children: model.description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 136,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap gap-1",
                                    children: model.features.map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "outline",
                                            className: "text-xs",
                                            children: feature
                                        }, index, false, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 142,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 140,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, model.id, true, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 109,
                            columnNumber: 13
                        }, this);
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 151,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 text-xs text-muted-foreground",
                        children: "Each Revo model offers different capabilities and features"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 152,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
        lineNumber: 73,
        columnNumber: 5
    }, this);
}
_s(RevoModelSelector, "+sus0Lb0ewKHdwiUhiTAJFoFyQ0=");
_c = RevoModelSelector;
;
var _c;
__turbopack_context__.k.register(_c, "RevoModelSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/chat-input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-input.tsx
__turbopack_context__.s({
    "ChatInput": (()=>ChatInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$paperclip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Paperclip$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/paperclip.js [app-client] (ecmascript) <export default as Paperclip>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-client] (ecmascript) <export default as Send>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/image.js [app-client] (ecmascript) <export default as Image>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/video.js [app-client] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-client] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-client] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/switch.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/radio-group.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$revo$2d$model$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/revo-model-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
function ChatInput({ input, setInput, handleSubmit, isLoading, imagePreview, setImagePreview, setImageDataUrl, useBrandProfile, setUseBrandProfile, outputType, setOutputType, handleImageUpload, isBrandProfileAvailable, onEditImage, aspectRatio, setAspectRatio, selectedRevoModel, setSelectedRevoModel, userCredits }) {
    _s();
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const handleKeyDown = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const form = e.currentTarget.form;
            if (form) {
                form.requestSubmit();
            }
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatInput.useEffect": ()=>{
            if (inputRef.current) {
                inputRef.current.style.height = 'auto';
                inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
            }
        }
    }["ChatInput.useEffect"], [
        input
    ]);
    const handleRemoveImage = ()=>{
        setImagePreview(null);
        setImageDataUrl(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative mt-auto w-full border-t",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            onSubmit: handleSubmit,
            className: "p-4 space-y-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        imagePreview && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "group absolute bottom-full mb-2 w-24 h-24",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: imagePreview,
                                    alt: "Image preview",
                                    layout: "fill",
                                    objectFit: "cover",
                                    className: "rounded-md"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 93,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-md",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                    asChild: true,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "ghost",
                                                        size: "icon",
                                                        className: "h-8 w-8 text-white hover:bg-white/20 hover:text-white",
                                                        onClick: ()=>onEditImage(imagePreview),
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "sr-only",
                                                                children: "Edit image"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                                lineNumber: 105,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                                lineNumber: 106,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                                        lineNumber: 98,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 97,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                    children: "Edit this image"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 109,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 96,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 95,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 94,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "button",
                                    variant: "ghost",
                                    size: "icon",
                                    className: "absolute -right-2 -top-2 h-6 w-6 rounded-full bg-background",
                                    onClick: handleRemoveImage,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Remove image"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 120,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 121,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 113,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 92,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                            ref: inputRef,
                            value: input,
                            onChange: (e)=>setInput(e.target.value),
                            onKeyDown: handleKeyDown,
                            placeholder: "Describe the image or video you want to create...",
                            className: "pr-20 resize-none min-h-[4rem] max-h-40",
                            rows: 1,
                            disabled: isLoading
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                asChild: true,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                    type: "button",
                                                    size: "icon",
                                                    variant: "ghost",
                                                    onClick: ()=>fileInputRef.current?.click(),
                                                    disabled: isLoading,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$paperclip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Paperclip$3e$__["Paperclip"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 140,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: "Attach image"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 141,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 139,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 138,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                children: "Attach a reference image"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 144,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 137,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "file",
                                    ref: fileInputRef,
                                    className: "hidden",
                                    accept: "image/*",
                                    onChange: handleImageUpload
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 147,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "submit",
                                    size: "icon",
                                    variant: "ghost",
                                    disabled: isLoading || !input,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__["Send"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 155,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Send message"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 156,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 154,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/chat-input.tsx",
                    lineNumber: 90,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col sm:flex-row items-center justify-between gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Switch"], {
                                    id: "brand-profile-switch",
                                    checked: useBrandProfile,
                                    onCheckedChange: setUseBrandProfile,
                                    disabled: !isBrandProfileAvailable
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 163,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    htmlFor: "brand-profile-switch",
                                    children: "Apply Brand Profile"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 169,
                                    columnNumber: 13
                                }, this),
                                !isBrandProfileAvailable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                asChild: true,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs text-muted-foreground",
                                                    children: "(No profile found)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 173,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: "Go to Brand Profile to set one up."
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 176,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 172,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 171,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 162,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "AI Model:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 185,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$revo$2d$model$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RevoModelSelector"], {
                                    selectedModel: selectedRevoModel,
                                    onModelChange: setSelectedRevoModel,
                                    disabled: !isBrandProfileAvailable || outputType !== 'image',
                                    showCredits: true,
                                    userCredits: userCredits
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 186,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 184,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {
                            orientation: "vertical",
                            className: "h-6 hidden sm:block"
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 195,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "Output Type:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 198,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroup"], {
                                    value: outputType,
                                    onValueChange: (v)=>setOutputType(v),
                                    className: "flex items-center space-x-4",
                                    disabled: isLoading,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "image",
                                                    id: "r-image"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 201,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-image",
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__["Image"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 202,
                                                            columnNumber: 78
                                                        }, this),
                                                        " Image"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 202,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 200,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "video",
                                                    id: "r-video"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-video",
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 206,
                                                            columnNumber: 78
                                                        }, this),
                                                        " Video"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 206,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 204,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 199,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 197,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center space-x-4", outputType === 'video' ? 'opacity-100' : 'opacity-0'),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "Aspect Ratio:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 212,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroup"], {
                                    value: aspectRatio,
                                    onValueChange: (v)=>setAspectRatio(v),
                                    className: "flex items-center space-x-4",
                                    disabled: isLoading || outputType !== 'video',
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "16:9",
                                                    id: "r-16-9"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 215,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-16-9",
                                                    className: "flex items-center gap-2",
                                                    children: "16:9 (Sound)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 216,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 214,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "9:16",
                                                    id: "r-9-16"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 219,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-9-16",
                                                    className: "flex items-center gap-2",
                                                    children: "9:16 (No Sound)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 218,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 213,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isLoading || !input,
                            children: isLoading ? 'Generating...' : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 226,
                                        columnNumber: 46
                                    }, this),
                                    " Generate"
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 225,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/chat-input.tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/studio/chat-input.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-input.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
_s(ChatInput, "kyX8tf4W/Ccr5InjDYL8qSDZnvE=");
_c = ChatInput;
var _c;
__turbopack_context__.k.register(_c, "ChatInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:fd43ec [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7fa71ea55dac91d9f2fbbc47e38b16f32c34406758":"generateCreativeAssetAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateCreativeAssetAction": (()=>generateCreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7fa71ea55dac91d9f2fbbc47e38b16f32c34406758", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:2e449c [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f36c3e89c9711bb510eae73accd75d39762aa73f5":"generateEnhancedDesignAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateEnhancedDesignAction": (()=>generateEnhancedDesignAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateEnhancedDesignAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f36c3e89c9711bb510eae73accd75d39762aa73f5", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateEnhancedDesignAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/actions/data:cbbfc4 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"70ecaa50ef609cec48931812d952a2688c5a97bca8":"generateRevo2CreativeAssetAction"},"src/app/actions/revo-2-actions.ts",""] */ __turbopack_context__.s({
    "generateRevo2CreativeAssetAction": (()=>generateRevo2CreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateRevo2CreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("70ecaa50ef609cec48931812d952a2688c5a97bca8", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateRevo2CreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/chat-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-layout.tsx
__turbopack_context__.s({
    "ChatLayout": (()=>ChatLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$messages$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-messages.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$wrap$2d$balancer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-wrap-balancer/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-client] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$fd43ec__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:fd43ec [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$2e449c__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:2e449c [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$data$3a$cbbfc4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/actions/data:cbbfc4 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
function ChatLayout({ brandProfile, onEditImage }) {
    _s();
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [input, setInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [imagePreview, setImagePreview] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [imageDataUrl, setImageDataUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [useBrandProfile, setUseBrandProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!!brandProfile);
    const [outputType, setOutputType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('image');
    const [aspectRatio, setAspectRatio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('16:9');
    const [selectedRevoModel, setSelectedRevoModel] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('revo-1.5');
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatLayout.useEffect": ()=>{
            setUseBrandProfile(!!brandProfile);
        }
    }["ChatLayout.useEffect"], [
        brandProfile
    ]);
    const handleImageUpload = (event)=>{
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = ()=>{
                const dataUrl = reader.result;
                setImagePreview(dataUrl);
                setImageDataUrl(dataUrl);
            };
            reader.readAsDataURL(file);
        }
    };
    const handleSetReferenceAsset = (url, type)=>{
        if (url) {
            setOutputType(type);
            setImagePreview(url); // Using imagePreview for both image and video previews in the input area.
            setImageDataUrl(url);
        }
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!input.trim()) {
            toast({
                variant: 'destructive',
                title: 'Input Required',
                description: 'Please describe the image or video you want to create.'
            });
            return;
        }
        ;
        const newUserMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: input,
            // For simplicity, we just show the preview, which could be an image data URL for a video.
            imageUrl: imagePreview
        };
        setMessages([
            ...messages,
            newUserMessage
        ]);
        const currentInput = input;
        const currentImageDataUrl = imageDataUrl;
        setInput('');
        setImagePreview(null);
        setImageDataUrl(null);
        setIsLoading(true);
        try {
            let result;
            let aiResponse;
            if (selectedRevoModel === 'revo-2.0' && outputType === 'image' && brandProfile) {
                // Use Revo 2.0 next-generation AI for images with brand profile
                const revo2Result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$data$3a$cbbfc4__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateRevo2CreativeAssetAction"])(currentInput, brandProfile, {
                    platform: 'Instagram',
                    aspectRatio: '1:1',
                    style: 'photographic',
                    quality: 'ultra',
                    mood: 'professional'
                });
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: `🌟 ${selectedRevoModel} Revolutionary AI Generated!\n\nQuality Score: ${revo2Result.qualityScore}/10\nEnhancements: ${revo2Result.enhancementsApplied.join(', ')}\nProcessing Time: ${revo2Result.processingTime}ms\n\nThis image was created using our next-generation AI engine with revolutionary capabilities and ultra-high quality results.`,
                    imageUrl: revo2Result.imageUrl
                };
            } else if (selectedRevoModel === 'revo-1.5' && outputType === 'image' && brandProfile) {
                // Use enhanced design generation for images with brand profile
                const enhancedResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$2e449c__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateEnhancedDesignAction"])(brandProfile.businessType || 'business', 'Instagram', brandProfile.visualStyle || 'modern', currentInput, brandProfile, true, {
                    strictConsistency: true,
                    followBrandColors: true
                } // Enable all enhancements for Creative Studio
                );
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: `✨ ${selectedRevoModel} Enhanced Design Generated!\n\nQuality Score: ${enhancedResult.qualityScore}/10\nEnhancements: ${enhancedResult.enhancementsApplied.join(', ')}\nProcessing Time: ${enhancedResult.processingTime}ms\n\nThis design uses professional design principles, platform optimization, and quality validation for superior results.`,
                    imageUrl: enhancedResult.imageUrl
                };
            } else {
                // Use standard creative asset generation
                console.log(`🚀 Using ${selectedRevoModel} for standard generation`);
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$fd43ec__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"])(currentInput, outputType, currentImageDataUrl, useBrandProfile, brandProfile, null, outputType === 'video' ? aspectRatio : undefined);
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: result.aiExplanation,
                    imageUrl: result.imageUrl,
                    videoUrl: result.videoUrl
                };
            }
            setMessages((prevMessages)=>[
                    ...prevMessages,
                    aiResponse
                ]);
        } catch (error) {
            const errorResponse = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: `Sorry, I ran into an error: ${error.message}`
            };
            setMessages((prevMessages)=>[
                    ...prevMessages,
                    errorResponse
                ]);
            toast({
                variant: 'destructive',
                title: 'Generation Failed',
                description: error.message
            });
        } finally{
            setIsLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative flex h-full flex-col",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 overflow-y-auto",
                children: messages.length === 0 && !isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex h-full flex-col items-center justify-center text-center p-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                        className: "max-w-2xl w-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                    className: "mx-auto h-12 w-12 text-primary mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 176,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold font-headline",
                                    children: "Creative Studio"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 177,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-muted-foreground mt-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$wrap$2d$balancer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        children: "Welcome to your AI-powered creative partner. Describe the ad you want, upload an image to edit, or start from scratch."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-layout.tsx",
                                        lineNumber: 179,
                                        columnNumber: 37
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 178,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-layout.tsx",
                            lineNumber: 175,
                            columnNumber: 29
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/studio/chat-layout.tsx",
                        lineNumber: 174,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                    lineNumber: 173,
                    columnNumber: 21
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$messages$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatMessages"], {
                    messages: messages,
                    isLoading: isLoading,
                    onSetReferenceAsset: handleSetReferenceAsset,
                    onEditImage: onEditImage
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                    lineNumber: 187,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-layout.tsx",
                lineNumber: 171,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatInput"], {
                input: input,
                setInput: setInput,
                handleSubmit: handleSubmit,
                isLoading: isLoading,
                imagePreview: imagePreview,
                setImagePreview: setImagePreview,
                setImageDataUrl: setImageDataUrl,
                useBrandProfile: useBrandProfile,
                setUseBrandProfile: setUseBrandProfile,
                outputType: outputType,
                setOutputType: setOutputType,
                handleImageUpload: handleImageUpload,
                isBrandProfileAvailable: !!brandProfile,
                onEditImage: onEditImage,
                aspectRatio: aspectRatio,
                setAspectRatio: setAspectRatio,
                selectedRevoModel: selectedRevoModel,
                setSelectedRevoModel: setSelectedRevoModel
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-layout.tsx",
                lineNumber: 196,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/studio/chat-layout.tsx",
        lineNumber: 170,
        columnNumber: 9
    }, this);
}
_s(ChatLayout, "xXrZMHM3mjOHkCi/dOQ9dzRZpb8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = ChatLayout;
var _c;
__turbopack_context__.k.register(_c, "ChatLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/slider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Slider": (()=>Slider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slider/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
const Slider = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative flex w-full touch-none select-none items-center", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Track"], {
                className: "relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"], {
                    className: "absolute h-full bg-primary"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/slider.tsx",
                    lineNumber: 21,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/slider.tsx",
                lineNumber: 20,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Thumb"], {
                className: "block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/slider.tsx",
                lineNumber: 23,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/slider.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
_c1 = Slider;
Slider.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Slider$React.forwardRef");
__turbopack_context__.k.register(_c1, "Slider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/image-editor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/image-editor.tsx
__turbopack_context__.s({
    "ImageEditor": (()=>ImageEditor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-client] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-client] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eraser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eraser$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eraser.js [app-client] (ecmascript) <export default as Eraser>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/undo.js [app-client] (ecmascript) <export default as Undo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/redo.js [app-client] (ecmascript) <export default as Redo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rectangle$2d$horizontal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RectangleHorizontal$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rectangle-horizontal.js [app-client] (ecmascript) <export default as RectangleHorizontal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$slider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/slider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$fd43ec__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:fd43ec [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
function ImageEditor({ imageUrl, onClose, brandProfile }) {
    _s();
    const imageCanvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const drawingCanvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isDrawing, setIsDrawing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [brushSize, setBrushSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(40);
    const [tool, setTool] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('brush');
    const [prompt, setPrompt] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // History for drawing actions (masking)
    const [drawHistory, setDrawHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [drawHistoryIndex, setDrawHistoryIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1);
    // History for generated images
    const [imageHistory, setImageHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        imageUrl
    ]);
    const [imageHistoryIndex, setImageHistoryIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const [rectStart, setRectStart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const currentImageUrl = imageHistory[imageHistoryIndex];
    // Function to draw the main image onto its canvas
    const drawImage = (url)=>{
        const imageCanvas = imageCanvasRef.current;
        const drawingCanvas = drawingCanvasRef.current;
        if (!imageCanvas || !drawingCanvas) return;
        const imageCtx = imageCanvas.getContext('2d');
        const drawingCtx = drawingCanvas.getContext('2d');
        if (!imageCtx || !drawingCtx) return;
        const image = new window.Image();
        image.crossOrigin = "anonymous";
        image.src = url;
        image.onload = ()=>{
            imageCanvas.width = image.naturalWidth;
            imageCanvas.height = image.naturalHeight;
            drawingCanvas.width = image.naturalWidth;
            drawingCanvas.height = image.naturalHeight;
            imageCtx.drawImage(image, 0, 0);
            // Clear drawing canvas and reset its history
            drawingCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);
            const initialImageData = drawingCtx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);
            setDrawHistory([
                initialImageData
            ]);
            setDrawHistoryIndex(0);
        };
        image.onerror = ()=>{
            toast({
                variant: 'destructive',
                title: "Error loading image",
                description: "Could not load the image for editing."
            });
        };
    };
    // Redraw the image whenever the currentImageUrl changes (from undo/redo or new generation)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ImageEditor.useEffect": ()=>{
            if (currentImageUrl) {
                drawImage(currentImageUrl);
            }
        }
    }["ImageEditor.useEffect"], [
        currentImageUrl
    ]);
    const getDrawingContext = ()=>drawingCanvasRef.current?.getContext('2d', {
            willReadFrequently: true
        });
    const saveToDrawHistory = ()=>{
        const drawingCtx = getDrawingContext();
        if (!drawingCtx || !drawingCanvasRef.current) return;
        const newHistory = drawHistory.slice(0, drawHistoryIndex + 1);
        newHistory.push(drawingCtx.getImageData(0, 0, drawingCanvasRef.current.width, drawingCanvasRef.current.height));
        setDrawHistory(newHistory);
        setDrawHistoryIndex(newHistory.length - 1);
    };
    const handleDrawUndo = ()=>{
        if (drawHistoryIndex > 0) {
            const newIndex = drawHistoryIndex - 1;
            setDrawHistoryIndex(newIndex);
            const drawingCtx = getDrawingContext();
            if (drawingCtx) {
                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);
            }
        }
    };
    const handleDrawRedo = ()=>{
        if (drawHistoryIndex < drawHistory.length - 1) {
            const newIndex = drawHistoryIndex + 1;
            setDrawHistoryIndex(newIndex);
            const drawingCtx = getDrawingContext();
            if (drawingCtx) {
                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);
            }
        }
    };
    const handleGenerationUndo = ()=>{
        if (imageHistoryIndex > 0) {
            setImageHistoryIndex((prev)=>prev - 1);
        }
    };
    const getMousePos = (canvas, evt)=>{
        const rect = canvas.getBoundingClientRect();
        return {
            x: (evt.clientX - rect.left) / rect.width * canvas.width,
            y: (evt.clientY - rect.top) / rect.height * canvas.height
        };
    };
    const startDrawing = (e)=>{
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        const { x, y } = getMousePos(canvas, e);
        setIsDrawing(true);
        if (tool === 'brush' || tool === 'eraser') {
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineWidth = brushSize;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.globalCompositeOperation = tool === 'brush' ? 'source-over' : 'destination-out';
        } else if (tool === 'rect') {
            setRectStart({
                x,
                y
            });
        }
    };
    const draw = (e)=>{
        if (!isDrawing) return;
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        const { x, y } = getMousePos(canvas, e);
        if (tool === 'brush' || tool === 'eraser') {
            ctx.lineTo(x, y);
            ctx.stroke();
        } else if (tool === 'rect' && rectStart) {
            // Restore previous state to draw rect preview
            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.lineWidth = 2;
            ctx.strokeRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);
        }
    };
    const stopDrawing = (e)=>{
        if (!isDrawing) return;
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        if (tool === 'brush' || tool === 'eraser') {
            ctx.closePath();
        } else if (tool === 'rect' && rectStart) {
            // Restore canvas before drawing final rect
            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);
            const { x, y } = getMousePos(canvas, e);
            ctx.globalCompositeOperation = 'source-over';
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);
            setRectStart(null);
        }
        setIsDrawing(false);
        saveToDrawHistory();
    };
    const getMaskDataUrl = ()=>{
        const drawingCanvas = drawingCanvasRef.current;
        if (!drawingCanvas) return '';
        const ctx = getDrawingContext();
        if (!ctx) return '';
        const originalImageData = ctx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);
        const originalData = originalImageData.data;
        const maskCanvas = document.createElement('canvas');
        maskCanvas.width = drawingCanvas.width;
        maskCanvas.height = drawingCanvas.height;
        const maskCtx = maskCanvas.getContext('2d');
        if (!maskCtx) return '';
        const maskImageData = maskCtx.createImageData(drawingCanvas.width, drawingCanvas.height);
        const maskData = maskImageData.data;
        for(let i = 0; i < originalData.length; i += 4){
            if (originalData[i + 3] > 0) {
                maskData[i] = 0; // R (black)
                maskData[i + 1] = 0; // G
                maskData[i + 2] = 0; // B
                maskData[i + 3] = 255; // A (opaque)
            } else {
                maskData[i] = 255; // R (white)
                maskData[i + 1] = 255; // G
                maskData[i + 2] = 255; // B
                maskData[i + 3] = 255; // A (opaque)
            }
        }
        maskCtx.putImageData(maskImageData, 0, 0);
        return maskCanvas.toDataURL('image/png');
    };
    const handleGenerate = async ()=>{
        if (!prompt.trim()) {
            toast({
                variant: 'destructive',
                title: "Prompt is required",
                description: "Please describe what you want to change."
            });
            return;
        }
        setIsLoading(true);
        const maskDataUrl = getMaskDataUrl();
        if (!maskDataUrl) {
            toast({
                variant: 'destructive',
                title: "Mask Error",
                description: "Could not generate the mask data."
            });
            setIsLoading(false);
            return;
        }
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$fd43ec__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"])(prompt, 'image', currentImageUrl, !!brandProfile, brandProfile, maskDataUrl);
            if (result.imageUrl) {
                const newHistory = imageHistory.slice(0, imageHistoryIndex + 1);
                newHistory.push(result.imageUrl);
                setImageHistory(newHistory);
                setImageHistoryIndex(newHistory.length - 1);
                toast({
                    title: "Image Updated!",
                    description: result.aiExplanation
                });
            } else {
                toast({
                    variant: 'destructive',
                    title: "Generation Failed",
                    description: "The AI did not return an image."
                });
            }
        } catch (error) {
            toast({
                variant: 'destructive',
                title: "Generation Failed",
                description: error.message
            });
        } finally{
            setIsLoading(false);
            setPrompt("");
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-full w-full bg-background",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-64 flex-shrink-0 border-r bg-card p-4 flex flex-col gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-xl font-bold",
                                children: "Image Editor"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 282,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                onClick: onClose,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/image-editor.tsx",
                                    lineNumber: 283,
                                    columnNumber: 75
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 283,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 281,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "History"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 287,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        onClick: handleDrawUndo,
                                        disabled: drawHistoryIndex <= 0,
                                        children: [
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                                lineNumber: 289,
                                                columnNumber: 110
                                            }, this),
                                            " Undo Mask"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 289,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        onClick: handleDrawRedo,
                                        disabled: drawHistoryIndex >= drawHistory.length - 1,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__["Redo"], {
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                                lineNumber: 290,
                                                columnNumber: 130
                                            }, this),
                                            " Redo Mask"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 290,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 288,
                                columnNumber: 22
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: handleGenerationUndo,
                                disabled: imageHistoryIndex <= 0,
                                className: "w-full",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                                        className: "mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 293,
                                        columnNumber: 25
                                    }, this),
                                    " Undo Generation"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 292,
                                columnNumber: 22
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 286,
                        columnNumber: 18
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Tool"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 298,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-3 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'brush' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('brush'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 300,
                                            columnNumber: 119
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 300,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'rect' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('rect'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rectangle$2d$horizontal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RectangleHorizontal$3e$__["RectangleHorizontal"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 301,
                                            columnNumber: 117
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 301,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'eraser' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('eraser'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eraser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eraser$3e$__["Eraser"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 302,
                                            columnNumber: 121
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 302,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 299,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 297,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                htmlFor: "brush-size",
                                children: [
                                    "Brush Size: ",
                                    brushSize,
                                    "px"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 307,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$slider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slider"], {
                                id: "brush-size",
                                min: 5,
                                max: 100,
                                value: [
                                    brushSize
                                ],
                                onValueChange: (v)=>setBrushSize(v[0])
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 308,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 306,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 310,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2 flex-1 flex flex-col",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                htmlFor: "inpaint-prompt",
                                children: "Edit Prompt"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 312,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                id: "inpaint-prompt",
                                placeholder: "e.g., 'add sunglasses'",
                                value: prompt,
                                onChange: (e)=>setPrompt(e.target.value),
                                disabled: isLoading
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 313,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 311,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: handleGenerate,
                        disabled: isLoading || !prompt,
                        children: [
                            isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                className: "mr-2 animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 322,
                                columnNumber: 34
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 322,
                                columnNumber: 77
                            }, this),
                            "Generate"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 321,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/studio/image-editor.tsx",
                lineNumber: 280,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex items-center justify-center p-4 overflow-hidden bg-muted/20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                            ref: imageCanvasRef,
                            className: "max-w-full max-h-full object-contain rounded-md shadow-lg"
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/image-editor.tsx",
                            lineNumber: 329,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                            ref: drawingCanvasRef,
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("absolute top-0 left-0 max-w-full max-h-full object-contain cursor-crosshair"),
                            onMouseDown: startDrawing,
                            onMouseMove: draw,
                            onMouseUp: stopDrawing,
                            onMouseLeave: stopDrawing
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/image-editor.tsx",
                            lineNumber: 333,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/image-editor.tsx",
                    lineNumber: 328,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/image-editor.tsx",
                lineNumber: 327,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/studio/image-editor.tsx",
        lineNumber: 278,
        columnNumber: 9
    }, this);
}
_s(ImageEditor, "0rwuOkHx93pNP4sRWLWjPl87Qes=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = ImageEditor;
var _c;
__turbopack_context__.k.register(_c, "ImageEditor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/unified-brand-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BrandContent": (()=>BrandContent),
    "BrandSwitchingStatus": (()=>BrandSwitchingStatus),
    "ConditionalBrandContent": (()=>ConditionalBrandContent),
    "UnifiedBrandLayout": (()=>UnifiedBrandLayout),
    "useBrandAware": (()=>useBrandAware),
    "useBrandScopedData": (()=>useBrandScopedData),
    "withBrandAware": (()=>withBrandAware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
'use client';
;
;
// Inner component that uses the unified brand context
function UnifiedBrandLayoutContent({ children }) {
    _s();
    const { currentBrand, loading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Listen for brand changes and log them
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "UnifiedBrandLayoutContent.useBrandChangeListener": (brand)=>{
            console.log('🔄 Brand changed in layout:', brand?.businessName || brand?.name || 'none');
            // Mark as initialized once we have a brand or finished loading
            if (!isInitialized && (!loading || brand)) {
                setIsInitialized(true);
            }
        }
    }["UnifiedBrandLayoutContent.useBrandChangeListener"]);
    // Show loading state while initializing
    if (!isInitialized && loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 31,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Loading brand profiles..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 32,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 30,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this);
    }
    // Show error state if there's an error
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-600 text-2xl",
                            children: "⚠️"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                            lineNumber: 44,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-red-900 mb-2",
                        children: "Error Loading Brands"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-600 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 47,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        className: "px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",
                        children: "Retry"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 42,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 41,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "unified-brand-layout",
        children: [
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: "🔥 Unified Brand System"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 64,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "Brand: ",
                            currentBrand?.businessName || currentBrand?.name || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "ID: ",
                            currentBrand?.id || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
_s(UnifiedBrandLayoutContent, "fCqT7M8oQD2mKm1cC+OOL+jMWzQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c = UnifiedBrandLayoutContent;
function UnifiedBrandLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnifiedBrandProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(UnifiedBrandLayoutContent, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 79,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, this);
}
_c1 = UnifiedBrandLayout;
function useBrandAware() {
    _s1();
    const { currentBrand, selectBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    return {
        currentBrand,
        selectBrand,
        loading,
        isReady: !loading && currentBrand !== null,
        brandId: currentBrand?.id || null,
        brandName: currentBrand?.businessName || currentBrand?.name || null
    };
}
_s1(useBrandAware, "w47bVP/loHCLTWfG0SMBg7xbNQg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
function withBrandAware(Component) {
    var _s = __turbopack_context__.k.signature();
    return _s(function BrandAwareComponent(props) {
        _s();
        const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props,
            brand: currentBrand
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 107,
            columnNumber: 12
        }, this);
    }, "MN56VmfrkrwEAMHnmgJmh3z4nas=", false, function() {
        return [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
        ];
    });
}
function BrandContent({ children, fallback }) {
    _s2();
    const { currentBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center p-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 123,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 122,
            columnNumber: 7
        }, this);
    }
    if (!currentBrand) {
        return fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-gray-400 text-2xl",
                        children: "🏢"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 131,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-lg font-semibold text-gray-900 mb-2",
                    children: "No Brand Selected"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Please select a brand to continue."
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 135,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 130,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children(currentBrand)
    }, void 0, false);
}
_s2(BrandContent, "SDXm9VlEhbnzCURAKLqu5+JG9ko=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
_c2 = BrandContent;
function ConditionalBrandContent({ brandId, brandName, children, fallback }) {
    _s3();
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const shouldRender = (!brandId || currentBrand?.id === brandId) && (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);
    if (shouldRender) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    return fallback || null;
}
_s3(ConditionalBrandContent, "MN56VmfrkrwEAMHnmgJmh3z4nas=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
_c3 = ConditionalBrandContent;
function useBrandScopedData(feature, defaultValue, loader) {
    _s4();
    const { currentBrand, getBrandStorage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBrandScopedData.useEffect": ()=>{
            if (!currentBrand?.id) {
                setData(defaultValue);
                return;
            }
            const storage = getBrandStorage(feature);
            if (!storage) {
                setData(defaultValue);
                return;
            }
            setLoading(true);
            try {
                if (loader) {
                    // Use custom loader
                    const result = loader(currentBrand.id);
                    if (result instanceof Promise) {
                        result.then({
                            "useBrandScopedData.useEffect": (loadedData)=>{
                                setData(loadedData);
                                setLoading(false);
                            }
                        }["useBrandScopedData.useEffect"]).catch({
                            "useBrandScopedData.useEffect": (error)=>{
                                console.error(`Failed to load ${feature} data:`, error);
                                setData(defaultValue);
                                setLoading(false);
                            }
                        }["useBrandScopedData.useEffect"]);
                    } else {
                        setData(result);
                        setLoading(false);
                    }
                } else {
                    // Use storage
                    const storedData = storage.getItem();
                    setData(storedData || defaultValue);
                    setLoading(false);
                }
            } catch (error) {
                console.error(`Failed to load ${feature} data:`, error);
                setData(defaultValue);
                setLoading(false);
            }
        }
    }["useBrandScopedData.useEffect"], [
        currentBrand?.id,
        feature,
        defaultValue,
        loader,
        getBrandStorage
    ]);
    // Save data function
    const saveData = (newData)=>{
        setData(newData);
        if (currentBrand?.id) {
            const storage = getBrandStorage(feature);
            if (storage) {
                storage.setItem(newData);
            }
        }
    };
    return [
        data,
        saveData,
        loading
    ];
}
_s4(useBrandScopedData, "ZdiOY6elsL57Z0m55fnPycGGj2E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
function BrandSwitchingStatus() {
    _s5();
    const { loading, currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [switching, setSwitching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "BrandSwitchingStatus.useBrandChangeListener": (brand)=>{
            setSwitching(true);
            const timer = setTimeout({
                "BrandSwitchingStatus.useBrandChangeListener.timer": ()=>setSwitching(false)
            }["BrandSwitchingStatus.useBrandChangeListener.timer"], 1000);
            return ({
                "BrandSwitchingStatus.useBrandChangeListener": ()=>clearTimeout(timer)
            })["BrandSwitchingStatus.useBrandChangeListener"];
        }
    }["BrandSwitchingStatus.useBrandChangeListener"]);
    if (!switching && !loading) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm",
                    children: switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 257,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 255,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 254,
        columnNumber: 5
    }, this);
}
_s5(BrandSwitchingStatus, "sajUo/soq/Xgt0zh0dhNkAG60WA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c4 = BrandSwitchingStatus;
var _c, _c1, _c2, _c3, _c4;
__turbopack_context__.k.register(_c, "UnifiedBrandLayoutContent");
__turbopack_context__.k.register(_c1, "UnifiedBrandLayout");
__turbopack_context__.k.register(_c2, "BrandContent");
__turbopack_context__.k.register(_c3, "ConditionalBrandContent");
__turbopack_context__.k.register(_c4, "BrandSwitchingStatus");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/creative-studio/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/app/creative-studio/page.tsx
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-client] (ecmascript) <export default as Palette>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$image$2d$editor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/image-editor.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/unified-brand-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/brand-scoped-storage.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
function CreativeStudioPageContent() {
    _s();
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [editorImage, setEditorImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const creativeStudioStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandStorage"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_FEATURES"].CREATIVE_STUDIO);
    // Load Creative Studio data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "CreativeStudioPageContent.useBrandChangeListener": (brand)=>{
            if (brand) {
                console.log(`🎨 Creative Studio: brand changed to: ${brand.businessName || brand.name}`);
                // Load any Creative Studio specific data for this brand
                if (creativeStudioStorage) {
                    const studioData = creativeStudioStorage.getItem();
                    if (studioData) {
                        console.log(`📂 Loaded Creative Studio data for brand ${brand.businessName || brand.name}`);
                    // Apply any saved studio settings here
                    }
                }
            } else {
                console.log('🎨 Creative Studio: no brand selected');
            }
        }
    }["CreativeStudioPageContent.useBrandChangeListener"]);
    // Convert CompleteBrandProfile to BrandProfile for compatibility
    const brandProfile = currentBrand ? {
        businessName: currentBrand.businessName,
        businessType: currentBrand.businessType,
        location: currentBrand.location,
        description: currentBrand.description,
        targetAudience: currentBrand.targetAudience,
        keyFeatures: currentBrand.keyFeatures,
        competitiveAdvantages: currentBrand.competitiveAdvantages,
        visualStyle: currentBrand.visualStyle,
        writingTone: currentBrand.writingTone,
        contentThemes: currentBrand.contentThemes,
        primaryColor: currentBrand.primaryColor,
        accentColor: currentBrand.accentColor,
        backgroundColor: currentBrand.backgroundColor,
        logoDataUrl: currentBrand.logoDataUrl,
        websiteUrl: currentBrand.websiteUrl,
        socialMedia: {
            facebook: currentBrand.facebookUrl,
            instagram: currentBrand.instagramUrl,
            twitter: currentBrand.twitterUrl,
            linkedin: currentBrand.linkedinUrl
        },
        contactInfo: {
            phone: currentBrand.contactPhone,
            email: currentBrand.contactEmail,
            address: currentBrand.contactAddress
        }
    } : null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SidebarInset"], {
        fullWidth: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "flex h-14 items-center justify-between border-b bg-card px-4 lg:h-[60px] lg:px-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {}, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                asChild: true,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "secondary",
                                    size: "icon",
                                    className: "rounded-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                                    src: "https://placehold.co/40x40.png",
                                                    alt: "User",
                                                    "data-ai-hint": "user avatar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                                    lineNumber: 84,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {}, void 0, false, {
                                                        fileName: "[project]/src/app/creative-studio/page.tsx",
                                                        lineNumber: 89,
                                                        columnNumber: 33
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                                    lineNumber: 89,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/creative-studio/page.tsx",
                                            lineNumber: 83,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Toggle user menu"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/creative-studio/page.tsx",
                                            lineNumber: 91,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                    lineNumber: 82,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 81,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                align: "end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                                    children: "My Account"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                    lineNumber: 95,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 78,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1 overflow-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container mx-auto px-4 py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-7xl mx-auto",
                            children: editorImage ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$image$2d$editor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ImageEditor"], {
                                imageUrl: editorImage,
                                onClose: ()=>setEditorImage(null),
                                brandProfile: brandProfile
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 104,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatLayout"], {
                                brandProfile: brandProfile,
                                onEditImage: setEditorImage
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 110,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/creative-studio/page.tsx",
                            lineNumber: 102,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/creative-studio/page.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 99,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
}
_s(CreativeStudioPageContent, "Mq/JsPR2x0mG04vUD+yEheI7FsQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandStorage"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c = CreativeStudioPageContent;
function CreativeStudioPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrandContent"], {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__["Palette"], {
                            className: "w-8 h-8 text-gray-400"
                        }, void 0, false, {
                            fileName: "[project]/src/app/creative-studio/page.tsx",
                            lineNumber: 129,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 128,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-gray-900 mb-2",
                        children: "No Brand Selected"
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 131,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Please select a brand to access the Creative Studio."
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, void 0)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 127,
                columnNumber: 9
            }, void 0)
        }, void 0, false, {
            fileName: "[project]/src/app/creative-studio/page.tsx",
            lineNumber: 126,
            columnNumber: 7
        }, void 0),
        children: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CreativeStudioPageContent, {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 136,
                columnNumber: 14
            }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 125,
        columnNumber: 5
    }, this);
}
_c1 = CreativeStudioPage;
function CreativeStudioPageWithUnifiedBrand() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnifiedBrandLayout"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CreativeStudioPage, {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 144,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrandSwitchingStatus"], {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 145,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
_c2 = CreativeStudioPageWithUnifiedBrand;
const __TURBOPACK__default__export__ = CreativeStudioPageWithUnifiedBrand;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "CreativeStudioPageContent");
__turbopack_context__.k.register(_c1, "CreativeStudioPage");
__turbopack_context__.k.register(_c2, "CreativeStudioPageWithUnifiedBrand");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_14fe06ca._.js.map
/**
 * Imagen 4 Service - Google's Most Advanced Image Generation Model
 *
 * Features:
 * - 4K Resolution (up to 4096x4096)
 * - Superior photorealism and detail
 * - Perfect text rendering in images
 * - Advanced style controls
 * - Better brand consistency
 * - Precise prompt following
 */

import * as fs from 'fs';
import * as path from 'path';
import * as jwt from 'jsonwebtoken';
import { TextValidationService } from './text-validation-service';

interface Imagen4Config {
  projectId: string;
  location: string;
  model: string;
}

interface Imagen4Request {
  prompt: string;
  aspectRatio?: '1:1' | '9:16' | '16:9' | '4:3' | '3:4';
  outputFormat?: 'PNG' | 'JPEG';
  seed?: number;
  guidanceScale?: number;
  negativePrompt?: string;
  safetyFilterLevel?: 'BLOCK_NONE' | 'BLOCK_FEW' | 'BLOCK_SOME' | 'BLOCK_MOST';
  personGeneration?: 'ALLOW_ADULT' | 'ALLOW_ALL' | 'DONT_ALLOW';
  baseImage?: string; // Base64 encoded image for logo integration
}

interface Imagen4Response {
  success: boolean;
  imageData?: string; // Base64 encoded image
  error?: string;
  metadata?: {
    model: string;
    aspectRatio: string;
    seed: number;
    guidanceScale: number;
    processingTime?: number;
  };
}

interface ServiceAccountKey {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

export class Imagen4Service {
  private config: Imagen4Config;
  private serviceAccountKey: ServiceAccountKey | null = null;

  constructor() {
    this.config = {
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || 'localbuzz-mpkuv',
      location: 'us-central1', // Imagen 4 is available in us-central1
      model: 'imagen-4.0-ultra-generate-preview-06-06' // Imagen 4 ULTRA - Best text rendering quality
    };

    console.log('🔧 Imagen4Service initialized with:', {
      projectId: this.config.projectId,
      credentials: process.env.FIREBASE_SERVICE_ACCOUNT_KEY ? 'Environment Variable' : 'Not Found'
    });
  }

  /**
   * Load service account key from file or environment
   */
  private loadServiceAccountKey(): ServiceAccountKey {
    if (this.serviceAccountKey) {
      return this.serviceAccountKey;
    }

    try {
      // Try to load from FIREBASE_SERVICE_ACCOUNT_KEY environment variable first (primary)
      if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        console.log('🔑 Loading service account key from FIREBASE_SERVICE_ACCOUNT_KEY...');
        this.serviceAccountKey = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
        console.log('✅ Service account key loaded from FIREBASE_SERVICE_ACCOUNT_KEY successfully');
        return this.serviceAccountKey;
      }

      // Try to load from GOOGLE_CLOUD_KEY environment variable (secondary)
      if (process.env.GOOGLE_CLOUD_KEY) {
        console.log('🔑 Loading service account key from GOOGLE_CLOUD_KEY...');
        this.serviceAccountKey = JSON.parse(process.env.GOOGLE_CLOUD_KEY);
        console.log('✅ Service account key loaded from GOOGLE_CLOUD_KEY successfully');
        return this.serviceAccountKey;
      }

      // Try to load from GOOGLE_APPLICATION_CREDENTIALS file (fallback)
      if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        console.log('🔑 Loading service account key from file:', process.env.GOOGLE_APPLICATION_CREDENTIALS);
        const keyPath = path.resolve(process.env.GOOGLE_APPLICATION_CREDENTIALS);
        const keyContent = fs.readFileSync(keyPath, 'utf8');
        this.serviceAccountKey = JSON.parse(keyContent);
        console.log('✅ Service account key loaded from file successfully');
        return this.serviceAccountKey;
      }

      throw new Error('No service account key found in FIREBASE_SERVICE_ACCOUNT_KEY, GOOGLE_CLOUD_KEY, or GOOGLE_APPLICATION_CREDENTIALS');
    } catch (error) {
      console.error('❌ Failed to load service account key:', error);
      console.error('🔍 Environment variables checked:');
      console.error('   - FIREBASE_SERVICE_ACCOUNT_KEY:', !!process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
      console.error('   - GOOGLE_CLOUD_KEY:', !!process.env.GOOGLE_CLOUD_KEY);
      console.error('   - GOOGLE_APPLICATION_CREDENTIALS:', process.env.GOOGLE_APPLICATION_CREDENTIALS);
      throw error;
    }
  }

  /**
   * Create JWT token for Google Cloud API authentication
   */
  private createJWT(serviceAccountKey: ServiceAccountKey): string {
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: serviceAccountKey.client_email,
      scope: 'https://www.googleapis.com/auth/cloud-platform',
      aud: 'https://oauth2.googleapis.com/token',
      exp: now + 3600, // 1 hour
      iat: now
    };

    // Simple JWT creation (in production, use a proper JWT library)
    const header = Buffer.from(JSON.stringify({ alg: 'RS256', typ: 'JWT' })).toString('base64url');
    const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64url');

    // For now, we'll use a simplified approach and call Google's token endpoint directly
    return `${header}.${payloadStr}`;
  }

  /**
   * Get access token using service account key
   */
  private async getAccessToken(): Promise<string> {
    try {
      const serviceAccountKey = this.loadServiceAccountKey();

      console.log('🔑 Getting access token for:', serviceAccountKey.client_email);

      // Use Google's token endpoint with service account
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
          assertion: await this.createJWTAssertion(serviceAccountKey)
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Token request failed: ${response.status} ${errorText}`);
      }

      const tokenData = await response.json();
      console.log('✅ Access token obtained successfully');
      return tokenData.access_token;
    } catch (error) {
      console.error('❌ Failed to get access token:', error);
      throw error;
    }
  }

  /**
   * Create JWT assertion for service account authentication
   */
  private async createJWTAssertion(serviceAccountKey: ServiceAccountKey): Promise<string> {
    const now = Math.floor(Date.now() / 1000);

    const payload = {
      iss: serviceAccountKey.client_email,
      scope: 'https://www.googleapis.com/auth/cloud-platform',
      aud: 'https://oauth2.googleapis.com/token',
      exp: now + 3600,
      iat: now
    };

    console.log('🔐 Creating JWT assertion for:', serviceAccountKey.client_email);

    // Sign the JWT with the private key
    const token = jwt.sign(payload, serviceAccountKey.private_key, {
      algorithm: 'RS256',
      header: {
        alg: 'RS256',
        typ: 'JWT'
      }
    });

    return token;
  }

  /**
   * Check if we have valid Google Cloud credentials
   */
  private async checkCredentials(): Promise<boolean> {
    console.log('🔍 Checking credentials...');
    console.log('📁 GOOGLE_APPLICATION_CREDENTIALS:', process.env.GOOGLE_APPLICATION_CREDENTIALS);
    console.log('🔑 GOOGLE_CLOUD_KEY exists:', !!process.env.GOOGLE_CLOUD_KEY);
    console.log('🏗️ GOOGLE_CLOUD_PROJECT_ID:', process.env.GOOGLE_CLOUD_PROJECT_ID);

    try {
      // Try to load service account key
      const serviceAccountKey = this.loadServiceAccountKey();
      console.log('✅ Service account key loaded successfully');
      console.log('📧 Service account email:', serviceAccountKey.client_email);
      console.log('🏗️ Project ID:', serviceAccountKey.project_id);
      return true;
    } catch (error) {
      console.error('❌ Credential verification failed:', error);
      return false;
    }
  }

  /**
   * Generate image using Imagen 4 (Vertex AI) - NO FALLBACKS
   */
  async generateImage(request: Imagen4Request): Promise<Imagen4Response> {
    const startTime = Date.now();

    console.log('🎨 Starting REAL Imagen 4 generation - NO FALLBACKS...');
    console.log('📝 Prompt:', request.prompt.substring(0, 100) + '...');

    // Pre-validate any text content in the prompt
    const textValidation = TextValidationService.preValidateForAI(request.prompt);
    if (!textValidation.isValid) {
      console.warn('⚠️ Text validation warnings:', textValidation.warnings);
    }

    // Use validated text
    const validatedRequest = {
      ...request,
      prompt: textValidation.safeText
    };

    // Get access token - this will throw if credentials are missing
    const accessToken = await this.getAccessToken();

    // Call Vertex AI REST API - this will throw if API call fails
    const result = await this.callVertexAIRest(accessToken, validatedRequest);

    if (result.success) {
      console.log('✅ Real Imagen 4 generation successful!');
      return {
        ...result,
        metadata: {
          ...result.metadata!,
          processingTime: Date.now() - startTime
        }
      };
    }

    // If we get here, something went wrong but didn't throw
    throw new Error(`Imagen 4 generation failed: ${result.error || 'Unknown error'}`);
  }

  /**
   * Call real Vertex AI Imagen API using REST
   */
  private async callVertexAIRest(accessToken: string, request: Imagen4Request): Promise<Imagen4Response> {
    try {
      const endpoint = `https://${this.config.location}-aiplatform.googleapis.com/v1/projects/${this.config.projectId}/locations/${this.config.location}/publishers/google/models/${this.config.model}:predict`;

      // Build the request payload for Imagen
      const instances = [{
        prompt: this.buildImagePrompt(request),
        ...(request.aspectRatio && { aspectRatio: request.aspectRatio }),
        ...(request.seed && { seed: request.seed }),
        ...(request.guidanceScale && { guidanceScale: request.guidanceScale }),
        ...(request.negativePrompt && { negativePrompt: request.negativePrompt }),
        ...(request.safetyFilterLevel && { safetyFilterLevel: request.safetyFilterLevel }),
        ...(request.personGeneration && { personGeneration: request.personGeneration }),
        outputFormat: request.outputFormat || 'PNG',
        ...(request.baseImage && { baseImage: { bytesBase64Encoded: request.baseImage } })
      }];

      const parameters = {
        sampleCount: 1
      };

      const payload = {
        instances,
        parameters
      };

      console.log('🚀 Calling Vertex AI Imagen REST API...');
      console.log('📍 Endpoint:', endpoint);
      console.log('📦 Payload:', { instances: instances.length, parameters });

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Response Error:', response.status, errorText);
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const responseData = await response.json();
      console.log('📥 API Response:', { predictions: responseData.predictions?.length || 0 });

      if (responseData.predictions && responseData.predictions.length > 0) {
        const prediction = responseData.predictions[0];
        const imageData = prediction.bytesBase64Encoded;

        if (imageData) {
          console.log('✅ Imagen 4 REST API call successful!');
          return {
            success: true,
            imageData: `data:image/png;base64,${imageData}`,
            metadata: {
              model: 'Imagen 4 (Real - REST)',
              aspectRatio: request.aspectRatio || '1:1',
              seed: request.seed || 0,
              guidanceScale: request.guidanceScale || 8.0
            }
          };
        }
      }

      throw new Error('No image data returned from Vertex AI REST API');

    } catch (error) {
      console.error('❌ Vertex AI REST API call failed:', error);
      throw error;
    }
  }



  /**
   * Build comprehensive image prompt for Imagen 4 with focus on text rendering
   */
  private buildImagePrompt(request: Imagen4Request): string {
    // Add anti-corruption validation
    const antiCorruptionPrompt = TextValidationService.getAntiCorruptionPrompt(request.prompt);

    let prompt = `Generate a high-quality, professional image: ${request.prompt}

${antiCorruptionPrompt}`;

    // Add technical specifications with emphasis on text quality
    prompt += `\n\nTechnical Requirements:
- Ultra-high resolution and detail (4K quality)
- Professional photography quality
- Perfect composition and lighting
- Crisp, clear details throughout
- Vibrant, accurate colors
- PERFECT TEXT RENDERING: All text must be sharp, readable, and properly formatted
- Clear typography with proper contrast
- Professional text layout and spacing`;

    // Add aspect ratio guidance
    if (request.aspectRatio) {
      prompt += `\n- Aspect ratio: ${request.aspectRatio}`;
    }

    // Add text-specific guidance
    prompt += `\n\nText Quality Requirements:
- Text must be crystal clear and easily readable
- Use high-contrast colors for text visibility
- Ensure proper font sizing for readability
- Apply professional typography principles
- Text should integrate naturally with the design`;

    // Add negative prompt if provided
    if (request.negativePrompt) {
      prompt += `\n\nAvoid: ${request.negativePrompt}, blurry text, unreadable text, distorted text, pixelated text`;
    } else {
      prompt += `\n\nAvoid: blurry text, unreadable text, distorted text, pixelated text, poor typography`;
    }

    return prompt;
  }

  /**
   * Generate design for specific business type and platform
   */
  async generateBusinessDesign(params: {
    businessType: string;
    platform: string;
    brandName?: string;
    visualStyle?: string;
    imageText?: string;
    brandColors?: string[];
    logoDataUrl?: string;
  }): Promise<Imagen4Response> {
    const prompt = this.buildBusinessDesignPrompt(params);

    // Extract base64 data from logo if provided
    const baseImage = params.logoDataUrl ?
      params.logoDataUrl.split(',')[1] : undefined;

    return this.generateImage({
      prompt,
      aspectRatio: this.getAspectRatioForPlatform(params.platform),
      guidanceScale: 8.0, // Higher guidance for better brand consistency
      safetyFilterLevel: 'BLOCK_SOME',
      personGeneration: 'ALLOW_ADULT',
      // baseImage: REMOVED - doesn't work as intended for logo integration
    });
  }

  /**
   * Build business-specific design prompt
   */
  private buildBusinessDesignPrompt(params: {
    businessType: string;
    platform: string;
    brandName?: string;
    visualStyle?: string;
    imageText?: string;
    brandColors?: string[];
  }): string {
    let prompt = `Create a professional, high-quality ${params.platform} design for ${params.businessType}.`;

    if (params.brandName) {
      prompt += ` Brand: ${params.brandName}.`;
    }

    if (params.visualStyle) {
      prompt += ` Style: ${params.visualStyle}.`;
    }

    if (params.imageText) {
      prompt += ` Include text: "${params.imageText}".`;
    }

    if (params.brandColors && params.brandColors.length > 0) {
      prompt += ` Use brand colors: ${params.brandColors.join(', ')}.`;
    }

    // Add platform-specific requirements
    prompt += this.getPlatformRequirements(params.platform);

    // Add business type specific elements
    prompt += this.getBusinessTypeElements(params.businessType);

    return prompt;
  }

  /**
   * Get aspect ratio for platform
   */
  private getAspectRatioForPlatform(platform: string): '1:1' | '9:16' | '16:9' | '4:3' | '3:4' {
    switch (platform.toLowerCase()) {
      case 'instagram':
      case 'facebook':
        return '1:1';
      case 'instagram-story':
      case 'tiktok':
        return '9:16';
      case 'youtube':
      case 'linkedin':
        return '16:9';
      case 'pinterest':
        return '3:4';
      default:
        return '1:1';
    }
  }

  /**
   * Get platform-specific requirements
   */
  private getPlatformRequirements(platform: string): string {
    switch (platform.toLowerCase()) {
      case 'instagram':
        return ' Optimized for Instagram feed with engaging visual hierarchy, mobile-friendly design.';
      case 'facebook':
        return ' Facebook-optimized with clear call-to-action, social media friendly.';
      case 'linkedin':
        return ' Professional LinkedIn aesthetic, business-focused, corporate style.';
      case 'twitter':
        return ' Twitter-optimized, concise visual message, trending-aware design.';
      default:
        return ' Social media optimized with engaging visual elements.';
    }
  }

  /**
   * Get business type specific elements
   */
  private getBusinessTypeElements(businessType: string): string {
    const type = businessType.toLowerCase();

    if (type.includes('restaurant') || type.includes('food')) {
      return ' Include appetizing food imagery, warm lighting, inviting atmosphere.';
    } else if (type.includes('fitness') || type.includes('gym')) {
      return ' Include dynamic fitness imagery, energetic atmosphere, motivational elements.';
    } else if (type.includes('tech') || type.includes('software')) {
      return ' Include modern tech elements, clean digital aesthetic, innovation themes.';
    } else if (type.includes('fashion') || type.includes('clothing')) {
      return ' Include stylish fashion elements, trendy aesthetic, lifestyle imagery.';
    } else if (type.includes('real estate')) {
      return ' Include property imagery, professional presentation, luxury elements.';
    } else {
      return ' Include relevant industry imagery, professional presentation, brand-appropriate elements.';
    }
  }

  /**
   * Check if Imagen 4 is properly configured and available
   */
  async isConfigured(): Promise<{ configured: boolean; details: any }> {
    try {
      const hasCredentials = await this.checkCredentials();
      const hasProjectId = !!this.config.projectId;

      return {
        configured: hasCredentials && hasProjectId,
        details: {
          hasCredentials,
          hasProjectId,
          projectId: this.config.projectId,
          location: this.config.location,
          model: this.config.model,
          credentialSources: {
            applicationCredentials: !!process.env.GOOGLE_APPLICATION_CREDENTIALS,
            cloudKey: !!process.env.GOOGLE_CLOUD_KEY,
            projectId: !!process.env.GOOGLE_CLOUD_PROJECT_ID
          }
        }
      };
    } catch (error) {
      return {
        configured: false,
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          hasCredentials: false,
          hasProjectId: !!this.config.projectId
        }
      };
    }
  }
}

export const imagen4Service = new Imagen4Service();

module.exports = {

"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_a212883e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript)");
    });
});
}}),

};
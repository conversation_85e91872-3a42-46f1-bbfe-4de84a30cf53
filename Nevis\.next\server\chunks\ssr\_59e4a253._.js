module.exports = {

"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/ai/flows/generate-creative-asset.ts
__turbopack_context__.s({});
"use turbopack no side effects";
;
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/data:c0455b [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4060851bba2dd643a600428bdf8a566b397fe38d6d":"generateCreativeAsset"},"src/ai/flows/generate-creative-asset.ts",""] */ __turbopack_context__.s({
    "generateCreativeAsset": (()=>generateCreativeAsset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAsset = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("4060851bba2dd643a600428bdf8a566b397fe38d6d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAsset"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$c0455b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$c0455b__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/data:c0455b [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-ssr] (ecmascript) <exports>");
}}),
"[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// This file must be bundled in the app's client layer, it shouldn't be directly
// imported by the server.
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    callServer: null,
    createServerReference: null,
    findSourceMapURL: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    callServer: function() {
        return _appcallserver.callServer;
    },
    createServerReference: function() {
        return createServerReference;
    },
    findSourceMapURL: function() {
        return _appfindsourcemapurl.findSourceMapURL;
    }
});
const _appcallserver = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-call-server.js [app-ssr] (ecmascript)");
const _appfindsourcemapurl = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-find-source-map-url.js [app-ssr] (ecmascript)");
const createServerReference = (("TURBOPACK compile-time truthy", 1) ? __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client-edge.js [app-ssr] (ecmascript)") : ("TURBOPACK unreachable", undefined)).createServerReference; //# sourceMappingURL=action-client-wrapper.js.map
}}),

};

//# sourceMappingURL=_59e4a253._.js.map
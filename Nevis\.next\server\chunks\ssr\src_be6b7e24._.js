module.exports = {

"[project]/src/components/studio/chat-avatar.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/components/studio/chat-avatar.tsx
__turbopack_context__.s({
    "ChatAvatar": (()=>ChatAvatar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-ssr] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-ssr] (ecmascript)");
;
;
;
function ChatAvatar({ role }) {
    if (role === 'user') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Avatar"], {
            className: "h-8 w-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-avatar.tsx",
                    lineNumber: 14,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-avatar.tsx",
                lineNumber: 13,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-avatar.tsx",
            lineNumber: 12,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Avatar"], {
        className: "h-8 w-8 bg-primary text-primary-foreground",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarFallback"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-avatar.tsx",
                lineNumber: 23,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-avatar.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-avatar.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/studio/chat-messages.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/components/studio/chat-messages.tsx
__turbopack_context__.s({
    "ChatMessages": (()=>ChatMessages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-avatar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-ssr] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-ssr] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tooltip.tsx [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
function ChatMessages({ messages, isLoading, onSetReferenceAsset, onEditImage }) {
    const scrollableContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (scrollableContainerRef.current) {
            scrollableContainerRef.current.scrollTop = scrollableContainerRef.current.scrollHeight;
        }
    }, [
        messages,
        isLoading
    ]);
    const handleDownload = async (url, type)=>{
        if (!url) {
            toast({
                variant: 'destructive',
                title: 'Download Failed',
                description: 'No asset URL found.'
            });
            return;
        }
        try {
            // Download the original HD file directly to preserve quality
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            const fileExtension = type === 'image' ? 'png' : 'mp4';
            link.download = `nevis-hd-${type}-${Date.now()}.${fileExtension}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);
            toast({
                title: 'HD Download Complete',
                description: `High-definition ${type} downloaded successfully.`
            });
        } catch (error) {
            console.error('Download failed:', error);
            toast({
                variant: 'destructive',
                title: 'Download Failed',
                description: `Could not download the ${type}. Please try again.`
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: scrollableContainerRef,
        className: "flex-1 overflow-y-auto p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mx-auto max-w-3xl space-y-6",
                children: [
                    messages.map((message, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex items-start gap-4', message.role === 'user' && 'justify-end'),
                            children: [
                                message.role === 'assistant' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                    role: "assistant"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 79,
                                    columnNumber: 48
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('max-w-[80%] space-y-2 rounded-lg p-3', message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'),
                                    children: [
                                        message.role === 'user' && message.imageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative w-48 h-48 overflow-hidden rounded-md border",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                src: message.imageUrl,
                                                alt: "User upload preview",
                                                layout: "fill",
                                                objectFit: "cover"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                lineNumber: 89,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 88,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "whitespace-pre-wrap text-sm",
                                            children: message.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 93,
                                            columnNumber: 17
                                        }, this),
                                        message.role === 'assistant' && message.imageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "group relative w-full max-w-sm overflow-hidden rounded-md border",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    src: message.imageUrl,
                                                    alt: "Generated image",
                                                    width: 512,
                                                    height: 512,
                                                    className: "w-full h-auto object-contain",
                                                    crossOrigin: "anonymous"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 98,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onEditImage(message.imageUrl),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 108,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Edit Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 109,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 102,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 101,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Edit with Inpainting"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 112,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 100,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onSetReferenceAsset(message.imageUrl, 'image'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 122,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Refine Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 123,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 116,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 115,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Refine this image (new prompt)"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 126,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 114,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>handleDownload(message.imageUrl, 'image'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 136,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Download Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 137,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 130,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 129,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Download image"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 140,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 128,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 99,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 97,
                                            columnNumber: 19
                                        }, this),
                                        message.role === 'assistant' && message.videoUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "group relative w-full max-w-sm overflow-hidden rounded-md border",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                                    controls: true,
                                                    autoPlay: true,
                                                    src: message.videoUrl,
                                                    className: "w-full"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 149,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onSetReferenceAsset(message.videoUrl, 'video'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 159,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Refine Video"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 160,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 153,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 152,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Refine this video (new prompt)"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 163,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 151,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>handleDownload(message.videoUrl, 'video'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 173,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Download Video"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 174,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 167,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 166,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Download video"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 177,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 165,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 148,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 80,
                                    columnNumber: 15
                                }, this),
                                message.role === 'user' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                    role: "user"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 184,
                                    columnNumber: 43
                                }, this)
                            ]
                        }, index, true, {
                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                            lineNumber: 78,
                            columnNumber: 13
                        }, this)),
                    isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                role: "assistant"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                lineNumber: 190,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2 rounded-lg bg-muted p-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                        className: "h-5 w-5 animate-spin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                        lineNumber: 192,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm",
                                        children: "Generating..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                        lineNumber: 193,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                lineNumber: 191,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                        lineNumber: 189,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/studio/chat-messages.tsx",
                lineNumber: 76,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-messages.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-messages.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/ui/textarea.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 8,
        columnNumber: 7
    }, this);
});
Textarea.displayName = 'Textarea';
;
}}),
"[project]/src/components/ui/label.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const labelVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");
const Label = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(labelVariants(), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/label.tsx",
        lineNumber: 18,
        columnNumber: 3
    }, this));
Label.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[project]/src/components/ui/switch.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Switch": (()=>Switch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-switch/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const Switch = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input", className),
        ...props,
        ref: ref,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Thumb"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")
        }, void 0, false, {
            fileName: "[project]/src/components/ui/switch.tsx",
            lineNumber: 20,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/switch.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
Switch.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[project]/src/components/ui/radio-group.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RadioGroup": (()=>RadioGroup),
    "RadioGroupItem": (()=>RadioGroupItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-radio-group/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle.js [app-ssr] (ecmascript) <export default as Circle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const RadioGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("grid gap-2", className),
        ...props,
        ref: ref
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radio-group.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
});
RadioGroup.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
const RadioGroupItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Item"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Indicator"], {
            className: "flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__["Circle"], {
                className: "h-2.5 w-2.5 fill-current text-current"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/radio-group.tsx",
                lineNumber: 37,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/radio-group.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radio-group.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
});
RadioGroupItem.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Item"].displayName;
;
}}),
"[project]/src/components/ui/revo-model-selector.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "REVO_MODELS": (()=>REVO_MODELS),
    "RevoModelSelector": (()=>RevoModelSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-ssr] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-ssr] (ecmascript) <export default as Sparkles>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-ssr] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rocket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Rocket$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rocket.js [app-ssr] (ecmascript) <export default as Rocket>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
const REVO_MODELS = [
    {
        id: 'revo-1.0',
        name: 'Revo 1.0',
        description: 'Standard Model - Stable Foundation',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"],
        badge: 'Stable',
        badgeVariant: 'secondary',
        features: [
            'Reliable AI Engine',
            '1:1 Images',
            'Core Features',
            'Proven Performance'
        ],
        status: 'stable'
    },
    {
        id: 'revo-1.5',
        name: 'Revo 1.5',
        description: 'Enhanced Model - Advanced Features',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        badge: 'Enhanced',
        badgeVariant: 'default',
        features: [
            'Advanced AI Engine',
            'Superior Quality',
            'Enhanced Design',
            'Smart Optimizations'
        ],
        status: 'enhanced'
    }
];
function RevoModelSelector({ selectedModel, onModelChange, disabled = false, className }) {
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const currentModel = REVO_MODELS.find((model)=>model.id === selectedModel) || REVO_MODELS[0];
    const CurrentIcon = currentModel.icon;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenu"], {
        open: isOpen,
        onOpenChange: setIsOpen,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                asChild: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    disabled: disabled,
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("min-w-[180px] justify-between", className),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CurrentIcon, {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 84,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: currentModel.name
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 85,
                                    columnNumber: 13
                                }, this),
                                currentModel.badge && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: currentModel.badgeVariant,
                                    className: "text-xs",
                                    children: currentModel.badge
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 87,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 83,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                            className: "h-4 w-4 opacity-50"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                    lineNumber: 75,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                align: "start",
                className: "w-80",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rocket$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Rocket$3e$__["Rocket"], {
                                className: "w-4 h-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                lineNumber: 98,
                                columnNumber: 11
                            }, this),
                            "Select Revo Model"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 101,
                        columnNumber: 9
                    }, this),
                    REVO_MODELS.map((model)=>{
                        const IconComponent = model.icon;
                        const isSelected = selectedModel === model.id;
                        const isAvailable = model.status !== 'development';
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                            onClick: ()=>{
                                if (isAvailable) {
                                    onModelChange(model.id);
                                    setIsOpen(false);
                                }
                            },
                            disabled: !isAvailable,
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex flex-col items-start gap-2 p-4 cursor-pointer", !isAvailable && "opacity-50 cursor-not-allowed"),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(IconComponent, {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 125,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: model.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 126,
                                                    columnNumber: 19
                                                }, this),
                                                model.badge && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: model.badgeVariant,
                                                    className: "text-xs",
                                                    children: model.badge
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 128,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 124,
                                            columnNumber: 17
                                        }, this),
                                        isSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                            className: "h-4 w-4 text-green-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 133,
                                            columnNumber: 32
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 123,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-muted-foreground",
                                    children: model.description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 136,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap gap-1",
                                    children: model.features.map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "outline",
                                            className: "text-xs",
                                            children: feature
                                        }, index, false, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 142,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 140,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, model.id, true, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 109,
                            columnNumber: 13
                        }, this);
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 151,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 text-xs text-muted-foreground",
                        children: "Each Revo model offers different capabilities and features"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 152,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
        lineNumber: 73,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/src/components/studio/chat-input.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/components/studio/chat-input.tsx
__turbopack_context__.s({
    "ChatInput": (()=>ChatInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$paperclip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Paperclip$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/paperclip.js [app-ssr] (ecmascript) <export default as Paperclip>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-ssr] (ecmascript) <export default as Send>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/image.js [app-ssr] (ecmascript) <export default as Image>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/video.js [app-ssr] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-ssr] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-ssr] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tooltip.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/switch.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/radio-group.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$revo$2d$model$2d$selector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/revo-model-selector.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
function ChatInput({ input, setInput, handleSubmit, isLoading, imagePreview, setImagePreview, setImageDataUrl, useBrandProfile, setUseBrandProfile, outputType, setOutputType, handleImageUpload, isBrandProfileAvailable, onEditImage, aspectRatio, setAspectRatio, selectedRevoModel, setSelectedRevoModel, userCredits }) {
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const handleKeyDown = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const form = e.currentTarget.form;
            if (form) {
                form.requestSubmit();
            }
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (inputRef.current) {
            inputRef.current.style.height = 'auto';
            inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
        }
    }, [
        input
    ]);
    const handleRemoveImage = ()=>{
        setImagePreview(null);
        setImageDataUrl(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative mt-auto w-full border-t",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            onSubmit: handleSubmit,
            className: "p-4 space-y-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        imagePreview && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "group absolute bottom-full mb-2 w-24 h-24",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    src: imagePreview,
                                    alt: "Image preview",
                                    layout: "fill",
                                    objectFit: "cover",
                                    className: "rounded-md"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 93,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-md",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                    asChild: true,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "ghost",
                                                        size: "icon",
                                                        className: "h-8 w-8 text-white hover:bg-white/20 hover:text-white",
                                                        onClick: ()=>onEditImage(imagePreview),
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "sr-only",
                                                                children: "Edit image"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                                lineNumber: 105,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                                lineNumber: 106,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                                        lineNumber: 98,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 97,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                    children: "Edit this image"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 109,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 96,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 95,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 94,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "button",
                                    variant: "ghost",
                                    size: "icon",
                                    className: "absolute -right-2 -top-2 h-6 w-6 rounded-full bg-background",
                                    onClick: handleRemoveImage,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Remove image"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 120,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 121,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 113,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 92,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Textarea"], {
                            ref: inputRef,
                            value: input,
                            onChange: (e)=>setInput(e.target.value),
                            onKeyDown: handleKeyDown,
                            placeholder: "Describe the image or video you want to create...",
                            className: "pr-20 resize-none min-h-[4rem] max-h-40",
                            rows: 1,
                            disabled: isLoading
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                asChild: true,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                    type: "button",
                                                    size: "icon",
                                                    variant: "ghost",
                                                    onClick: ()=>fileInputRef.current?.click(),
                                                    disabled: isLoading,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$paperclip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Paperclip$3e$__["Paperclip"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 140,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: "Attach image"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 141,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 139,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 138,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                children: "Attach a reference image"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 144,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 137,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "file",
                                    ref: fileInputRef,
                                    className: "hidden",
                                    accept: "image/*",
                                    onChange: handleImageUpload
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 147,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "submit",
                                    size: "icon",
                                    variant: "ghost",
                                    disabled: isLoading || !input,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__["Send"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 155,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Send message"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 156,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 154,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/chat-input.tsx",
                    lineNumber: 90,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col sm:flex-row items-center justify-between gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Switch"], {
                                    id: "brand-profile-switch",
                                    checked: useBrandProfile,
                                    onCheckedChange: setUseBrandProfile,
                                    disabled: !isBrandProfileAvailable
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 163,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                    htmlFor: "brand-profile-switch",
                                    children: "Apply Brand Profile"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 169,
                                    columnNumber: 13
                                }, this),
                                !isBrandProfileAvailable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                asChild: true,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs text-muted-foreground",
                                                    children: "(No profile found)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 173,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: "Go to Brand Profile to set one up."
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 176,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 172,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 171,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 162,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "AI Model:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 185,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$revo$2d$model$2d$selector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RevoModelSelector"], {
                                    selectedModel: selectedRevoModel,
                                    onModelChange: setSelectedRevoModel,
                                    disabled: !isBrandProfileAvailable || outputType !== 'image',
                                    showCredits: true,
                                    userCredits: userCredits
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 186,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 184,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {
                            orientation: "vertical",
                            className: "h-6 hidden sm:block"
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 195,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "Output Type:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 198,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroup"], {
                                    value: outputType,
                                    onValueChange: (v)=>setOutputType(v),
                                    className: "flex items-center space-x-4",
                                    disabled: isLoading,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "image",
                                                    id: "r-image"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 201,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-image",
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__["Image"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 202,
                                                            columnNumber: 78
                                                        }, this),
                                                        " Image"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 202,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 200,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "video",
                                                    id: "r-video"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-video",
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 206,
                                                            columnNumber: 78
                                                        }, this),
                                                        " Video"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 206,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 204,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 199,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 197,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center space-x-4", outputType === 'video' ? 'opacity-100' : 'opacity-0'),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "Aspect Ratio:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 212,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroup"], {
                                    value: aspectRatio,
                                    onValueChange: (v)=>setAspectRatio(v),
                                    className: "flex items-center space-x-4",
                                    disabled: isLoading || outputType !== 'video',
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "16:9",
                                                    id: "r-16-9"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 215,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-16-9",
                                                    className: "flex items-center gap-2",
                                                    children: "16:9 (Sound)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 216,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 214,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "9:16",
                                                    id: "r-9-16"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 219,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-9-16",
                                                    className: "flex items-center gap-2",
                                                    children: "9:16 (No Sound)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 218,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 213,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isLoading || !input,
                            children: isLoading ? 'Generating...' : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 226,
                                        columnNumber: 46
                                    }, this),
                                    " Generate"
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 225,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/chat-input.tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/studio/chat-input.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-input.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/data:718dac [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7fa71ea55dac91d9f2fbbc47e38b16f32c34406758":"generateCreativeAssetAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateCreativeAssetAction": (()=>generateCreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7fa71ea55dac91d9f2fbbc47e38b16f32c34406758", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/data:12f81d [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f36c3e89c9711bb510eae73accd75d39762aa73f5":"generateEnhancedDesignAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateEnhancedDesignAction": (()=>generateEnhancedDesignAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateEnhancedDesignAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7f36c3e89c9711bb510eae73accd75d39762aa73f5", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateEnhancedDesignAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/app/actions/data:cbbfc4 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"70ecaa50ef609cec48931812d952a2688c5a97bca8":"generateRevo2CreativeAssetAction"},"src/app/actions/revo-2-actions.ts",""] */ __turbopack_context__.s({
    "generateRevo2CreativeAssetAction": (()=>generateRevo2CreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var generateRevo2CreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("70ecaa50ef609cec48931812d952a2688c5a97bca8", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateRevo2CreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/src/components/studio/chat-layout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/components/studio/chat-layout.tsx
__turbopack_context__.s({
    "ChatLayout": (()=>ChatLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$messages$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-messages.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$wrap$2d$balancer$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-wrap-balancer/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-ssr] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$718dac__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:718dac [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$12f81d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:12f81d [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$data$3a$cbbfc4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/actions/data:cbbfc4 [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
function ChatLayout({ brandProfile, onEditImage }) {
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [input, setInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [imagePreview, setImagePreview] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [imageDataUrl, setImageDataUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [useBrandProfile, setUseBrandProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!!brandProfile);
    const [outputType, setOutputType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('image');
    const [aspectRatio, setAspectRatio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('16:9');
    const [selectedRevoModel, setSelectedRevoModel] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('revo-1.5');
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setUseBrandProfile(!!brandProfile);
    }, [
        brandProfile
    ]);
    const handleImageUpload = (event)=>{
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = ()=>{
                const dataUrl = reader.result;
                setImagePreview(dataUrl);
                setImageDataUrl(dataUrl);
            };
            reader.readAsDataURL(file);
        }
    };
    const handleSetReferenceAsset = (url, type)=>{
        if (url) {
            setOutputType(type);
            setImagePreview(url); // Using imagePreview for both image and video previews in the input area.
            setImageDataUrl(url);
        }
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!input.trim()) {
            toast({
                variant: 'destructive',
                title: 'Input Required',
                description: 'Please describe the image or video you want to create.'
            });
            return;
        }
        ;
        const newUserMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: input,
            // For simplicity, we just show the preview, which could be an image data URL for a video.
            imageUrl: imagePreview
        };
        setMessages([
            ...messages,
            newUserMessage
        ]);
        const currentInput = input;
        const currentImageDataUrl = imageDataUrl;
        setInput('');
        setImagePreview(null);
        setImageDataUrl(null);
        setIsLoading(true);
        try {
            let result;
            let aiResponse;
            if (selectedRevoModel === 'revo-2.0' && outputType === 'image' && brandProfile) {
                // Use Revo 2.0 next-generation AI for images with brand profile
                const revo2Result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$data$3a$cbbfc4__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateRevo2CreativeAssetAction"])(currentInput, brandProfile, {
                    platform: 'Instagram',
                    aspectRatio: '1:1',
                    style: 'photographic',
                    quality: 'ultra',
                    mood: 'professional'
                });
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: `🌟 ${selectedRevoModel} Revolutionary AI Generated!\n\nQuality Score: ${revo2Result.qualityScore}/10\nEnhancements: ${revo2Result.enhancementsApplied.join(', ')}\nProcessing Time: ${revo2Result.processingTime}ms\n\nThis image was created using our next-generation AI engine with revolutionary capabilities and ultra-high quality results.`,
                    imageUrl: revo2Result.imageUrl
                };
            } else if (selectedRevoModel === 'revo-1.5' && outputType === 'image' && brandProfile) {
                // Use enhanced design generation for images with brand profile
                const enhancedResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$12f81d__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateEnhancedDesignAction"])(brandProfile.businessType || 'business', 'Instagram', brandProfile.visualStyle || 'modern', currentInput, brandProfile, true, {
                    strictConsistency: true,
                    followBrandColors: true
                } // Enable all enhancements for Creative Studio
                );
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: `✨ ${selectedRevoModel} Enhanced Design Generated!\n\nQuality Score: ${enhancedResult.qualityScore}/10\nEnhancements: ${enhancedResult.enhancementsApplied.join(', ')}\nProcessing Time: ${enhancedResult.processingTime}ms\n\nThis design uses professional design principles, platform optimization, and quality validation for superior results.`,
                    imageUrl: enhancedResult.imageUrl
                };
            } else {
                // Use standard creative asset generation
                console.log(`🚀 Using ${selectedRevoModel} for standard generation`);
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$718dac__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"])(currentInput, outputType, currentImageDataUrl, useBrandProfile, brandProfile, null, outputType === 'video' ? aspectRatio : undefined);
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: result.aiExplanation,
                    imageUrl: result.imageUrl,
                    videoUrl: result.videoUrl
                };
            }
            setMessages((prevMessages)=>[
                    ...prevMessages,
                    aiResponse
                ]);
        } catch (error) {
            const errorResponse = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: `Sorry, I ran into an error: ${error.message}`
            };
            setMessages((prevMessages)=>[
                    ...prevMessages,
                    errorResponse
                ]);
            toast({
                variant: 'destructive',
                title: 'Generation Failed',
                description: error.message
            });
        } finally{
            setIsLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative flex h-full flex-col",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 overflow-y-auto",
                children: messages.length === 0 && !isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex h-full flex-col items-center justify-center text-center p-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        className: "max-w-2xl w-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                    className: "mx-auto h-12 w-12 text-primary mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 176,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold font-headline",
                                    children: "Creative Studio"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 177,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-muted-foreground mt-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$wrap$2d$balancer$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        children: "Welcome to your AI-powered creative partner. Describe the ad you want, upload an image to edit, or start from scratch."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-layout.tsx",
                                        lineNumber: 179,
                                        columnNumber: 37
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 178,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-layout.tsx",
                            lineNumber: 175,
                            columnNumber: 29
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/studio/chat-layout.tsx",
                        lineNumber: 174,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                    lineNumber: 173,
                    columnNumber: 21
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$messages$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChatMessages"], {
                    messages: messages,
                    isLoading: isLoading,
                    onSetReferenceAsset: handleSetReferenceAsset,
                    onEditImage: onEditImage
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                    lineNumber: 187,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-layout.tsx",
                lineNumber: 171,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChatInput"], {
                input: input,
                setInput: setInput,
                handleSubmit: handleSubmit,
                isLoading: isLoading,
                imagePreview: imagePreview,
                setImagePreview: setImagePreview,
                setImageDataUrl: setImageDataUrl,
                useBrandProfile: useBrandProfile,
                setUseBrandProfile: setUseBrandProfile,
                outputType: outputType,
                setOutputType: setOutputType,
                handleImageUpload: handleImageUpload,
                isBrandProfileAvailable: !!brandProfile,
                onEditImage: onEditImage,
                aspectRatio: aspectRatio,
                setAspectRatio: setAspectRatio,
                selectedRevoModel: selectedRevoModel,
                setSelectedRevoModel: setSelectedRevoModel
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-layout.tsx",
                lineNumber: 196,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/studio/chat-layout.tsx",
        lineNumber: 170,
        columnNumber: 9
    }, this);
}
}}),
"[project]/src/components/ui/slider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Slider": (()=>Slider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slider/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const Slider = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("relative flex w-full touch-none select-none items-center", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Track"], {
                className: "relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"], {
                    className: "absolute h-full bg-primary"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/slider.tsx",
                    lineNumber: 21,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/slider.tsx",
                lineNumber: 20,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Thumb"], {
                className: "block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/slider.tsx",
                lineNumber: 23,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/slider.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
Slider.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[project]/src/components/studio/image-editor.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/components/studio/image-editor.tsx
__turbopack_context__.s({
    "ImageEditor": (()=>ImageEditor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-ssr] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-ssr] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eraser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eraser$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eraser.js [app-ssr] (ecmascript) <export default as Eraser>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/undo.js [app-ssr] (ecmascript) <export default as Undo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/redo.js [app-ssr] (ecmascript) <export default as Redo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rectangle$2d$horizontal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RectangleHorizontal$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rectangle-horizontal.js [app-ssr] (ecmascript) <export default as RectangleHorizontal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$slider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/slider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$718dac__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:718dac [app-ssr] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
function ImageEditor({ imageUrl, onClose, brandProfile }) {
    const imageCanvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const drawingCanvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isDrawing, setIsDrawing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [brushSize, setBrushSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(40);
    const [tool, setTool] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('brush');
    const [prompt, setPrompt] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // History for drawing actions (masking)
    const [drawHistory, setDrawHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [drawHistoryIndex, setDrawHistoryIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(-1);
    // History for generated images
    const [imageHistory, setImageHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        imageUrl
    ]);
    const [imageHistoryIndex, setImageHistoryIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const [rectStart, setRectStart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const currentImageUrl = imageHistory[imageHistoryIndex];
    // Function to draw the main image onto its canvas
    const drawImage = (url)=>{
        const imageCanvas = imageCanvasRef.current;
        const drawingCanvas = drawingCanvasRef.current;
        if (!imageCanvas || !drawingCanvas) return;
        const imageCtx = imageCanvas.getContext('2d');
        const drawingCtx = drawingCanvas.getContext('2d');
        if (!imageCtx || !drawingCtx) return;
        const image = new window.Image();
        image.crossOrigin = "anonymous";
        image.src = url;
        image.onload = ()=>{
            imageCanvas.width = image.naturalWidth;
            imageCanvas.height = image.naturalHeight;
            drawingCanvas.width = image.naturalWidth;
            drawingCanvas.height = image.naturalHeight;
            imageCtx.drawImage(image, 0, 0);
            // Clear drawing canvas and reset its history
            drawingCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);
            const initialImageData = drawingCtx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);
            setDrawHistory([
                initialImageData
            ]);
            setDrawHistoryIndex(0);
        };
        image.onerror = ()=>{
            toast({
                variant: 'destructive',
                title: "Error loading image",
                description: "Could not load the image for editing."
            });
        };
    };
    // Redraw the image whenever the currentImageUrl changes (from undo/redo or new generation)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (currentImageUrl) {
            drawImage(currentImageUrl);
        }
    }, [
        currentImageUrl
    ]);
    const getDrawingContext = ()=>drawingCanvasRef.current?.getContext('2d', {
            willReadFrequently: true
        });
    const saveToDrawHistory = ()=>{
        const drawingCtx = getDrawingContext();
        if (!drawingCtx || !drawingCanvasRef.current) return;
        const newHistory = drawHistory.slice(0, drawHistoryIndex + 1);
        newHistory.push(drawingCtx.getImageData(0, 0, drawingCanvasRef.current.width, drawingCanvasRef.current.height));
        setDrawHistory(newHistory);
        setDrawHistoryIndex(newHistory.length - 1);
    };
    const handleDrawUndo = ()=>{
        if (drawHistoryIndex > 0) {
            const newIndex = drawHistoryIndex - 1;
            setDrawHistoryIndex(newIndex);
            const drawingCtx = getDrawingContext();
            if (drawingCtx) {
                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);
            }
        }
    };
    const handleDrawRedo = ()=>{
        if (drawHistoryIndex < drawHistory.length - 1) {
            const newIndex = drawHistoryIndex + 1;
            setDrawHistoryIndex(newIndex);
            const drawingCtx = getDrawingContext();
            if (drawingCtx) {
                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);
            }
        }
    };
    const handleGenerationUndo = ()=>{
        if (imageHistoryIndex > 0) {
            setImageHistoryIndex((prev)=>prev - 1);
        }
    };
    const getMousePos = (canvas, evt)=>{
        const rect = canvas.getBoundingClientRect();
        return {
            x: (evt.clientX - rect.left) / rect.width * canvas.width,
            y: (evt.clientY - rect.top) / rect.height * canvas.height
        };
    };
    const startDrawing = (e)=>{
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        const { x, y } = getMousePos(canvas, e);
        setIsDrawing(true);
        if (tool === 'brush' || tool === 'eraser') {
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineWidth = brushSize;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.globalCompositeOperation = tool === 'brush' ? 'source-over' : 'destination-out';
        } else if (tool === 'rect') {
            setRectStart({
                x,
                y
            });
        }
    };
    const draw = (e)=>{
        if (!isDrawing) return;
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        const { x, y } = getMousePos(canvas, e);
        if (tool === 'brush' || tool === 'eraser') {
            ctx.lineTo(x, y);
            ctx.stroke();
        } else if (tool === 'rect' && rectStart) {
            // Restore previous state to draw rect preview
            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.lineWidth = 2;
            ctx.strokeRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);
        }
    };
    const stopDrawing = (e)=>{
        if (!isDrawing) return;
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        if (tool === 'brush' || tool === 'eraser') {
            ctx.closePath();
        } else if (tool === 'rect' && rectStart) {
            // Restore canvas before drawing final rect
            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);
            const { x, y } = getMousePos(canvas, e);
            ctx.globalCompositeOperation = 'source-over';
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);
            setRectStart(null);
        }
        setIsDrawing(false);
        saveToDrawHistory();
    };
    const getMaskDataUrl = ()=>{
        const drawingCanvas = drawingCanvasRef.current;
        if (!drawingCanvas) return '';
        const ctx = getDrawingContext();
        if (!ctx) return '';
        const originalImageData = ctx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);
        const originalData = originalImageData.data;
        const maskCanvas = document.createElement('canvas');
        maskCanvas.width = drawingCanvas.width;
        maskCanvas.height = drawingCanvas.height;
        const maskCtx = maskCanvas.getContext('2d');
        if (!maskCtx) return '';
        const maskImageData = maskCtx.createImageData(drawingCanvas.width, drawingCanvas.height);
        const maskData = maskImageData.data;
        for(let i = 0; i < originalData.length; i += 4){
            if (originalData[i + 3] > 0) {
                maskData[i] = 0; // R (black)
                maskData[i + 1] = 0; // G
                maskData[i + 2] = 0; // B
                maskData[i + 3] = 255; // A (opaque)
            } else {
                maskData[i] = 255; // R (white)
                maskData[i + 1] = 255; // G
                maskData[i + 2] = 255; // B
                maskData[i + 3] = 255; // A (opaque)
            }
        }
        maskCtx.putImageData(maskImageData, 0, 0);
        return maskCanvas.toDataURL('image/png');
    };
    const handleGenerate = async ()=>{
        if (!prompt.trim()) {
            toast({
                variant: 'destructive',
                title: "Prompt is required",
                description: "Please describe what you want to change."
            });
            return;
        }
        setIsLoading(true);
        const maskDataUrl = getMaskDataUrl();
        if (!maskDataUrl) {
            toast({
                variant: 'destructive',
                title: "Mask Error",
                description: "Could not generate the mask data."
            });
            setIsLoading(false);
            return;
        }
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$718dac__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"])(prompt, 'image', currentImageUrl, !!brandProfile, brandProfile, maskDataUrl);
            if (result.imageUrl) {
                const newHistory = imageHistory.slice(0, imageHistoryIndex + 1);
                newHistory.push(result.imageUrl);
                setImageHistory(newHistory);
                setImageHistoryIndex(newHistory.length - 1);
                toast({
                    title: "Image Updated!",
                    description: result.aiExplanation
                });
            } else {
                toast({
                    variant: 'destructive',
                    title: "Generation Failed",
                    description: "The AI did not return an image."
                });
            }
        } catch (error) {
            toast({
                variant: 'destructive',
                title: "Generation Failed",
                description: error.message
            });
        } finally{
            setIsLoading(false);
            setPrompt("");
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-full w-full bg-background",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-64 flex-shrink-0 border-r bg-card p-4 flex flex-col gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-xl font-bold",
                                children: "Image Editor"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 282,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                onClick: onClose,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/image-editor.tsx",
                                    lineNumber: 283,
                                    columnNumber: 75
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 283,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 281,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                children: "History"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 287,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        onClick: handleDrawUndo,
                                        disabled: drawHistoryIndex <= 0,
                                        children: [
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                                lineNumber: 289,
                                                columnNumber: 110
                                            }, this),
                                            " Undo Mask"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 289,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        onClick: handleDrawRedo,
                                        disabled: drawHistoryIndex >= drawHistory.length - 1,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__["Redo"], {
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                                lineNumber: 290,
                                                columnNumber: 130
                                            }, this),
                                            " Redo Mask"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 290,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 288,
                                columnNumber: 22
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: handleGenerationUndo,
                                disabled: imageHistoryIndex <= 0,
                                className: "w-full",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                                        className: "mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 293,
                                        columnNumber: 25
                                    }, this),
                                    " Undo Generation"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 292,
                                columnNumber: 22
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 286,
                        columnNumber: 18
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Tool"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 298,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-3 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'brush' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('brush'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 300,
                                            columnNumber: 119
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 300,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'rect' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('rect'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rectangle$2d$horizontal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RectangleHorizontal$3e$__["RectangleHorizontal"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 301,
                                            columnNumber: 117
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 301,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'eraser' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('eraser'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eraser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eraser$3e$__["Eraser"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 302,
                                            columnNumber: 121
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 302,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 299,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 297,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                htmlFor: "brush-size",
                                children: [
                                    "Brush Size: ",
                                    brushSize,
                                    "px"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 307,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$slider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slider"], {
                                id: "brush-size",
                                min: 5,
                                max: 100,
                                value: [
                                    brushSize
                                ],
                                onValueChange: (v)=>setBrushSize(v[0])
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 308,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 306,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 310,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2 flex-1 flex flex-col",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                htmlFor: "inpaint-prompt",
                                children: "Edit Prompt"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 312,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                id: "inpaint-prompt",
                                placeholder: "e.g., 'add sunglasses'",
                                value: prompt,
                                onChange: (e)=>setPrompt(e.target.value),
                                disabled: isLoading
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 313,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 311,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: handleGenerate,
                        disabled: isLoading || !prompt,
                        children: [
                            isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                className: "mr-2 animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 322,
                                columnNumber: 34
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 322,
                                columnNumber: 77
                            }, this),
                            "Generate"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 321,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/studio/image-editor.tsx",
                lineNumber: 280,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex items-center justify-center p-4 overflow-hidden bg-muted/20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                            ref: imageCanvasRef,
                            className: "max-w-full max-h-full object-contain rounded-md shadow-lg"
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/image-editor.tsx",
                            lineNumber: 329,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                            ref: drawingCanvasRef,
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("absolute top-0 left-0 max-w-full max-h-full object-contain cursor-crosshair"),
                            onMouseDown: startDrawing,
                            onMouseMove: draw,
                            onMouseUp: stopDrawing,
                            onMouseLeave: stopDrawing
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/image-editor.tsx",
                            lineNumber: 333,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/image-editor.tsx",
                    lineNumber: 328,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/image-editor.tsx",
                lineNumber: 327,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/studio/image-editor.tsx",
        lineNumber: 278,
        columnNumber: 9
    }, this);
}
}}),
"[project]/src/components/layout/unified-brand-layout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BrandContent": (()=>BrandContent),
    "BrandSwitchingStatus": (()=>BrandSwitchingStatus),
    "ConditionalBrandContent": (()=>ConditionalBrandContent),
    "UnifiedBrandLayout": (()=>UnifiedBrandLayout),
    "useBrandAware": (()=>useBrandAware),
    "useBrandScopedData": (()=>useBrandScopedData),
    "withBrandAware": (()=>withBrandAware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
// Inner component that uses the unified brand context
function UnifiedBrandLayoutContent({ children }) {
    const { currentBrand, loading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Listen for brand changes and log them
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandChangeListener"])((brand)=>{
        console.log('🔄 Brand changed in layout:', brand?.businessName || brand?.name || 'none');
        // Mark as initialized once we have a brand or finished loading
        if (!isInitialized && (!loading || brand)) {
            setIsInitialized(true);
        }
    });
    // Show loading state while initializing
    if (!isInitialized && loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 31,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Loading brand profiles..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 32,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 30,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this);
    }
    // Show error state if there's an error
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-600 text-2xl",
                            children: "⚠️"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                            lineNumber: 44,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-red-900 mb-2",
                        children: "Error Loading Brands"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-600 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 47,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        className: "px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",
                        children: "Retry"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 42,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 41,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "unified-brand-layout",
        children: [
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: "🔥 Unified Brand System"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 64,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "Brand: ",
                            currentBrand?.businessName || currentBrand?.name || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "ID: ",
                            currentBrand?.id || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
function UnifiedBrandLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UnifiedBrandProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(UnifiedBrandLayoutContent, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 79,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, this);
}
function useBrandAware() {
    const { currentBrand, selectBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    return {
        currentBrand,
        selectBrand,
        loading,
        isReady: !loading && currentBrand !== null,
        brandId: currentBrand?.id || null,
        brandName: currentBrand?.businessName || currentBrand?.name || null
    };
}
function withBrandAware(Component) {
    return function BrandAwareComponent(props) {
        const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props,
            brand: currentBrand
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 107,
            columnNumber: 12
        }, this);
    };
}
function BrandContent({ children, fallback }) {
    const { currentBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center p-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 123,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 122,
            columnNumber: 7
        }, this);
    }
    if (!currentBrand) {
        return fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-gray-400 text-2xl",
                        children: "🏢"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 131,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-lg font-semibold text-gray-900 mb-2",
                    children: "No Brand Selected"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Please select a brand to continue."
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 135,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 130,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children(currentBrand)
    }, void 0, false);
}
function ConditionalBrandContent({ brandId, brandName, children, fallback }) {
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const shouldRender = (!brandId || currentBrand?.id === brandId) && (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);
    if (shouldRender) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    return fallback || null;
}
function useBrandScopedData(feature, defaultValue, loader) {
    const { currentBrand, getBrandStorage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!currentBrand?.id) {
            setData(defaultValue);
            return;
        }
        const storage = getBrandStorage(feature);
        if (!storage) {
            setData(defaultValue);
            return;
        }
        setLoading(true);
        try {
            if (loader) {
                // Use custom loader
                const result = loader(currentBrand.id);
                if (result instanceof Promise) {
                    result.then((loadedData)=>{
                        setData(loadedData);
                        setLoading(false);
                    }).catch((error)=>{
                        console.error(`Failed to load ${feature} data:`, error);
                        setData(defaultValue);
                        setLoading(false);
                    });
                } else {
                    setData(result);
                    setLoading(false);
                }
            } else {
                // Use storage
                const storedData = storage.getItem();
                setData(storedData || defaultValue);
                setLoading(false);
            }
        } catch (error) {
            console.error(`Failed to load ${feature} data:`, error);
            setData(defaultValue);
            setLoading(false);
        }
    }, [
        currentBrand?.id,
        feature,
        defaultValue,
        loader,
        getBrandStorage
    ]);
    // Save data function
    const saveData = (newData)=>{
        setData(newData);
        if (currentBrand?.id) {
            const storage = getBrandStorage(feature);
            if (storage) {
                storage.setItem(newData);
            }
        }
    };
    return [
        data,
        saveData,
        loading
    ];
}
function BrandSwitchingStatus() {
    const { loading, currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [switching, setSwitching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandChangeListener"])((brand)=>{
        setSwitching(true);
        const timer = setTimeout(()=>setSwitching(false), 1000);
        return ()=>clearTimeout(timer);
    });
    if (!switching && !loading) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm",
                    children: switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 257,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 255,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 254,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/creative-studio/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/app/creative-studio/page.tsx
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sidebar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-layout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-ssr] (ecmascript) <export default as Palette>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$image$2d$editor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/image-editor.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/unified-brand-layout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/brand-scoped-storage.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
function CreativeStudioPageContent() {
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [editorImage, setEditorImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const creativeStudioStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandStorage"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["STORAGE_FEATURES"].CREATIVE_STUDIO);
    // Load Creative Studio data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useBrandChangeListener"])((brand)=>{
        if (brand) {
            console.log(`🎨 Creative Studio: brand changed to: ${brand.businessName || brand.name}`);
            // Load any Creative Studio specific data for this brand
            if (creativeStudioStorage) {
                const studioData = creativeStudioStorage.getItem();
                if (studioData) {
                    console.log(`📂 Loaded Creative Studio data for brand ${brand.businessName || brand.name}`);
                // Apply any saved studio settings here
                }
            }
        } else {
            console.log('🎨 Creative Studio: no brand selected');
        }
    });
    // Convert CompleteBrandProfile to BrandProfile for compatibility
    const brandProfile = currentBrand ? {
        businessName: currentBrand.businessName,
        businessType: currentBrand.businessType,
        location: currentBrand.location,
        description: currentBrand.description,
        targetAudience: currentBrand.targetAudience,
        keyFeatures: currentBrand.keyFeatures,
        competitiveAdvantages: currentBrand.competitiveAdvantages,
        visualStyle: currentBrand.visualStyle,
        writingTone: currentBrand.writingTone,
        contentThemes: currentBrand.contentThemes,
        primaryColor: currentBrand.primaryColor,
        accentColor: currentBrand.accentColor,
        backgroundColor: currentBrand.backgroundColor,
        logoDataUrl: currentBrand.logoDataUrl,
        websiteUrl: currentBrand.websiteUrl,
        socialMedia: {
            facebook: currentBrand.facebookUrl,
            instagram: currentBrand.instagramUrl,
            twitter: currentBrand.twitterUrl,
            linkedin: currentBrand.linkedinUrl
        },
        contactInfo: {
            phone: currentBrand.contactPhone,
            email: currentBrand.contactEmail,
            address: currentBrand.contactAddress
        }
    } : null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SidebarInset"], {
        fullWidth: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "flex h-14 items-center justify-between border-b bg-card px-4 lg:h-[60px] lg:px-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {}, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                asChild: true,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "secondary",
                                    size: "icon",
                                    className: "rounded-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Avatar"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                                    src: "https://placehold.co/40x40.png",
                                                    alt: "User",
                                                    "data-ai-hint": "user avatar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                                    lineNumber: 84,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {}, void 0, false, {
                                                        fileName: "[project]/src/app/creative-studio/page.tsx",
                                                        lineNumber: 89,
                                                        columnNumber: 33
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                                    lineNumber: 89,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/creative-studio/page.tsx",
                                            lineNumber: 83,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Toggle user menu"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/creative-studio/page.tsx",
                                            lineNumber: 91,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                    lineNumber: 82,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 81,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                align: "end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                                    children: "My Account"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                    lineNumber: 95,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 78,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1 overflow-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container mx-auto px-4 py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-7xl mx-auto",
                            children: editorImage ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$image$2d$editor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ImageEditor"], {
                                imageUrl: editorImage,
                                onClose: ()=>setEditorImage(null),
                                brandProfile: brandProfile
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 104,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChatLayout"], {
                                brandProfile: brandProfile,
                                onEditImage: setEditorImage
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 110,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/creative-studio/page.tsx",
                            lineNumber: 102,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/creative-studio/page.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 99,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
}
function CreativeStudioPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrandContent"], {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__["Palette"], {
                            className: "w-8 h-8 text-gray-400"
                        }, void 0, false, {
                            fileName: "[project]/src/app/creative-studio/page.tsx",
                            lineNumber: 129,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 128,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-gray-900 mb-2",
                        children: "No Brand Selected"
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 131,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Please select a brand to access the Creative Studio."
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, void 0)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 127,
                columnNumber: 9
            }, void 0)
        }, void 0, false, {
            fileName: "[project]/src/app/creative-studio/page.tsx",
            lineNumber: 126,
            columnNumber: 7
        }, void 0),
        children: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CreativeStudioPageContent, {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 136,
                columnNumber: 14
            }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 125,
        columnNumber: 5
    }, this);
}
function CreativeStudioPageWithUnifiedBrand() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["UnifiedBrandLayout"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CreativeStudioPage, {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 144,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BrandSwitchingStatus"], {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 145,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = CreativeStudioPageWithUnifiedBrand;
}}),

};

//# sourceMappingURL=src_be6b7e24._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/studio/chat-avatar.tsx"], "sourcesContent": ["// src/components/studio/chat-avatar.tsx\r\nimport { Bo<PERSON>, User } from 'lucide-react';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\n\r\ninterface ChatAvatarProps {\r\n  role: 'user' | 'assistant';\r\n}\r\n\r\nexport function ChatAvatar({ role }: ChatAvatarProps) {\r\n  if (role === 'user') {\r\n    return (\r\n      <Avatar className=\"h-8 w-8\">\r\n        <AvatarFallback>\r\n          <User className=\"h-4 w-4\" />\r\n        </AvatarFallback>\r\n      </Avatar>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Avatar className=\"h-8 w-8 bg-primary text-primary-foreground\">\r\n      <AvatarFallback>\r\n        <Bot className=\"h-4 w-4\" />\r\n      </AvatarFallback>\r\n    </Avatar>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AACxC;AAAA;AACA;;;;AAMO,SAAS,WAAW,EAAE,IAAI,EAAmB;IAClD,IAAI,SAAS,QAAQ;QACnB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,WAAU;sBAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;0BACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIxB;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,WAAU;kBAChB,cAAA,6LAAC,qIAAA,CAAA,iBAAc;sBACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB;KAlBgB", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/studio/chat-messages.tsx"], "sourcesContent": ["// src/components/studio/chat-messages.tsx\r\nimport * as React from 'react';\r\nimport type { Message } from '@/lib/types';\r\nimport { cn } from '@/lib/utils';\r\nimport { ChatAvatar } from './chat-avatar';\r\nimport { Loader2, Download, Wand, Brush } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport { Button } from '@/components/ui/button';\r\nimport { useToast } from '@/hooks/use-toast';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\n\r\ninterface ChatMessagesProps {\r\n  messages: Message[];\r\n  isLoading: boolean;\r\n  onSetReferenceAsset: (url: string | null | undefined, type: 'image' | 'video') => void;\r\n  onEditImage: (url: string) => void;\r\n}\r\n\r\nexport function ChatMessages({ messages, isLoading, onSetReferenceAsset, onEditImage }: ChatMessagesProps) {\r\n  const scrollableContainerRef = React.useRef<HTMLDivElement>(null);\r\n  const { toast } = useToast();\r\n\r\n  React.useEffect(() => {\r\n    if (scrollableContainerRef.current) {\r\n      scrollableContainerRef.current.scrollTop = scrollableContainerRef.current.scrollHeight;\r\n    }\r\n  }, [messages, isLoading]);\r\n\r\n  const handleDownload = async (url: string | null | undefined, type: 'image' | 'video') => {\r\n    if (!url) {\r\n      toast({\r\n        variant: 'destructive',\r\n        title: 'Download Failed',\r\n        description: 'No asset URL found.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Download the original HD file directly to preserve quality\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const blob = await response.blob();\r\n      const downloadUrl = window.URL.createObjectURL(blob);\r\n\r\n      const link = document.createElement('a');\r\n      link.href = downloadUrl;\r\n      const fileExtension = type === 'image' ? 'png' : 'mp4';\r\n      link.download = `nevis-hd-${type}-${Date.now()}.${fileExtension}`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(downloadUrl);\r\n\r\n      toast({\r\n        title: 'HD Download Complete',\r\n        description: `High-definition ${type} downloaded successfully.`,\r\n      });\r\n    } catch (error) {\r\n      console.error('Download failed:', error);\r\n      toast({\r\n        variant: 'destructive',\r\n        title: 'Download Failed',\r\n        description: `Could not download the ${type}. Please try again.`,\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n  return (\r\n    <div ref={scrollableContainerRef} className=\"flex-1 overflow-y-auto p-4\">\r\n      <TooltipProvider>\r\n        <div className=\"mx-auto max-w-3xl space-y-6\">\r\n          {messages.map((message, index) => (\r\n            <div key={index} className={cn('flex items-start gap-4', message.role === 'user' && 'justify-end')}>\r\n              {message.role === 'assistant' && <ChatAvatar role=\"assistant\" />}\r\n              <div\r\n                className={cn(\r\n                  'max-w-[80%] space-y-2 rounded-lg p-3',\r\n                  message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'\r\n                )}\r\n              >\r\n                {/* User uploaded image preview */}\r\n                {message.role === 'user' && message.imageUrl && (\r\n                  <div className=\"relative w-48 h-48 overflow-hidden rounded-md border\">\r\n                    <Image src={message.imageUrl} alt=\"User upload preview\" layout=\"fill\" objectFit=\"cover\" />\r\n                  </div>\r\n                )}\r\n\r\n                <p className=\"whitespace-pre-wrap text-sm\">{message.content}</p>\r\n\r\n                {/* AI generated image */}\r\n                {message.role === 'assistant' && message.imageUrl && (\r\n                  <div className=\"group relative w-full max-w-sm overflow-hidden rounded-md border\">\r\n                    <Image src={message.imageUrl} alt=\"Generated image\" width={512} height={512} className=\"w-full h-auto object-contain\" crossOrigin='anonymous' />\r\n                    <div className=\"absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"icon\"\r\n                            className=\"h-8 w-8\"\r\n                            onClick={() => onEditImage(message.imageUrl!)}\r\n                          >\r\n                            <Brush className=\"h-4 w-4\" />\r\n                            <span className=\"sr-only\">Edit Image</span>\r\n                          </Button>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>Edit with Inpainting</TooltipContent>\r\n                      </Tooltip>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"icon\"\r\n                            className=\"h-8 w-8\"\r\n                            onClick={() => onSetReferenceAsset(message.imageUrl, 'image')}\r\n                          >\r\n                            <Wand className=\"h-4 w-4\" />\r\n                            <span className=\"sr-only\">Refine Image</span>\r\n                          </Button>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>Refine this image (new prompt)</TooltipContent>\r\n                      </Tooltip>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"icon\"\r\n                            className=\"h-8 w-8\"\r\n                            onClick={() => handleDownload(message.imageUrl, 'image')}\r\n                          >\r\n                            <Download className=\"h-4 w-4\" />\r\n                            <span className=\"sr-only\">Download Image</span>\r\n                          </Button>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>Download image</TooltipContent>\r\n                      </Tooltip>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* AI generated video */}\r\n                {message.role === 'assistant' && message.videoUrl && (\r\n                  <div className=\"group relative w-full max-w-sm overflow-hidden rounded-md border\">\r\n                    <video controls autoPlay src={message.videoUrl} className=\"w-full\" />\r\n                    <div className=\"absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"icon\"\r\n                            className=\"h-8 w-8\"\r\n                            onClick={() => onSetReferenceAsset(message.videoUrl, 'video')}\r\n                          >\r\n                            <Wand className=\"h-4 w-4\" />\r\n                            <span className=\"sr-only\">Refine Video</span>\r\n                          </Button>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>Refine this video (new prompt)</TooltipContent>\r\n                      </Tooltip>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <Button\r\n                            variant=\"secondary\"\r\n                            size=\"icon\"\r\n                            className=\"h-8 w-8\"\r\n                            onClick={() => handleDownload(message.videoUrl, 'video')}\r\n                          >\r\n                            <Download className=\"h-4 w-4\" />\r\n                            <span className=\"sr-only\">Download Video</span>\r\n                          </Button>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>Download video</TooltipContent>\r\n                      </Tooltip>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n              </div>\r\n              {message.role === 'user' && <ChatAvatar role=\"user\" />}\r\n            </div>\r\n          ))}\r\n\r\n          {isLoading && (\r\n            <div className=\"flex items-start gap-4\">\r\n              <ChatAvatar role=\"assistant\" />\r\n              <div className=\"flex items-center space-x-2 rounded-lg bg-muted p-3\">\r\n                <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                <span className=\"text-sm\">Generating...</span>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </TooltipProvider>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0CAA0C;;;;;AAC1C;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,mBAAmB,EAAE,WAAW,EAAqB;;IACvG,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAkB;IAC5D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;kCAAE;YACd,IAAI,uBAAuB,OAAO,EAAE;gBAClC,uBAAuB,OAAO,CAAC,SAAS,GAAG,uBAAuB,OAAO,CAAC,YAAY;YACxF;QACF;iCAAG;QAAC;QAAU;KAAU;IAExB,MAAM,iBAAiB,OAAO,KAAgC;QAC5D,IAAI,CAAC,KAAK;YACR,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,IAAI;YACF,6DAA6D;YAC7D,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,cAAc,OAAO,GAAG,CAAC,eAAe,CAAC;YAE/C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,MAAM,gBAAgB,SAAS,UAAU,QAAQ;YACjD,KAAK,QAAQ,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,eAAe;YACjE,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,gBAAgB,EAAE,KAAK,yBAAyB,CAAC;YACjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,CAAC,uBAAuB,EAAE,KAAK,mBAAmB,CAAC;YAClE;QACF;IACF;IAGA,qBACE,6LAAC;QAAI,KAAK;QAAwB,WAAU;kBAC1C,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;;oBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAAgB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,QAAQ,IAAI,KAAK,UAAU;;gCACjF,QAAQ,IAAI,KAAK,6BAAe,6LAAC,iJAAA,CAAA,aAAU;oCAAC,MAAK;;;;;;8CAClD,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wCACA,QAAQ,IAAI,KAAK,SAAS,uCAAuC;;wCAIlE,QAAQ,IAAI,KAAK,UAAU,QAAQ,QAAQ,kBAC1C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDAAC,KAAK,QAAQ,QAAQ;gDAAE,KAAI;gDAAsB,QAAO;gDAAO,WAAU;;;;;;;;;;;sDAIpF,6LAAC;4CAAE,WAAU;sDAA+B,QAAQ,OAAO;;;;;;wCAG1D,QAAQ,IAAI,KAAK,eAAe,QAAQ,QAAQ,kBAC/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDAAC,KAAK,QAAQ,QAAQ;oDAAE,KAAI;oDAAkB,OAAO;oDAAK,QAAQ;oDAAK,WAAU;oDAA+B,aAAY;;;;;;8DAClI,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sIAAA,CAAA,UAAO;;8EACN,6LAAC,sIAAA,CAAA,iBAAc;oEAAC,OAAO;8EACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,YAAY,QAAQ,QAAQ;;0FAE3C,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,6LAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;8EAG9B,6LAAC,sIAAA,CAAA,iBAAc;8EAAC;;;;;;;;;;;;sEAElB,6LAAC,sIAAA,CAAA,UAAO;;8EACN,6LAAC,sIAAA,CAAA,iBAAc;oEAAC,OAAO;8EACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,oBAAoB,QAAQ,QAAQ,EAAE;;0FAErD,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;8EAG9B,6LAAC,sIAAA,CAAA,iBAAc;8EAAC;;;;;;;;;;;;sEAElB,6LAAC,sIAAA,CAAA,UAAO;;8EACN,6LAAC,sIAAA,CAAA,iBAAc;oEAAC,OAAO;8EACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,eAAe,QAAQ,QAAQ,EAAE;;0FAEhD,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;8EAG9B,6LAAC,sIAAA,CAAA,iBAAc;8EAAC;;;;;;;;;;;;;;;;;;;;;;;;wCAOvB,QAAQ,IAAI,KAAK,eAAe,QAAQ,QAAQ,kBAC/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,QAAQ;oDAAC,QAAQ;oDAAC,KAAK,QAAQ,QAAQ;oDAAE,WAAU;;;;;;8DAC1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,sIAAA,CAAA,UAAO;;8EACN,6LAAC,sIAAA,CAAA,iBAAc;oEAAC,OAAO;8EACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,oBAAoB,QAAQ,QAAQ,EAAE;;0FAErD,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;8EAG9B,6LAAC,sIAAA,CAAA,iBAAc;8EAAC;;;;;;;;;;;;sEAElB,6LAAC,sIAAA,CAAA,UAAO;;8EACN,6LAAC,sIAAA,CAAA,iBAAc;oEAAC,OAAO;8EACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,eAAe,QAAQ,QAAQ,EAAE;;0FAEhD,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;gFAAK,WAAU;0FAAU;;;;;;;;;;;;;;;;;8EAG9B,6LAAC,sIAAA,CAAA,iBAAc;8EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOzB,QAAQ,IAAI,KAAK,wBAAU,6LAAC,iJAAA,CAAA,aAAU;oCAAC,MAAK;;;;;;;2BA1GrC;;;;;oBA8GX,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,aAAU;gCAAC,MAAK;;;;;;0CACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GAtLgB;;QAEI,+HAAA,CAAA,WAAQ;;;KAFZ", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\r\nimport { Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn(\"grid gap-2\", className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  )\r\n})\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      ref={ref}\r\n      className={cn(\r\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  )\r\n})\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\r\n\r\nexport { RadioGroup, RadioGroupItem }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/revo-model-selector.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Check, ChevronDown, <PERSON><PERSON>les, Zap, Rocket } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\n\r\nexport type RevoModel = 'revo-1.0' | 'revo-1.5'\r\n\r\ninterface RevoModelOption {\r\n  id: RevoModel\r\n  name: string\r\n  description: string\r\n  icon: React.ComponentType<{ className?: string }>\r\n  badge?: string\r\n  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline'\r\n  features: string[]\r\n  status: 'stable' | 'enhanced' | 'development'\r\n}\r\n\r\nconst REVO_MODELS: RevoModelOption[] = [\r\n  {\r\n    id: 'revo-1.0',\r\n    name: 'Revo 1.0',\r\n    description: 'Standard Model - Stable Foundation',\r\n    icon: Z<PERSON>,\r\n    badge: 'Stable',\r\n    badgeVariant: 'secondary',\r\n    features: ['Reliable AI Engine', '1:1 Images', 'Core Features', 'Proven Performance'],\r\n    status: 'stable'\r\n  },\r\n  {\r\n    id: 'revo-1.5',\r\n    name: 'Revo 1.5',\r\n    description: 'Enhanced Model - Advanced Features',\r\n    icon: Sparkles,\r\n    badge: 'Enhanced',\r\n    badgeVariant: 'default',\r\n    features: ['Advanced AI Engine', 'Superior Quality', 'Enhanced Design', 'Smart Optimizations'],\r\n    status: 'enhanced'\r\n  },\r\n\r\n]\r\n\r\ninterface RevoModelSelectorProps {\r\n  selectedModel: RevoModel\r\n  onModelChange: (model: RevoModel) => void\r\n  disabled?: boolean\r\n  className?: string\r\n}\r\n\r\nexport function RevoModelSelector({\r\n  selectedModel,\r\n  onModelChange,\r\n  disabled = false,\r\n  className\r\n}: RevoModelSelectorProps) {\r\n  const [isOpen, setIsOpen] = React.useState(false)\r\n\r\n  const currentModel = REVO_MODELS.find(model => model.id === selectedModel) || REVO_MODELS[0]\r\n  const CurrentIcon = currentModel.icon\r\n\r\n  return (\r\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          disabled={disabled}\r\n          className={cn(\r\n            \"min-w-[180px] justify-between\",\r\n            className\r\n          )}\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <CurrentIcon className=\"h-4 w-4\" />\r\n            <span>{currentModel.name}</span>\r\n            {currentModel.badge && (\r\n              <Badge variant={currentModel.badgeVariant} className=\"text-xs\">\r\n                {currentModel.badge}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n\r\n      <DropdownMenuContent align=\"start\" className=\"w-80\">\r\n        <DropdownMenuLabel className=\"flex items-center gap-2\">\r\n          <Rocket className=\"w-4 h-4\" />\r\n          Select Revo Model\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n\r\n        {REVO_MODELS.map((model) => {\r\n          const IconComponent = model.icon\r\n          const isSelected = selectedModel === model.id\r\n          const isAvailable = model.status !== 'development'\r\n\r\n          return (\r\n            <DropdownMenuItem\r\n              key={model.id}\r\n              onClick={() => {\r\n                if (isAvailable) {\r\n                  onModelChange(model.id)\r\n                  setIsOpen(false)\r\n                }\r\n              }}\r\n              disabled={!isAvailable}\r\n              className={cn(\r\n                \"flex flex-col items-start gap-2 p-4 cursor-pointer\",\r\n                !isAvailable && \"opacity-50 cursor-not-allowed\"\r\n              )}\r\n            >\r\n              <div className=\"flex items-center justify-between w-full\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <IconComponent className=\"h-4 w-4\" />\r\n                  <span className=\"font-medium\">{model.name}</span>\r\n                  {model.badge && (\r\n                    <Badge variant={model.badgeVariant} className=\"text-xs\">\r\n                      {model.badge}\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n                {isSelected && <Check className=\"h-4 w-4 text-green-600\" />}\r\n              </div>\r\n\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                {model.description}\r\n              </p>\r\n\r\n              <div className=\"flex flex-wrap gap-1\">\r\n                {model.features.map((feature, index) => (\r\n                  <Badge key={index} variant=\"outline\" className=\"text-xs\">\r\n                    {feature}\r\n                  </Badge>\r\n                ))}\r\n              </div>\r\n            </DropdownMenuItem>\r\n          )\r\n        })}\r\n\r\n        <DropdownMenuSeparator />\r\n        <div className=\"p-2 text-xs text-muted-foreground\">\r\n          Each Revo model offers different capabilities and features\r\n        </div>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  )\r\n}\r\n\r\nexport { REVO_MODELS, type RevoModelOption }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAQA;;;AAdA;;;;;;;AA6BA,MAAM,cAAiC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,cAAc;QACd,UAAU;YAAC;YAAsB;YAAc;YAAiB;SAAqB;QACrF,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;QACP,cAAc;QACd,UAAU;YAAC;YAAsB;YAAoB;YAAmB;SAAsB;QAC9F,QAAQ;IACV;CAED;AASM,SAAS,kBAAkB,EAChC,aAAa,EACb,aAAa,EACb,WAAW,KAAK,EAChB,SAAS,EACc;;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE3C,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,kBAAkB,WAAW,CAAC,EAAE;IAC5F,MAAM,cAAc,aAAa,IAAI;IAErC,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,UAAU;oBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iCACA;;sCAGF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAY,WAAU;;;;;;8CACvB,6LAAC;8CAAM,aAAa,IAAI;;;;;;gCACvB,aAAa,KAAK,kBACjB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAS,aAAa,YAAY;oCAAE,WAAU;8CAClD,aAAa,KAAK;;;;;;;;;;;;sCAIzB,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI3B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAQ,WAAU;;kCAC3C,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGhC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;oBAErB,YAAY,GAAG,CAAC,CAAC;wBAChB,MAAM,gBAAgB,MAAM,IAAI;wBAChC,MAAM,aAAa,kBAAkB,MAAM,EAAE;wBAC7C,MAAM,cAAc,MAAM,MAAM,KAAK;wBAErC,qBACE,6LAAC,+IAAA,CAAA,mBAAgB;4BAEf,SAAS;gCACP,IAAI,aAAa;oCACf,cAAc,MAAM,EAAE;oCACtB,UAAU;gCACZ;4BACF;4BACA,UAAU,CAAC;4BACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,CAAC,eAAe;;8CAGlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAc,WAAU;;;;;;8DACzB,6LAAC;oDAAK,WAAU;8DAAe,MAAM,IAAI;;;;;;gDACxC,MAAM,KAAK,kBACV,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAS,MAAM,YAAY;oDAAE,WAAU;8DAC3C,MAAM,KAAK;;;;;;;;;;;;wCAIjB,4BAAc,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAGlC,6LAAC;oCAAE,WAAU;8CACV,MAAM,WAAW;;;;;;8CAGpB,6LAAC;oCAAI,WAAU;8CACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,6LAAC,oIAAA,CAAA,QAAK;4CAAa,SAAQ;4CAAU,WAAU;sDAC5C;2CADS;;;;;;;;;;;2BAhCX,MAAM,EAAE;;;;;oBAuCnB;kCAEA,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCACtB,6LAAC;wBAAI,WAAU;kCAAoC;;;;;;;;;;;;;;;;;;AAM3D;GAjGgB;KAAA", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/studio/chat-input.tsx"], "sourcesContent": ["// src/components/studio/chat-input.tsx\r\nimport * as React from 'react';\r\nimport { Paperclip, Send, Image as ImageIcon, Video, Wand, X, Brush } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';\r\nimport { RevoModelSelector, type RevoModel } from '@/components/ui/revo-model-selector';\r\nimport { getUserCredits } from '@/app/actions/pricing-actions';\r\nimport Image from \"next/image\";\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface ChatInputProps {\r\n  input: string;\r\n  setInput: (value: string) => void;\r\n  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;\r\n  isLoading: boolean;\r\n  imagePreview: string | null;\r\n  setImagePreview: (value: string | null) => void;\r\n  setImageDataUrl: (value: string | null) => void;\r\n  useBrandProfile: boolean;\r\n  setUseBrandProfile: (value: boolean) => void;\r\n  outputType: 'image' | 'video';\r\n  setOutputType: (value: 'image' | 'video') => void;\r\n  handleImageUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;\r\n  isBrandProfileAvailable: boolean;\r\n  onEditImage: (url: string) => void;\r\n  aspectRatio: '16:9' | '9:16';\r\n  setAspectRatio: (value: '16:9' | '9:16') => void;\r\n  selectedRevoModel: RevoModel;\r\n  setSelectedRevoModel: (value: RevoModel) => void;\r\n  userCredits?: number;\r\n}\r\n\r\nexport function ChatInput({\r\n  input,\r\n  setInput,\r\n  handleSubmit,\r\n  isLoading,\r\n  imagePreview,\r\n  setImagePreview,\r\n  setImageDataUrl,\r\n  useBrandProfile,\r\n  setUseBrandProfile,\r\n  outputType,\r\n  setOutputType,\r\n  handleImageUpload,\r\n  isBrandProfileAvailable,\r\n  onEditImage,\r\n  aspectRatio,\r\n  setAspectRatio,\r\n  selectedRevoModel,\r\n  setSelectedRevoModel,\r\n  userCredits,\r\n}: ChatInputProps) {\r\n  const inputRef = React.useRef<HTMLTextAreaElement>(null);\r\n  const fileInputRef = React.useRef<HTMLInputElement>(null);\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      const form = e.currentTarget.form;\r\n      if (form) {\r\n        form.requestSubmit();\r\n      }\r\n    }\r\n  };\r\n\r\n  React.useEffect(() => {\r\n    if (inputRef.current) {\r\n      inputRef.current.style.height = 'auto';\r\n      inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;\r\n    }\r\n  }, [input]);\r\n\r\n  const handleRemoveImage = () => {\r\n    setImagePreview(null);\r\n    setImageDataUrl(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = \"\";\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative mt-auto w-full border-t\">\r\n      <form onSubmit={handleSubmit} className=\"p-4 space-y-4\">\r\n        <div className=\"relative\">\r\n          {imagePreview && (\r\n            <div className=\"group absolute bottom-full mb-2 w-24 h-24\">\r\n              <Image src={imagePreview} alt=\"Image preview\" layout=\"fill\" objectFit=\"cover\" className=\"rounded-md\" />\r\n              <div className=\"absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-md\">\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        className=\"h-8 w-8 text-white hover:bg-white/20 hover:text-white\"\r\n                        onClick={() => onEditImage(imagePreview)}\r\n                      >\r\n                        <span className=\"sr-only\">Edit image</span>\r\n                        <Brush className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>Edit this image</TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              </div>\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"absolute -right-2 -top-2 h-6 w-6 rounded-full bg-background\"\r\n                onClick={handleRemoveImage}\r\n              >\r\n                <span className=\"sr-only\">Remove image</span>\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          )}\r\n          <Textarea\r\n            ref={inputRef}\r\n            value={input}\r\n            onChange={(e) => setInput(e.target.value)}\r\n            onKeyDown={handleKeyDown}\r\n            placeholder=\"Describe the image or video you want to create...\"\r\n            className=\"pr-20 resize-none min-h-[4rem] max-h-40\"\r\n            rows={1}\r\n            disabled={isLoading}\r\n          />\r\n          <div className=\"absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1\">\r\n            <TooltipProvider>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <Button type=\"button\" size=\"icon\" variant=\"ghost\" onClick={() => fileInputRef.current?.click()} disabled={isLoading}>\r\n                    <Paperclip />\r\n                    <span className=\"sr-only\">Attach image</span>\r\n                  </Button>\r\n                </TooltipTrigger>\r\n                <TooltipContent>Attach a reference image</TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider>\r\n            <input\r\n              type=\"file\"\r\n              ref={fileInputRef}\r\n              className=\"hidden\"\r\n              accept=\"image/*\"\r\n              onChange={handleImageUpload}\r\n            />\r\n            <Button type=\"submit\" size=\"icon\" variant=\"ghost\" disabled={isLoading || !input}>\r\n              <Send />\r\n              <span className=\"sr-only\">Send message</span>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Switch\r\n              id=\"brand-profile-switch\"\r\n              checked={useBrandProfile}\r\n              onCheckedChange={setUseBrandProfile}\r\n              disabled={!isBrandProfileAvailable}\r\n            />\r\n            <Label htmlFor=\"brand-profile-switch\">Apply Brand Profile</Label>\r\n            {!isBrandProfileAvailable && (\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <span className=\"text-xs text-muted-foreground\">(No profile found)</span>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>\r\n                    <p>Go to Brand Profile to set one up.</p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Label>AI Model:</Label>\r\n            <RevoModelSelector\r\n              selectedModel={selectedRevoModel}\r\n              onModelChange={setSelectedRevoModel}\r\n              disabled={!isBrandProfileAvailable || outputType !== 'image'}\r\n              showCredits={true}\r\n              userCredits={userCredits}\r\n            />\r\n          </div>\r\n\r\n          <Separator orientation=\"vertical\" className=\"h-6 hidden sm:block\" />\r\n\r\n          <div className=\"flex items-center space-x-4\">\r\n            <Label>Output Type:</Label>\r\n            <RadioGroup value={outputType} onValueChange={(v) => setOutputType(v as 'image' | 'video')} className=\"flex items-center space-x-4\" disabled={isLoading}>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"image\" id=\"r-image\" />\r\n                <Label htmlFor=\"r-image\" className=\"flex items-center gap-2\"><ImageIcon className=\"h-4 w-4\" /> Image</Label>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"video\" id=\"r-video\" />\r\n                <Label htmlFor=\"r-video\" className=\"flex items-center gap-2\"><Video className=\"h-4 w-4\" /> Video</Label>\r\n              </div>\r\n            </RadioGroup>\r\n          </div>\r\n\r\n          <div className={cn(\"flex items-center space-x-4\", outputType === 'video' ? 'opacity-100' : 'opacity-0')}>\r\n            <Label>Aspect Ratio:</Label>\r\n            <RadioGroup value={aspectRatio} onValueChange={(v) => setAspectRatio(v as '16:9' | '9:16')} className=\"flex items-center space-x-4\" disabled={isLoading || outputType !== 'video'}>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"16:9\" id=\"r-16-9\" />\r\n                <Label htmlFor=\"r-16-9\" className=\"flex items-center gap-2\">16:9 (Sound)</Label>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"9:16\" id=\"r-9-16\" />\r\n                <Label htmlFor=\"r-9-16\" className=\"flex items-center gap-2\">9:16 (No Sound)</Label>\r\n              </div>\r\n            </RadioGroup>\r\n          </div>\r\n\r\n          <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isLoading || !input}>\r\n            {isLoading ? 'Generating...' : <><Wand className=\"mr-2 h-4 w-4\" /> Generate</>}\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;AACvC;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;AAwBO,SAAS,UAAU,EACxB,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,eAAe,EACf,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,UAAU,EACV,aAAa,EACb,iBAAiB,EACjB,uBAAuB,EACvB,WAAW,EACX,WAAW,EACX,cAAc,EACd,iBAAiB,EACjB,oBAAoB,EACpB,WAAW,EACI;;IACf,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAuB;IACnD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAoB;IAEpD,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB,MAAM,OAAO,EAAE,aAAa,CAAC,IAAI;YACjC,IAAI,MAAM;gBACR,KAAK,aAAa;YACpB;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;+BAAE;YACd,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;gBAChC,SAAS,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACtE;QACF;8BAAG;QAAC;KAAM;IAEV,MAAM,oBAAoB;QACxB,gBAAgB;QAChB,gBAAgB;QAChB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,6LAAC;oBAAI,WAAU;;wBACZ,8BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,UAAK;oCAAC,KAAK;oCAAc,KAAI;oCAAgB,QAAO;oCAAO,WAAU;oCAAQ,WAAU;;;;;;8CACxF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,sIAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,YAAY;;0EAE3B,6LAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGrB,6LAAC,sIAAA,CAAA,iBAAc;8DAAC;;;;;;;;;;;;;;;;;;;;;;8CAItB,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAInB,6LAAC,uIAAA,CAAA,WAAQ;4BACP,KAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAW;4BACX,aAAY;4BACZ,WAAU;4BACV,MAAM;4BACN,UAAU;;;;;;sCAEZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sIAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;0DACN,6LAAC,sIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAS,MAAK;oDAAO,SAAQ;oDAAQ,SAAS,IAAM,aAAa,OAAO,EAAE;oDAAS,UAAU;;sEACxG,6LAAC,+MAAA,CAAA,YAAS;;;;;sEACV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;0DAG9B,6LAAC,sIAAA,CAAA,iBAAc;0DAAC;;;;;;;;;;;;;;;;;8CAGpB,6LAAC;oCACC,MAAK;oCACL,KAAK;oCACL,WAAU;oCACV,QAAO;oCACP,UAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,MAAK;oCAAO,SAAQ;oCAAQ,UAAU,aAAa,CAAC;;sDACxE,6LAAC,qMAAA,CAAA,OAAI;;;;;sDACL,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;8BAKhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,IAAG;oCACH,SAAS;oCACT,iBAAiB;oCACjB,UAAU,CAAC;;;;;;8CAEb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAuB;;;;;;gCACrC,CAAC,yCACA,6LAAC,sIAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;0DACN,6LAAC,sIAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;0DAElD,6LAAC,sIAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC,wJAAA,CAAA,oBAAiB;oCAChB,eAAe;oCACf,eAAe;oCACf,UAAU,CAAC,2BAA2B,eAAe;oCACrD,aAAa;oCACb,aAAa;;;;;;;;;;;;sCAIjB,6LAAC,wIAAA,CAAA,YAAS;4BAAC,aAAY;4BAAW,WAAU;;;;;;sCAE5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC,6IAAA,CAAA,aAAU;oCAAC,OAAO;oCAAY,eAAe,CAAC,IAAM,cAAc;oCAAyB,WAAU;oCAA8B,UAAU;;sDAC5I,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAQ,IAAG;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;sEAA0B,6LAAC,uMAAA,CAAA,QAAS;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;sDAEhG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAQ,IAAG;;;;;;8DACjC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;sEAA0B,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;sCAKhG,6LAAC;4BAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B,eAAe,UAAU,gBAAgB;;8CACzF,6LAAC,oIAAA,CAAA,QAAK;8CAAC;;;;;;8CACP,6LAAC,6IAAA,CAAA,aAAU;oCAAC,OAAO;oCAAa,eAAe,CAAC,IAAM,eAAe;oCAAuB,WAAU;oCAA8B,UAAU,aAAa,eAAe;;sDACxK,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAO,IAAG;;;;;;8DAChC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;8DAA0B;;;;;;;;;;;;sDAE9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAO,IAAG;;;;;;8DAChC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;sCAKlE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,WAAU;4BAAmB,UAAU,aAAa,CAAC;sCACxE,YAAY,gCAAkB;;kDAAE,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAM9E;GAlMgB;KAAA", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { generatePostFromProfile as generatePostFromProfileFlow } from \"@/ai/flows/generate-post-from-profile\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\n// import { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\"; // Temporarily disabled\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\nimport { DesignGenerationService } from \"@/ai/models/services/design-generation-service\";\r\nimport type { RevoModelId } from \"@/ai/models/types/model-types\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\r\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\r\n    console.log(\"🔍 Result type:\", typeof result);\r\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n    console.error(\"❌ Error analyzing brand:\", error);\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    const postDetails = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: effectiveDesignExamples, // Use design examples based on consistency preference\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek,\r\n      currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      // Pass new detailed fields\r\n      services: servicesString,\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      // Pass brand consistency preferences\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline,\r\n      callToAction: postDetails.callToAction,\r\n      // Include enhanced AI features\r\n      contentVariants: postDetails.contentVariants,\r\n      hashtagAnalysis: postDetails.hashtagAnalysis,\r\n      // Include advanced AI features\r\n      marketIntelligence: postDetails.marketIntelligence,\r\n      // Include local context features\r\n      localContext: postDetails.localContext,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    console.error(\"Error generating content:\", error);\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    console.error(\"Error generating video content:\", error);\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Error generating creative asset:\", error);\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n    console.log('🎨 Enhanced Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', finalImageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n    console.log('- Enhancements Enabled:', enableEnhancements);\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n      });\r\n\r\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\r\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\r\n\r\n    } catch (gemini25Error) {\r\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\r\n\r\n      try {\r\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\r\n      } catch (openaiError) {\r\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\r\n\r\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\r\n      }\r\n    }\r\n\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating enhanced design:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n    console.log('🎨 Gemini HD Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', imageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n\r\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n    console.log('✅ Gemini HD enhanced design generated successfully');\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error in Gemini HD design generation:', error);\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log('🎨 Generating content with artifacts...');\r\n    console.log('- Platform:', platform);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n    console.log('- Enhanced Design:', useEnhancedDesign);\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\r\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\r\n        id: a.id,\r\n        name: a.name,\r\n        type: a.type,\r\n        usageType: a.usageType,\r\n        isActive: a.isActive,\r\n        instructions: a.instructions\r\n      })));\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      console.log('🔄 Enhanced design disabled, using base content generation');\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\r\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\r\n\r\n    if (targetArtifacts.length === 0) {\r\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\r\n    } else {\r\n      console.log('🎯 Using enhanced design with artifact context');\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n          console.log('📝 Using message from exact-use artifact');\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      console.log('🎨 Applying style references from artifacts');\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n        console.log('🎨 Using artifact-inspired visual style');\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    console.log('✅ Enhanced content with artifacts generated successfully');\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating content with artifacts:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content using the new Revo model system\r\n * This action uses the proper model architecture with version-specific implementations\r\n */\r\nexport async function generateContentWithRevoModelAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  revoModel: RevoModelId,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = []\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log(`🎨 Generating content with ${revoModel} model...`);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Business Type:', profile.businessType);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n\r\n    // Handle artifacts if provided\r\n    let targetArtifacts: Artifact[] = [];\r\n    if (artifactIds.length > 0) {\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    // Prepare artifact instructions\r\n    let artifactInstructions = '';\r\n    if (targetArtifacts.length > 0) {\r\n      const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      if (exactUseArtifacts.length > 0) {\r\n        artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\\n';\r\n        exactUseArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\\n`;\r\n        });\r\n      }\r\n\r\n      if (referenceArtifacts.length > 0) {\r\n        artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\\n';\r\n        referenceArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\\n`;\r\n        });\r\n      }\r\n    }\r\n\r\n    // Use simplified Revo model generation with text validation\r\n    console.log(`🎨 Using Revo ${revoModel} with text validation...`);\r\n    console.log('🔧 DEBUG: This is the SIMPLIFIED code path');\r\n\r\n    // Generate dynamic and varied text for each design\r\n    const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);\r\n    let imageText = textVariations.selectedText;\r\n\r\n    if (revoModel === 'revo-1.0') {\r\n      console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');\r\n      console.log('🔍 Original text:', imageText);\r\n\r\n      // Simple text validation for Revo 1.0\r\n      const words = imageText.split(' ').filter(word => word.length > 0);\r\n      if (words.length > 25) {\r\n        console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);\r\n        imageText = words.slice(0, 25).join(' ');\r\n      }\r\n      console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);\r\n    }\r\n\r\n    // Use sophisticated design prompt created by 20-year veteran designer + marketer\r\n    const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);\r\n\r\n    const designResult = await generateCreativeAssetFlow({\r\n      prompt: designPrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: profile,\r\n      maskDataUrl: null\r\n    });\r\n\r\n    if (!designResult.imageUrl) {\r\n      throw new Error('Design generation failed: No image URL returned');\r\n    }\r\n\r\n    // Generate content using the standard flow for caption and hashtags\r\n    const contentResult = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      services: Array.isArray(profile.services)\r\n        ? profile.services.map(s => typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\\n')\r\n        : profile.services || '',\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\\n') : profile.keyFeatures || '',\r\n      competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\\n') : profile.competitiveAdvantages || '',\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    // Combine the design result with content result\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: new Date().toISOString(),\r\n      content: contentResult.content,\r\n      hashtags: contentResult.hashtags,\r\n      status: 'generated',\r\n      variants: [{\r\n        platform,\r\n        imageUrl: designResult.imageUrl || '',\r\n        caption: contentResult.content,\r\n        hashtags: contentResult.hashtags\r\n      }],\r\n      catchyWords: contentResult.catchyWords,\r\n      subheadline: contentResult.subheadline,\r\n      callToAction: contentResult.callToAction,\r\n      contentVariants: contentResult.contentVariants,\r\n      hashtagAnalysis: contentResult.hashtagAnalysis,\r\n      marketIntelligence: contentResult.marketIntelligence,\r\n      localContext: contentResult.localContext,\r\n      // Add Revo model metadata\r\n      revoModelUsed: revoModel,\r\n      qualityScore: 8, // Default quality score for Revo models\r\n      processingTime: Date.now() - Date.now(), // Will be calculated properly\r\n      creditsUsed: 1,\r\n      enhancementsApplied: [`Revo ${revoModel} Generation`, 'Text Validation', 'Professional Design']\r\n    };\r\n\r\n    console.log(`✅ Content generated successfully with ${revoModel}`);\r\n    console.log(`⭐ Quality Score: 8/10`);\r\n    console.log(`⚡ Processing Time: Fast generation`);\r\n    console.log(`💰 Credits Used: 1`);\r\n\r\n    return newPost;\r\n\r\n  } catch (error) {\r\n    console.error(`❌ Error generating content with ${revoModel}:`, error);\r\n    throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate professional marketing-driven text with cultural awareness\r\n * Designed by a 20-year veteran designer + 20-year marketing expert\r\n * Now deeply connected to actual brand profile information\r\n */\r\nfunction generateDynamicTextForRevo(profile: BrandProfile, revoModel: RevoModelId, platform: Platform) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n\r\n  // Generate sophisticated marketing copy using actual brand profile data\r\n  const marketingCopy = generateMarketingCopy(profile, platform);\r\n\r\n  console.log(`🎯 Generated personalized marketing copy for ${businessName}: \"${marketingCopy}\"`);\r\n\r\n  return {\r\n    selectedText: marketingCopy,\r\n    allVariations: [marketingCopy],\r\n    variationIndex: 0\r\n  };\r\n}\r\n\r\n/**\r\n * Generate sophisticated marketing copy that sells\r\n * Combines 20 years of design + marketing expertise with actual brand profile data\r\n * Now deeply personalized using real business information\r\n */\r\nfunction generateMarketingCopy(profile: BrandProfile, platform: Platform): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract real business intelligence from profile\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Cultural and regional insights\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Generate catchy headline using actual business strengths (max 5 words)\r\n  const catchyHeadlines = [\r\n    `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,\r\n    `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,\r\n    `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,\r\n    `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,\r\n    `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`\r\n  ];\r\n\r\n  // Generate subheadline using real competitive advantages (max 14 words)\r\n  const subheadlines = [\r\n    `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,\r\n    `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,\r\n    `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,\r\n    `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,\r\n    `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`\r\n  ];\r\n\r\n  // Generate call-to-action using actual business context\r\n  const callToActions = [\r\n    `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,\r\n    `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,\r\n    `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,\r\n    `${getRandomElement(['Book', 'Schedule', 'Request'])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,\r\n    `${getRandomElement(['Start', 'Begin', 'Launch'])} Your ${businessIntelligence.journeyType} Today`\r\n  ];\r\n\r\n  // Randomly select components\r\n  const catchyWords = getRandomElement(catchyHeadlines);\r\n  const subheadline = getRandomElement(subheadlines);\r\n  const cta = getRandomElement(callToActions);\r\n\r\n  // Combine based on marketing best practices and business context\r\n  const marketingFormats = [\r\n    `${catchyWords}\\n${subheadline}\\n${cta}`,\r\n    `${catchyWords}\\n${subheadline}`,\r\n    `${catchyWords}\\n${cta}`,\r\n    `${subheadline}\\n${cta}`,\r\n    catchyWords\r\n  ];\r\n\r\n  return getRandomElement(marketingFormats);\r\n}\r\n\r\n/**\r\n * Get cultural context and local market insights\r\n */\r\nfunction getCulturalContext(location: string) {\r\n  // Default context\r\n  let context = {\r\n    localTerms: ['Premium', 'Professional', 'Expert'],\r\n    marketingStyle: 'Professional',\r\n    targetAudience: 'businesses',\r\n    localMarket: 'modern',\r\n    socialProof: 'thousands of clients',\r\n    valueProposition: 'Industry-leading',\r\n    competitiveAdvantage: 'proven expertise',\r\n    urgencyTrigger: 'Don\\'t miss out on',\r\n    actionWords: 'Connect with',\r\n    localCTA: 'Get Started',\r\n    ctaUrgency: 'Now'\r\n  };\r\n\r\n  // Location-specific cultural adaptations\r\n  if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {\r\n    context = {\r\n      localTerms: ['Premium', 'Luxury', 'Elite', 'Exclusive'],\r\n      marketingStyle: 'Luxury-focused',\r\n      targetAudience: 'discerning clients',\r\n      localMarket: 'Dubai\\'s dynamic',\r\n      socialProof: 'leading UAE businesses',\r\n      valueProposition: 'World-class',\r\n      competitiveAdvantage: 'international excellence',\r\n      urgencyTrigger: 'Seize the opportunity for',\r\n      actionWords: 'Experience',\r\n      localCTA: 'Book Your Exclusive Consultation',\r\n      ctaUrgency: 'Today'\r\n    };\r\n  } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {\r\n    context = {\r\n      localTerms: ['Bespoke', 'Tailored', 'Refined'],\r\n      marketingStyle: 'Sophisticated',\r\n      targetAudience: 'discerning professionals',\r\n      localMarket: 'London\\'s competitive',\r\n      socialProof: 'established UK enterprises',\r\n      valueProposition: 'Expertly crafted',\r\n      competitiveAdvantage: 'British excellence',\r\n      urgencyTrigger: 'Secure your',\r\n      actionWords: 'Discover',\r\n      localCTA: 'Arrange Your Consultation',\r\n      ctaUrgency: 'Promptly'\r\n    };\r\n  } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {\r\n    context = {\r\n      localTerms: ['Cutting-edge', 'Innovative', 'Game-changing'],\r\n      marketingStyle: 'Bold and direct',\r\n      targetAudience: 'ambitious professionals',\r\n      localMarket: 'NYC\\'s fast-paced',\r\n      socialProof: 'successful New York businesses',\r\n      valueProposition: 'Results-driven',\r\n      competitiveAdvantage: 'New York hustle',\r\n      urgencyTrigger: 'Don\\'t let competitors get',\r\n      actionWords: 'Dominate with',\r\n      localCTA: 'Schedule Your Strategy Session',\r\n      ctaUrgency: 'ASAP'\r\n    };\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n/**\r\n * Extract business intelligence from brand profile for personalized marketing\r\n * Analyzes actual business data to create relevant marketing copy\r\n */\r\nfunction extractBusinessIntelligence(profile: BrandProfile) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n\r\n  // Extract primary service information\r\n  const primaryService = services[0]?.name || services[0] || businessType;\r\n  const serviceDescription = services[0]?.description || '';\r\n  const targetAudience = services[0]?.targetAudience || 'businesses';\r\n  const keyFeatures = services[0]?.keyFeatures || '';\r\n  const competitiveAdvantages = services[0]?.competitiveAdvantages || '';\r\n\r\n  // Analyze description for key terms\r\n  const descriptionWords = description.toLowerCase().split(/\\s+/);\r\n  const strengthWords = extractStrengthWords(description, businessType);\r\n  const valueWords = extractValueWords(description, keyFeatures);\r\n  const benefitWords = extractBenefitWords(description, competitiveAdvantages);\r\n\r\n  // Extract competitive advantages\r\n  const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);\r\n  const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);\r\n  const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);\r\n\r\n  // Extract target audience specifics\r\n  const actualTargetAudience = extractTargetAudience(targetAudience, businessType);\r\n\r\n  // Generate contextual elements\r\n  const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);\r\n  const mainValue = extractMainValue(description, keyFeatures);\r\n  const offerType = generateOfferType(businessType, services);\r\n  const consultationType = generateConsultationType(businessType);\r\n  const journeyType = generateJourneyType(businessType, primaryService);\r\n  const urgencyTrigger = generateUrgencyTrigger(businessType, location);\r\n\r\n  // Extract outcome words from business context\r\n  const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);\r\n  const differentiators = extractDifferentiators(competitiveAdvantages, businessType);\r\n\r\n  return {\r\n    primaryService,\r\n    strengthWords,\r\n    valueWords,\r\n    benefitWords,\r\n    realCompetitiveAdvantage,\r\n    uniqueFeature,\r\n    realDifferentiator,\r\n    actualTargetAudience,\r\n    keyBenefit,\r\n    mainValue,\r\n    offerType,\r\n    consultationType,\r\n    journeyType,\r\n    urgencyTrigger,\r\n    outcomeWords,\r\n    differentiators\r\n  };\r\n}\r\n\r\n/**\r\n * Get random element from array\r\n */\r\nfunction getRandomElement<T>(array: T[]): T {\r\n  return array[Math.floor(Math.random() * array.length)];\r\n}\r\n\r\n/**\r\n * Create professional design prompt with 20 years of design + marketing expertise\r\n * Combines cultural awareness, psychology, and visual design mastery\r\n */\r\nfunction createProfessionalDesignPrompt(imageText: string, platform: Platform, profile: BrandProfile, revoModel: RevoModelId): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const primaryService = services[0]?.name || services[0] || 'Quality Services';\r\n\r\n  // Get cultural context for design decisions\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Industry-specific design psychology\r\n  const industryDesignPsychology = getIndustryDesignPsychology(businessType);\r\n\r\n  // Platform-specific design requirements\r\n  const platformRequirements = getPlatformDesignRequirements(platform);\r\n\r\n  // Color psychology based on business type and culture\r\n  const colorPsychology = getColorPsychology(businessType, location);\r\n\r\n  // Typography psychology for conversion\r\n  const typographyStrategy = getTypographyStrategy(businessType, platform);\r\n\r\n  return `Create an exceptional, conversion-focused ${platform} design that embodies 20 years of professional design and marketing expertise.\r\n\r\nBUSINESS CONTEXT:\r\n- Company: ${businessName}\r\n- Industry: ${businessType}\r\n- Primary Service: ${primaryService}\r\n- Location: ${location}\r\n- Cultural Context: ${culturalContext.marketingStyle}\r\n\r\nTEXT TO INTEGRATE: \"${imageText}\"\r\n\r\nDESIGN PSYCHOLOGY & STRATEGY:\r\n${industryDesignPsychology}\r\n\r\nVISUAL HIERARCHY & COMPOSITION:\r\n- Apply the golden ratio and rule of thirds for optimal visual flow\r\n- Create clear focal points that guide the eye to key conversion elements\r\n- Use strategic white space to enhance readability and premium feel\r\n- Implement Z-pattern or F-pattern layout for maximum engagement\r\n\r\nCOLOR STRATEGY:\r\n${colorPsychology}\r\n\r\nTYPOGRAPHY MASTERY:\r\n${typographyStrategy}\r\n\r\nCULTURAL DESIGN ADAPTATION:\r\n- ${culturalContext.localMarket} aesthetic preferences\r\n- ${culturalContext.targetAudience} visual expectations\r\n- Regional design trends and cultural symbols\r\n- Local color associations and meanings\r\n\r\nCONVERSION OPTIMIZATION:\r\n- Design elements that create urgency and desire\r\n- Visual cues that guide toward call-to-action\r\n- Trust signals through professional presentation\r\n- Emotional triggers through strategic imagery and layout\r\n\r\nPLATFORM OPTIMIZATION:\r\n${platformRequirements}\r\n\r\nTECHNICAL EXCELLENCE:\r\n- Aspect Ratio: 1:1 (perfect square)\r\n- Resolution: Ultra-high quality, print-ready standards\r\n- Text Clarity: Crystal clear, perfectly readable at all sizes\r\n- Brand Consistency: Align with professional brand standards\r\n- Mobile Optimization: Ensure perfect display on all devices\r\n\r\nFINAL QUALITY STANDARDS:\r\nThis design must look like it was created by a top-tier creative agency for a Fortune 500 company. Every element should be intentional, every color choice strategic, every font decision conversion-focused. The result should be a design that not only looks stunning but actually drives business results and sales.\r\n\r\nMake it absolutely irresistible and professionally compelling.`;\r\n}\r\n\r\n/**\r\n * Get industry-specific design psychology\r\n */\r\nfunction getIndustryDesignPsychology(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {\r\n    return `- Use warm, appetizing colors that stimulate hunger and comfort\r\n- Incorporate food photography principles with rich textures\r\n- Create cozy, inviting atmosphere through design elements\r\n- Focus on sensory appeal and mouth-watering visual presentation`;\r\n  }\r\n\r\n  if (type.includes('tech') || type.includes('software') || type.includes('digital')) {\r\n    return `- Employ clean, minimalist design with high-tech aesthetics\r\n- Use gradients and modern geometric shapes\r\n- Incorporate subtle tech-inspired elements and icons\r\n- Focus on innovation, efficiency, and cutting-edge appeal`;\r\n  }\r\n\r\n  if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {\r\n    return `- Use calming, trustworthy colors that convey safety and care\r\n- Incorporate clean, sterile design elements\r\n- Focus on professionalism, expertise, and patient comfort\r\n- Use imagery that suggests health, vitality, and well-being`;\r\n  }\r\n\r\n  if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {\r\n    return `- Employ sophisticated, conservative design elements\r\n- Use colors that convey stability, trust, and prosperity\r\n- Incorporate subtle luxury elements and professional imagery\r\n- Focus on security, growth, and financial success`;\r\n  }\r\n\r\n  if (type.includes('real estate') || type.includes('property')) {\r\n    return `- Use aspirational imagery and luxury design elements\r\n- Incorporate architectural lines and premium materials\r\n- Focus on lifestyle, investment, and dream fulfillment\r\n- Use colors that suggest stability, growth, and success`;\r\n  }\r\n\r\n  // Default professional services\r\n  return `- Use professional, trustworthy design elements\r\n- Incorporate subtle premium touches and quality indicators\r\n- Focus on expertise, reliability, and professional excellence\r\n- Use colors and imagery that convey competence and success`;\r\n}\r\n\r\n/**\r\n * Get platform-specific design requirements\r\n */\r\nfunction getPlatformDesignRequirements(platform: Platform): string {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return `- Optimize for Instagram's visual-first environment\r\n- Use bold, eye-catching elements that stand out in feeds\r\n- Incorporate Instagram-native design trends and aesthetics\r\n- Ensure design works perfectly in both feed and story formats`;\r\n\r\n    case 'Facebook':\r\n      return `- Design for Facebook's diverse, multi-generational audience\r\n- Use clear, readable elements that work across age groups\r\n- Incorporate social proof and community-focused elements\r\n- Ensure design is engaging but not overwhelming`;\r\n\r\n    case 'LinkedIn':\r\n      return `- Employ professional, business-focused design elements\r\n- Use conservative colors and sophisticated typography\r\n- Incorporate industry-specific imagery and professional symbols\r\n- Focus on credibility, expertise, and business value`;\r\n\r\n    case 'Twitter':\r\n      return `- Create concise, impactful design that communicates quickly\r\n- Use bold typography and clear visual hierarchy\r\n- Incorporate trending design elements and current aesthetics\r\n- Ensure design is optimized for rapid consumption`;\r\n\r\n    default:\r\n      return `- Create versatile design that works across multiple platforms\r\n- Use universal design principles and broad appeal\r\n- Ensure scalability and readability across different contexts\r\n- Focus on timeless, professional aesthetics`;\r\n  }\r\n}\r\n\r\n/**\r\n * Get color psychology based on business type and location\r\n */\r\nfunction getColorPsychology(businessType: string, location: string): string {\r\n  const type = businessType.toLowerCase();\r\n  const loc = location.toLowerCase();\r\n\r\n  let baseColors = '';\r\n  let culturalColors = '';\r\n\r\n  // Business type color psychology\r\n  if (type.includes('restaurant') || type.includes('food')) {\r\n    baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    baseColors = 'modern blues, teals, and purples to convey innovation and trust';\r\n  } else if (type.includes('health') || type.includes('medical')) {\r\n    baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';\r\n  } else if (type.includes('finance') || type.includes('banking')) {\r\n    baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';\r\n  } else {\r\n    baseColors = 'professional blues, grays, and accent colors to convey trust and competence';\r\n  }\r\n\r\n  // Cultural color adaptations\r\n  if (loc.includes('dubai') || loc.includes('uae')) {\r\n    culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\\'s premium market expectations';\r\n  } else if (loc.includes('london') || loc.includes('uk')) {\r\n    culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';\r\n  } else if (loc.includes('new york') || loc.includes('nyc')) {\r\n    culturalColors = 'Employ bold, confident colors that match New York\\'s dynamic business environment';\r\n  } else {\r\n    culturalColors = 'Use universally appealing professional color combinations';\r\n  }\r\n\r\n  return `- Primary Strategy: ${baseColors}\r\n- Cultural Adaptation: ${culturalColors}\r\n- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors\r\n- Contrast Optimization: Ensure maximum readability and visual impact`;\r\n}\r\n\r\n/**\r\n * Get typography strategy for conversion\r\n */\r\nfunction getTypographyStrategy(businessType: string, platform: Platform): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  let fontStrategy = '';\r\n  let hierarchyStrategy = '';\r\n\r\n  if (type.includes('luxury') || type.includes('premium')) {\r\n    fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';\r\n  } else if (type.includes('creative') || type.includes('design')) {\r\n    fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';\r\n  } else {\r\n    fontStrategy = 'Professional, highly readable fonts that convey trust and competence';\r\n  }\r\n\r\n  hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact\r\n- Secondary Text: Clear, readable subheadings that support the main message\r\n- Call-to-Action: Distinctive typography that stands out and drives action\r\n- Supporting Text: Clean, professional fonts for additional information`;\r\n\r\n  return `- Font Selection: ${fontStrategy}\r\n- Visual Hierarchy: ${hierarchyStrategy}\r\n- Readability: Optimized for ${platform} viewing conditions and mobile devices\r\n- Conversion Focus: Typography choices designed to guide the eye and encourage action`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqOsB,8BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { generatePostFromProfile as generatePostFromProfileFlow } from \"@/ai/flows/generate-post-from-profile\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\n// import { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\"; // Temporarily disabled\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\nimport { DesignGenerationService } from \"@/ai/models/services/design-generation-service\";\r\nimport type { RevoModelId } from \"@/ai/models/types/model-types\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\r\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\r\n    console.log(\"🔍 Result type:\", typeof result);\r\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n    console.error(\"❌ Error analyzing brand:\", error);\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    const postDetails = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: effectiveDesignExamples, // Use design examples based on consistency preference\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek,\r\n      currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      // Pass new detailed fields\r\n      services: servicesString,\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      // Pass brand consistency preferences\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline,\r\n      callToAction: postDetails.callToAction,\r\n      // Include enhanced AI features\r\n      contentVariants: postDetails.contentVariants,\r\n      hashtagAnalysis: postDetails.hashtagAnalysis,\r\n      // Include advanced AI features\r\n      marketIntelligence: postDetails.marketIntelligence,\r\n      // Include local context features\r\n      localContext: postDetails.localContext,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    console.error(\"Error generating content:\", error);\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    console.error(\"Error generating video content:\", error);\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Error generating creative asset:\", error);\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n    console.log('🎨 Enhanced Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', finalImageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n    console.log('- Enhancements Enabled:', enableEnhancements);\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n      });\r\n\r\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\r\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\r\n\r\n    } catch (gemini25Error) {\r\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\r\n\r\n      try {\r\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\r\n      } catch (openaiError) {\r\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\r\n\r\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\r\n      }\r\n    }\r\n\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating enhanced design:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n    console.log('🎨 Gemini HD Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', imageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n\r\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n    console.log('✅ Gemini HD enhanced design generated successfully');\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error in Gemini HD design generation:', error);\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log('🎨 Generating content with artifacts...');\r\n    console.log('- Platform:', platform);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n    console.log('- Enhanced Design:', useEnhancedDesign);\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\r\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\r\n        id: a.id,\r\n        name: a.name,\r\n        type: a.type,\r\n        usageType: a.usageType,\r\n        isActive: a.isActive,\r\n        instructions: a.instructions\r\n      })));\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      console.log('🔄 Enhanced design disabled, using base content generation');\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\r\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\r\n\r\n    if (targetArtifacts.length === 0) {\r\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\r\n    } else {\r\n      console.log('🎯 Using enhanced design with artifact context');\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n          console.log('📝 Using message from exact-use artifact');\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      console.log('🎨 Applying style references from artifacts');\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n        console.log('🎨 Using artifact-inspired visual style');\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    console.log('✅ Enhanced content with artifacts generated successfully');\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating content with artifacts:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content using the new Revo model system\r\n * This action uses the proper model architecture with version-specific implementations\r\n */\r\nexport async function generateContentWithRevoModelAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  revoModel: RevoModelId,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = []\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log(`🎨 Generating content with ${revoModel} model...`);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Business Type:', profile.businessType);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n\r\n    // Handle artifacts if provided\r\n    let targetArtifacts: Artifact[] = [];\r\n    if (artifactIds.length > 0) {\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    // Prepare artifact instructions\r\n    let artifactInstructions = '';\r\n    if (targetArtifacts.length > 0) {\r\n      const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      if (exactUseArtifacts.length > 0) {\r\n        artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\\n';\r\n        exactUseArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\\n`;\r\n        });\r\n      }\r\n\r\n      if (referenceArtifacts.length > 0) {\r\n        artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\\n';\r\n        referenceArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\\n`;\r\n        });\r\n      }\r\n    }\r\n\r\n    // Use simplified Revo model generation with text validation\r\n    console.log(`🎨 Using Revo ${revoModel} with text validation...`);\r\n    console.log('🔧 DEBUG: This is the SIMPLIFIED code path');\r\n\r\n    // Generate dynamic and varied text for each design\r\n    const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);\r\n    let imageText = textVariations.selectedText;\r\n\r\n    if (revoModel === 'revo-1.0') {\r\n      console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');\r\n      console.log('🔍 Original text:', imageText);\r\n\r\n      // Simple text validation for Revo 1.0\r\n      const words = imageText.split(' ').filter(word => word.length > 0);\r\n      if (words.length > 25) {\r\n        console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);\r\n        imageText = words.slice(0, 25).join(' ');\r\n      }\r\n      console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);\r\n    }\r\n\r\n    // Use sophisticated design prompt created by 20-year veteran designer + marketer\r\n    const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);\r\n\r\n    const designResult = await generateCreativeAssetFlow({\r\n      prompt: designPrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: profile,\r\n      maskDataUrl: null\r\n    });\r\n\r\n    if (!designResult.imageUrl) {\r\n      throw new Error('Design generation failed: No image URL returned');\r\n    }\r\n\r\n    // Generate content using the standard flow for caption and hashtags\r\n    const contentResult = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      services: Array.isArray(profile.services)\r\n        ? profile.services.map(s => typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\\n')\r\n        : profile.services || '',\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\\n') : profile.keyFeatures || '',\r\n      competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\\n') : profile.competitiveAdvantages || '',\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    // Combine the design result with content result\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: new Date().toISOString(),\r\n      content: contentResult.content,\r\n      hashtags: contentResult.hashtags,\r\n      status: 'generated',\r\n      variants: [{\r\n        platform,\r\n        imageUrl: designResult.imageUrl || '',\r\n        caption: contentResult.content,\r\n        hashtags: contentResult.hashtags\r\n      }],\r\n      catchyWords: contentResult.catchyWords,\r\n      subheadline: contentResult.subheadline,\r\n      callToAction: contentResult.callToAction,\r\n      contentVariants: contentResult.contentVariants,\r\n      hashtagAnalysis: contentResult.hashtagAnalysis,\r\n      marketIntelligence: contentResult.marketIntelligence,\r\n      localContext: contentResult.localContext,\r\n      // Add Revo model metadata\r\n      revoModelUsed: revoModel,\r\n      qualityScore: 8, // Default quality score for Revo models\r\n      processingTime: Date.now() - Date.now(), // Will be calculated properly\r\n      creditsUsed: 1,\r\n      enhancementsApplied: [`Revo ${revoModel} Generation`, 'Text Validation', 'Professional Design']\r\n    };\r\n\r\n    console.log(`✅ Content generated successfully with ${revoModel}`);\r\n    console.log(`⭐ Quality Score: 8/10`);\r\n    console.log(`⚡ Processing Time: Fast generation`);\r\n    console.log(`💰 Credits Used: 1`);\r\n\r\n    return newPost;\r\n\r\n  } catch (error) {\r\n    console.error(`❌ Error generating content with ${revoModel}:`, error);\r\n    throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate professional marketing-driven text with cultural awareness\r\n * Designed by a 20-year veteran designer + 20-year marketing expert\r\n * Now deeply connected to actual brand profile information\r\n */\r\nfunction generateDynamicTextForRevo(profile: BrandProfile, revoModel: RevoModelId, platform: Platform) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n\r\n  // Generate sophisticated marketing copy using actual brand profile data\r\n  const marketingCopy = generateMarketingCopy(profile, platform);\r\n\r\n  console.log(`🎯 Generated personalized marketing copy for ${businessName}: \"${marketingCopy}\"`);\r\n\r\n  return {\r\n    selectedText: marketingCopy,\r\n    allVariations: [marketingCopy],\r\n    variationIndex: 0\r\n  };\r\n}\r\n\r\n/**\r\n * Generate sophisticated marketing copy that sells\r\n * Combines 20 years of design + marketing expertise with actual brand profile data\r\n * Now deeply personalized using real business information\r\n */\r\nfunction generateMarketingCopy(profile: BrandProfile, platform: Platform): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract real business intelligence from profile\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Cultural and regional insights\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Generate catchy headline using actual business strengths (max 5 words)\r\n  const catchyHeadlines = [\r\n    `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,\r\n    `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,\r\n    `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,\r\n    `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,\r\n    `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`\r\n  ];\r\n\r\n  // Generate subheadline using real competitive advantages (max 14 words)\r\n  const subheadlines = [\r\n    `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,\r\n    `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,\r\n    `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,\r\n    `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,\r\n    `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`\r\n  ];\r\n\r\n  // Generate call-to-action using actual business context\r\n  const callToActions = [\r\n    `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,\r\n    `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,\r\n    `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,\r\n    `${getRandomElement(['Book', 'Schedule', 'Request'])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,\r\n    `${getRandomElement(['Start', 'Begin', 'Launch'])} Your ${businessIntelligence.journeyType} Today`\r\n  ];\r\n\r\n  // Randomly select components\r\n  const catchyWords = getRandomElement(catchyHeadlines);\r\n  const subheadline = getRandomElement(subheadlines);\r\n  const cta = getRandomElement(callToActions);\r\n\r\n  // Combine based on marketing best practices and business context\r\n  const marketingFormats = [\r\n    `${catchyWords}\\n${subheadline}\\n${cta}`,\r\n    `${catchyWords}\\n${subheadline}`,\r\n    `${catchyWords}\\n${cta}`,\r\n    `${subheadline}\\n${cta}`,\r\n    catchyWords\r\n  ];\r\n\r\n  return getRandomElement(marketingFormats);\r\n}\r\n\r\n/**\r\n * Get cultural context and local market insights\r\n */\r\nfunction getCulturalContext(location: string) {\r\n  // Default context\r\n  let context = {\r\n    localTerms: ['Premium', 'Professional', 'Expert'],\r\n    marketingStyle: 'Professional',\r\n    targetAudience: 'businesses',\r\n    localMarket: 'modern',\r\n    socialProof: 'thousands of clients',\r\n    valueProposition: 'Industry-leading',\r\n    competitiveAdvantage: 'proven expertise',\r\n    urgencyTrigger: 'Don\\'t miss out on',\r\n    actionWords: 'Connect with',\r\n    localCTA: 'Get Started',\r\n    ctaUrgency: 'Now'\r\n  };\r\n\r\n  // Location-specific cultural adaptations\r\n  if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {\r\n    context = {\r\n      localTerms: ['Premium', 'Luxury', 'Elite', 'Exclusive'],\r\n      marketingStyle: 'Luxury-focused',\r\n      targetAudience: 'discerning clients',\r\n      localMarket: 'Dubai\\'s dynamic',\r\n      socialProof: 'leading UAE businesses',\r\n      valueProposition: 'World-class',\r\n      competitiveAdvantage: 'international excellence',\r\n      urgencyTrigger: 'Seize the opportunity for',\r\n      actionWords: 'Experience',\r\n      localCTA: 'Book Your Exclusive Consultation',\r\n      ctaUrgency: 'Today'\r\n    };\r\n  } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {\r\n    context = {\r\n      localTerms: ['Bespoke', 'Tailored', 'Refined'],\r\n      marketingStyle: 'Sophisticated',\r\n      targetAudience: 'discerning professionals',\r\n      localMarket: 'London\\'s competitive',\r\n      socialProof: 'established UK enterprises',\r\n      valueProposition: 'Expertly crafted',\r\n      competitiveAdvantage: 'British excellence',\r\n      urgencyTrigger: 'Secure your',\r\n      actionWords: 'Discover',\r\n      localCTA: 'Arrange Your Consultation',\r\n      ctaUrgency: 'Promptly'\r\n    };\r\n  } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {\r\n    context = {\r\n      localTerms: ['Cutting-edge', 'Innovative', 'Game-changing'],\r\n      marketingStyle: 'Bold and direct',\r\n      targetAudience: 'ambitious professionals',\r\n      localMarket: 'NYC\\'s fast-paced',\r\n      socialProof: 'successful New York businesses',\r\n      valueProposition: 'Results-driven',\r\n      competitiveAdvantage: 'New York hustle',\r\n      urgencyTrigger: 'Don\\'t let competitors get',\r\n      actionWords: 'Dominate with',\r\n      localCTA: 'Schedule Your Strategy Session',\r\n      ctaUrgency: 'ASAP'\r\n    };\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n/**\r\n * Extract business intelligence from brand profile for personalized marketing\r\n * Analyzes actual business data to create relevant marketing copy\r\n */\r\nfunction extractBusinessIntelligence(profile: BrandProfile) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n\r\n  // Extract primary service information\r\n  const primaryService = services[0]?.name || services[0] || businessType;\r\n  const serviceDescription = services[0]?.description || '';\r\n  const targetAudience = services[0]?.targetAudience || 'businesses';\r\n  const keyFeatures = services[0]?.keyFeatures || '';\r\n  const competitiveAdvantages = services[0]?.competitiveAdvantages || '';\r\n\r\n  // Analyze description for key terms\r\n  const descriptionWords = description.toLowerCase().split(/\\s+/);\r\n  const strengthWords = extractStrengthWords(description, businessType);\r\n  const valueWords = extractValueWords(description, keyFeatures);\r\n  const benefitWords = extractBenefitWords(description, competitiveAdvantages);\r\n\r\n  // Extract competitive advantages\r\n  const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);\r\n  const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);\r\n  const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);\r\n\r\n  // Extract target audience specifics\r\n  const actualTargetAudience = extractTargetAudience(targetAudience, businessType);\r\n\r\n  // Generate contextual elements\r\n  const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);\r\n  const mainValue = extractMainValue(description, keyFeatures);\r\n  const offerType = generateOfferType(businessType, services);\r\n  const consultationType = generateConsultationType(businessType);\r\n  const journeyType = generateJourneyType(businessType, primaryService);\r\n  const urgencyTrigger = generateUrgencyTrigger(businessType, location);\r\n\r\n  // Extract outcome words from business context\r\n  const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);\r\n  const differentiators = extractDifferentiators(competitiveAdvantages, businessType);\r\n\r\n  return {\r\n    primaryService,\r\n    strengthWords,\r\n    valueWords,\r\n    benefitWords,\r\n    realCompetitiveAdvantage,\r\n    uniqueFeature,\r\n    realDifferentiator,\r\n    actualTargetAudience,\r\n    keyBenefit,\r\n    mainValue,\r\n    offerType,\r\n    consultationType,\r\n    journeyType,\r\n    urgencyTrigger,\r\n    outcomeWords,\r\n    differentiators\r\n  };\r\n}\r\n\r\n/**\r\n * Get random element from array\r\n */\r\nfunction getRandomElement<T>(array: T[]): T {\r\n  return array[Math.floor(Math.random() * array.length)];\r\n}\r\n\r\n/**\r\n * Create professional design prompt with 20 years of design + marketing expertise\r\n * Combines cultural awareness, psychology, and visual design mastery\r\n */\r\nfunction createProfessionalDesignPrompt(imageText: string, platform: Platform, profile: BrandProfile, revoModel: RevoModelId): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const primaryService = services[0]?.name || services[0] || 'Quality Services';\r\n\r\n  // Get cultural context for design decisions\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Industry-specific design psychology\r\n  const industryDesignPsychology = getIndustryDesignPsychology(businessType);\r\n\r\n  // Platform-specific design requirements\r\n  const platformRequirements = getPlatformDesignRequirements(platform);\r\n\r\n  // Color psychology based on business type and culture\r\n  const colorPsychology = getColorPsychology(businessType, location);\r\n\r\n  // Typography psychology for conversion\r\n  const typographyStrategy = getTypographyStrategy(businessType, platform);\r\n\r\n  return `Create an exceptional, conversion-focused ${platform} design that embodies 20 years of professional design and marketing expertise.\r\n\r\nBUSINESS CONTEXT:\r\n- Company: ${businessName}\r\n- Industry: ${businessType}\r\n- Primary Service: ${primaryService}\r\n- Location: ${location}\r\n- Cultural Context: ${culturalContext.marketingStyle}\r\n\r\nTEXT TO INTEGRATE: \"${imageText}\"\r\n\r\nDESIGN PSYCHOLOGY & STRATEGY:\r\n${industryDesignPsychology}\r\n\r\nVISUAL HIERARCHY & COMPOSITION:\r\n- Apply the golden ratio and rule of thirds for optimal visual flow\r\n- Create clear focal points that guide the eye to key conversion elements\r\n- Use strategic white space to enhance readability and premium feel\r\n- Implement Z-pattern or F-pattern layout for maximum engagement\r\n\r\nCOLOR STRATEGY:\r\n${colorPsychology}\r\n\r\nTYPOGRAPHY MASTERY:\r\n${typographyStrategy}\r\n\r\nCULTURAL DESIGN ADAPTATION:\r\n- ${culturalContext.localMarket} aesthetic preferences\r\n- ${culturalContext.targetAudience} visual expectations\r\n- Regional design trends and cultural symbols\r\n- Local color associations and meanings\r\n\r\nCONVERSION OPTIMIZATION:\r\n- Design elements that create urgency and desire\r\n- Visual cues that guide toward call-to-action\r\n- Trust signals through professional presentation\r\n- Emotional triggers through strategic imagery and layout\r\n\r\nPLATFORM OPTIMIZATION:\r\n${platformRequirements}\r\n\r\nTECHNICAL EXCELLENCE:\r\n- Aspect Ratio: 1:1 (perfect square)\r\n- Resolution: Ultra-high quality, print-ready standards\r\n- Text Clarity: Crystal clear, perfectly readable at all sizes\r\n- Brand Consistency: Align with professional brand standards\r\n- Mobile Optimization: Ensure perfect display on all devices\r\n\r\nFINAL QUALITY STANDARDS:\r\nThis design must look like it was created by a top-tier creative agency for a Fortune 500 company. Every element should be intentional, every color choice strategic, every font decision conversion-focused. The result should be a design that not only looks stunning but actually drives business results and sales.\r\n\r\nMake it absolutely irresistible and professionally compelling.`;\r\n}\r\n\r\n/**\r\n * Get industry-specific design psychology\r\n */\r\nfunction getIndustryDesignPsychology(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {\r\n    return `- Use warm, appetizing colors that stimulate hunger and comfort\r\n- Incorporate food photography principles with rich textures\r\n- Create cozy, inviting atmosphere through design elements\r\n- Focus on sensory appeal and mouth-watering visual presentation`;\r\n  }\r\n\r\n  if (type.includes('tech') || type.includes('software') || type.includes('digital')) {\r\n    return `- Employ clean, minimalist design with high-tech aesthetics\r\n- Use gradients and modern geometric shapes\r\n- Incorporate subtle tech-inspired elements and icons\r\n- Focus on innovation, efficiency, and cutting-edge appeal`;\r\n  }\r\n\r\n  if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {\r\n    return `- Use calming, trustworthy colors that convey safety and care\r\n- Incorporate clean, sterile design elements\r\n- Focus on professionalism, expertise, and patient comfort\r\n- Use imagery that suggests health, vitality, and well-being`;\r\n  }\r\n\r\n  if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {\r\n    return `- Employ sophisticated, conservative design elements\r\n- Use colors that convey stability, trust, and prosperity\r\n- Incorporate subtle luxury elements and professional imagery\r\n- Focus on security, growth, and financial success`;\r\n  }\r\n\r\n  if (type.includes('real estate') || type.includes('property')) {\r\n    return `- Use aspirational imagery and luxury design elements\r\n- Incorporate architectural lines and premium materials\r\n- Focus on lifestyle, investment, and dream fulfillment\r\n- Use colors that suggest stability, growth, and success`;\r\n  }\r\n\r\n  // Default professional services\r\n  return `- Use professional, trustworthy design elements\r\n- Incorporate subtle premium touches and quality indicators\r\n- Focus on expertise, reliability, and professional excellence\r\n- Use colors and imagery that convey competence and success`;\r\n}\r\n\r\n/**\r\n * Get platform-specific design requirements\r\n */\r\nfunction getPlatformDesignRequirements(platform: Platform): string {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return `- Optimize for Instagram's visual-first environment\r\n- Use bold, eye-catching elements that stand out in feeds\r\n- Incorporate Instagram-native design trends and aesthetics\r\n- Ensure design works perfectly in both feed and story formats`;\r\n\r\n    case 'Facebook':\r\n      return `- Design for Facebook's diverse, multi-generational audience\r\n- Use clear, readable elements that work across age groups\r\n- Incorporate social proof and community-focused elements\r\n- Ensure design is engaging but not overwhelming`;\r\n\r\n    case 'LinkedIn':\r\n      return `- Employ professional, business-focused design elements\r\n- Use conservative colors and sophisticated typography\r\n- Incorporate industry-specific imagery and professional symbols\r\n- Focus on credibility, expertise, and business value`;\r\n\r\n    case 'Twitter':\r\n      return `- Create concise, impactful design that communicates quickly\r\n- Use bold typography and clear visual hierarchy\r\n- Incorporate trending design elements and current aesthetics\r\n- Ensure design is optimized for rapid consumption`;\r\n\r\n    default:\r\n      return `- Create versatile design that works across multiple platforms\r\n- Use universal design principles and broad appeal\r\n- Ensure scalability and readability across different contexts\r\n- Focus on timeless, professional aesthetics`;\r\n  }\r\n}\r\n\r\n/**\r\n * Get color psychology based on business type and location\r\n */\r\nfunction getColorPsychology(businessType: string, location: string): string {\r\n  const type = businessType.toLowerCase();\r\n  const loc = location.toLowerCase();\r\n\r\n  let baseColors = '';\r\n  let culturalColors = '';\r\n\r\n  // Business type color psychology\r\n  if (type.includes('restaurant') || type.includes('food')) {\r\n    baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    baseColors = 'modern blues, teals, and purples to convey innovation and trust';\r\n  } else if (type.includes('health') || type.includes('medical')) {\r\n    baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';\r\n  } else if (type.includes('finance') || type.includes('banking')) {\r\n    baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';\r\n  } else {\r\n    baseColors = 'professional blues, grays, and accent colors to convey trust and competence';\r\n  }\r\n\r\n  // Cultural color adaptations\r\n  if (loc.includes('dubai') || loc.includes('uae')) {\r\n    culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\\'s premium market expectations';\r\n  } else if (loc.includes('london') || loc.includes('uk')) {\r\n    culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';\r\n  } else if (loc.includes('new york') || loc.includes('nyc')) {\r\n    culturalColors = 'Employ bold, confident colors that match New York\\'s dynamic business environment';\r\n  } else {\r\n    culturalColors = 'Use universally appealing professional color combinations';\r\n  }\r\n\r\n  return `- Primary Strategy: ${baseColors}\r\n- Cultural Adaptation: ${culturalColors}\r\n- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors\r\n- Contrast Optimization: Ensure maximum readability and visual impact`;\r\n}\r\n\r\n/**\r\n * Get typography strategy for conversion\r\n */\r\nfunction getTypographyStrategy(businessType: string, platform: Platform): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  let fontStrategy = '';\r\n  let hierarchyStrategy = '';\r\n\r\n  if (type.includes('luxury') || type.includes('premium')) {\r\n    fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';\r\n  } else if (type.includes('creative') || type.includes('design')) {\r\n    fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';\r\n  } else {\r\n    fontStrategy = 'Professional, highly readable fonts that convey trust and competence';\r\n  }\r\n\r\n  hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact\r\n- Secondary Text: Clear, readable subheadings that support the main message\r\n- Call-to-Action: Distinctive typography that stands out and drives action\r\n- Supporting Text: Clean, professional fonts for additional information`;\r\n\r\n  return `- Font Selection: ${fontStrategy}\r\n- Visual Hierarchy: ${hierarchyStrategy}\r\n- Readability: Optimized for ${platform} viewing conditions and mobile devices\r\n- Conversion Focus: Typography choices designed to guide the eye and encourage action`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgQsB,+BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions/revo-2-actions.ts"], "sourcesContent": ["'use server';\r\n\r\n/**\r\n * Revo 2.0 Server Actions\r\n * Next-generation content creation with Imagen 4 integration\r\n */\r\n\r\nimport { generateRevo2Content, type Revo2GenerationInput } from '@/ai/flows/revo-2-generation';\r\nimport type { BrandProfile, Platform, BrandConsistencyPreferences, GeneratedPost } from '@/lib/types';\r\n\r\n/**\r\n * Generate content with Revo 2.0 (Imagen 4)\r\n */\r\nexport async function generateRevo2ContentAction(\r\n  brandProfile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency: BrandConsistencyPreferences,\r\n  prompt?: string,\r\n  options?: {\r\n    aspectRatio?: '1:1' | '16:9' | '9:16' | '21:9' | '4:5';\r\n    style?: 'photographic' | 'artistic' | 'digital_art' | 'cinematic' | 'anime' | 'sketch';\r\n    quality?: 'standard' | 'high' | 'ultra';\r\n    mood?: 'professional' | 'energetic' | 'calm' | 'vibrant' | 'elegant' | 'bold';\r\n  }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log('🌟 Starting Revo 2.0 content generation...');\r\n    console.log(`📱 Platform: ${platform}`);\r\n    console.log(`🎨 Style: ${options?.style || 'photographic'}`);\r\n    console.log(`⚡ Quality: ${options?.quality || 'ultra'}`);\r\n\r\n    // Generate content prompt if not provided\r\n    const contentPrompt = prompt || generateContentPrompt(brandProfile, platform);\r\n\r\n    // Prepare Revo 2.0 input\r\n    const revo2Input: Revo2GenerationInput = {\r\n      prompt: contentPrompt,\r\n      platform,\r\n      aspectRatio: options?.aspectRatio,\r\n      brandProfile,\r\n      brandConsistency,\r\n      style: options?.style || 'photographic',\r\n      quality: options?.quality || 'ultra',\r\n      mood: options?.mood || 'professional',\r\n      enhancePrompt: true\r\n    };\r\n\r\n    // Generate with Revo 2.0\r\n    const result = await generateRevo2Content(revo2Input);\r\n\r\n    console.log(`✅ Revo 2.0 generation completed!`);\r\n    console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n    console.log(`🚀 Enhancements: ${result.enhancementsApplied.length}`);\r\n\r\n    // Convert to GeneratedPost format\r\n    const generatedPost: GeneratedPost = {\r\n      id: `revo2-${Date.now()}`,\r\n      date: new Date().toISOString(),\r\n      platform: platform.toLowerCase() as Platform, // Fix: Ensure lowercase for Firestore compatibility\r\n      postType: 'post',\r\n      imageUrl: result.imageUrl,\r\n      content: result.caption, // Fix: Use 'content' instead of 'caption'\r\n      hashtags: result.hashtags,\r\n      status: 'generated',\r\n      variants: [{\r\n        platform: platform.toLowerCase() as Platform, // Fix: Ensure lowercase for Firestore compatibility\r\n        imageUrl: result.imageUrl\r\n      }],\r\n      catchyWords: '', // Will be populated from result if available\r\n      createdAt: new Date(),\r\n      brandProfileId: brandProfile.id || 'unknown',\r\n      qualityScore: result.qualityScore,\r\n      metadata: {\r\n        model: 'revo-2.0',\r\n        qualityScore: result.qualityScore,\r\n        processingTime: result.processingTime,\r\n        enhancementsApplied: result.enhancementsApplied,\r\n        aspectRatio: result.aspectRatio,\r\n        ...result.metadata\r\n      }\r\n    };\r\n\r\n    return generatedPost;\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 2.0 content generation failed:', error);\r\n    throw new Error(`Revo 2.0 generation failed: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate creative asset with Revo 2.0\r\n */\r\nexport async function generateRevo2CreativeAssetAction(\r\n  prompt: string,\r\n  brandProfile?: BrandProfile,\r\n  options?: {\r\n    platform?: Platform;\r\n    aspectRatio?: '1:1' | '16:9' | '9:16' | '21:9' | '4:5';\r\n    style?: 'photographic' | 'artistic' | 'digital_art' | 'cinematic' | 'anime' | 'sketch';\r\n    quality?: 'standard' | 'high' | 'ultra';\r\n    mood?: 'professional' | 'energetic' | 'calm' | 'vibrant' | 'elegant' | 'bold';\r\n  }\r\n): Promise<{\r\n  imageUrl: string;\r\n  aiExplanation: string;\r\n  qualityScore: number;\r\n  processingTime: number;\r\n  enhancementsApplied: string[];\r\n}> {\r\n  try {\r\n    console.log('🎨 Starting Revo 2.0 creative asset generation...');\r\n    console.log(`🎯 Prompt: ${prompt}`);\r\n    console.log(`🎨 Style: ${options?.style || 'photographic'}`);\r\n\r\n    // Prepare Revo 2.0 input\r\n    const revo2Input: Revo2GenerationInput = {\r\n      prompt,\r\n      platform: options?.platform || 'Instagram',\r\n      aspectRatio: options?.aspectRatio,\r\n      brandProfile,\r\n      style: options?.style || 'photographic',\r\n      quality: options?.quality || 'ultra',\r\n      mood: options?.mood || 'professional',\r\n      enhancePrompt: true\r\n    };\r\n\r\n    // Generate with Revo 2.0\r\n    const result = await generateRevo2Content(revo2Input);\r\n\r\n    // Create AI explanation\r\n    const aiExplanation = `🌟 Revo 2.0 Ultra Generation Complete!\r\n\r\n🎯 Quality Score: ${result.qualityScore}/10\r\n⚡ Processing Time: ${result.processingTime}ms\r\n🎨 Style: ${result.metadata.style}\r\n📐 Aspect Ratio: ${result.aspectRatio}\r\n🔧 Model: ${result.metadata.model}\r\n\r\n🚀 Enhancements Applied:\r\n${result.enhancementsApplied.map(enhancement => `• ${enhancement}`).join('\\n')}\r\n\r\nThis image was created using our next-generation AI engine with revolutionary capabilities, delivering ultra-high quality results with advanced style control and professional optimization.`;\r\n\r\n    console.log(`✅ Revo 2.0 creative asset completed!`);\r\n    console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      aiExplanation,\r\n      qualityScore: result.qualityScore,\r\n      processingTime: result.processingTime,\r\n      enhancementsApplied: result.enhancementsApplied\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error('❌ Revo 2.0 creative asset generation failed:', error);\r\n    throw new Error(`Revo 2.0 creative asset generation failed: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content prompt based on brand profile and platform\r\n */\r\nfunction generateContentPrompt(brandProfile: BrandProfile, platform: Platform): string {\r\n  const businessType = brandProfile.businessType || 'business';\r\n  const visualStyle = brandProfile.visualStyle || 'modern';\r\n\r\n  const platformPrompts = {\r\n    Instagram: `Create an engaging Instagram post for a ${businessType} with ${visualStyle} style`,\r\n    Facebook: `Design a shareable Facebook post for a ${businessType} with ${visualStyle} aesthetic`,\r\n    Twitter: `Generate a Twitter-optimized image for a ${businessType} with ${visualStyle} design`,\r\n    LinkedIn: `Create a professional LinkedIn post for a ${businessType} with ${visualStyle} style`,\r\n  };\r\n\r\n  let prompt = platformPrompts[platform] || platformPrompts.Instagram;\r\n\r\n  // Add brand-specific context\r\n  if (brandProfile.businessName) {\r\n    prompt += ` for ${brandProfile.businessName}`;\r\n  }\r\n\r\n  if (brandProfile.targetAudience) {\r\n    prompt += `, targeting ${brandProfile.targetAudience}`;\r\n  }\r\n\r\n  if (brandProfile.primaryColor) {\r\n    prompt += `, incorporating ${brandProfile.primaryColor} brand color`;\r\n  }\r\n\r\n  return prompt;\r\n}\r\n\r\n/**\r\n * Get Revo 2.0 capabilities\r\n */\r\nexport async function getRevo2CapabilitiesAction() {\r\n  return {\r\n    name: 'Revo 2.0',\r\n    description: 'Next-generation AI content creation with revolutionary capabilities',\r\n    features: [\r\n      'Ultra-high quality image generation',\r\n      'Multi-aspect ratio support (1:1, 16:9, 9:16, 21:9, 4:5)',\r\n      'Advanced style control (6 professional styles)',\r\n      'Professional mood settings',\r\n      'Brand color integration',\r\n      'Platform-optimized generation',\r\n      'Enhanced prompt engineering',\r\n      'Revolutionary AI engine',\r\n      'Smart caption generation',\r\n      'Intelligent hashtag strategy'\r\n    ],\r\n    styles: [\r\n      { id: 'photographic', name: 'Photographic', description: 'Professional photography style' },\r\n      { id: 'artistic', name: 'Artistic', description: 'Creative artistic interpretation' },\r\n      { id: 'digital_art', name: 'Digital Art', description: 'Modern digital art style' },\r\n      { id: 'cinematic', name: 'Cinematic', description: 'Movie-like cinematic quality' },\r\n      { id: 'anime', name: 'Anime', description: 'Anime art style' },\r\n      { id: 'sketch', name: 'Sketch', description: 'Hand-drawn sketch aesthetic' }\r\n    ],\r\n    qualities: [\r\n      { id: 'standard', name: 'Standard', description: 'Good quality, fast generation' },\r\n      { id: 'high', name: 'High', description: 'High quality, balanced performance' },\r\n      { id: 'ultra', name: 'Ultra', description: 'Maximum quality, premium results' }\r\n    ],\r\n    aspectRatios: [\r\n      { id: '1:1', name: 'Square', description: 'Perfect for Instagram posts' },\r\n      { id: '16:9', name: 'Landscape', description: 'Great for Facebook, YouTube' },\r\n      { id: '9:16', name: 'Portrait', description: 'Ideal for TikTok, Stories' },\r\n      { id: '21:9', name: 'Ultra Wide', description: 'Cinematic wide format' },\r\n      { id: '4:5', name: 'Tall', description: 'Perfect for Pinterest' }\r\n    ],\r\n    supportedPlatforms: ['Instagram', 'Facebook', 'Twitter', 'LinkedIn'],\r\n    qualityRange: '8.5-10.0/10',\r\n    status: 'Revolutionary'\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA6FsB,mCAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/studio/chat-layout.tsx"], "sourcesContent": ["// src/components/studio/chat-layout.tsx\r\nimport * as React from 'react';\r\nimport { ChatMessages } from './chat-messages';\r\nimport { ChatInput } from './chat-input';\r\nimport type { BrandProfile, Message } from '@/lib/types';\r\nimport Balancer from 'react-wrap-balancer';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Bot } from 'lucide-react';\r\nimport { generateCreativeAssetAction, generateEnhancedDesignAction } from '@/app/actions';\r\nimport { generateRevo2CreativeAssetAction } from '@/app/actions/revo-2-actions';\r\nimport { useToast } from '@/hooks/use-toast';\r\nimport { type RevoModel } from '@/components/ui/revo-model-selector';\r\n\r\n\r\ninterface ChatLayoutProps {\r\n    brandProfile: BrandProfile | null;\r\n    onEditImage: (imageUrl: string) => void;\r\n}\r\n\r\nexport function ChatLayout({ brandProfile, onEditImage }: ChatLayoutProps) {\r\n    const [messages, setMessages] = React.useState<Message[]>([]);\r\n    const [isLoading, setIsLoading] = React.useState(false);\r\n    const [input, setInput] = React.useState('');\r\n    const [imagePreview, setImagePreview] = React.useState<string | null>(null);\r\n    const [imageDataUrl, setImageDataUrl] = React.useState<string | null>(null);\r\n    const [useBrandProfile, setUseBrandProfile] = React.useState(!!brandProfile);\r\n    const [outputType, setOutputType] = React.useState<'image' | 'video'>('image');\r\n    const [aspectRatio, setAspectRatio] = React.useState<'16:9' | '9:16'>('16:9');\r\n    const [selectedRevoModel, setSelectedRevoModel] = React.useState<RevoModel>('revo-1.5');\r\n    const { toast } = useToast();\r\n\r\n\r\n    React.useEffect(() => {\r\n        setUseBrandProfile(!!brandProfile);\r\n    }, [brandProfile]);\r\n\r\n    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n        const file = event.target.files?.[0];\r\n        if (file) {\r\n            const reader = new FileReader();\r\n            reader.onloadend = () => {\r\n                const dataUrl = reader.result as string;\r\n                setImagePreview(dataUrl);\r\n                setImageDataUrl(dataUrl);\r\n            };\r\n            reader.readAsDataURL(file);\r\n        }\r\n    };\r\n\r\n    const handleSetReferenceAsset = (url: string | null | undefined, type: 'image' | 'video') => {\r\n        if (url) {\r\n            setOutputType(type);\r\n            setImagePreview(url); // Using imagePreview for both image and video previews in the input area.\r\n            setImageDataUrl(url);\r\n        }\r\n    }\r\n\r\n    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {\r\n        e.preventDefault();\r\n        if (!input.trim()) {\r\n            toast({\r\n                variant: 'destructive',\r\n                title: 'Input Required',\r\n                description: 'Please describe the image or video you want to create.',\r\n            });\r\n            return\r\n        };\r\n\r\n        const newUserMessage: Message = {\r\n            id: Date.now().toString(),\r\n            role: 'user',\r\n            content: input,\r\n            // For simplicity, we just show the preview, which could be an image data URL for a video.\r\n            imageUrl: imagePreview,\r\n        };\r\n        setMessages([...messages, newUserMessage]);\r\n\r\n        const currentInput = input;\r\n        const currentImageDataUrl = imageDataUrl;\r\n\r\n        setInput('');\r\n        setImagePreview(null);\r\n        setImageDataUrl(null);\r\n        setIsLoading(true);\r\n\r\n        try {\r\n            let result;\r\n            let aiResponse: Message;\r\n\r\n            if (selectedRevoModel === 'revo-2.0' && outputType === 'image' && brandProfile) {\r\n                // Use Revo 2.0 next-generation AI for images with brand profile\r\n                const revo2Result = await generateRevo2CreativeAssetAction(\r\n                    currentInput,\r\n                    brandProfile,\r\n                    {\r\n                        platform: 'Instagram', // Default platform for Creative Studio\r\n                        aspectRatio: '1:1', // Default to square for Creative Studio\r\n                        style: 'photographic',\r\n                        quality: 'ultra',\r\n                        mood: 'professional'\r\n                    }\r\n                );\r\n\r\n                aiResponse = {\r\n                    id: (Date.now() + 1).toString(),\r\n                    role: 'assistant',\r\n                    content: `🌟 ${selectedRevoModel} Revolutionary AI Generated!\\n\\nQuality Score: ${revo2Result.qualityScore}/10\\nEnhancements: ${revo2Result.enhancementsApplied.join(', ')}\\nProcessing Time: ${revo2Result.processingTime}ms\\n\\nThis image was created using our next-generation AI engine with revolutionary capabilities and ultra-high quality results.`,\r\n                    imageUrl: revo2Result.imageUrl,\r\n                };\r\n            } else if (selectedRevoModel === 'revo-1.5' && outputType === 'image' && brandProfile) {\r\n                // Use enhanced design generation for images with brand profile\r\n                const enhancedResult = await generateEnhancedDesignAction(\r\n                    brandProfile.businessType || 'business',\r\n                    'Instagram', // Default platform, could be made configurable\r\n                    brandProfile.visualStyle || 'modern',\r\n                    currentInput, // Creative Studio uses simple text input for now\r\n                    brandProfile,\r\n                    true,\r\n                    { strictConsistency: true, followBrandColors: true } // Enable all enhancements for Creative Studio\r\n                );\r\n\r\n                aiResponse = {\r\n                    id: (Date.now() + 1).toString(),\r\n                    role: 'assistant',\r\n                    content: `✨ ${selectedRevoModel} Enhanced Design Generated!\\n\\nQuality Score: ${enhancedResult.qualityScore}/10\\nEnhancements: ${enhancedResult.enhancementsApplied.join(', ')}\\nProcessing Time: ${enhancedResult.processingTime}ms\\n\\nThis design uses professional design principles, platform optimization, and quality validation for superior results.`,\r\n                    imageUrl: enhancedResult.imageUrl,\r\n                };\r\n            } else {\r\n                // Use standard creative asset generation\r\n                console.log(`🚀 Using ${selectedRevoModel} for standard generation`);\r\n                result = await generateCreativeAssetAction(\r\n                    currentInput,\r\n                    outputType,\r\n                    currentImageDataUrl,\r\n                    useBrandProfile,\r\n                    brandProfile,\r\n                    null, // maskDataUrl\r\n                    outputType === 'video' ? aspectRatio : undefined,\r\n                );\r\n\r\n                aiResponse = {\r\n                    id: (Date.now() + 1).toString(),\r\n                    role: 'assistant',\r\n                    content: result.aiExplanation,\r\n                    imageUrl: result.imageUrl,\r\n                    videoUrl: result.videoUrl,\r\n                };\r\n            }\r\n\r\n            setMessages(prevMessages => [...prevMessages, aiResponse]);\r\n\r\n        } catch (error) {\r\n            const errorResponse: Message = {\r\n                id: (Date.now() + 1).toString(),\r\n                role: 'assistant',\r\n                content: `Sorry, I ran into an error: ${(error as Error).message}`,\r\n            };\r\n            setMessages(prevMessages => [...prevMessages, errorResponse]);\r\n            toast({\r\n                variant: 'destructive',\r\n                title: 'Generation Failed',\r\n                description: (error as Error).message,\r\n            });\r\n        } finally {\r\n            setIsLoading(false);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className=\"relative flex h-full flex-col\">\r\n            <div className=\"flex-1 overflow-y-auto\">\r\n                {messages.length === 0 && !isLoading ? (\r\n                    <div className=\"flex h-full flex-col items-center justify-center text-center p-4\">\r\n                        <Card className=\"max-w-2xl w-full\">\r\n                            <CardContent className=\"p-6\">\r\n                                <Bot className=\"mx-auto h-12 w-12 text-primary mb-4\" />\r\n                                <h1 className=\"text-2xl font-bold font-headline\">Creative Studio</h1>\r\n                                <p className=\"text-muted-foreground mt-2\">\r\n                                    <Balancer>\r\n                                        Welcome to your AI-powered creative partner. Describe the ad you want, upload an image to edit, or start from scratch.\r\n                                    </Balancer>\r\n                                </p>\r\n                            </CardContent>\r\n                        </Card>\r\n                    </div>\r\n                ) : (\r\n                    <ChatMessages\r\n                        messages={messages}\r\n                        isLoading={isLoading}\r\n                        onSetReferenceAsset={handleSetReferenceAsset}\r\n                        onEditImage={onEditImage}\r\n                    />\r\n                )}\r\n            </div>\r\n\r\n            <ChatInput\r\n                input={input}\r\n                setInput={setInput}\r\n                handleSubmit={handleSubmit}\r\n                isLoading={isLoading}\r\n                imagePreview={imagePreview}\r\n                setImagePreview={setImagePreview}\r\n                setImageDataUrl={setImageDataUrl}\r\n                useBrandProfile={useBrandProfile}\r\n                setUseBrandProfile={setUseBrandProfile}\r\n                outputType={outputType}\r\n                setOutputType={setOutputType}\r\n                handleImageUpload={handleImageUpload}\r\n                isBrandProfileAvailable={!!brandProfile}\r\n                onEditImage={onEditImage}\r\n                aspectRatio={aspectRatio}\r\n                setAspectRatio={setAspectRatio}\r\n                selectedRevoModel={selectedRevoModel}\r\n                setSelectedRevoModel={setSelectedRevoModel}\r\n            />\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AACxC;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;;AASO,SAAS,WAAW,EAAE,YAAY,EAAE,WAAW,EAAmB;;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAa,EAAE;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAiB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,CAAC,CAAC;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAqB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAmB;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAa;IAC5E,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAGzB,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;gCAAE;YACZ,mBAAmB,CAAC,CAAC;QACzB;+BAAG;QAAC;KAAa;IAEjB,MAAM,oBAAoB,CAAC;QACvB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACN,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACf,MAAM,UAAU,OAAO,MAAM;gBAC7B,gBAAgB;gBAChB,gBAAgB;YACpB;YACA,OAAO,aAAa,CAAC;QACzB;IACJ;IAEA,MAAM,0BAA0B,CAAC,KAAgC;QAC7D,IAAI,KAAK;YACL,cAAc;YACd,gBAAgB,MAAM,0EAA0E;YAChG,gBAAgB;QACpB;IACJ;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;YACf,MAAM;gBACF,SAAS;gBACT,OAAO;gBACP,aAAa;YACjB;YACA;QACJ;;QAEA,MAAM,iBAA0B;YAC5B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,0FAA0F;YAC1F,UAAU;QACd;QACA,YAAY;eAAI;YAAU;SAAe;QAEzC,MAAM,eAAe;QACrB,MAAM,sBAAsB;QAE5B,SAAS;QACT,gBAAgB;QAChB,gBAAgB;QAChB,aAAa;QAEb,IAAI;YACA,IAAI;YACJ,IAAI;YAEJ,IAAI,sBAAsB,cAAc,eAAe,WAAW,cAAc;gBAC5E,gEAAgE;gBAChE,MAAM,cAAc,MAAM,CAAA,GAAA,gKAAA,CAAA,mCAAgC,AAAD,EACrD,cACA,cACA;oBACI,UAAU;oBACV,aAAa;oBACb,OAAO;oBACP,SAAS;oBACT,MAAM;gBACV;gBAGJ,aAAa;oBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS,CAAC,GAAG,EAAE,kBAAkB,+CAA+C,EAAE,YAAY,YAAY,CAAC,mBAAmB,EAAE,YAAY,mBAAmB,CAAC,IAAI,CAAC,MAAM,mBAAmB,EAAE,YAAY,cAAc,CAAC,gIAAgI,CAAC;oBAC5V,UAAU,YAAY,QAAQ;gBAClC;YACJ,OAAO,IAAI,sBAAsB,cAAc,eAAe,WAAW,cAAc;gBACnF,+DAA+D;gBAC/D,MAAM,iBAAiB,MAAM,CAAA,GAAA,qJAAA,CAAA,+BAA4B,AAAD,EACpD,aAAa,YAAY,IAAI,YAC7B,aACA,aAAa,WAAW,IAAI,UAC5B,cACA,cACA,MACA;oBAAE,mBAAmB;oBAAM,mBAAmB;gBAAK,EAAE,8CAA8C;;gBAGvG,aAAa;oBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS,CAAC,EAAE,EAAE,kBAAkB,8CAA8C,EAAE,eAAe,YAAY,CAAC,mBAAmB,EAAE,eAAe,mBAAmB,CAAC,IAAI,CAAC,MAAM,mBAAmB,EAAE,eAAe,cAAc,CAAC,0HAA0H,CAAC;oBAC7V,UAAU,eAAe,QAAQ;gBACrC;YACJ,OAAO;gBACH,yCAAyC;gBACzC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,kBAAkB,wBAAwB,CAAC;gBACnE,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,8BAA2B,AAAD,EACrC,cACA,YACA,qBACA,iBACA,cACA,MACA,eAAe,UAAU,cAAc;gBAG3C,aAAa;oBACT,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS,OAAO,aAAa;oBAC7B,UAAU,OAAO,QAAQ;oBACzB,UAAU,OAAO,QAAQ;gBAC7B;YACJ;YAEA,YAAY,CAAA,eAAgB;uBAAI;oBAAc;iBAAW;QAE7D,EAAE,OAAO,OAAO;YACZ,MAAM,gBAAyB;gBAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,CAAC,4BAA4B,EAAE,AAAC,MAAgB,OAAO,EAAE;YACtE;YACA,YAAY,CAAA,eAAgB;uBAAI;oBAAc;iBAAc;YAC5D,MAAM;gBACF,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACzC;QACJ,SAAU;YACN,aAAa;QACjB;IACJ;IAEA,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC;gBAAI,WAAU;0BACV,SAAS,MAAM,KAAK,KAAK,CAAC,0BACvB,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACnB,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CACT,cAAA,6LAAC,8JAAA,CAAA,UAAQ;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;yCAQ1B,6LAAC,mJAAA,CAAA,eAAY;oBACT,UAAU;oBACV,WAAW;oBACX,qBAAqB;oBACrB,aAAa;;;;;;;;;;;0BAKzB,6LAAC,gJAAA,CAAA,YAAS;gBACN,OAAO;gBACP,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,cAAc;gBACd,iBAAiB;gBACjB,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,YAAY;gBACZ,eAAe;gBACf,mBAAmB;gBACnB,yBAAyB,CAAC,CAAC;gBAC3B,aAAa;gBACb,aAAa;gBACb,gBAAgB;gBAChB,mBAAmB;gBACnB,sBAAsB;;;;;;;;;;;;AAItC;GAtMgB;;QAUM,+HAAA,CAAA,WAAQ;;;KAVd", "debugId": null}}, {"offset": {"line": 2030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Slider = React.forwardRef<\r\n  React.ElementRef<typeof SliderPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SliderPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full touch-none select-none items-center\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\r\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\r\n    </SliderPrimitive.Track>\r\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\r\n  </SliderPrimitive.Root>\r\n))\r\nSlider.displayName = SliderPrimitive.Root.displayName\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/studio/image-editor.tsx"], "sourcesContent": ["// src/components/studio/image-editor.tsx\r\n\"use client\";\r\n\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport { X, Wand, Brush, Eraser, Undo, Redo, Loader2, RectangleHorizontal } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { useToast } from '@/hooks/use-toast';\r\nimport { generateCreativeAssetAction } from '@/app/actions';\r\nimport type { BrandProfile } from '@/lib/types';\r\nimport { cn } from '@/lib/utils';\r\n\r\n\r\ninterface ImageEditorProps {\r\n    imageUrl: string;\r\n    onClose: () => void;\r\n    brandProfile: BrandProfile | null;\r\n}\r\n\r\nexport function ImageEditor({ imageUrl, onClose, brandProfile }: ImageEditorProps) {\r\n    const imageCanvasRef = useRef<HTMLCanvasElement>(null);\r\n    const drawingCanvasRef = useRef<HTMLCanvasElement>(null);\r\n\r\n    const [isDrawing, setIsDrawing] = useState(false);\r\n    const [brushSize, setBrushSize] = useState(40);\r\n    const [tool, setTool] = useState<'brush' | 'eraser' | 'rect'>('brush');\r\n    const [prompt, setPrompt] = useState('');\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    \r\n    // History for drawing actions (masking)\r\n    const [drawHistory, setDrawHistory] = useState<ImageData[]>([]);\r\n    const [drawHistoryIndex, setDrawHistoryIndex] = useState(-1);\r\n\r\n    // History for generated images\r\n    const [imageHistory, setImageHistory] = useState<string[]>([imageUrl]);\r\n    const [imageHistoryIndex, setImageHistoryIndex] = useState(0);\r\n\r\n    const { toast } = useToast();\r\n    \r\n    const [rectStart, setRectStart] = useState<{x: number, y: number} | null>(null);\r\n\r\n    const currentImageUrl = imageHistory[imageHistoryIndex];\r\n\r\n    // Function to draw the main image onto its canvas\r\n    const drawImage = (url: string) => {\r\n        const imageCanvas = imageCanvasRef.current;\r\n        const drawingCanvas = drawingCanvasRef.current;\r\n        if (!imageCanvas || !drawingCanvas) return;\r\n\r\n        const imageCtx = imageCanvas.getContext('2d');\r\n        const drawingCtx = drawingCanvas.getContext('2d');\r\n        if (!imageCtx || !drawingCtx) return;\r\n\r\n        const image = new window.Image();\r\n        image.crossOrigin = \"anonymous\";\r\n        image.src = url;\r\n        image.onload = () => {\r\n            imageCanvas.width = image.naturalWidth;\r\n            imageCanvas.height = image.naturalHeight;\r\n            drawingCanvas.width = image.naturalWidth;\r\n            drawingCanvas.height = image.naturalHeight;\r\n\r\n            imageCtx.drawImage(image, 0, 0);\r\n\r\n            // Clear drawing canvas and reset its history\r\n            drawingCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);\r\n            const initialImageData = drawingCtx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);\r\n            setDrawHistory([initialImageData]);\r\n            setDrawHistoryIndex(0);\r\n        };\r\n        image.onerror = () => {\r\n            toast({ variant: 'destructive', title: \"Error loading image\", description: \"Could not load the image for editing.\" });\r\n        }\r\n    };\r\n    \r\n    // Redraw the image whenever the currentImageUrl changes (from undo/redo or new generation)\r\n    useEffect(() => {\r\n        if (currentImageUrl) {\r\n            drawImage(currentImageUrl);\r\n        }\r\n    }, [currentImageUrl]);\r\n\r\n\r\n    const getDrawingContext = () => drawingCanvasRef.current?.getContext('2d', { willReadFrequently: true });\r\n    \r\n    const saveToDrawHistory = () => {\r\n        const drawingCtx = getDrawingContext();\r\n        if (!drawingCtx || !drawingCanvasRef.current) return;\r\n        \r\n        const newHistory = drawHistory.slice(0, drawHistoryIndex + 1);\r\n        newHistory.push(drawingCtx.getImageData(0, 0, drawingCanvasRef.current.width, drawingCanvasRef.current.height));\r\n        setDrawHistory(newHistory);\r\n        setDrawHistoryIndex(newHistory.length - 1);\r\n    }\r\n    \r\n    const handleDrawUndo = () => {\r\n        if (drawHistoryIndex > 0) {\r\n            const newIndex = drawHistoryIndex - 1;\r\n            setDrawHistoryIndex(newIndex);\r\n            const drawingCtx = getDrawingContext();\r\n            if (drawingCtx) {\r\n                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);\r\n            }\r\n        }\r\n    }\r\n    \r\n    const handleDrawRedo = () => {\r\n        if (drawHistoryIndex < drawHistory.length - 1) {\r\n            const newIndex = drawHistoryIndex + 1;\r\n            setDrawHistoryIndex(newIndex);\r\n            const drawingCtx = getDrawingContext();\r\n            if (drawingCtx) {\r\n                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);\r\n            }\r\n        }\r\n    }\r\n    \r\n    const handleGenerationUndo = () => {\r\n        if (imageHistoryIndex > 0) {\r\n            setImageHistoryIndex(prev => prev - 1);\r\n        }\r\n    }\r\n\r\n    const getMousePos = (canvas: HTMLCanvasElement, evt: React.MouseEvent<HTMLCanvasElement>) => {\r\n        const rect = canvas.getBoundingClientRect();\r\n        return {\r\n            x: ((evt.clientX - rect.left) / rect.width) * canvas.width,\r\n            y: ((evt.clientY - rect.top) / rect.height) * canvas.height,\r\n        };\r\n    }\r\n\r\n    const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {\r\n        const canvas = drawingCanvasRef.current;\r\n        const ctx = getDrawingContext();\r\n        if (!ctx || !canvas) return;\r\n\r\n        const { x, y } = getMousePos(canvas, e);\r\n        setIsDrawing(true);\r\n        \r\n        if (tool === 'brush' || tool === 'eraser') {\r\n            ctx.beginPath();\r\n            ctx.moveTo(x, y);\r\n            ctx.lineWidth = brushSize;\r\n            ctx.lineCap = 'round';\r\n            ctx.lineJoin = 'round';\r\n            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';\r\n            ctx.globalCompositeOperation = tool === 'brush' ? 'source-over' : 'destination-out';\r\n        } else if (tool === 'rect') {\r\n            setRectStart({ x, y });\r\n        }\r\n    };\r\n\r\n    const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {\r\n        if (!isDrawing) return;\r\n        const canvas = drawingCanvasRef.current;\r\n        const ctx = getDrawingContext();\r\n        if (!ctx || !canvas) return;\r\n        \r\n        const { x, y } = getMousePos(canvas, e);\r\n        \r\n        if (tool === 'brush' || tool === 'eraser') {\r\n            ctx.lineTo(x, y);\r\n            ctx.stroke();\r\n        } else if (tool === 'rect' && rectStart) {\r\n            // Restore previous state to draw rect preview\r\n            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);\r\n            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';\r\n            ctx.lineWidth = 2;\r\n            ctx.strokeRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);\r\n        }\r\n    };\r\n    \r\n    const stopDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {\r\n        if (!isDrawing) return;\r\n        const canvas = drawingCanvasRef.current;\r\n        const ctx = getDrawingContext();\r\n        if (!ctx || !canvas) return;\r\n\r\n        if (tool === 'brush' || tool === 'eraser') {\r\n            ctx.closePath();\r\n        } else if (tool === 'rect' && rectStart) {\r\n            // Restore canvas before drawing final rect\r\n            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);\r\n            const { x, y } = getMousePos(canvas, e);\r\n            ctx.globalCompositeOperation = 'source-over';\r\n            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';\r\n            ctx.fillRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);\r\n            setRectStart(null);\r\n        }\r\n\r\n        setIsDrawing(false);\r\n        saveToDrawHistory();\r\n    };\r\n\r\n\r\n    const getMaskDataUrl = (): string => {\r\n        const drawingCanvas = drawingCanvasRef.current;\r\n        if (!drawingCanvas) return '';\r\n        const ctx = getDrawingContext();\r\n        if (!ctx) return '';\r\n        \r\n        const originalImageData = ctx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);\r\n        const originalData = originalImageData.data;\r\n\r\n        const maskCanvas = document.createElement('canvas');\r\n        maskCanvas.width = drawingCanvas.width;\r\n        maskCanvas.height = drawingCanvas.height;\r\n        const maskCtx = maskCanvas.getContext('2d');\r\n        if (!maskCtx) return '';\r\n\r\n        const maskImageData = maskCtx.createImageData(drawingCanvas.width, drawingCanvas.height);\r\n        const maskData = maskImageData.data;\r\n        \r\n        for (let i = 0; i < originalData.length; i += 4) {\r\n            if (originalData[i + 3] > 0) { // If pixel has alpha\r\n                maskData[i] = 0;     // R (black)\r\n                maskData[i + 1] = 0; // G\r\n                maskData[i + 2] = 0; // B\r\n                maskData[i + 3] = 255; // A (opaque)\r\n            } else {\r\n                maskData[i] = 255;   // R (white)\r\n                maskData[i + 1] = 255; // G\r\n                maskData[i + 2] = 255; // B\r\n                maskData[i + 3] = 255; // A (opaque)\r\n            }\r\n        }\r\n\r\n        maskCtx.putImageData(maskImageData, 0, 0);\r\n        return maskCanvas.toDataURL('image/png');\r\n    };\r\n\r\n    const handleGenerate = async () => {\r\n        if (!prompt.trim()) {\r\n            toast({ variant: 'destructive', title: \"Prompt is required\", description: \"Please describe what you want to change.\" });\r\n            return;\r\n        }\r\n        setIsLoading(true);\r\n\r\n        const maskDataUrl = getMaskDataUrl();\r\n        if (!maskDataUrl) {\r\n            toast({ variant: 'destructive', title: \"Mask Error\", description: \"Could not generate the mask data.\" });\r\n            setIsLoading(false);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const result = await generateCreativeAssetAction(\r\n                prompt,\r\n                'image',\r\n                currentImageUrl,\r\n                !!brandProfile,\r\n                brandProfile,\r\n                maskDataUrl\r\n            );\r\n\r\n            if (result.imageUrl) {\r\n                const newHistory = imageHistory.slice(0, imageHistoryIndex + 1);\r\n                newHistory.push(result.imageUrl);\r\n                setImageHistory(newHistory);\r\n                setImageHistoryIndex(newHistory.length - 1);\r\n                 toast({ title: \"Image Updated!\", description: result.aiExplanation });\r\n            } else {\r\n                 toast({ variant: 'destructive', title: \"Generation Failed\", description: \"The AI did not return an image.\" });\r\n            }\r\n        } catch (error) {\r\n            toast({ variant: 'destructive', title: \"Generation Failed\", description: (error as Error).message });\r\n        } finally {\r\n            setIsLoading(false);\r\n            setPrompt(\"\");\r\n        }\r\n    };\r\n\r\n\r\n    return (\r\n        <div className=\"flex h-full w-full bg-background\">\r\n            {/* Toolbar */}\r\n            <div className=\"w-64 flex-shrink-0 border-r bg-card p-4 flex flex-col gap-6\">\r\n                <div className=\"flex items-center justify-between\">\r\n                    <h2 className=\"text-xl font-bold\">Image Editor</h2>\r\n                    <Button variant=\"ghost\" size=\"icon\" onClick={onClose}><X className=\"h-4 w-4\"/></Button>\r\n                </div>\r\n                \r\n                 <div className=\"space-y-4\">\r\n                    <Label>History</Label>\r\n                     <div className=\"grid grid-cols-2 gap-2\">\r\n                        <Button variant='outline' onClick={handleDrawUndo} disabled={drawHistoryIndex <= 0}> <Undo className=\"mr-2\"/> Undo Mask</Button>\r\n                        <Button variant='outline' onClick={handleDrawRedo} disabled={drawHistoryIndex >= drawHistory.length - 1}><Redo className=\"mr-2\"/> Redo Mask</Button>\r\n                    </div>\r\n                     <Button variant='outline' onClick={handleGenerationUndo} disabled={imageHistoryIndex <= 0} className=\"w-full\">\r\n                        <Undo className=\"mr-2\"/> Undo Generation\r\n                    </Button>\r\n                </div>\r\n\r\n                <div className=\"space-y-4\">\r\n                    <Label>Tool</Label>\r\n                    <div className=\"grid grid-cols-3 gap-2\">\r\n                        <Button variant={tool === 'brush' ? 'secondary' : 'outline'} onClick={() => setTool('brush')}><Brush/></Button>\r\n                        <Button variant={tool === 'rect' ? 'secondary' : 'outline'} onClick={() => setTool('rect')}><RectangleHorizontal/></Button>\r\n                        <Button variant={tool === 'eraser' ? 'secondary' : 'outline'} onClick={() => setTool('eraser')}><Eraser/></Button>\r\n                    </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-4\">\r\n                    <Label htmlFor=\"brush-size\">Brush Size: {brushSize}px</Label>\r\n                    <Slider id=\"brush-size\" min={5} max={100} value={[brushSize]} onValueChange={(v) => setBrushSize(v[0])} />\r\n                </div>\r\n                <Separator />\r\n                <div className=\"space-y-2 flex-1 flex flex-col\">\r\n                    <Label htmlFor=\"inpaint-prompt\">Edit Prompt</Label>\r\n                    <Input \r\n                        id=\"inpaint-prompt\" \r\n                        placeholder=\"e.g., 'add sunglasses'\" \r\n                        value={prompt}\r\n                        onChange={(e) => setPrompt(e.target.value)}\r\n                        disabled={isLoading}\r\n                    />\r\n                </div>\r\n                <Button onClick={handleGenerate} disabled={isLoading || !prompt}>\r\n                    {isLoading ? <Loader2 className=\"mr-2 animate-spin\"/> : <Wand className=\"mr-2\" />}\r\n                    Generate\r\n                </Button>\r\n            </div>\r\n            {/* Canvas Area */}\r\n            <div className=\"flex-1 flex items-center justify-center p-4 overflow-hidden bg-muted/20\">\r\n                <div className=\"relative\">\r\n                    <canvas\r\n                        ref={imageCanvasRef}\r\n                        className=\"max-w-full max-h-full object-contain rounded-md shadow-lg\"\r\n                    />\r\n                    <canvas\r\n                        ref={drawingCanvasRef}\r\n                        className={cn(\r\n                            \"absolute top-0 left-0 max-w-full max-h-full object-contain cursor-crosshair\"\r\n                        )}\r\n                        onMouseDown={startDrawing}\r\n                        onMouseMove={draw}\r\n                        onMouseUp={stopDrawing}\r\n                        onMouseLeave={stopDrawing}\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAZA;;;;;;;;;;;AAqBO,SAAS,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAoB;;IAC7E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IACjD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAEnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAC9D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wCAAwC;IACxC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE1D,+BAA+B;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAS;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAE1E,MAAM,kBAAkB,YAAY,CAAC,kBAAkB;IAEvD,kDAAkD;IAClD,MAAM,YAAY,CAAC;QACf,MAAM,cAAc,eAAe,OAAO;QAC1C,MAAM,gBAAgB,iBAAiB,OAAO;QAC9C,IAAI,CAAC,eAAe,CAAC,eAAe;QAEpC,MAAM,WAAW,YAAY,UAAU,CAAC;QACxC,MAAM,aAAa,cAAc,UAAU,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,YAAY;QAE9B,MAAM,QAAQ,IAAI,OAAO,KAAK;QAC9B,MAAM,WAAW,GAAG;QACpB,MAAM,GAAG,GAAG;QACZ,MAAM,MAAM,GAAG;YACX,YAAY,KAAK,GAAG,MAAM,YAAY;YACtC,YAAY,MAAM,GAAG,MAAM,aAAa;YACxC,cAAc,KAAK,GAAG,MAAM,YAAY;YACxC,cAAc,MAAM,GAAG,MAAM,aAAa;YAE1C,SAAS,SAAS,CAAC,OAAO,GAAG;YAE7B,6CAA6C;YAC7C,WAAW,SAAS,CAAC,GAAG,GAAG,cAAc,KAAK,EAAE,cAAc,MAAM;YACpE,MAAM,mBAAmB,WAAW,YAAY,CAAC,GAAG,GAAG,cAAc,KAAK,EAAE,cAAc,MAAM;YAChG,eAAe;gBAAC;aAAiB;YACjC,oBAAoB;QACxB;QACA,MAAM,OAAO,GAAG;YACZ,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAuB,aAAa;YAAwC;QACvH;IACJ;IAEA,2FAA2F;IAC3F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,IAAI,iBAAiB;gBACjB,UAAU;YACd;QACJ;gCAAG;QAAC;KAAgB;IAGpB,MAAM,oBAAoB,IAAM,iBAAiB,OAAO,EAAE,WAAW,MAAM;YAAE,oBAAoB;QAAK;IAEtG,MAAM,oBAAoB;QACtB,MAAM,aAAa;QACnB,IAAI,CAAC,cAAc,CAAC,iBAAiB,OAAO,EAAE;QAE9C,MAAM,aAAa,YAAY,KAAK,CAAC,GAAG,mBAAmB;QAC3D,WAAW,IAAI,CAAC,WAAW,YAAY,CAAC,GAAG,GAAG,iBAAiB,OAAO,CAAC,KAAK,EAAE,iBAAiB,OAAO,CAAC,MAAM;QAC7G,eAAe;QACf,oBAAoB,WAAW,MAAM,GAAG;IAC5C;IAEA,MAAM,iBAAiB;QACnB,IAAI,mBAAmB,GAAG;YACtB,MAAM,WAAW,mBAAmB;YACpC,oBAAoB;YACpB,MAAM,aAAa;YACnB,IAAI,YAAY;gBACZ,WAAW,YAAY,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG;YACtD;QACJ;IACJ;IAEA,MAAM,iBAAiB;QACnB,IAAI,mBAAmB,YAAY,MAAM,GAAG,GAAG;YAC3C,MAAM,WAAW,mBAAmB;YACpC,oBAAoB;YACpB,MAAM,aAAa;YACnB,IAAI,YAAY;gBACZ,WAAW,YAAY,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG;YACtD;QACJ;IACJ;IAEA,MAAM,uBAAuB;QACzB,IAAI,oBAAoB,GAAG;YACvB,qBAAqB,CAAA,OAAQ,OAAO;QACxC;IACJ;IAEA,MAAM,cAAc,CAAC,QAA2B;QAC5C,MAAM,OAAO,OAAO,qBAAqB;QACzC,OAAO;YACH,GAAG,AAAC,CAAC,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,GAAI,OAAO,KAAK;YAC1D,GAAG,AAAC,CAAC,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM,GAAI,OAAO,MAAM;QAC/D;IACJ;IAEA,MAAM,eAAe,CAAC;QAClB,MAAM,SAAS,iBAAiB,OAAO;QACvC,MAAM,MAAM;QACZ,IAAI,CAAC,OAAO,CAAC,QAAQ;QAErB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,QAAQ;QACrC,aAAa;QAEb,IAAI,SAAS,WAAW,SAAS,UAAU;YACvC,IAAI,SAAS;YACb,IAAI,MAAM,CAAC,GAAG;YACd,IAAI,SAAS,GAAG;YAChB,IAAI,OAAO,GAAG;YACd,IAAI,QAAQ,GAAG;YACf,IAAI,WAAW,GAAG;YAClB,IAAI,wBAAwB,GAAG,SAAS,UAAU,gBAAgB;QACtE,OAAO,IAAI,SAAS,QAAQ;YACxB,aAAa;gBAAE;gBAAG;YAAE;QACxB;IACJ;IAEA,MAAM,OAAO,CAAC;QACV,IAAI,CAAC,WAAW;QAChB,MAAM,SAAS,iBAAiB,OAAO;QACvC,MAAM,MAAM;QACZ,IAAI,CAAC,OAAO,CAAC,QAAQ;QAErB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,QAAQ;QAErC,IAAI,SAAS,WAAW,SAAS,UAAU;YACvC,IAAI,MAAM,CAAC,GAAG;YACd,IAAI,MAAM;QACd,OAAO,IAAI,SAAS,UAAU,WAAW;YACrC,8CAA8C;YAC9C,IAAI,YAAY,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG;YACnD,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC;QAC7E;IACJ;IAEA,MAAM,cAAc,CAAC;QACjB,IAAI,CAAC,WAAW;QAChB,MAAM,SAAS,iBAAiB,OAAO;QACvC,MAAM,MAAM;QACZ,IAAI,CAAC,OAAO,CAAC,QAAQ;QAErB,IAAI,SAAS,WAAW,SAAS,UAAU;YACvC,IAAI,SAAS;QACjB,OAAO,IAAI,SAAS,UAAU,WAAW;YACrC,2CAA2C;YAC3C,IAAI,YAAY,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG;YACnD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY,QAAQ;YACrC,IAAI,wBAAwB,GAAG;YAC/B,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC;YACvE,aAAa;QACjB;QAEA,aAAa;QACb;IACJ;IAGA,MAAM,iBAAiB;QACnB,MAAM,gBAAgB,iBAAiB,OAAO;QAC9C,IAAI,CAAC,eAAe,OAAO;QAC3B,MAAM,MAAM;QACZ,IAAI,CAAC,KAAK,OAAO;QAEjB,MAAM,oBAAoB,IAAI,YAAY,CAAC,GAAG,GAAG,cAAc,KAAK,EAAE,cAAc,MAAM;QAC1F,MAAM,eAAe,kBAAkB,IAAI;QAE3C,MAAM,aAAa,SAAS,aAAa,CAAC;QAC1C,WAAW,KAAK,GAAG,cAAc,KAAK;QACtC,WAAW,MAAM,GAAG,cAAc,MAAM;QACxC,MAAM,UAAU,WAAW,UAAU,CAAC;QACtC,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,gBAAgB,QAAQ,eAAe,CAAC,cAAc,KAAK,EAAE,cAAc,MAAM;QACvF,MAAM,WAAW,cAAc,IAAI;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,EAAG;YAC7C,IAAI,YAAY,CAAC,IAAI,EAAE,GAAG,GAAG;gBACzB,QAAQ,CAAC,EAAE,GAAG,GAAO,YAAY;gBACjC,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI;gBACzB,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI;gBACzB,QAAQ,CAAC,IAAI,EAAE,GAAG,KAAK,aAAa;YACxC,OAAO;gBACH,QAAQ,CAAC,EAAE,GAAG,KAAO,YAAY;gBACjC,QAAQ,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI;gBAC3B,QAAQ,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI;gBAC3B,QAAQ,CAAC,IAAI,EAAE,GAAG,KAAK,aAAa;YACxC;QACJ;QAEA,QAAQ,YAAY,CAAC,eAAe,GAAG;QACvC,OAAO,WAAW,SAAS,CAAC;IAChC;IAEA,MAAM,iBAAiB;QACnB,IAAI,CAAC,OAAO,IAAI,IAAI;YAChB,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAsB,aAAa;YAA2C;YACrH;QACJ;QACA,aAAa;QAEb,MAAM,cAAc;QACpB,IAAI,CAAC,aAAa;YACd,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAc,aAAa;YAAoC;YACtG,aAAa;YACb;QACJ;QAEA,IAAI;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,8BAA2B,AAAD,EAC3C,QACA,SACA,iBACA,CAAC,CAAC,cACF,cACA;YAGJ,IAAI,OAAO,QAAQ,EAAE;gBACjB,MAAM,aAAa,aAAa,KAAK,CAAC,GAAG,oBAAoB;gBAC7D,WAAW,IAAI,CAAC,OAAO,QAAQ;gBAC/B,gBAAgB;gBAChB,qBAAqB,WAAW,MAAM,GAAG;gBACxC,MAAM;oBAAE,OAAO;oBAAkB,aAAa,OAAO,aAAa;gBAAC;YACxE,OAAO;gBACF,MAAM;oBAAE,SAAS;oBAAe,OAAO;oBAAqB,aAAa;gBAAkC;YAChH;QACJ,EAAE,OAAO,OAAO;YACZ,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAqB,aAAa,AAAC,MAAgB,OAAO;YAAC;QACtG,SAAU;YACN,aAAa;YACb,UAAU;QACd;IACJ;IAGA,qBACI,6LAAC;QAAI,WAAU;;0BAEX,6LAAC;gBAAI,WAAU;;kCACX,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;0CAClC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,SAAS;0CAAS,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGtE,6LAAC;wBAAI,WAAU;;0CACZ,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACN,6LAAC;gCAAI,WAAU;;kDACZ,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;wCAAgB,UAAU,oBAAoB;;4CAAG;0DAAC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAQ;;;;;;;kDAC7G,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;wCAAgB,UAAU,oBAAoB,YAAY,MAAM,GAAG;;0DAAG,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAQ;;;;;;;;;;;;;0CAEpI,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAsB,UAAU,qBAAqB;gCAAG,WAAU;;kDAClG,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAQ;;;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,SAAS,UAAU,cAAc;wCAAW,SAAS,IAAM,QAAQ;kDAAU,cAAA,6LAAC,uMAAA,CAAA,QAAK;;;;;;;;;;kDACpG,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,SAAS,SAAS,cAAc;wCAAW,SAAS,IAAM,QAAQ;kDAAS,cAAA,6LAAC,uOAAA,CAAA,sBAAmB;;;;;;;;;;kDAChH,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,SAAS,WAAW,cAAc;wCAAW,SAAS,IAAM,QAAQ;kDAAW,cAAA,6LAAC,yMAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;kCAI/G,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;;oCAAa;oCAAa;oCAAU;;;;;;;0CACnD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,IAAG;gCAAa,KAAK;gCAAG,KAAK;gCAAK,OAAO;oCAAC;iCAAU;gCAAE,eAAe,CAAC,IAAM,aAAa,CAAC,CAAC,EAAE;;;;;;;;;;;;kCAEzG,6LAAC,wIAAA,CAAA,YAAS;;;;;kCACV,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAiB;;;;;;0CAChC,6LAAC,oIAAA,CAAA,QAAK;gCACF,IAAG;gCACH,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,UAAU;;;;;;;;;;;;kCAGlB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAgB,UAAU,aAAa,CAAC;;4BACpD,0BAAY,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAAwB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAU;;;;;;;;;;;;;0BAK1F,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BACG,KAAK;4BACL,WAAU;;;;;;sCAEd,6LAAC;4BACG,KAAK;4BACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR;4BAEJ,aAAa;4BACb,aAAa;4BACb,WAAW;4BACX,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAMtC;GApUgB;;QAkBM,+HAAA,CAAA,WAAQ;;;KAlBd", "debugId": null}}, {"offset": {"line": 2710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/layout/unified-brand-layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { UnifiedBrandProvider, useUnifiedBrand, useBrandChangeListener } from '@/contexts/unified-brand-context';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\ninterface UnifiedBrandLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Inner component that uses the unified brand context\r\nfunction UnifiedBrandLayoutContent({ children }: UnifiedBrandLayoutProps) {\r\n  const { currentBrand, loading, error } = useUnifiedBrand();\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Listen for brand changes and log them\r\n  useBrandChangeListener((brand) => {\r\n    console.log('🔄 Brand changed in layout:', brand?.businessName || brand?.name || 'none');\r\n    \r\n    // Mark as initialized once we have a brand or finished loading\r\n    if (!isInitialized && (!loading || brand)) {\r\n      setIsInitialized(true);\r\n    }\r\n  });\r\n\r\n  // Show loading state while initializing\r\n  if (!isInitialized && loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading brand profiles...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if there's an error\r\n  if (error) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n            <span className=\"text-red-600 text-2xl\">⚠️</span>\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-red-900 mb-2\">Error Loading Brands</h2>\r\n          <p className=\"text-red-600 mb-4\">{error}</p>\r\n          <button \r\n            onClick={() => window.location.reload()} \r\n            className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"unified-brand-layout\">\r\n      {/* Debug info in development */}\r\n      {process.env.NODE_ENV === 'development' && (\r\n        <div className=\"fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl\">\r\n          <div>🔥 Unified Brand System</div>\r\n          <div>Brand: {currentBrand?.businessName || currentBrand?.name || 'None'}</div>\r\n          <div>ID: {currentBrand?.id || 'None'}</div>\r\n        </div>\r\n      )}\r\n      \r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Main layout component that provides the unified brand context\r\nexport function UnifiedBrandLayout({ children }: UnifiedBrandLayoutProps) {\r\n  return (\r\n    <UnifiedBrandProvider>\r\n      <UnifiedBrandLayoutContent>\r\n        {children}\r\n      </UnifiedBrandLayoutContent>\r\n    </UnifiedBrandProvider>\r\n  );\r\n}\r\n\r\n// Hook to make any component brand-aware\r\nexport function useBrandAware() {\r\n  const { currentBrand, selectBrand, loading } = useUnifiedBrand();\r\n  \r\n  return {\r\n    currentBrand,\r\n    selectBrand,\r\n    loading,\r\n    isReady: !loading && currentBrand !== null,\r\n    brandId: currentBrand?.id || null,\r\n    brandName: currentBrand?.businessName || currentBrand?.name || null,\r\n  };\r\n}\r\n\r\n// Higher-order component to make any component brand-aware\r\nexport function withBrandAware<P extends object>(\r\n  Component: React.ComponentType<P & { brand: CompleteBrandProfile | null }>\r\n) {\r\n  return function BrandAwareComponent(props: P) {\r\n    const { currentBrand } = useUnifiedBrand();\r\n    \r\n    return <Component {...props} brand={currentBrand} />;\r\n  };\r\n}\r\n\r\n// Component to show brand-specific content\r\ninterface BrandContentProps {\r\n  children: (brand: CompleteBrandProfile) => React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function BrandContent({ children, fallback }: BrandContentProps) {\r\n  const { currentBrand, loading } = useUnifiedBrand();\r\n  \r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  if (!currentBrand) {\r\n    return fallback || (\r\n      <div className=\"text-center p-8\">\r\n        <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n          <span className=\"text-gray-400 text-2xl\">🏢</span>\r\n        </div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Brand Selected</h3>\r\n        <p className=\"text-gray-600\">Please select a brand to continue.</p>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  return <>{children(currentBrand)}</>;\r\n}\r\n\r\n// Component to conditionally render content based on brand\r\ninterface ConditionalBrandContentProps {\r\n  brandId?: string;\r\n  brandName?: string;\r\n  children: React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function ConditionalBrandContent({ \r\n  brandId, \r\n  brandName, \r\n  children, \r\n  fallback \r\n}: ConditionalBrandContentProps) {\r\n  const { currentBrand } = useUnifiedBrand();\r\n  \r\n  const shouldRender = \r\n    (!brandId || currentBrand?.id === brandId) &&\r\n    (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);\r\n  \r\n  if (shouldRender) {\r\n    return <>{children}</>;\r\n  }\r\n  \r\n  return fallback || null;\r\n}\r\n\r\n// Hook to get brand-scoped data with automatic updates\r\nexport function useBrandScopedData<T>(\r\n  feature: string,\r\n  defaultValue: T,\r\n  loader?: (brandId: string) => T | Promise<T>\r\n): [T, (data: T) => void, boolean] {\r\n  const { currentBrand, getBrandStorage } = useUnifiedBrand();\r\n  const [data, setData] = useState<T>(defaultValue);\r\n  const [loading, setLoading] = useState(false);\r\n  \r\n  // Load data when brand changes\r\n  useEffect(() => {\r\n    if (!currentBrand?.id) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    const storage = getBrandStorage(feature);\r\n    if (!storage) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    \r\n    try {\r\n      if (loader) {\r\n        // Use custom loader\r\n        const result = loader(currentBrand.id);\r\n        if (result instanceof Promise) {\r\n          result.then(loadedData => {\r\n            setData(loadedData);\r\n            setLoading(false);\r\n          }).catch(error => {\r\n            console.error(`Failed to load ${feature} data:`, error);\r\n            setData(defaultValue);\r\n            setLoading(false);\r\n          });\r\n        } else {\r\n          setData(result);\r\n          setLoading(false);\r\n        }\r\n      } else {\r\n        // Use storage\r\n        const storedData = storage.getItem<T>();\r\n        setData(storedData || defaultValue);\r\n        setLoading(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to load ${feature} data:`, error);\r\n      setData(defaultValue);\r\n      setLoading(false);\r\n    }\r\n  }, [currentBrand?.id, feature, defaultValue, loader, getBrandStorage]);\r\n  \r\n  // Save data function\r\n  const saveData = (newData: T) => {\r\n    setData(newData);\r\n    \r\n    if (currentBrand?.id) {\r\n      const storage = getBrandStorage(feature);\r\n      if (storage) {\r\n        storage.setItem(newData);\r\n      }\r\n    }\r\n  };\r\n  \r\n  return [data, saveData, loading];\r\n}\r\n\r\n// Component to display brand switching status\r\nexport function BrandSwitchingStatus() {\r\n  const { loading, currentBrand } = useUnifiedBrand();\r\n  const [switching, setSwitching] = useState(false);\r\n  \r\n  useBrandChangeListener((brand) => {\r\n    setSwitching(true);\r\n    const timer = setTimeout(() => setSwitching(false), 1000);\r\n    return () => clearTimeout(timer);\r\n  });\r\n  \r\n  if (!switching && !loading) return null;\r\n  \r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n        <span className=\"text-sm\">\r\n          {switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AA6DO;;AA3DP;AACA;;;AAHA;;;AAUA,sDAAsD;AACtD,SAAS,0BAA0B,EAAE,QAAQ,EAA2B;;IACtE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD;4DAAE,CAAC;YACtB,QAAQ,GAAG,CAAC,+BAA+B,OAAO,gBAAgB,OAAO,QAAQ;YAEjF,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,KAAK,GAAG;gBACzC,iBAAiB;YACnB;QACF;;IAEA,wCAAwC;IACxC,IAAI,CAAC,iBAAiB,SAAS;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,uCAAuC;IACvC,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;kCAE1C,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,oDAAyB,+BACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAI;;;;;;kCACL,6LAAC;;4BAAI;4BAAQ,cAAc,gBAAgB,cAAc,QAAQ;;;;;;;kCACjE,6LAAC;;4BAAI;4BAAK,cAAc,MAAM;;;;;;;;;;;;;YAIjC;;;;;;;AAGP;GA7DS;;QACkC,kJAAA,CAAA,kBAAe;QAIxD,kJAAA,CAAA,yBAAsB;;;KALf;AAgEF,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,qBACE,6LAAC,kJAAA,CAAA,uBAAoB;kBACnB,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;MARgB;AAWT,SAAS;;IACd,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,WAAW,iBAAiB;QACtC,SAAS,cAAc,MAAM;QAC7B,WAAW,cAAc,gBAAgB,cAAc,QAAQ;IACjE;AACF;IAXgB;;QACiC,kJAAA,CAAA,kBAAe;;;AAazD,SAAS,eACd,SAA0E;;IAE1E,UAAO,SAAS,oBAAoB,KAAQ;;QAC1C,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;QAEvC,qBAAO,6LAAC;YAAW,GAAG,KAAK;YAAE,OAAO;;;;;;IACtC;;YAH2B,kJAAA,CAAA,kBAAe;;;AAI5C;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAqB;;IACpE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAEhD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,0BACL,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAyB;;;;;;;;;;;8BAE3C,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBAAO;kBAAG,SAAS;;AACrB;IAxBgB;;QACoB,kJAAA,CAAA,kBAAe;;;MADnC;AAkCT,SAAS,wBAAwB,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,EACqB;;IAC7B,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAEvC,MAAM,eACJ,CAAC,CAAC,WAAW,cAAc,OAAO,OAAO,KACzC,CAAC,CAAC,aAAa,cAAc,iBAAiB,aAAa,cAAc,SAAS,SAAS;IAE7F,IAAI,cAAc;QAChB,qBAAO;sBAAG;;IACZ;IAEA,OAAO,YAAY;AACrB;IAjBgB;;QAMW,kJAAA,CAAA,kBAAe;;;MAN1B;AAoBT,SAAS,mBACd,OAAe,EACf,YAAe,EACf,MAA4C;;IAE5C,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,cAAc,IAAI;gBACrB,QAAQ;gBACR;YACF;YAEA,MAAM,UAAU,gBAAgB;YAChC,IAAI,CAAC,SAAS;gBACZ,QAAQ;gBACR;YACF;YAEA,WAAW;YAEX,IAAI;gBACF,IAAI,QAAQ;oBACV,oBAAoB;oBACpB,MAAM,SAAS,OAAO,aAAa,EAAE;oBACrC,IAAI,kBAAkB,SAAS;wBAC7B,OAAO,IAAI;4DAAC,CAAA;gCACV,QAAQ;gCACR,WAAW;4BACb;2DAAG,KAAK;4DAAC,CAAA;gCACP,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,EAAE;gCACjD,QAAQ;gCACR,WAAW;4BACb;;oBACF,OAAO;wBACL,QAAQ;wBACR,WAAW;oBACb;gBACF,OAAO;oBACL,cAAc;oBACd,MAAM,aAAa,QAAQ,OAAO;oBAClC,QAAQ,cAAc;oBACtB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,EAAE;gBACjD,QAAQ;gBACR,WAAW;YACb;QACF;uCAAG;QAAC,cAAc;QAAI;QAAS;QAAc;QAAQ;KAAgB;IAErE,qBAAqB;IACrB,MAAM,WAAW,CAAC;QAChB,QAAQ;QAER,IAAI,cAAc,IAAI;YACpB,MAAM,UAAU,gBAAgB;YAChC,IAAI,SAAS;gBACX,QAAQ,OAAO,CAAC;YAClB;QACF;IACF;IAEA,OAAO;QAAC;QAAM;QAAU;KAAQ;AAClC;IAnEgB;;QAK4B,kJAAA,CAAA,kBAAe;;;AAiEpD,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD;uDAAE,CAAC;YACtB,aAAa;YACb,MAAM,QAAQ;qEAAW,IAAM,aAAa;oEAAQ;YACpD;+DAAO,IAAM,aAAa;;QAC5B;;IAEA,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BACb,YAAY,CAAC,aAAa,EAAE,cAAc,gBAAgB,cAAc,KAAK,GAAG,CAAC,GAAG;;;;;;;;;;;;;;;;;AAK/F;IAtBgB;;QACoB,kJAAA,CAAA,kBAAe;QAGjD,kJAAA,CAAA,yBAAsB;;;MAJR", "debugId": null}}, {"offset": {"line": 3179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/creative-studio/page.tsx"], "sourcesContent": ["// src/app/creative-studio/page.tsx\r\n\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport type { BrandProfile } from \"@/lib/types\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { SidebarInset } from \"@/components/ui/sidebar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { ChatLayout } from \"@/components/studio/chat-layout\";\r\nimport { User, Palette } from \"lucide-react\";\r\nimport { ImageEditor } from \"@/components/studio/image-editor\";\r\nimport { useUnifiedBrand, useBrandStorage, useBrandChangeListener } from \"@/contexts/unified-brand-context\";\r\nimport { UnifiedBrandLayout, BrandContent, BrandSwitchingStatus } from \"@/components/layout/unified-brand-layout\";\r\nimport { STORAGE_FEATURES } from \"@/lib/services/brand-scoped-storage\";\r\n\r\nfunction CreativeStudioPageContent() {\r\n  const { currentBrand } = useUnifiedBrand();\r\n  const [editorImage, setEditorImage] = useState<string | null>(null);\r\n  const creativeStudioStorage = useBrandStorage(STORAGE_FEATURES.CREATIVE_STUDIO);\r\n\r\n  // Load Creative Studio data when brand changes\r\n  useBrandChangeListener((brand) => {\r\n    if (brand) {\r\n      console.log(`🎨 Creative Studio: brand changed to: ${brand.businessName || brand.name}`);\r\n      // Load any Creative Studio specific data for this brand\r\n      if (creativeStudioStorage) {\r\n        const studioData = creativeStudioStorage.getItem();\r\n        if (studioData) {\r\n          console.log(`📂 Loaded Creative Studio data for brand ${brand.businessName || brand.name}`);\r\n          // Apply any saved studio settings here\r\n        }\r\n      }\r\n    } else {\r\n      console.log('🎨 Creative Studio: no brand selected');\r\n    }\r\n  });\r\n\r\n  // Convert CompleteBrandProfile to BrandProfile for compatibility\r\n  const brandProfile: BrandProfile | null = currentBrand ? {\r\n    businessName: currentBrand.businessName,\r\n    businessType: currentBrand.businessType,\r\n    location: currentBrand.location,\r\n    description: currentBrand.description,\r\n    targetAudience: currentBrand.targetAudience,\r\n    keyFeatures: currentBrand.keyFeatures,\r\n    competitiveAdvantages: currentBrand.competitiveAdvantages,\r\n    visualStyle: currentBrand.visualStyle,\r\n    writingTone: currentBrand.writingTone,\r\n    contentThemes: currentBrand.contentThemes,\r\n    primaryColor: currentBrand.primaryColor,\r\n    accentColor: currentBrand.accentColor,\r\n    backgroundColor: currentBrand.backgroundColor,\r\n    logoDataUrl: currentBrand.logoDataUrl,\r\n    websiteUrl: currentBrand.websiteUrl,\r\n    socialMedia: {\r\n      facebook: currentBrand.facebookUrl,\r\n      instagram: currentBrand.instagramUrl,\r\n      twitter: currentBrand.twitterUrl,\r\n      linkedin: currentBrand.linkedinUrl,\r\n    },\r\n    contactInfo: {\r\n      phone: currentBrand.contactPhone,\r\n      email: currentBrand.contactEmail,\r\n      address: currentBrand.contactAddress,\r\n    },\r\n  } : null;\r\n\r\n  return (\r\n    <SidebarInset fullWidth>\r\n      <header className=\"flex h-14 items-center justify-between border-b bg-card px-4 lg:h-[60px] lg:px-6\">\r\n        <div />\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"secondary\" size=\"icon\" className=\"rounded-full\">\r\n              <Avatar>\r\n                <AvatarImage\r\n                  src=\"https://placehold.co/40x40.png\"\r\n                  alt=\"User\"\r\n                  data-ai-hint=\"user avatar\"\r\n                />\r\n                <AvatarFallback><User /></AvatarFallback>\r\n              </Avatar>\r\n              <span className=\"sr-only\">Toggle user menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuLabel>My Account</DropdownMenuLabel>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </header>\r\n      <main className=\"flex-1 overflow-auto\">\r\n        <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n              {editorImage ? (\r\n                <ImageEditor\r\n                  imageUrl={editorImage}\r\n                  onClose={() => setEditorImage(null)}\r\n                  brandProfile={brandProfile}\r\n                />\r\n              ) : (\r\n                <ChatLayout\r\n                  brandProfile={brandProfile}\r\n                  onEditImage={setEditorImage}\r\n                />\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </SidebarInset>\r\n  );\r\n}\r\n\r\nfunction CreativeStudioPage() {\r\n  return (\r\n    <BrandContent fallback={\r\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n            <Palette className=\"w-8 h-8 text-gray-400\" />\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">No Brand Selected</h2>\r\n          <p className=\"text-gray-600\">Please select a brand to access the Creative Studio.</p>\r\n        </div>\r\n      </div>\r\n    }>\r\n      {() => <CreativeStudioPageContent />}\r\n    </BrandContent>\r\n  );\r\n}\r\n\r\nfunction CreativeStudioPageWithUnifiedBrand() {\r\n  return (\r\n    <UnifiedBrandLayout>\r\n      <CreativeStudioPage />\r\n      <BrandSwitchingStatus />\r\n    </UnifiedBrandLayout>\r\n  );\r\n}\r\n\r\nexport default CreativeStudioPageWithUnifiedBrand;\r\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;AAGnC;AAEA;AAQA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;;;AAsBA,SAAS;;IACP,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,wBAAwB,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD,EAAE,uJAAA,CAAA,mBAAgB,CAAC,eAAe;IAE9E,+CAA+C;IAC/C,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD;4DAAE,CAAC;YACtB,IAAI,OAAO;gBACT,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,EAAE;gBACvF,wDAAwD;gBACxD,IAAI,uBAAuB;oBACzB,MAAM,aAAa,sBAAsB,OAAO;oBAChD,IAAI,YAAY;wBACd,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,MAAM,YAAY,IAAI,MAAM,IAAI,EAAE;oBAC1F,uCAAuC;oBACzC;gBACF;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF;;IAEA,iEAAiE;IACjE,MAAM,eAAoC,eAAe;QACvD,cAAc,aAAa,YAAY;QACvC,cAAc,aAAa,YAAY;QACvC,UAAU,aAAa,QAAQ;QAC/B,aAAa,aAAa,WAAW;QACrC,gBAAgB,aAAa,cAAc;QAC3C,aAAa,aAAa,WAAW;QACrC,uBAAuB,aAAa,qBAAqB;QACzD,aAAa,aAAa,WAAW;QACrC,aAAa,aAAa,WAAW;QACrC,eAAe,aAAa,aAAa;QACzC,cAAc,aAAa,YAAY;QACvC,aAAa,aAAa,WAAW;QACrC,iBAAiB,aAAa,eAAe;QAC7C,aAAa,aAAa,WAAW;QACrC,YAAY,aAAa,UAAU;QACnC,aAAa;YACX,UAAU,aAAa,WAAW;YAClC,WAAW,aAAa,YAAY;YACpC,SAAS,aAAa,UAAU;YAChC,UAAU,aAAa,WAAW;QACpC;QACA,aAAa;YACX,OAAO,aAAa,YAAY;YAChC,OAAO,aAAa,YAAY;YAChC,SAAS,aAAa,cAAc;QACtC;IACF,IAAI;IAEJ,qBACE,6LAAC,sIAAA,CAAA,eAAY;QAAC,SAAS;;0BACrB,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;;;;;kCACD,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAO,WAAU;;sDAChD,6LAAC,qIAAA,CAAA,SAAM;;8DACL,6LAAC,qIAAA,CAAA,cAAW;oDACV,KAAI;oDACJ,KAAI;oDACJ,gBAAa;;;;;;8DAEf,6LAAC,qIAAA,CAAA,iBAAc;8DAAC,cAAA,6LAAC,qMAAA,CAAA,OAAI;;;;;;;;;;;;;;;;sDAEvB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAM;0CACzB,cAAA,6LAAC,+IAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAIzB,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,4BACC,6LAAC,kJAAA,CAAA,cAAW;gCACV,UAAU;gCACV,SAAS,IAAM,eAAe;gCAC9B,cAAc;;;;;qDAGhB,6LAAC,iJAAA,CAAA,aAAU;gCACT,cAAc;gCACd,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B;GAjGS;;QACkB,kJAAA,CAAA,kBAAe;QAEV,kJAAA,CAAA,kBAAe;QAG7C,kJAAA,CAAA,yBAAsB;;;KANf;AAmGT,SAAS;IACP,qBACE,6LAAC,6JAAA,CAAA,eAAY;QAAC,wBACZ,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;kBAIhC,kBAAM,6LAAC;;;;;;;;;;AAGd;MAhBS;AAkBT,SAAS;IACP,qBACE,6LAAC,6JAAA,CAAA,qBAAkB;;0BACjB,6LAAC;;;;;0BACD,6LAAC,6JAAA,CAAA,uBAAoB;;;;;;;;;;;AAG3B;MAPS;uCASM", "debugId": null}}]}
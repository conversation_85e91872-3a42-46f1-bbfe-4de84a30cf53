module.exports = {

"[project]/src/ai/openai-enhanced-design.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_ai_flows_generate-creative-asset_ts_c8582b86._.js",
  "server/chunks/ssr/node_modules_openai_c85072cb._.js",
  "server/chunks/ssr/src_ai_25ae053d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/ai/openai-enhanced-design.ts [app-ssr] (ecmascript)");
    });
});
}}),

};
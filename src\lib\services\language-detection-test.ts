/**
 * Language Detection Test
 * Test the intelligent language detection system
 */

import { 
  detectLanguagesForLocation, 
  getPrimaryLanguageCode,
  generateLanguageInstruction 
} from './language-detection-service';
import { 
  detectAndPopulateLanguages,
  getLanguageInstructionForProfile 
} from './brand-language-service';
import type { BrandProfile } from '@/lib/types';

// Test cases for different locations
const testLocations = [
  'United States',
  'USA', 
  'New York, USA',
  'Spain',
  'Mexico',
  'France',
  'Saudi Arabia',
  'UAE',
  'United Arab Emirates',
  'Egypt',
  'Germany',
  'Italy',
  'Brazil',
  'China',
  'Japan',
  'India',
  'Unknown Location'
];

/**
 * Test language detection for various locations
 */
export function testLanguageDetection() {
  console.log('🌍 Testing Language Detection System\n');
  
  testLocations.forEach(location => {
    const detection = detectLanguagesForLocation(location);
    const primaryCode = getPrimaryLanguageCode(location);
    const instruction = generateLanguageInstruction(location);
    
    console.log(`📍 Location: ${location}`);
    console.log(`   Primary: ${detection.primaryLanguage.name} (${detection.primaryLanguage.nativeName})`);
    if (detection.secondaryLanguages?.length) {
      console.log(`   Secondary: ${detection.secondaryLanguages.map(l => l.name).join(', ')}`);
    }
    console.log(`   Region: ${detection.region}`);
    console.log(`   Code: ${primaryCode}`);
    console.log(`   RTL: ${detection.primaryLanguage.isRTL ? 'Yes' : 'No'}`);
    console.log('');
  });
}

/**
 * Test brand profile language integration
 */
export function testBrandProfileLanguages() {
  console.log('🏢 Testing Brand Profile Language Integration\n');
  
  const testProfiles: Partial<BrandProfile>[] = [
    {
      businessName: 'NYC Restaurant',
      businessType: 'Restaurant',
      location: 'New York, USA',
      logoDataUrl: '',
      visualStyle: 'modern',
      writingTone: 'friendly',
      contentThemes: 'food'
    },
    {
      businessName: 'Madrid Café',
      businessType: 'Café',
      location: 'Madrid, Spain',
      logoDataUrl: '',
      visualStyle: 'cozy',
      writingTone: 'warm',
      contentThemes: 'coffee'
    },
    {
      businessName: 'Dubai Tech',
      businessType: 'Technology',
      location: 'Dubai, UAE',
      logoDataUrl: '',
      visualStyle: 'professional',
      writingTone: 'professional',
      contentThemes: 'innovation'
    },
    {
      businessName: 'Paris Boutique',
      businessType: 'Fashion',
      location: 'Paris, France',
      logoDataUrl: '',
      visualStyle: 'elegant',
      writingTone: 'sophisticated',
      contentThemes: 'fashion'
    }
  ];

  testProfiles.forEach(profile => {
    const profileWithLanguages = detectAndPopulateLanguages(profile as BrandProfile);
    const instruction = getLanguageInstructionForProfile(profileWithLanguages);
    
    console.log(`🏢 Business: ${profile.businessName} (${profile.location})`);
    console.log(`   Detected Languages:`, profileWithLanguages.detectedLanguages);
    console.log(`   AI Instruction Preview:`);
    console.log(`   ${instruction.split('\n')[0]}`);
    console.log('');
  });
}

/**
 * Test language validation
 */
export function testLanguageValidation() {
  console.log('✅ Testing Language Validation\n');
  
  const testCases = [
    {
      location: 'United States',
      content: 'Welcome to our amazing restaurant! Fresh food daily.',
      shouldPass: true
    },
    {
      location: 'United States', 
      content: 'مرحبا بكم في مطعمنا الرائع', // Arabic text
      shouldPass: false
    },
    {
      location: 'Saudi Arabia',
      content: 'مرحبا بكم في مطعمنا الرائع', // Arabic text
      shouldPass: true
    },
    {
      location: 'Spain',
      content: '¡Bienvenidos a nuestro restaurante increíble!',
      shouldPass: true
    },
    {
      location: 'Spain',
      content: '欢迎来到我们的餐厅', // Chinese text
      shouldPass: false
    }
  ];

  testCases.forEach((testCase, index) => {
    const profile: BrandProfile = {
      businessName: `Test Business ${index + 1}`,
      businessType: 'Restaurant',
      location: testCase.location,
      logoDataUrl: '',
      visualStyle: 'modern',
      writingTone: 'friendly',
      contentThemes: 'food'
    };

    const profileWithLanguages = detectAndPopulateLanguages(profile);
    
    // Import validation function dynamically to avoid circular imports
    import('./brand-language-service').then(({ validateContentLanguage }) => {
      const validation = validateContentLanguage(testCase.content, profileWithLanguages);
      
      console.log(`Test ${index + 1}: ${testCase.location}`);
      console.log(`   Content: "${testCase.content}"`);
      console.log(`   Expected: ${testCase.shouldPass ? 'PASS' : 'FAIL'}`);
      console.log(`   Result: ${validation.isValid ? 'PASS' : 'FAIL'}`);
      console.log(`   Expected Language: ${validation.expectedLanguage}`);
      if (validation.detectedIssues.length > 0) {
        console.log(`   Issues: ${validation.detectedIssues.join(', ')}`);
      }
      console.log('');
    });
  });
}

/**
 * Run all tests
 */
export function runLanguageDetectionTests() {
  console.log('🧪 Running Language Detection System Tests\n');
  console.log('='.repeat(50));
  
  testLanguageDetection();
  console.log('='.repeat(50));
  
  testBrandProfileLanguages();
  console.log('='.repeat(50));
  
  testLanguageValidation();
  console.log('='.repeat(50));
  
  console.log('✅ Language Detection Tests Complete!');
}

// Export for use in development/debugging
if (typeof window !== 'undefined') {
  (window as any).testLanguageDetection = runLanguageDetectionTests;
}

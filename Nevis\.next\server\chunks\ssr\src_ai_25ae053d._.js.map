{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/modern-design-prompts.ts"], "sourcesContent": ["/**\r\n * Modern Design Generation Prompts (2024-2025)\r\n * \r\n * Ultra-modern design specifications incorporating the latest design trends,\r\n * contemporary aesthetics, and cutting-edge visual techniques.\r\n */\r\n\r\nexport const MODERN_DESIGN_TRENDS_2024_2025 = `\r\n**GLASSMORPHISM & TRANSPARENCY EFFECTS:**\r\n- Semi-transparent backgrounds with frosted glass appearance\r\n- Subtle blur effects (backdrop-filter: blur(10px))\r\n- Layered transparency with depth perception\r\n- Light border highlights on glass elements\r\n- Soft, diffused lighting through transparent surfaces\r\n\r\n**NEUMORPHISM & SOFT UI:**\r\n- Subtle inset and outset shadows for depth\r\n- Soft, pillow-like button appearances\r\n- Minimal contrast with gentle elevation\r\n- Organic, rounded corner treatments\r\n- Tactile, touchable interface elements\r\n\r\n**ADVANCED GRADIENT TECHNIQUES:**\r\n- Multi-point gradient meshes with 3+ colors\r\n- Radial gradients with complex color stops\r\n- Gradient overlays for depth and atmosphere\r\n- Animated gradient effects (implied through positioning)\r\n- Color bleeding and soft transitions\r\n\r\n**CONTEMPORARY TYPOGRAPHY:**\r\n- Oversized, bold headlines (48px+ equivalent)\r\n- Modern sans-serif fonts (Inter, SF Pro, system-ui)\r\n- Creative letter spacing and line height\r\n- Typography as visual elements, not just text\r\n- Mixed font weights for hierarchy and contrast\r\n\r\n**ORGANIC & FLUID SHAPES:**\r\n- Blob-like, organic forms mixed with geometric elements\r\n- Flowing, curved lines and natural boundaries\r\n- Asymmetrical layouts with intentional imbalance\r\n- Liquid-like shapes and morphing elements\r\n- Nature-inspired forms and patterns\r\n`;\r\n\r\nexport const MODERN_COLOR_PSYCHOLOGY_2024 = `\r\n**VIBRANT & SATURATED PALETTES:**\r\n- High-contrast color combinations\r\n- Neon accents with dark backgrounds\r\n- Monochromatic schemes with pop colors\r\n- Gradient color stories across elements\r\n- Color psychology for emotional impact\r\n\r\n**TRENDING COLOR SCHEMES:**\r\n- Electric blues (#3B82F6, #6366F1, #8B5CF6)\r\n- Vibrant purples (#A855F7, #C084FC, #E879F9)\r\n- Energetic oranges (#F59E0B, #FB923C, #FDBA74)\r\n- Modern greens (#10B981, #34D399, #6EE7B7)\r\n- Contemporary pinks (#EC4899, #F472B6, #F9A8D4)\r\n\r\n**ADVANCED COLOR TECHNIQUES:**\r\n- 60-30-10 rule with modern twist\r\n- Analogous colors with high saturation\r\n- Complementary contrasts for visual impact\r\n- Triadic schemes for dynamic energy\r\n- Split-complementary for sophisticated balance\r\n`;\r\n\r\nexport const MODERN_LAYOUT_PRINCIPLES = `\r\n**ASYMMETRICAL COMPOSITIONS:**\r\n- Intentionally unbalanced layouts for visual interest\r\n- Dynamic grid systems with broken elements\r\n- Floating components with strategic placement\r\n- Visual weight distribution for modern appeal\r\n- Negative space as a design element\r\n\r\n**CONTEMPORARY SPACING:**\r\n- Generous white space (minimum 24px equivalent)\r\n- Rhythmic spacing patterns\r\n- Breathing room around key elements\r\n- Intentional crowding for emphasis\r\n- Micro-interactions through spacing\r\n\r\n**MODERN VISUAL HIERARCHY:**\r\n- Size contrast for immediate attention\r\n- Color contrast for emotional response\r\n- Position contrast for flow direction\r\n- Texture contrast for tactile appeal\r\n- Motion contrast for dynamic energy\r\n`;\r\n\r\nexport const MODERN_VISUAL_EFFECTS = `\r\n**ADVANCED SHADOW TECHNIQUES:**\r\n- Multiple light source shadows\r\n- Soft, realistic drop shadows (0 20px 25px rgba(0,0,0,0.1))\r\n- Inner shadows for depth perception\r\n- Colored shadows matching brand palette\r\n- Layered shadows for complex depth\r\n\r\n**CONTEMPORARY TEXTURES:**\r\n- Subtle noise overlays (opacity: 0.03-0.05)\r\n- Organic grain patterns\r\n- Geometric texture patterns\r\n- Paper-like textures for warmth\r\n- Digital glitch effects for tech brands\r\n\r\n**MODERN LIGHTING EFFECTS:**\r\n- Rim lighting on key elements\r\n- Ambient lighting for atmosphere\r\n- Directional lighting for drama\r\n- Soft, diffused lighting for approachability\r\n- High-contrast lighting for impact\r\n`;\r\n\r\nexport const PLATFORM_MODERN_OPTIMIZATIONS = {\r\n  instagram: `\r\n**INSTAGRAM 2024-2025 TRENDS:**\r\n- Bold, thumb-stopping visuals for feed\r\n- High contrast for mobile viewing\r\n- Story-optimized vertical compositions\r\n- Reel-ready dynamic layouts\r\n- Carousel-friendly modular designs\r\n- Modern hashtag integration\r\n- Contemporary emoji usage patterns\r\n`,\r\n\r\n  linkedin: `\r\n**LINKEDIN PROFESSIONAL MODERN:**\r\n- Sophisticated color palettes\r\n- Professional typography with personality\r\n- Clean, minimal layouts with impact\r\n- Industry-appropriate modern trends\r\n- Thought leadership visual language\r\n- Corporate glassmorphism effects\r\n- Professional gradient applications\r\n`,\r\n\r\n  facebook: `\r\n**FACEBOOK CONTEMPORARY DESIGN:**\r\n- Community-focused visual language\r\n- Warm, approachable modern aesthetics\r\n- Multi-generational appeal\r\n- Accessible modern design principles\r\n- Social proof integration\r\n- Contemporary engagement patterns\r\n- Modern storytelling visuals\r\n`,\r\n\r\n  twitter: `\r\n**TWITTER/X MODERN AESTHETICS:**\r\n- Quick-scan optimized layouts\r\n- High-impact minimal designs\r\n- Trending topic integration\r\n- Modern conversation starters\r\n- Contemporary meme aesthetics\r\n- Real-time relevant visuals\r\n- Modern social commentary style\r\n`,\r\n\r\n  tiktok: `\r\n**TIKTOK CUTTING-EDGE TRENDS:**\r\n- Gen Z optimized aesthetics\r\n- Vertical-first modern design\r\n- Trend-aware visual language\r\n- Contemporary youth culture\r\n- Modern video-ready layouts\r\n- Dynamic, energetic compositions\r\n- Current viral visual patterns\r\n`\r\n};\r\n\r\nexport const BUSINESS_TYPE_MODERN_DNA = {\r\n  tech: `\r\n**TECH MODERN AESTHETICS:**\r\n- Futuristic gradient applications\r\n- Digital-first design language\r\n- AI-inspired visual elements\r\n- Cyberpunk color influences\r\n- Modern data visualization\r\n- Contemporary tech iconography\r\n- Innovation-focused compositions\r\n`,\r\n\r\n  healthcare: `\r\n**HEALTHCARE CONTEMPORARY DESIGN:**\r\n- Trustworthy modern aesthetics\r\n- Calming gradient applications\r\n- Professional glassmorphism\r\n- Accessible modern typography\r\n- Contemporary wellness visuals\r\n- Modern medical iconography\r\n- Healing-focused color psychology\r\n`,\r\n\r\n  finance: `\r\n**FINANCE MODERN SOPHISTICATION:**\r\n- Premium gradient applications\r\n- Trustworthy contemporary design\r\n- Professional modern aesthetics\r\n- Sophisticated color palettes\r\n- Modern financial iconography\r\n- Contemporary wealth visuals\r\n- Security-focused design language\r\n`,\r\n\r\n  retail: `\r\n**RETAIL CONTEMPORARY TRENDS:**\r\n- Shopping-optimized modern design\r\n- Product-focused compositions\r\n- Contemporary e-commerce aesthetics\r\n- Modern lifestyle integration\r\n- Trend-aware visual language\r\n- Contemporary consumer psychology\r\n- Modern brand storytelling\r\n`,\r\n\r\n  restaurant: `\r\n**RESTAURANT MODERN APPEAL:**\r\n- Food-focused contemporary design\r\n- Appetite-appealing modern colors\r\n- Contemporary culinary aesthetics\r\n- Modern dining experience visuals\r\n- Trend-aware food photography style\r\n- Contemporary hospitality design\r\n- Modern taste visualization\r\n`\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;AAEM,MAAM,iCAAiC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmC/C,CAAC;AAEM,MAAM,+BAA+B,CAAC;;;;;;;;;;;;;;;;;;;;;AAqB7C,CAAC;AAEM,MAAM,2BAA2B,CAAC;;;;;;;;;;;;;;;;;;;;;AAqBzC,CAAC;AAEM,MAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;;;;;;;AAqBtC,CAAC;AAEM,MAAM,gCAAgC;IAC3C,WAAW,CAAC;;;;;;;;;AASd,CAAC;IAEC,UAAU,CAAC;;;;;;;;;AASb,CAAC;IAEC,UAAU,CAAC;;;;;;;;;AASb,CAAC;IAEC,SAAS,CAAC;;;;;;;;;AASZ,CAAC;IAEC,QAAQ,CAAC;;;;;;;;;AASX,CAAC;AACD;AAEO,MAAM,2BAA2B;IACtC,MAAM,CAAC;;;;;;;;;AAST,CAAC;IAEC,YAAY,CAAC;;;;;;;;;AASf,CAAC;IAEC,SAAS,CAAC;;;;;;;;;AASZ,CAAC;IAEC,QAAQ,CAAC;;;;;;;;;AASX,CAAC;IAEC,YAAY,CAAC;;;;;;;;;AASf,CAAC;AACD", "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/prompts/text-readability-prompts.ts"], "sourcesContent": ["/**\n * Enhanced Text Readability Prompts\n * Prevents corrupted text generation and ensures readable output\n */\n\nexport const ANTI_CORRUPTION_PROMPT = `\n🚨 CRITICAL TEXT GENERATION RULES - ZERO TOLERANCE FOR CORRUPTION 🚨\n\nABSOLUTE REQUIREMENTS:\n1. ALL TEXT MUST BE IN CLEAR, READABLE ENGLISH\n2. NO GARBLED, CORRUPTED, OR NONSENSICAL CHARACTER SEQUENCES\n3. NO RANDOM LETTER COMBINATIONS LIKE \"AUTTENG\", \"BAMALE\", \"COMEASUE\"\n4. NO BROKEN WORDS OR FRAGMENTED TEXT\n5. NO ENCODING ERRORS OR CHARACTER CORRUPTION\n\nFORBIDDEN TEXT PATTERNS:\n❌ \"AUTTENG\" - NEVER generate corrupted words\n❌ \"BAMALE\" - NEVE<PERSON> generate broken text\n❌ \"COMEASUE\" - NEVER generate garbled sequences\n❌ \"repairent tyaathfcoligetrick\" - NEVER generate nonsense\n❌ \"marchtstrg, areadnr, gaod\" - NEVER generate corrupted words\n❌ Any text that looks like encoding errors\n❌ Any text with random character combinations\n❌ Any text that is not proper English words\n\nREQUIRED TEXT QUALITY:\n✅ Use ONLY proper English words from standard dictionary\n✅ Ensure every word is spelled correctly\n✅ Use clear, professional business language\n✅ Make text large, bold, and highly readable\n✅ Apply high contrast between text and background\n✅ Use clean, modern typography\n✅ Ensure text is the focal point of the design\n\nTEXT VALIDATION CHECKLIST:\n- Is every word a real English word? ✅\n- Can the text be read clearly? ✅\n- Does the text make business sense? ✅\n- Is the spelling perfect? ✅\n- Are there any corrupted characters? ❌\n- Are there any nonsense sequences? ❌\n`;\n\nexport const READABLE_TEXT_INSTRUCTIONS = `\n📝 TEXT READABILITY REQUIREMENTS:\n\nTYPOGRAPHY STANDARDS:\n- Font size: Large and bold (minimum 24pt equivalent)\n- Font family: Clean, modern sans-serif (Arial, Helvetica, Roboto)\n- Font weight: Bold or semi-bold for maximum readability\n- Letter spacing: Optimal spacing for clarity\n- Line height: 1.2-1.4 for multi-line text\n\nCONTRAST REQUIREMENTS:\n- Minimum contrast ratio: 4.5:1 (WCAG AA standard)\n- Preferred contrast ratio: 7:1 (WCAG AAA standard)\n- Use dark text on light backgrounds OR light text on dark backgrounds\n- Add text shadows or outlines if needed for visibility\n- Apply semi-transparent backgrounds behind text if necessary\n\nPOSITIONING GUIDELINES:\n- Place text in high-visibility areas\n- Use rule of thirds for optimal placement\n- Ensure text doesn't overlap with busy background elements\n- Leave adequate white space around text\n- Center important text for maximum impact\n\nCOLOR COMBINATIONS (HIGH CONTRAST):\n✅ Black text on white background\n✅ White text on dark blue background\n✅ Dark navy text on light gray background\n✅ White text on black background\n✅ Dark text on yellow/bright background\n❌ Light gray text on white background\n❌ Dark blue text on black background\n❌ Red text on green background\n❌ Any low-contrast combinations\n`;\n\nexport const TEXT_GENERATION_SAFEGUARDS = `\n🛡️ TEXT GENERATION SAFEGUARDS:\n\nLANGUAGE VALIDATION:\n1. Generate text in proper English only\n2. Use business-appropriate vocabulary\n3. Ensure grammatical correctness\n4. Avoid slang, jargon, or technical terms\n5. Use clear, concise messaging\n\nCORRUPTION PREVENTION:\n1. Never generate random character sequences\n2. Always use complete, real words\n3. Ensure proper word spacing\n4. Maintain consistent capitalization\n5. Use standard punctuation only\n\nQUALITY ASSURANCE:\n1. Every word must pass spell-check\n2. Text must make logical business sense\n3. Avoid repetitive or redundant phrases\n4. Use action-oriented language\n5. Ensure professional tone throughout\n\nFALLBACK PROTOCOLS:\nIf uncertain about text generation:\n- Use simple, clear business terms\n- Default to \"Professional Services\"\n- Avoid complex or unusual words\n- Keep messages short and direct\n- Prioritize clarity over creativity\n`;\n\nexport const ENHANCED_PROMPT_TEMPLATE = (imageText: string) => `\n${ANTI_CORRUPTION_PROMPT}\n\nTARGET TEXT TO DISPLAY: \"${imageText}\"\n\n${READABLE_TEXT_INSTRUCTIONS}\n\n${TEXT_GENERATION_SAFEGUARDS}\n\nFINAL VALIDATION:\n- Verify the text \"${imageText}\" is displayed exactly as provided\n- Ensure no corruption or alteration of the original text\n- Confirm text is large, bold, and highly readable\n- Check that contrast meets accessibility standards\n- Validate that text integrates well with the design\n\nCRITICAL SUCCESS CRITERIA:\n✅ Text is identical to provided input: \"${imageText}\"\n✅ Text is completely readable and clear\n✅ No corrupted or garbled characters\n✅ Professional typography and layout\n✅ High contrast and visibility\n✅ Proper English spelling and grammar\n`;\n\nexport const NEGATIVE_PROMPT_ADDITIONS = `\nblurry text, unreadable text, distorted text, pixelated text, corrupted text, \ngarbled text, nonsense text, random characters, encoding errors, broken words, \nfragmented text, illegible text, poor typography, low contrast text, \nmicroscopic text, overlapping text, cut-off text, partial text, \n\"AUTTENG\", \"BAMALE\", \"COMEASUE\", corrupted character sequences,\nrandom letter combinations, broken English, gibberish, malformed text,\ncharacter corruption, encoding issues, text artifacts, scrambled letters\n`;\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAEM,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCvC,CAAC;AAEM,MAAM,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkC3C,CAAC;AAEM,MAAM,6BAA6B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+B3C,CAAC;AAEM,MAAM,2BAA2B,CAAC,YAAsB,CAAC;AAChE,EAAE,uBAAuB;;yBAEA,EAAE,UAAU;;AAErC,EAAE,2BAA2B;;AAE7B,EAAE,2BAA2B;;;mBAGV,EAAE,UAAU;;;;;;;wCAOS,EAAE,UAAU;;;;;;AAMpD,CAAC;AAEM,MAAM,4BAA4B,CAAC;;;;;;;;AAQ1C,CAAC", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/ai/openai-enhanced-design.ts"], "sourcesContent": ["import OpenAI from 'openai';\r\nimport { BrandProfile } from '@/lib/types';\r\nimport {\r\n  MODERN_DESIGN_TRENDS_2024_2025,\r\n  MODERN_COLOR_PSYCHOLOGY_2024,\r\n  <PERSON><PERSON><PERSON><PERSON>_LAYOUT_PRINCIPLES,\r\n  <PERSON><PERSON><PERSON><PERSON>_VISUAL_EFFECTS,\r\n  PLATFORM_MODERN_OPTIMIZATIONS,\r\n  BUSINESS_TYPE_MODERN_DNA\r\n} from './prompts/modern-design-prompts';\r\nimport {\r\n  ANTI_CORRUPTION_PROMPT,\r\n  READABLE_TEXT_INSTRUCTIONS,\r\n  TEXT_GENERATION_SAFEGUARDS,\r\n  NEGATIVE_PROMPT_ADDITIONS\r\n} from './prompts/text-readability-prompts';\r\n\r\n// Initialize OpenAI client with latest configuration\r\nconst openai = new OpenAI({\r\n  apiKey: process.env.OPENAI_API_KEY || '',\r\n  // Use latest API version for optimal performance\r\n  defaultHeaders: {\r\n    'OpenAI-Beta': 'assistants=v2', // Enable latest features\r\n  },\r\n  timeout: 60000, // 60 second timeout for image generation\r\n  maxRetries: 3, // Retry failed requests up to 3 times\r\n});\r\n\r\nexport interface OpenAIEnhancedDesignInput {\r\n  businessType: string;\r\n  platform: string;\r\n  visualStyle: string;\r\n  imageText: string;\r\n  brandProfile: BrandProfile;\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  };\r\n  artifactInstructions?: string;\r\n}\r\n\r\nexport interface OpenAIEnhancedDesignResult {\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}\r\n\r\n/**\r\n * Generate enhanced design using OpenAI DALL-E 3\r\n * This provides superior text readability, brand color compliance, and design quality\r\n */\r\nexport async function generateOpenAIEnhancedDesign(\r\n  input: OpenAIEnhancedDesignInput\r\n): Promise<OpenAIEnhancedDesignResult> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!process.env.OPENAI_API_KEY) {\r\n      throw new Error('OpenAI API key is required. Please set OPENAI_API_KEY environment variable.');\r\n    }\r\n\r\n    // Validate and clean the text input\r\n    const cleanedText = validateAndCleanText(input.imageText);\r\n    const inputWithCleanText = { ...input, imageText: cleanedText };\r\n\r\n    // Build enhanced prompt optimized for DALL-E 3\r\n    const enhancedPrompt = buildDALLE3Prompt(inputWithCleanText);\r\n    enhancementsApplied.push('DALL-E 3 Optimized Prompting', 'Text Validation & Cleaning');\r\n\r\n    console.log('🎨 Generating enhanced design with OpenAI DALL-E 3...');\r\n    console.log('📝 Original text:', `\"${input.imageText}\"`);\r\n    console.log('🧹 Cleaned text:', `\"${cleanedText}\"`);\r\n    console.log('🔄 Text changed:', input.imageText !== cleanedText ? 'YES' : 'NO');\r\n    console.log('📏 Prompt length:', enhancedPrompt.length);\r\n    console.log('🎯 Full prompt preview:', enhancedPrompt.substring(0, 200) + '...');\r\n\r\n    // Generate image with GPT-Image 1 (Latest OpenAI Image Model - 2025)\r\n    // GPT-Image 1 replaced DALL-E 3 as the most advanced OpenAI image model\r\n    // Enhanced for MAXIMUM QUALITY and PERFECT FACE RENDERING\r\n    const response = await openai.images.generate({\r\n      model: 'gpt-image-1', // Latest OpenAI image model (successor to DALL-E 3)\r\n      prompt: enhancedPrompt,\r\n      size: getPlatformSize(input.platform), // Using highest available resolution\r\n      quality: 'hd', // MAXIMUM quality setting - highest available\r\n      style: getGPTImageStyle(input.visualStyle), // Optimized style for quality\r\n      n: 1, // GPT-Image 1 supports single high-quality generation\r\n      response_format: 'url', // Explicitly request URL format\r\n    });\r\n\r\n    const imageUrl = response.data[0]?.url;\r\n    if (!imageUrl) {\r\n      throw new Error('No image URL returned from OpenAI');\r\n    }\r\n\r\n    enhancementsApplied.push(\r\n      'Professional Design Principles',\r\n      'Brand Color Compliance',\r\n      'Text Readability Optimization',\r\n      'Platform Optimization',\r\n      'HD Quality Generation'\r\n    );\r\n\r\n    if (input.brandConsistency?.strictConsistency) {\r\n      enhancementsApplied.push('Strict Design Consistency');\r\n    }\r\n\r\n    if (input.brandConsistency?.followBrandColors) {\r\n      enhancementsApplied.push('Brand Color Enforcement');\r\n    }\r\n\r\n    console.log('✅ Enhanced design generated successfully');\r\n    console.log('🔗 Image URL:', imageUrl);\r\n\r\n    return {\r\n      imageUrl,\r\n      qualityScore: 9.9, // GPT-Image 1 Ultra-HD - Maximum quality with perfect face rendering\r\n      enhancementsApplied,\r\n      processingTime: Date.now() - startTime,\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error generating OpenAI enhanced design:', error);\r\n    throw new Error(`OpenAI enhanced design generation failed: ${(error as Error).message}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Build optimized prompt for DALL-E 3 (Latest OpenAI Image Model)\r\n * Enhanced with 2024 best practices for maximum quality and accuracy\r\n */\r\nfunction buildDALLE3Prompt(input: OpenAIEnhancedDesignInput): string {\r\n  const { businessType, platform, visualStyle, imageText, brandProfile, brandConsistency, artifactInstructions } = input;\r\n\r\n  // Generate unique variation elements for each request\r\n  const generationId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n  const variationSeed = Math.floor(Math.random() * 1000);\r\n\r\n  // Random layout variations\r\n  const layoutVariations = [\r\n    'asymmetrical composition with dynamic balance',\r\n    'grid-based layout with clean alignment',\r\n    'centered composition with radial elements',\r\n    'diagonal flow with leading lines',\r\n    'layered depth with foreground/background separation'\r\n  ];\r\n  const selectedLayout = layoutVariations[Math.floor(Math.random() * layoutVariations.length)];\r\n\r\n  // Random style modifiers\r\n  const styleModifiers = [\r\n    'with subtle gradient overlays',\r\n    'with bold geometric accents',\r\n    'with organic flowing elements',\r\n    'with modern minimalist approach',\r\n    'with dynamic energy and movement'\r\n  ];\r\n  const selectedModifier = styleModifiers[Math.floor(Math.random() * styleModifiers.length)];\r\n\r\n  // Enhanced color instructions optimized for DALL-E 3's color accuracy\r\n  const colorInstructions = brandProfile.primaryColor && brandProfile.accentColor\r\n    ? `Primary brand color: ${brandProfile.primaryColor}, Secondary brand color: ${brandProfile.accentColor}. Use these colors prominently and consistently throughout the design.`\r\n    : 'Use a cohesive, professional color palette with high contrast and modern appeal.';\r\n\r\n  // Advanced people inclusion logic for better engagement with HD face rendering\r\n  const shouldIncludePeople = shouldIncludePeopleInDesign(businessType, imageText, visualStyle);\r\n  const peopleInstructions = shouldIncludePeople\r\n    ? 'Include diverse, authentic people (various ethnicities, ages) with PERFECT FACIAL FEATURES - complete faces, symmetrical features, natural expressions, professional poses. Ensure faces are fully visible, well-lit, and anatomically correct with no deformations or missing features.'\r\n    : 'Focus on clean, minimalist design without people, emphasizing the product/service/message with ultra-sharp details.';\r\n\r\n  // Enhanced platform-specific optimization\r\n  const platformSpecs = getPlatformSpecifications(platform);\r\n\r\n  // Get platform-specific guidelines\r\n  const platformGuidelines = PLATFORM_SPECIFIC_GUIDELINES[platform.toLowerCase() as keyof typeof PLATFORM_SPECIFIC_GUIDELINES] || PLATFORM_SPECIFIC_GUIDELINES.instagram;\r\n\r\n  // Get business-specific design DNA\r\n  const businessDNA = BUSINESS_TYPE_DESIGN_DNA[businessType.toLowerCase() as keyof typeof BUSINESS_TYPE_DESIGN_DNA] || BUSINESS_TYPE_DESIGN_DNA.default;\r\n\r\n  // Build rich, diverse design prompt like standard generation\r\n  const prompt = `You are a world-class creative director and visual designer with expertise in social media marketing, brand design, and visual psychology.\r\n\r\n**DESIGN BRIEF:**\r\nCreate a professional, high-impact social media design for a ${businessType} business.\r\nTarget Platform: ${platform} | Aspect Ratio: 1:1 (1080x1080px)\r\nVisual Style: ${visualStyle} | Location: ${brandProfile.location || 'Global'}\r\n\r\n**TEXT CONTENT TO INCLUDE:**\r\nPrimary Text: \"${imageText}\"\r\n${brandProfile.businessName ? `Business Name: \"${brandProfile.businessName}\"` : ''}\r\n\r\n${ADVANCED_DESIGN_PRINCIPLES}\r\n\r\n${platformGuidelines}\r\n\r\n${businessDNA}\r\n\r\n**BRAND GUIDELINES:**\r\n${colorInstructions}\r\n${peopleInstructions}\r\n\r\n**CREATIVE VARIATION REQUIREMENTS:**\r\n- Layout Style: Use ${selectedLayout}\r\n- Design Approach: Create design ${selectedModifier}\r\n- Uniqueness: This design must be visually distinct from any previous generations\r\n- Generation ID: ${generationId} (use this to ensure uniqueness)\r\n- Variation Seed: ${variationSeed} (apply subtle randomization based on this number)\r\n\r\n**PLATFORM SPECIFICATIONS:**\r\n${platformSpecs}\r\n\r\n**TEXT RENDERING REQUIREMENTS:**\r\n- Use the provided text: \"${imageText}\"\r\n- Ensure perfect text clarity and readability\r\n- High-quality typography with proper spacing\r\n- Professional font choices that match the visual style\r\n\r\n🧑 PERFECT HUMAN RENDERING (MANDATORY):\r\n- Complete, symmetrical faces with all features present\r\n- Natural, professional expressions with clear eyes\r\n- Proper anatomy with no deformations or missing parts\r\n- High-quality skin textures and realistic lighting\r\n- Diverse representation with authentic appearance\r\n\r\n🎨 ULTRA-MODERN DESIGN SPECIFICATIONS (2024-2025 TRENDS):\r\n\r\n**CONTEMPORARY VISUAL STYLE:**\r\n- ${visualStyle} with cutting-edge 2024-2025 design trends\r\n- Implement glassmorphism effects: frosted glass backgrounds with subtle transparency\r\n- Use neumorphism/soft UI: subtle shadows and highlights for depth\r\n- Apply modern gradient overlays: multi-directional, vibrant gradients\r\n- Include contemporary typography: bold, clean sans-serif fonts with perfect spacing\r\n- Modern color psychology: ${colorInstructions}\r\n\r\n**ADVANCED LAYOUT & COMPOSITION:**\r\n- Asymmetrical layouts with dynamic visual hierarchy\r\n- Generous white space with intentional negative space design\r\n- Floating elements with subtle drop shadows and depth\r\n- Modern grid systems with broken grid elements for visual interest\r\n- Contemporary card-based layouts with rounded corners and elevation\r\n\r\n**CUTTING-EDGE VISUAL EFFECTS:**\r\n- Glassmorphism: Semi-transparent backgrounds with blur effects\r\n- Gradient meshes: Complex, multi-point gradients for depth\r\n- Subtle animations implied through motion blur and dynamic positioning\r\n- Modern shadows: Soft, realistic shadows with multiple light sources\r\n- Contemporary textures: Subtle noise, grain, or organic patterns\r\n\r\n**2024-2025 DESIGN TRENDS:**\r\n- Bold, oversized typography with creative font pairings\r\n- Vibrant, saturated color palettes with high contrast\r\n- Organic shapes and fluid forms mixed with geometric elements\r\n- Modern iconography: minimal, line-based icons with perfect pixel alignment\r\n- Contemporary photography style: high contrast, vibrant, authentic moments\r\n\r\n**BRAND INTEGRATION:**\r\n- Brand Identity: ${brandProfile.businessName || businessType}\r\n- Platform Optimization: ${platformSpecs}\r\n- Human Elements: ${peopleInstructions}\r\n\r\n⚡ GPT-IMAGE 1 ULTRA-HD QUALITY ENHANCEMENTS:\r\n- MAXIMUM RESOLUTION: Ultra-high definition rendering (4K+ quality) for perfect small font clarity\r\n- SMALL FONT SIZE EXCELLENCE: Perfect rendering at 8pt, 10pt, 12pt, and all small font sizes\r\n- TINY TEXT PRECISION: Every character sharp and legible even when font size is very small\r\n- HIGH-DPI SMALL TEXT: Render small fonts as if on 300+ DPI display for maximum sharpness\r\n- MICRO-TYPOGRAPHY: Perfect letter formation and spacing at the smallest font sizes\r\n- PERFECT ANATOMY: Complete, symmetrical faces with natural expressions\r\n- SHARP DETAILS: Crystal-clear textures, no blur or artifacts\r\n- PROFESSIONAL LIGHTING: Studio-quality lighting with proper shadows\r\n- PREMIUM COMPOSITION: Golden ratio layouts with perfect balance\r\n- ADVANCED COLOR THEORY: Perfect contrast ratios (7:1 minimum) with vibrant, accurate colors\r\n- FLAWLESS RENDERING: No deformations, missing parts, or visual errors\r\n- PHOTOREALISTIC QUALITY: Magazine-level professional appearance\r\n- TEXT LEGIBILITY: All text sizes optimized for perfect readability and clarity\r\n\r\n🎨 MODERN DESIGN TRENDS (2024-2025):\r\n${MODERN_DESIGN_TRENDS_2024_2025}\r\n\r\n🌈 CONTEMPORARY COLOR PSYCHOLOGY:\r\n${MODERN_COLOR_PSYCHOLOGY_2024}\r\n\r\n📐 MODERN LAYOUT PRINCIPLES:\r\n${MODERN_LAYOUT_PRINCIPLES}\r\n\r\n✨ ADVANCED VISUAL EFFECTS:\r\n${MODERN_VISUAL_EFFECTS}\r\n\r\n📱 PLATFORM-SPECIFIC MODERN OPTIMIZATION:\r\n${PLATFORM_MODERN_OPTIMIZATIONS[platform.toLowerCase() as keyof typeof PLATFORM_MODERN_OPTIMIZATIONS] || PLATFORM_MODERN_OPTIMIZATIONS.instagram}\r\n\r\n🏢 BUSINESS-SPECIFIC MODERN DNA:\r\n${BUSINESS_TYPE_MODERN_DNA[businessType.toLowerCase() as keyof typeof BUSINESS_TYPE_MODERN_DNA] || BUSINESS_TYPE_MODERN_DNA.tech}\r\n\r\n${artifactInstructions ? `SPECIAL INSTRUCTIONS FROM UPLOADED CONTENT:\r\n${artifactInstructions}\r\n- Follow these instructions precisely when creating the design\r\n- These instructions specify how to use specific content elements\r\n\r\n` : ''}\r\n\r\n${ANTI_CORRUPTION_PROMPT}\r\n\r\n📝 ABSOLUTE TEXT ACCURACY REQUIREMENTS:\r\n- STRICT TEXT CONTROL: Use ONLY the exact text \"${imageText}\" - NO additional text allowed\r\n- NO RANDOM TEXT: Do not add placeholder text, lorem ipsum, sample content, or any extra words\r\n- NO FILLER CONTENT: Do not include random descriptions, fake company names, or dummy text\r\n- EXACT SPELLING: The text must be spelled EXACTLY as provided - do not alter any letters or words\r\n- SINGLE TEXT SOURCE: Only use the provided text \"${imageText}\" as the text content in the image\r\n- NO CORRUPTION: Absolutely NO garbled, corrupted, or nonsensical text like \"AUTTENG\", \"BAMALE\", \"COMEASUE\"\r\n\r\n${READABLE_TEXT_INSTRUCTIONS}\r\n\r\n- SMALL FONT SIZE HANDLING: When design requires small fonts (8pt-12pt), apply these rules:\r\n  * Increase contrast by 20% for small text visibility\r\n  * Use slightly bolder font weight to maintain character definition\r\n  * Ensure perfect pixel alignment for crisp edges\r\n  * Apply high-resolution anti-aliasing for smooth curves\r\n  * Maintain proper letter spacing even at small sizes\r\n- READABILITY GUARANTEE: Every character must be perfectly legible regardless of font size\r\n- PIXEL-PERFECT SMALL TEXT: Each letter rendered with maximum clarity at any size\r\n- BACKGROUND CONTRAST: Ensure sufficient contrast between small text and background\r\n\r\n🚫 ABSOLUTELY FORBIDDEN - WILL CAUSE FAILURE:\r\n- Do NOT add \"Payroll Banking Simplified\"\r\n- Do NOT add \"Banking Made Easy\"\r\n- Do NOT add \"Financial Services\"\r\n- Do NOT add \"Professional Banking\"\r\n- Do NOT add \"Secure Payments\"\r\n- Do NOT add \"Digital Banking\"\r\n- Do NOT add \"Money Management\"\r\n- Do NOT add ANY banking or financial terms\r\n- Do NOT add ANY business descriptions\r\n- Do NOT add ANY marketing copy\r\n- Do NOT add ANY placeholder text\r\n- Do NOT add ANY lorem ipsum\r\n- Do NOT add ANY sample content\r\n- Do NOT add ANY random words\r\n- Do NOT add ANY filler text\r\n- Do NOT create ANY fake headlines\r\n- Do NOT create ANY taglines\r\n- Do NOT generate corrupted text like \"AUTTENG\", \"BAMALE\", \"COMEASUE\"\r\n- Do NOT generate garbled character sequences\r\n- Do NOT generate nonsensical text combinations\r\n- CRITICAL: ONLY use the exact text: \"${imageText}\"\r\n- NOTHING ELSE IS ALLOWED\r\n\r\n${TEXT_GENERATION_SAFEGUARDS}\r\n\r\nNEGATIVE PROMPT ADDITIONS: ${NEGATIVE_PROMPT_ADDITIONS}`;\r\n\r\n  return prompt;\r\n}\r\n\r\n/**\r\n * Validate and clean text input for better DALL-E 3 results\r\n * MINIMAL cleaning to preserve text accuracy\r\n */\r\nfunction validateAndCleanText(text: string): string {\r\n  if (!text || text.trim().length === 0) {\r\n    return 'Professional Business Content';\r\n  }\r\n\r\n  let cleanedText = text.trim();\r\n\r\n  // Only remove truly problematic characters, preserve all letters and numbers\r\n  cleanedText = cleanedText\r\n    .replace(/[^\\w\\s\\-.,!?'\"()&%$#@]/g, '') // Keep more characters, only remove truly problematic ones\r\n    .replace(/\\s+/g, ' ') // Normalize whitespace only\r\n    .replace(/(.)\\1{4,}/g, '$1$1$1') // Only reduce excessive repetition (4+ chars -> 3)\r\n    .trim();\r\n\r\n  // Be more lenient with length - only trim if extremely long\r\n  if (cleanedText.length > 100) {\r\n    const words = cleanedText.split(' ');\r\n    if (words.length > 15) {\r\n      cleanedText = words.slice(0, 15).join(' ');\r\n    }\r\n  }\r\n\r\n  // Only fallback if completely empty\r\n  if (cleanedText.length === 0) {\r\n    return 'Professional Business Content';\r\n  }\r\n\r\n  return cleanedText;\r\n}\r\n\r\n/**\r\n * Determine if people should be included in the design\r\n */\r\nfunction shouldIncludePeopleInDesign(businessType: string, imageText: string, visualStyle: string): boolean {\r\n  const businessTypeLower = businessType.toLowerCase();\r\n  const imageTextLower = imageText.toLowerCase();\r\n  const visualStyleLower = visualStyle.toLowerCase();\r\n\r\n  // Business types that typically benefit from people\r\n  const peopleBusinessTypes = [\r\n    'fitness', 'gym', 'health', 'wellness', 'coaching', 'training',\r\n    'education', 'consulting', 'service', 'restaurant', 'retail',\r\n    'beauty', 'salon', 'spa', 'medical', 'dental', 'therapy'\r\n  ];\r\n\r\n  // Content that suggests people\r\n  const peopleContent = [\r\n    'team', 'customer', 'client', 'people', 'community', 'join',\r\n    'experience', 'service', 'help', 'support', 'training', 'class'\r\n  ];\r\n\r\n  // Visual styles that work well with people\r\n  const peopleStyles = ['lifestyle', 'authentic', 'personal', 'friendly', 'approachable'];\r\n\r\n  const hasPeopleBusinessType = peopleBusinessTypes.some(type => businessTypeLower.includes(type));\r\n  const hasPeopleContent = peopleContent.some(content => imageTextLower.includes(content));\r\n  const hasPeopleStyle = peopleStyles.some(style => visualStyleLower.includes(style));\r\n\r\n  return hasPeopleBusinessType || hasPeopleContent || hasPeopleStyle;\r\n}\r\n\r\n/**\r\n * Get platform-specific specifications for DALL-E 3 optimization\r\n */\r\nfunction getPlatformSpecifications(platform: string): string {\r\n  const platformLower = platform.toLowerCase();\r\n\r\n  if (platformLower.includes('instagram')) {\r\n    if (platformLower.includes('story')) {\r\n      return 'Instagram Story format (9:16 aspect ratio) with mobile-first design, thumb-stopping visuals, and story-specific UI considerations';\r\n    }\r\n    return 'Instagram feed post with square format, high engagement design, and mobile-optimized visual hierarchy';\r\n  }\r\n\r\n  if (platformLower.includes('linkedin')) {\r\n    return 'LinkedIn professional format with business-focused design, corporate aesthetics, and B2B appeal';\r\n  }\r\n\r\n  if (platformLower.includes('facebook')) {\r\n    return 'Facebook post format with broad audience appeal, social sharing optimization, and news feed visibility';\r\n  }\r\n\r\n  if (platformLower.includes('twitter') || platformLower.includes('x')) {\r\n    return 'Twitter/X format with concise visual messaging, trending topic relevance, and retweet optimization';\r\n  }\r\n\r\n  return 'Universal social media format with cross-platform compatibility and maximum engagement potential';\r\n}\r\n\r\n/**\r\n * Get appropriate image size for platform (DALL-E 3 Latest Model)\r\n * DALL-E 3 supports: 1024x1024, 1792x1024, 1024x1792\r\n */\r\nfunction getPlatformSize(platform: string): '1024x1024' | '1792x1024' | '1024x1792' {\r\n  const platformLower = platform.toLowerCase();\r\n\r\n  // Vertical formats (9:16 aspect ratio)\r\n  if (platformLower.includes('story') ||\r\n    platformLower.includes('reel') ||\r\n    platformLower.includes('tiktok') ||\r\n    platformLower.includes('youtube short')) {\r\n    return '1024x1792';\r\n  }\r\n\r\n  // Horizontal formats (16:9 aspect ratio)\r\n  if (platformLower.includes('linkedin') ||\r\n    platformLower.includes('twitter') ||\r\n    platformLower.includes('facebook') ||\r\n    platformLower.includes('youtube') ||\r\n    platformLower.includes('banner')) {\r\n    return '1792x1024';\r\n  }\r\n\r\n  // Square format (1:1 aspect ratio) - default for most social media posts\r\n  return '1024x1024';\r\n}\r\n\r\n/**\r\n * Get GPT-Image 1 style based on visual style for maximum quality\r\n */\r\nfunction getGPTImageStyle(visualStyle: string): 'vivid' | 'natural' {\r\n  const styleLower = visualStyle.toLowerCase();\r\n\r\n  if (styleLower.includes('vibrant') || styleLower.includes('bold') || styleLower.includes('modern')) {\r\n    return 'vivid';\r\n  } else {\r\n    return 'natural';\r\n  }\r\n}\r\n\r\n/**\r\n * Fallback to Gemini if OpenAI fails\r\n */\r\nexport async function generateEnhancedDesignWithFallback(\r\n  input: OpenAIEnhancedDesignInput\r\n): Promise<OpenAIEnhancedDesignResult> {\r\n  try {\r\n    // Try OpenAI first\r\n    return await generateOpenAIEnhancedDesign(input);\r\n  } catch (error) {\r\n    console.warn('⚠️ OpenAI failed, falling back to Gemini:', error);\r\n\r\n    // Import and use the existing Gemini-based creative asset flow\r\n    const { generateCreativeAsset } = await import('@/ai/flows/generate-creative-asset');\r\n\r\n    // Build enhanced prompt for Gemini fallback\r\n    const enhancedPrompt = buildGeminiFallbackPrompt(input);\r\n\r\n    const result = await generateCreativeAsset({\r\n      prompt: enhancedPrompt,\r\n      outputType: 'image' as const,\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: input.brandProfile,\r\n      maskDataUrl: null,\r\n    });\r\n\r\n    return {\r\n      imageUrl: result.imageUrl || '',\r\n      qualityScore: 7.5, // Lower score for fallback\r\n      enhancementsApplied: ['Gemini Fallback', 'Enhanced Prompting', 'Brand Integration'],\r\n      processingTime: Date.now() - Date.now(), // Approximate\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Build enhanced prompt for Gemini fallback\r\n */\r\nfunction buildGeminiFallbackPrompt(input: OpenAIEnhancedDesignInput): string {\r\n  const { businessType, platform, visualStyle, imageText, brandProfile, brandConsistency, artifactInstructions } = input;\r\n\r\n  const colorInstructions = brandProfile.primaryColor && brandProfile.accentColor && brandProfile.backgroundColor\r\n    ? `**MANDATORY BRAND COLORS - MUST USE EXACTLY:**\r\n       - Primary Color: ${brandProfile.primaryColor} (use as main brand color)\r\n       - Accent Color: ${brandProfile.accentColor} (use for highlights and CTAs)\r\n       - Background Color: ${brandProfile.backgroundColor} (use as base background)\r\n       - These colors are REQUIRED and must be prominently featured in the design`\r\n    : '';\r\n\r\n  const designExampleInstructions = brandConsistency?.strictConsistency && brandProfile.designExamples && brandProfile.designExamples.length > 0\r\n    ? `**STRICT DESIGN CONSISTENCY - MANDATORY:**\r\n       - Follow the exact visual style of the provided design examples\r\n       - Match typography, layout patterns, and design elements closely\r\n       - Maintain consistent brand aesthetic across all elements\r\n       - Use similar composition and visual hierarchy as examples`\r\n    : '';\r\n\r\n  return `Create a professional ${platform} social media post for a ${businessType} business.\r\n\r\n**TEXT TO DISPLAY ON IMAGE:** \"${imageText}\"\r\n**CRITICAL: This text must be displayed clearly and readably in ENGLISH ONLY**\r\n\r\n${colorInstructions}\r\n\r\n${designExampleInstructions}\r\n\r\n**ENHANCED DESIGN REQUIREMENTS:**\r\n\r\n**TEXT READABILITY (CRITICAL):**\r\n- Display the text \"${imageText}\" in large, bold, highly readable font\r\n- Use ENGLISH ONLY - no foreign languages or corrupted characters\r\n- Apply strong contrast between text and background (minimum 4.5:1 ratio)\r\n- Add text shadows, outlines, or semi-transparent backgrounds for readability\r\n- Position text strategically using rule of thirds\r\n- Make text size appropriate for mobile viewing (large and bold)\r\n\r\n**BRAND COLOR COMPLIANCE:**\r\n- Use the specified brand colors prominently throughout the design\r\n- Primary color should dominate the design (60% usage)\r\n- Accent color for highlights and important elements (30% usage)\r\n- Background color as base (10% usage)\r\n- Ensure colors work harmoniously together\r\n\r\n**PROFESSIONAL COMPOSITION:**\r\n- Apply rule of thirds for element placement\r\n- Create clear visual hierarchy with the text as primary focus\r\n- Use negative space effectively\r\n- Balance all design elements\r\n- Ensure mobile-first design approach\r\n\r\n**PLATFORM OPTIMIZATION FOR ${platform.toUpperCase()}:**\r\n- Design for ${platform} aspect ratio and best practices\r\n- Create thumb-stopping visual appeal\r\n- Optimize for mobile viewing\r\n- Use high contrast for small screen visibility\r\n\r\n**QUALITY STANDARDS:**\r\n- Professional ${visualStyle} aesthetic\r\n- High-resolution, crisp imagery\r\n- Consistent with ${businessType} industry standards\r\n- Suitable for social media compression\r\n\r\n${artifactInstructions ? `**SPECIAL INSTRUCTIONS FROM UPLOADED CONTENT:**\r\n${artifactInstructions}\r\n- Follow these instructions precisely when creating the design\r\n- These instructions specify how to use specific content elements\r\n` : ''}`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;AAQA;;;;AAOA,qDAAqD;AACrD,MAAM,SAAS,IAAI,sKAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc,IAAI;IACtC,iDAAiD;IACjD,gBAAgB;QACd,eAAe;IACjB;IACA,SAAS;IACT,YAAY;AACd;AA0BO,eAAe,6BACpB,KAAgC;IAEhC,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,sBAAgC,EAAE;IAExC,IAAI;QACF,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,oCAAoC;QACpC,MAAM,cAAc,qBAAqB,MAAM,SAAS;QACxD,MAAM,qBAAqB;YAAE,GAAG,KAAK;YAAE,WAAW;QAAY;QAE9D,+CAA+C;QAC/C,MAAM,iBAAiB,kBAAkB;QACzC,oBAAoB,IAAI,CAAC,gCAAgC;QAEzD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,qBAAqB,CAAC,CAAC,EAAE,MAAM,SAAS,CAAC,CAAC,CAAC;QACvD,QAAQ,GAAG,CAAC,oBAAoB,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;QAClD,QAAQ,GAAG,CAAC,oBAAoB,MAAM,SAAS,KAAK,cAAc,QAAQ;QAC1E,QAAQ,GAAG,CAAC,qBAAqB,eAAe,MAAM;QACtD,QAAQ,GAAG,CAAC,2BAA2B,eAAe,SAAS,CAAC,GAAG,OAAO;QAE1E,qEAAqE;QACrE,wEAAwE;QACxE,0DAA0D;QAC1D,MAAM,WAAW,MAAM,OAAO,MAAM,CAAC,QAAQ,CAAC;YAC5C,OAAO;YACP,QAAQ;YACR,MAAM,gBAAgB,MAAM,QAAQ;YACpC,SAAS;YACT,OAAO,iBAAiB,MAAM,WAAW;YACzC,GAAG;YACH,iBAAiB;QACnB;QAEA,MAAM,WAAW,SAAS,IAAI,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,oBAAoB,IAAI,CACtB,kCACA,0BACA,iCACA,yBACA;QAGF,IAAI,MAAM,gBAAgB,EAAE,mBAAmB;YAC7C,oBAAoB,IAAI,CAAC;QAC3B;QAEA,IAAI,MAAM,gBAAgB,EAAE,mBAAmB;YAC7C,oBAAoB,IAAI,CAAC;QAC3B;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,OAAO;YACL;YACA,cAAc;YACd;YACA,gBAAgB,KAAK,GAAG,KAAK;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM,IAAI,MAAM,CAAC,0CAA0C,EAAE,AAAC,MAAgB,OAAO,EAAE;IACzF;AACF;AAEA;;;CAGC,GACD,SAAS,kBAAkB,KAAgC;IACzD,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,GAAG;IAEjH,sDAAsD;IACtD,MAAM,eAAe,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC/E,MAAM,gBAAgB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAEjD,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,iBAAiB,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;IAE5F,yBAAyB;IACzB,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,mBAAmB,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;IAE1F,sEAAsE;IACtE,MAAM,oBAAoB,aAAa,YAAY,IAAI,aAAa,WAAW,GAC3E,CAAC,qBAAqB,EAAE,aAAa,YAAY,CAAC,yBAAyB,EAAE,aAAa,WAAW,CAAC,sEAAsE,CAAC,GAC7K;IAEJ,+EAA+E;IAC/E,MAAM,sBAAsB,4BAA4B,cAAc,WAAW;IACjF,MAAM,qBAAqB,sBACvB,6RACA;IAEJ,0CAA0C;IAC1C,MAAM,gBAAgB,0BAA0B;IAEhD,mCAAmC;IACnC,MAAM,qBAAqB,4BAA4B,CAAC,SAAS,WAAW,GAAgD,IAAI,6BAA6B,SAAS;IAEtK,mCAAmC;IACnC,MAAM,cAAc,wBAAwB,CAAC,aAAa,WAAW,GAA4C,IAAI,yBAAyB,OAAO;IAErJ,6DAA6D;IAC7D,MAAM,SAAS,CAAC;;;6DAG2C,EAAE,aAAa;iBAC3D,EAAE,SAAS;cACd,EAAE,YAAY,aAAa,EAAE,aAAa,QAAQ,IAAI,SAAS;;;eAG9D,EAAE,UAAU;AAC3B,EAAE,aAAa,YAAY,GAAG,CAAC,gBAAgB,EAAE,aAAa,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG;;AAEnF,EAAE,2BAA2B;;AAE7B,EAAE,mBAAmB;;AAErB,EAAE,YAAY;;;AAGd,EAAE,kBAAkB;AACpB,EAAE,mBAAmB;;;oBAGD,EAAE,eAAe;iCACJ,EAAE,iBAAiB;;iBAEnC,EAAE,aAAa;kBACd,EAAE,cAAc;;;AAGlC,EAAE,cAAc;;;0BAGU,EAAE,UAAU;;;;;;;;;;;;;;;EAepC,EAAE,YAAY;;;;;2BAKW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;kBAwB7B,EAAE,aAAa,YAAY,IAAI,aAAa;yBACrC,EAAE,cAAc;kBACvB,EAAE,mBAAmB;;;;;;;;;;;;;;;;;;AAkBvC,EAAE,mJAAA,CAAA,iCAA8B,CAAC;;;AAGjC,EAAE,mJAAA,CAAA,+BAA4B,CAAC;;;AAG/B,EAAE,mJAAA,CAAA,2BAAwB,CAAC;;;AAG3B,EAAE,mJAAA,CAAA,wBAAqB,CAAC;;;AAGxB,EAAE,mJAAA,CAAA,gCAA6B,CAAC,SAAS,WAAW,GAAiD,IAAI,mJAAA,CAAA,gCAA6B,CAAC,SAAS,CAAC;;;AAGjJ,EAAE,mJAAA,CAAA,2BAAwB,CAAC,aAAa,WAAW,GAA4C,IAAI,mJAAA,CAAA,2BAAwB,CAAC,IAAI,CAAC;;AAEjI,EAAE,uBAAuB,CAAC;AAC1B,EAAE,qBAAqB;;;;AAIvB,CAAC,GAAG,GAAG;;AAEP,EAAE,sJAAA,CAAA,yBAAsB,CAAC;;;gDAGuB,EAAE,UAAU;;;;kDAIV,EAAE,UAAU;;;AAG9D,EAAE,sJAAA,CAAA,6BAA0B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAiCS,EAAE,UAAU;;;AAGlD,EAAE,sJAAA,CAAA,6BAA0B,CAAC;;2BAEF,EAAE,sJAAA,CAAA,4BAAyB,EAAE;IAEtD,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,qBAAqB,IAAY;IACxC,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,KAAK,GAAG;QACrC,OAAO;IACT;IAEA,IAAI,cAAc,KAAK,IAAI;IAE3B,6EAA6E;IAC7E,cAAc,YACX,OAAO,CAAC,2BAA2B,IAAI,2DAA2D;KAClG,OAAO,CAAC,QAAQ,KAAK,4BAA4B;KACjD,OAAO,CAAC,cAAc,UAAU,mDAAmD;KACnF,IAAI;IAEP,4DAA4D;IAC5D,IAAI,YAAY,MAAM,GAAG,KAAK;QAC5B,MAAM,QAAQ,YAAY,KAAK,CAAC;QAChC,IAAI,MAAM,MAAM,GAAG,IAAI;YACrB,cAAc,MAAM,KAAK,CAAC,GAAG,IAAI,IAAI,CAAC;QACxC;IACF;IAEA,oCAAoC;IACpC,IAAI,YAAY,MAAM,KAAK,GAAG;QAC5B,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,4BAA4B,YAAoB,EAAE,SAAiB,EAAE,WAAmB;IAC/F,MAAM,oBAAoB,aAAa,WAAW;IAClD,MAAM,iBAAiB,UAAU,WAAW;IAC5C,MAAM,mBAAmB,YAAY,WAAW;IAEhD,oDAAoD;IACpD,MAAM,sBAAsB;QAC1B;QAAW;QAAO;QAAU;QAAY;QAAY;QACpD;QAAa;QAAc;QAAW;QAAc;QACpD;QAAU;QAAS;QAAO;QAAW;QAAU;KAChD;IAED,+BAA+B;IAC/B,MAAM,gBAAgB;QACpB;QAAQ;QAAY;QAAU;QAAU;QAAa;QACrD;QAAc;QAAW;QAAQ;QAAW;QAAY;KACzD;IAED,2CAA2C;IAC3C,MAAM,eAAe;QAAC;QAAa;QAAa;QAAY;QAAY;KAAe;IAEvF,MAAM,wBAAwB,oBAAoB,IAAI,CAAC,CAAA,OAAQ,kBAAkB,QAAQ,CAAC;IAC1F,MAAM,mBAAmB,cAAc,IAAI,CAAC,CAAA,UAAW,eAAe,QAAQ,CAAC;IAC/E,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAA,QAAS,iBAAiB,QAAQ,CAAC;IAE5E,OAAO,yBAAyB,oBAAoB;AACtD;AAEA;;CAEC,GACD,SAAS,0BAA0B,QAAgB;IACjD,MAAM,gBAAgB,SAAS,WAAW;IAE1C,IAAI,cAAc,QAAQ,CAAC,cAAc;QACvC,IAAI,cAAc,QAAQ,CAAC,UAAU;YACnC,OAAO;QACT;QACA,OAAO;IACT;IAEA,IAAI,cAAc,QAAQ,CAAC,aAAa;QACtC,OAAO;IACT;IAEA,IAAI,cAAc,QAAQ,CAAC,aAAa;QACtC,OAAO;IACT;IAEA,IAAI,cAAc,QAAQ,CAAC,cAAc,cAAc,QAAQ,CAAC,MAAM;QACpE,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,gBAAgB,QAAgB;IACvC,MAAM,gBAAgB,SAAS,WAAW;IAE1C,uCAAuC;IACvC,IAAI,cAAc,QAAQ,CAAC,YACzB,cAAc,QAAQ,CAAC,WACvB,cAAc,QAAQ,CAAC,aACvB,cAAc,QAAQ,CAAC,kBAAkB;QACzC,OAAO;IACT;IAEA,yCAAyC;IACzC,IAAI,cAAc,QAAQ,CAAC,eACzB,cAAc,QAAQ,CAAC,cACvB,cAAc,QAAQ,CAAC,eACvB,cAAc,QAAQ,CAAC,cACvB,cAAc,QAAQ,CAAC,WAAW;QAClC,OAAO;IACT;IAEA,yEAAyE;IACzE,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,iBAAiB,WAAmB;IAC3C,MAAM,aAAa,YAAY,WAAW;IAE1C,IAAI,WAAW,QAAQ,CAAC,cAAc,WAAW,QAAQ,CAAC,WAAW,WAAW,QAAQ,CAAC,WAAW;QAClG,OAAO;IACT,OAAO;QACL,OAAO;IACT;AACF;AAKO,eAAe,mCACpB,KAAgC;IAEhC,IAAI;QACF,mBAAmB;QACnB,OAAO,MAAM,6BAA6B;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,6CAA6C;QAE1D,+DAA+D;QAC/D,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAElC,4CAA4C;QAC5C,MAAM,iBAAiB,0BAA0B;QAEjD,MAAM,SAAS,MAAM,sBAAsB;YACzC,QAAQ;YACR,YAAY;YACZ,mBAAmB;YACnB,iBAAiB;YACjB,cAAc,MAAM,YAAY;YAChC,aAAa;QACf;QAEA,OAAO;YACL,UAAU,OAAO,QAAQ,IAAI;YAC7B,cAAc;YACd,qBAAqB;gBAAC;gBAAmB;gBAAsB;aAAoB;YACnF,gBAAgB,KAAK,GAAG,KAAK,KAAK,GAAG;QACvC;IACF;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,KAAgC;IACjE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,GAAG;IAEjH,MAAM,oBAAoB,aAAa,YAAY,IAAI,aAAa,WAAW,IAAI,aAAa,eAAe,GAC3G,CAAC;wBACiB,EAAE,aAAa,YAAY,CAAC;uBAC7B,EAAE,aAAa,WAAW,CAAC;2BACvB,EAAE,aAAa,eAAe,CAAC;iFACuB,CAAC,GAC5E;IAEJ,MAAM,4BAA4B,kBAAkB,qBAAqB,aAAa,cAAc,IAAI,aAAa,cAAc,CAAC,MAAM,GAAG,IACzI,CAAC;;;;iEAI0D,CAAC,GAC5D;IAEJ,OAAO,CAAC,sBAAsB,EAAE,SAAS,yBAAyB,EAAE,aAAa;;+BAEpD,EAAE,UAAU;;;AAG3C,EAAE,kBAAkB;;AAEpB,EAAE,0BAA0B;;;;;oBAKR,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;4BAqBJ,EAAE,SAAS,WAAW,GAAG;aACxC,EAAE,SAAS;;;;;;eAMT,EAAE,YAAY;;kBAEX,EAAE,aAAa;;;AAGjC,EAAE,uBAAuB,CAAC;AAC1B,EAAE,qBAAqB;;;AAGvB,CAAC,GAAG,IAAI;AACR", "debugId": null}}]}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/ai/flows/generate-creative-asset.ts
__turbopack_context__.s({});
"use turbopack no side effects";
;
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/data:5d174b [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4060851bba2dd643a600428bdf8a566b397fe38d6d":"generateCreativeAsset"},"src/ai/flows/generate-creative-asset.ts",""] */ __turbopack_context__.s({
    "generateCreativeAsset": (()=>generateCreativeAsset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAsset = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("4060851bba2dd643a600428bdf8a566b397fe38d6d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAsset"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$5d174b__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$5d174b__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/data:5d174b [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <exports>");
}}),
"[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// This file must be bundled in the app's client layer, it shouldn't be directly
// imported by the server.
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    callServer: null,
    createServerReference: null,
    findSourceMapURL: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    callServer: function() {
        return _appcallserver.callServer;
    },
    createServerReference: function() {
        return createServerReference;
    },
    findSourceMapURL: function() {
        return _appfindsourcemapurl.findSourceMapURL;
    }
});
const _appcallserver = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-call-server.js [app-client] (ecmascript)");
const _appfindsourcemapurl = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-find-source-map-url.js [app-client] (ecmascript)");
const createServerReference = (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-server-dom-turbopack/client.js [app-client] (ecmascript)")).createServerReference; //# sourceMappingURL=action-client-wrapper.js.map
}}),
}]);

//# sourceMappingURL=_bd92a062._.js.map
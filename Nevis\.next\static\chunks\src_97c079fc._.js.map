{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { generatePostFromProfile as generatePostFromProfileFlow } from \"@/ai/flows/generate-post-from-profile\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport {\r\n  detectAndPopulateLanguages,\r\n  getLanguageInstructionForProfile,\r\n  updateLanguageDetectionIfNeeded\r\n} from \"@/lib/services/brand-language-service\";\r\n// import { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\"; // Temporarily disabled\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\nimport { DesignGenerationService } from \"@/ai/models/services/design-generation-service\";\r\nimport type { RevoModelId } from \"@/ai/models/types/model-types\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\r\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\r\n    console.log(\"🔍 Result type:\", typeof result);\r\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n    console.error(\"❌ Error analyzing brand:\", error);\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Update language detection if needed\r\n    const profileWithLanguages = updateLanguageDetectionIfNeeded(profile);\r\n\r\n    // Generate language instructions for AI\r\n    const languageInstructions = getLanguageInstructionForProfile(profileWithLanguages);\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    const postDetails = await generatePostFromProfileFlow({\r\n      businessType: profileWithLanguages.businessType,\r\n      location: profileWithLanguages.location,\r\n      writingTone: profileWithLanguages.writingTone,\r\n      contentThemes: profileWithLanguages.contentThemes,\r\n      visualStyle: profileWithLanguages.visualStyle,\r\n      logoDataUrl: profileWithLanguages.logoDataUrl,\r\n      designExamples: effectiveDesignExamples, // Use design examples based on consistency preference\r\n      primaryColor: profileWithLanguages.primaryColor,\r\n      accentColor: profileWithLanguages.accentColor,\r\n      backgroundColor: profileWithLanguages.backgroundColor,\r\n      dayOfWeek,\r\n      currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      // Pass new detailed fields\r\n      services: servicesString,\r\n      targetAudience: profileWithLanguages.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      // Pass brand consistency preferences\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      // Pass intelligent language instructions\r\n      languageInstructions: languageInstructions,\r\n    });\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline,\r\n      callToAction: postDetails.callToAction,\r\n      // Include enhanced AI features\r\n      contentVariants: postDetails.contentVariants,\r\n      hashtagAnalysis: postDetails.hashtagAnalysis,\r\n      // Include advanced AI features\r\n      marketIntelligence: postDetails.marketIntelligence,\r\n      // Include local context features\r\n      localContext: postDetails.localContext,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    console.error(\"Error generating content:\", error);\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    console.error(\"Error generating video content:\", error);\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Error generating creative asset:\", error);\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n    console.log('🎨 Enhanced Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', finalImageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n    console.log('- Enhancements Enabled:', enableEnhancements);\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n      });\r\n\r\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\r\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\r\n\r\n    } catch (gemini25Error) {\r\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\r\n\r\n      try {\r\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\r\n      } catch (openaiError) {\r\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\r\n\r\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\r\n      }\r\n    }\r\n\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating enhanced design:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n    console.log('🎨 Gemini HD Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', imageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n\r\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n    console.log('✅ Gemini HD enhanced design generated successfully');\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error in Gemini HD design generation:', error);\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log('🎨 Generating content with artifacts...');\r\n    console.log('- Platform:', platform);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n    console.log('- Enhanced Design:', useEnhancedDesign);\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\r\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\r\n        id: a.id,\r\n        name: a.name,\r\n        type: a.type,\r\n        usageType: a.usageType,\r\n        isActive: a.isActive,\r\n        instructions: a.instructions\r\n      })));\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      console.log('🔄 Enhanced design disabled, using base content generation');\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\r\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\r\n\r\n    if (targetArtifacts.length === 0) {\r\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\r\n    } else {\r\n      console.log('🎯 Using enhanced design with artifact context');\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n          console.log('📝 Using message from exact-use artifact');\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      console.log('🎨 Applying style references from artifacts');\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n        console.log('🎨 Using artifact-inspired visual style');\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    console.log('✅ Enhanced content with artifacts generated successfully');\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating content with artifacts:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content using the new Revo model system\r\n * This action uses the proper model architecture with version-specific implementations\r\n */\r\nexport async function generateContentWithRevoModelAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  revoModel: RevoModelId,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = []\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log(`🎨 Generating content with ${revoModel} model...`);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Business Type:', profile.businessType);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n\r\n    // Handle artifacts if provided\r\n    let targetArtifacts: Artifact[] = [];\r\n    if (artifactIds.length > 0) {\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    // Prepare artifact instructions\r\n    let artifactInstructions = '';\r\n    if (targetArtifacts.length > 0) {\r\n      const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      if (exactUseArtifacts.length > 0) {\r\n        artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\\n';\r\n        exactUseArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\\n`;\r\n        });\r\n      }\r\n\r\n      if (referenceArtifacts.length > 0) {\r\n        artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\\n';\r\n        referenceArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\\n`;\r\n        });\r\n      }\r\n    }\r\n\r\n    // Use simplified Revo model generation with text validation\r\n    console.log(`🎨 Using Revo ${revoModel} with text validation...`);\r\n    console.log('🔧 DEBUG: This is the SIMPLIFIED code path');\r\n\r\n    // Generate dynamic and varied text for each design\r\n    const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);\r\n    let imageText = textVariations.selectedText;\r\n\r\n    if (revoModel === 'revo-1.0') {\r\n      console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');\r\n      console.log('🔍 Original text:', imageText);\r\n\r\n      // Simple text validation for Revo 1.0\r\n      const words = imageText.split(' ').filter(word => word.length > 0);\r\n      if (words.length > 25) {\r\n        console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);\r\n        imageText = words.slice(0, 25).join(' ');\r\n      }\r\n      console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);\r\n    }\r\n\r\n    // Use sophisticated design prompt created by 20-year veteran designer + marketer\r\n    const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);\r\n\r\n    const designResult = await generateCreativeAssetFlow({\r\n      prompt: designPrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: profile,\r\n      maskDataUrl: null\r\n    });\r\n\r\n    if (!designResult.imageUrl) {\r\n      throw new Error('Design generation failed: No image URL returned');\r\n    }\r\n\r\n    // Generate content using the standard flow for caption and hashtags\r\n    const contentResult = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      services: Array.isArray(profile.services)\r\n        ? profile.services.map(s => typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\\n')\r\n        : profile.services || '',\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\\n') : profile.keyFeatures || '',\r\n      competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\\n') : profile.competitiveAdvantages || '',\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    // Combine the design result with content result\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: new Date().toISOString(),\r\n      content: contentResult.content,\r\n      hashtags: contentResult.hashtags,\r\n      status: 'generated',\r\n      variants: [{\r\n        platform,\r\n        imageUrl: designResult.imageUrl || '',\r\n        caption: contentResult.content,\r\n        hashtags: contentResult.hashtags\r\n      }],\r\n      catchyWords: contentResult.catchyWords,\r\n      subheadline: contentResult.subheadline,\r\n      callToAction: contentResult.callToAction,\r\n      contentVariants: contentResult.contentVariants,\r\n      hashtagAnalysis: contentResult.hashtagAnalysis,\r\n      marketIntelligence: contentResult.marketIntelligence,\r\n      localContext: contentResult.localContext,\r\n      // Add Revo model metadata\r\n      revoModelUsed: revoModel,\r\n      qualityScore: 8, // Default quality score for Revo models\r\n      processingTime: Date.now() - Date.now(), // Will be calculated properly\r\n      creditsUsed: 1,\r\n      enhancementsApplied: [`Revo ${revoModel} Generation`, 'Text Validation', 'Professional Design']\r\n    };\r\n\r\n    console.log(`✅ Content generated successfully with ${revoModel}`);\r\n    console.log(`⭐ Quality Score: 8/10`);\r\n    console.log(`⚡ Processing Time: Fast generation`);\r\n    console.log(`💰 Credits Used: 1`);\r\n\r\n    return newPost;\r\n\r\n  } catch (error) {\r\n    console.error(`❌ Error generating content with ${revoModel}:`, error);\r\n    throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate professional marketing-driven text with cultural awareness\r\n * Designed by a 20-year veteran designer + 20-year marketing expert\r\n * Now deeply connected to actual brand profile information\r\n */\r\nfunction generateDynamicTextForRevo(profile: BrandProfile, revoModel: RevoModelId, platform: Platform) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n\r\n  // Generate sophisticated marketing copy using actual brand profile data\r\n  const marketingCopy = generateMarketingCopy(profile, platform);\r\n\r\n  console.log(`🎯 Generated personalized marketing copy for ${businessName}: \"${marketingCopy}\"`);\r\n\r\n  return {\r\n    selectedText: marketingCopy,\r\n    allVariations: [marketingCopy],\r\n    variationIndex: 0\r\n  };\r\n}\r\n\r\n/**\r\n * Generate sophisticated marketing copy that sells\r\n * Combines 20 years of design + marketing expertise with actual brand profile data\r\n * Now deeply personalized using real business information\r\n * LANGUAGE RESTRICTION: English only - no Arabic, Hindi, Chinese, or other non-English text\r\n */\r\nfunction generateMarketingCopy(profile: BrandProfile, platform: Platform): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract real business intelligence from profile\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Cultural and regional insights\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Generate catchy headline using actual business strengths (max 5 words)\r\n  const catchyHeadlines = [\r\n    `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,\r\n    `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,\r\n    `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,\r\n    `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,\r\n    `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`\r\n  ];\r\n\r\n  // Generate subheadline using real competitive advantages (max 14 words)\r\n  const subheadlines = [\r\n    `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,\r\n    `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,\r\n    `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,\r\n    `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,\r\n    `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`\r\n  ];\r\n\r\n  // Generate call-to-action using actual business context\r\n  const callToActions = [\r\n    `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,\r\n    `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,\r\n    `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,\r\n    `${getRandomElement(['Book', 'Schedule', 'Request'])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,\r\n    `${getRandomElement(['Start', 'Begin', 'Launch'])} Your ${businessIntelligence.journeyType} Today`\r\n  ];\r\n\r\n  // Randomly select components\r\n  const catchyWords = getRandomElement(catchyHeadlines);\r\n  const subheadline = getRandomElement(subheadlines);\r\n  const cta = getRandomElement(callToActions);\r\n\r\n  // Combine based on marketing best practices and business context\r\n  const marketingFormats = [\r\n    `${catchyWords}\\n${subheadline}\\n${cta}`,\r\n    `${catchyWords}\\n${subheadline}`,\r\n    `${catchyWords}\\n${cta}`,\r\n    `${subheadline}\\n${cta}`,\r\n    catchyWords\r\n  ];\r\n\r\n  return getRandomElement(marketingFormats);\r\n}\r\n\r\n/**\r\n * Get cultural context and local market insights\r\n */\r\nfunction getCulturalContext(location: string) {\r\n  // Default context\r\n  let context = {\r\n    localTerms: ['Premium', 'Professional', 'Expert'],\r\n    marketingStyle: 'Professional',\r\n    targetAudience: 'businesses',\r\n    localMarket: 'modern',\r\n    socialProof: 'thousands of clients',\r\n    valueProposition: 'Industry-leading',\r\n    competitiveAdvantage: 'proven expertise',\r\n    urgencyTrigger: 'Don\\'t miss out on',\r\n    actionWords: 'Connect with',\r\n    localCTA: 'Get Started',\r\n    ctaUrgency: 'Now'\r\n  };\r\n\r\n  // Location-specific cultural adaptations\r\n  if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {\r\n    context = {\r\n      localTerms: ['Premium', 'Luxury', 'Elite', 'Exclusive'],\r\n      marketingStyle: 'Luxury-focused',\r\n      targetAudience: 'discerning clients',\r\n      localMarket: 'Dubai\\'s dynamic',\r\n      socialProof: 'leading UAE businesses',\r\n      valueProposition: 'World-class',\r\n      competitiveAdvantage: 'international excellence',\r\n      urgencyTrigger: 'Seize the opportunity for',\r\n      actionWords: 'Experience',\r\n      localCTA: 'Book Your Exclusive Consultation',\r\n      ctaUrgency: 'Today'\r\n    };\r\n  } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {\r\n    context = {\r\n      localTerms: ['Bespoke', 'Tailored', 'Refined'],\r\n      marketingStyle: 'Sophisticated',\r\n      targetAudience: 'discerning professionals',\r\n      localMarket: 'London\\'s competitive',\r\n      socialProof: 'established UK enterprises',\r\n      valueProposition: 'Expertly crafted',\r\n      competitiveAdvantage: 'British excellence',\r\n      urgencyTrigger: 'Secure your',\r\n      actionWords: 'Discover',\r\n      localCTA: 'Arrange Your Consultation',\r\n      ctaUrgency: 'Promptly'\r\n    };\r\n  } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {\r\n    context = {\r\n      localTerms: ['Cutting-edge', 'Innovative', 'Game-changing'],\r\n      marketingStyle: 'Bold and direct',\r\n      targetAudience: 'ambitious professionals',\r\n      localMarket: 'NYC\\'s fast-paced',\r\n      socialProof: 'successful New York businesses',\r\n      valueProposition: 'Results-driven',\r\n      competitiveAdvantage: 'New York hustle',\r\n      urgencyTrigger: 'Don\\'t let competitors get',\r\n      actionWords: 'Dominate with',\r\n      localCTA: 'Schedule Your Strategy Session',\r\n      ctaUrgency: 'ASAP'\r\n    };\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n/**\r\n * Extract business intelligence from brand profile for personalized marketing\r\n * Analyzes actual business data to create relevant marketing copy\r\n */\r\nfunction extractBusinessIntelligence(profile: BrandProfile) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n\r\n  // Extract primary service information\r\n  const primaryService = services[0]?.name || services[0] || businessType;\r\n  const serviceDescription = services[0]?.description || '';\r\n  const targetAudience = services[0]?.targetAudience || 'businesses';\r\n  const keyFeatures = services[0]?.keyFeatures || '';\r\n  const competitiveAdvantages = services[0]?.competitiveAdvantages || '';\r\n\r\n  // Analyze description for key terms\r\n  const descriptionWords = description.toLowerCase().split(/\\s+/);\r\n  const strengthWords = extractStrengthWords(description, businessType);\r\n  const valueWords = extractValueWords(description, keyFeatures);\r\n  const benefitWords = extractBenefitWords(description, competitiveAdvantages);\r\n\r\n  // Extract competitive advantages\r\n  const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);\r\n  const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);\r\n  const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);\r\n\r\n  // Extract target audience specifics\r\n  const actualTargetAudience = extractTargetAudience(targetAudience, businessType);\r\n\r\n  // Generate contextual elements\r\n  const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);\r\n  const mainValue = extractMainValue(description, keyFeatures);\r\n  const offerType = generateOfferType(businessType, services);\r\n  const consultationType = generateConsultationType(businessType);\r\n  const journeyType = generateJourneyType(businessType, primaryService);\r\n  const urgencyTrigger = generateUrgencyTrigger(businessType, location);\r\n\r\n  // Extract outcome words from business context\r\n  const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);\r\n  const differentiators = extractDifferentiators(competitiveAdvantages, businessType);\r\n\r\n  return {\r\n    primaryService,\r\n    strengthWords,\r\n    valueWords,\r\n    benefitWords,\r\n    realCompetitiveAdvantage,\r\n    uniqueFeature,\r\n    realDifferentiator,\r\n    actualTargetAudience,\r\n    keyBenefit,\r\n    mainValue,\r\n    offerType,\r\n    consultationType,\r\n    journeyType,\r\n    urgencyTrigger,\r\n    outcomeWords,\r\n    differentiators\r\n  };\r\n}\r\n\r\n/**\r\n * Extract strength words from business description and type\r\n */\r\nfunction extractStrengthWords(description: string, businessType: string): string[] {\r\n  const strengthKeywords = ['leading', 'premium', 'expert', 'professional', 'trusted', 'innovative', 'cutting-edge', 'award-winning', 'certified', 'proven', 'reliable', 'secure', 'fast', 'efficient', 'quality', 'excellence', 'superior', 'advanced', 'specialized'];\r\n  const found = strengthKeywords.filter(word => description.toLowerCase().includes(word));\r\n\r\n  // Add business type specific strengths\r\n  const typeStrengths = getBusinessTypeStrengths(businessType);\r\n\r\n  return found.length > 0 ? found : typeStrengths;\r\n}\r\n\r\n/**\r\n * Extract value words from description and features\r\n */\r\nfunction extractValueWords(description: string, keyFeatures: string): string[] {\r\n  const valueKeywords = ['value', 'results', 'success', 'growth', 'efficiency', 'savings', 'profit', 'revenue', 'performance', 'productivity', 'quality', 'excellence', 'innovation', 'solutions', 'benefits'];\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n  const found = valueKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Excellence', 'Results', 'Success'];\r\n}\r\n\r\n/**\r\n * Extract benefit words from description and competitive advantages\r\n */\r\nfunction extractBenefitWords(description: string, competitiveAdvantages: string): string[] {\r\n  const benefitKeywords = ['success', 'growth', 'efficiency', 'savings', 'results', 'performance', 'quality', 'reliability', 'security', 'speed', 'convenience', 'expertise', 'support', 'innovation', 'excellence'];\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const found = benefitKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Success', 'Quality', 'Results'];\r\n}\r\n\r\n/**\r\n * Extract competitive advantage from actual business data\r\n */\r\nfunction extractCompetitiveAdvantage(competitiveAdvantages: string, businessType: string): string {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    // Extract first meaningful advantage\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim());\r\n    return advantages[0] || getDefaultAdvantage(businessType);\r\n  }\r\n  return getDefaultAdvantage(businessType);\r\n}\r\n\r\n/**\r\n * Extract unique feature from key features\r\n */\r\nfunction extractUniqueFeature(keyFeatures: string, businessType: string): string {\r\n  if (keyFeatures && keyFeatures.length > 10) {\r\n    const features = keyFeatures.split(',').map(s => s.trim());\r\n    return features[0] || getDefaultFeature(businessType);\r\n  }\r\n  return getDefaultFeature(businessType);\r\n}\r\n\r\n/**\r\n * Extract differentiator from competitive advantages and description\r\n */\r\nfunction extractDifferentiator(competitiveAdvantages: string, description: string): string {\r\n  const text = `${competitiveAdvantages} ${description}`.toLowerCase();\r\n\r\n  if (text.includes('24/7') || text.includes('24-7')) return '24/7 availability';\r\n  if (text.includes('fastest') || text.includes('quick') || text.includes('speed')) return 'fastest service';\r\n  if (text.includes('secure') || text.includes('security')) return 'advanced security';\r\n  if (text.includes('expert') || text.includes('experience')) return 'expert knowledge';\r\n  if (text.includes('custom') || text.includes('tailored')) return 'customized solutions';\r\n  if (text.includes('award') || text.includes('certified')) return 'award-winning service';\r\n\r\n  return 'professional excellence';\r\n}\r\n\r\n/**\r\n * Extract target audience from service data\r\n */\r\nfunction extractTargetAudience(targetAudience: string, businessType: string): string {\r\n  if (targetAudience && targetAudience.length > 5) {\r\n    return targetAudience.split(',')[0].trim() || getDefaultAudience(businessType);\r\n  }\r\n  return getDefaultAudience(businessType);\r\n}\r\n\r\n/**\r\n * Extract key benefit from service description and advantages\r\n */\r\nfunction extractKeyBenefit(serviceDescription: string, competitiveAdvantages: string): string {\r\n  const text = `${serviceDescription} ${competitiveAdvantages}`.toLowerCase();\r\n\r\n  if (text.includes('save') || text.includes('cost')) return 'cost savings';\r\n  if (text.includes('fast') || text.includes('quick') || text.includes('speed')) return 'faster results';\r\n  if (text.includes('secure') || text.includes('safety')) return 'enhanced security';\r\n  if (text.includes('grow') || text.includes('increase')) return 'business growth';\r\n  if (text.includes('efficient') || text.includes('optimize')) return 'improved efficiency';\r\n  if (text.includes('quality') || text.includes('premium')) return 'superior quality';\r\n\r\n  return 'exceptional results';\r\n}\r\n\r\n/**\r\n * Extract main value proposition\r\n */\r\nfunction extractMainValue(description: string, keyFeatures: string): string {\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n\r\n  if (text.includes('transform') || text.includes('revolutionize')) return 'transforms your business';\r\n  if (text.includes('maximize') || text.includes('optimize')) return 'maximizes your potential';\r\n  if (text.includes('accelerate') || text.includes('boost')) return 'accelerates your growth';\r\n  if (text.includes('streamline') || text.includes('simplify')) return 'streamlines your operations';\r\n  if (text.includes('enhance') || text.includes('improve')) return 'enhances your performance';\r\n\r\n  return 'delivers exceptional value';\r\n}\r\n\r\n/**\r\n * Generate business type-specific offer types\r\n */\r\nfunction generateOfferType(businessType: string, services: any[]): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Free Tasting';\r\n  if (type.includes('tech') || type.includes('software')) return 'Free Demo';\r\n  if (type.includes('health') || type.includes('medical')) return 'Free Consultation';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Free Assessment';\r\n  if (type.includes('real estate')) return 'Free Valuation';\r\n  if (type.includes('legal')) return 'Free Consultation';\r\n  if (type.includes('education') || type.includes('training')) return 'Free Trial';\r\n\r\n  return 'Free Consultation';\r\n}\r\n\r\n/**\r\n * Generate consultation types based on business\r\n */\r\nfunction generateConsultationType(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('software')) return 'Strategy Session';\r\n  if (type.includes('health') || type.includes('medical')) return 'Health Assessment';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Financial Review';\r\n  if (type.includes('real estate')) return 'Property Consultation';\r\n  if (type.includes('legal')) return 'Legal Consultation';\r\n  if (type.includes('marketing')) return 'Marketing Audit';\r\n\r\n  return 'Business Consultation';\r\n}\r\n\r\n/**\r\n * Generate journey types\r\n */\r\nfunction generateJourneyType(businessType: string, primaryService: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('digital')) return 'Digital Transformation';\r\n  if (type.includes('health') || type.includes('wellness')) return 'Wellness Journey';\r\n  if (type.includes('finance') || type.includes('investment')) return 'Financial Success';\r\n  if (type.includes('real estate')) return 'Property Investment';\r\n  if (type.includes('education')) return 'Learning Journey';\r\n\r\n  return 'Success Journey';\r\n}\r\n\r\n/**\r\n * Generate urgency triggers based on business and location\r\n */\r\nfunction generateUrgencyTrigger(businessType: string, location: string): string {\r\n  const triggers = ['Limited Time Offer', 'Act Now', 'Don\\'t Wait', 'Book Today', 'Available Now'];\r\n\r\n  if (location.toLowerCase().includes('dubai')) return 'Exclusive Dubai Offer';\r\n  if (location.toLowerCase().includes('london')) return 'Limited London Availability';\r\n  if (location.toLowerCase().includes('new york')) return 'NYC Exclusive Deal';\r\n\r\n  return getRandomElement(triggers);\r\n}\r\n\r\n/**\r\n * Get business type specific strengths\r\n */\r\nfunction getBusinessTypeStrengths(businessType: string): string[] {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return ['Premium', 'Fresh', 'Authentic'];\r\n  if (type.includes('tech') || type.includes('software')) return ['Innovative', 'Cutting-edge', 'Advanced'];\r\n  if (type.includes('health') || type.includes('medical')) return ['Trusted', 'Professional', 'Expert'];\r\n  if (type.includes('finance') || type.includes('banking')) return ['Secure', 'Reliable', 'Trusted'];\r\n  if (type.includes('real estate')) return ['Premium', 'Exclusive', 'Professional'];\r\n  if (type.includes('legal')) return ['Expert', 'Trusted', 'Professional'];\r\n  if (type.includes('education')) return ['Expert', 'Certified', 'Professional'];\r\n\r\n  return ['Professional', 'Quality', 'Trusted'];\r\n}\r\n\r\n/**\r\n * Get default competitive advantage by business type\r\n */\r\nfunction getDefaultAdvantage(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Fresh, authentic ingredients';\r\n  if (type.includes('tech') || type.includes('software')) return 'Cutting-edge technology';\r\n  if (type.includes('health') || type.includes('medical')) return 'Expert medical care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Secure financial solutions';\r\n  if (type.includes('real estate')) return 'Premium property expertise';\r\n  if (type.includes('legal')) return 'Expert legal guidance';\r\n\r\n  return 'Professional excellence';\r\n}\r\n\r\n/**\r\n * Get default feature by business type\r\n */\r\nfunction getDefaultFeature(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'farm-to-table freshness';\r\n  if (type.includes('tech') || type.includes('software')) return 'advanced automation';\r\n  if (type.includes('health') || type.includes('medical')) return 'personalized care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'secure transactions';\r\n  if (type.includes('real estate')) return 'market expertise';\r\n\r\n  return 'personalized service';\r\n}\r\n\r\n/**\r\n * Get default audience by business type\r\n */\r\nfunction getDefaultAudience(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'food enthusiasts';\r\n  if (type.includes('tech') || type.includes('software')) return 'forward-thinking businesses';\r\n  if (type.includes('health') || type.includes('medical')) return 'health-conscious individuals';\r\n  if (type.includes('finance') || type.includes('banking')) return 'smart investors';\r\n  if (type.includes('real estate')) return 'property investors';\r\n\r\n  return 'discerning clients';\r\n}\r\n\r\n/**\r\n * Extract outcome words from business context\r\n */\r\nfunction extractOutcomeWords(description: string, competitiveAdvantages: string): string[] {\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const outcomes = ['success', 'growth', 'results', 'performance', 'efficiency', 'savings', 'profit', 'revenue'];\r\n  const found = outcomes.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['success', 'results', 'growth'];\r\n}\r\n\r\n/**\r\n * Extract differentiators from competitive advantages\r\n */\r\nfunction extractDifferentiators(competitiveAdvantages: string, businessType: string): string[] {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim().split(' ')[0]);\r\n    return advantages.slice(0, 3);\r\n  }\r\n\r\n  return getBusinessTypeStrengths(businessType);\r\n}\r\n\r\n/**\r\n * Get random element from array\r\n */\r\nfunction getRandomElement<T>(array: T[]): T {\r\n  return array[Math.floor(Math.random() * array.length)];\r\n}\r\n\r\n/**\r\n * Create professional design prompt with 20 years of design + marketing expertise\r\n * Combines cultural awareness, psychology, and visual design mastery\r\n */\r\nfunction createProfessionalDesignPrompt(imageText: string, platform: Platform, profile: BrandProfile, revoModel: RevoModelId): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract business intelligence for design context\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Get cultural context for design decisions\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Industry-specific design psychology\r\n  const industryDesignPsychology = getIndustryDesignPsychology(businessType);\r\n\r\n  // Platform-specific design requirements\r\n  const platformRequirements = getPlatformDesignRequirements(platform);\r\n\r\n  // Color psychology based on business type and culture\r\n  const colorPsychology = getColorPsychology(businessType, location);\r\n\r\n  // Typography psychology for conversion\r\n  const typographyStrategy = getTypographyStrategy(businessType, platform);\r\n\r\n  return `Create an exceptional, conversion-focused ${platform} design for ${businessName} that embodies 20 years of professional design and marketing expertise.\r\n\r\nLANGUAGE REQUIREMENTS:\r\n- ALL TEXT MUST BE IN ENGLISH ONLY\r\n- NO Arabic, Hindi, Chinese, or other non-English characters\r\n- NO transliterated text or mixed languages\r\n- Use clear, professional English throughout\r\n\r\nBUSINESS INTELLIGENCE & CONTEXT:\r\n- Company: ${businessName} (${businessType})\r\n- Primary Service: ${businessIntelligence.primaryService}\r\n- Location: ${location}\r\n- Target Audience: ${businessIntelligence.actualTargetAudience}\r\n- Key Differentiator: ${businessIntelligence.realDifferentiator}\r\n- Unique Value: ${businessIntelligence.mainValue}\r\n- Competitive Advantage: ${businessIntelligence.realCompetitiveAdvantage}\r\n\r\nTEXT TO INTEGRATE: \"${imageText}\"\r\n\r\nBRAND-SPECIFIC DESIGN REQUIREMENTS:\r\n- Must communicate: ${businessIntelligence.realCompetitiveAdvantage}\r\n- Must highlight: ${businessIntelligence.uniqueFeature}\r\n- Must appeal to: ${businessIntelligence.actualTargetAudience}\r\n- Must convey: ${businessIntelligence.keyBenefit}\r\n\r\nDESIGN PSYCHOLOGY & STRATEGY:\r\n${industryDesignPsychology}\r\n\r\nVISUAL HIERARCHY & COMPOSITION:\r\n- Apply the golden ratio and rule of thirds for optimal visual flow\r\n- Create clear focal points that guide the eye to key conversion elements\r\n- Use strategic white space to enhance readability and premium feel\r\n- Implement Z-pattern or F-pattern layout for maximum engagement\r\n\r\nCOLOR STRATEGY:\r\n${colorPsychology}\r\n\r\nTYPOGRAPHY MASTERY:\r\n${typographyStrategy}\r\n\r\nCULTURAL DESIGN ADAPTATION:\r\n- ${culturalContext.localMarket} aesthetic preferences\r\n- ${culturalContext.targetAudience} visual expectations\r\n- Regional design trends and cultural symbols\r\n- Local color associations and meanings\r\n\r\nCONVERSION OPTIMIZATION:\r\n- Design elements that create urgency and desire\r\n- Visual cues that guide toward call-to-action\r\n- Trust signals through professional presentation\r\n- Emotional triggers through strategic imagery and layout\r\n\r\nPLATFORM OPTIMIZATION:\r\n${platformRequirements}\r\n\r\nTECHNICAL EXCELLENCE:\r\n- Aspect Ratio: 1:1 (perfect square)\r\n- Resolution: Ultra-high quality, print-ready standards\r\n- Text Clarity: Crystal clear, perfectly readable at all sizes\r\n- Brand Consistency: Align with professional brand standards\r\n- Mobile Optimization: Ensure perfect display on all devices\r\n\r\nFINAL QUALITY STANDARDS:\r\nThis design must look like it was created by a top-tier creative agency specifically for ${businessName}. Every element should reflect their unique value proposition: \"${businessIntelligence.realCompetitiveAdvantage}\". The design should immediately communicate their expertise in ${businessIntelligence.primaryService} while appealing directly to ${businessIntelligence.actualTargetAudience}.\r\n\r\nThe final result should be a sophisticated, professional design that drives ${businessIntelligence.actualTargetAudience} to choose ${businessName} over competitors and take immediate action.\r\n\r\nMake it absolutely irresistible for ${businessIntelligence.actualTargetAudience} and perfectly aligned with ${businessName}'s brand identity.`;\r\n}\r\n\r\n/**\r\n * Get industry-specific design psychology\r\n */\r\nfunction getIndustryDesignPsychology(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {\r\n    return `- Use warm, appetizing colors that stimulate hunger and comfort\r\n- Incorporate food photography principles with rich textures\r\n- Create cozy, inviting atmosphere through design elements\r\n- Focus on sensory appeal and mouth-watering visual presentation`;\r\n  }\r\n\r\n  if (type.includes('tech') || type.includes('software') || type.includes('digital')) {\r\n    return `- Employ clean, minimalist design with high-tech aesthetics\r\n- Use gradients and modern geometric shapes\r\n- Incorporate subtle tech-inspired elements and icons\r\n- Focus on innovation, efficiency, and cutting-edge appeal`;\r\n  }\r\n\r\n  if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {\r\n    return `- Use calming, trustworthy colors that convey safety and care\r\n- Incorporate clean, sterile design elements\r\n- Focus on professionalism, expertise, and patient comfort\r\n- Use imagery that suggests health, vitality, and well-being`;\r\n  }\r\n\r\n  if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {\r\n    return `- Employ sophisticated, conservative design elements\r\n- Use colors that convey stability, trust, and prosperity\r\n- Incorporate subtle luxury elements and professional imagery\r\n- Focus on security, growth, and financial success`;\r\n  }\r\n\r\n  if (type.includes('real estate') || type.includes('property')) {\r\n    return `- Use aspirational imagery and luxury design elements\r\n- Incorporate architectural lines and premium materials\r\n- Focus on lifestyle, investment, and dream fulfillment\r\n- Use colors that suggest stability, growth, and success`;\r\n  }\r\n\r\n  // Default professional services\r\n  return `- Use professional, trustworthy design elements\r\n- Incorporate subtle premium touches and quality indicators\r\n- Focus on expertise, reliability, and professional excellence\r\n- Use colors and imagery that convey competence and success`;\r\n}\r\n\r\n/**\r\n * Get platform-specific design requirements\r\n */\r\nfunction getPlatformDesignRequirements(platform: Platform): string {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return `- Optimize for Instagram's visual-first environment\r\n- Use bold, eye-catching elements that stand out in feeds\r\n- Incorporate Instagram-native design trends and aesthetics\r\n- Ensure design works perfectly in both feed and story formats`;\r\n\r\n    case 'Facebook':\r\n      return `- Design for Facebook's diverse, multi-generational audience\r\n- Use clear, readable elements that work across age groups\r\n- Incorporate social proof and community-focused elements\r\n- Ensure design is engaging but not overwhelming`;\r\n\r\n    case 'LinkedIn':\r\n      return `- Employ professional, business-focused design elements\r\n- Use conservative colors and sophisticated typography\r\n- Incorporate industry-specific imagery and professional symbols\r\n- Focus on credibility, expertise, and business value`;\r\n\r\n    case 'Twitter':\r\n      return `- Create concise, impactful design that communicates quickly\r\n- Use bold typography and clear visual hierarchy\r\n- Incorporate trending design elements and current aesthetics\r\n- Ensure design is optimized for rapid consumption`;\r\n\r\n    default:\r\n      return `- Create versatile design that works across multiple platforms\r\n- Use universal design principles and broad appeal\r\n- Ensure scalability and readability across different contexts\r\n- Focus on timeless, professional aesthetics`;\r\n  }\r\n}\r\n\r\n/**\r\n * Get color psychology based on business type and location\r\n */\r\nfunction getColorPsychology(businessType: string, location: string): string {\r\n  const type = businessType.toLowerCase();\r\n  const loc = location.toLowerCase();\r\n\r\n  let baseColors = '';\r\n  let culturalColors = '';\r\n\r\n  // Business type color psychology\r\n  if (type.includes('restaurant') || type.includes('food')) {\r\n    baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    baseColors = 'modern blues, teals, and purples to convey innovation and trust';\r\n  } else if (type.includes('health') || type.includes('medical')) {\r\n    baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';\r\n  } else if (type.includes('finance') || type.includes('banking')) {\r\n    baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';\r\n  } else {\r\n    baseColors = 'professional blues, grays, and accent colors to convey trust and competence';\r\n  }\r\n\r\n  // Cultural color adaptations\r\n  if (loc.includes('dubai') || loc.includes('uae')) {\r\n    culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\\'s premium market expectations';\r\n  } else if (loc.includes('london') || loc.includes('uk')) {\r\n    culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';\r\n  } else if (loc.includes('new york') || loc.includes('nyc')) {\r\n    culturalColors = 'Employ bold, confident colors that match New York\\'s dynamic business environment';\r\n  } else {\r\n    culturalColors = 'Use universally appealing professional color combinations';\r\n  }\r\n\r\n  return `- Primary Strategy: ${baseColors}\r\n- Cultural Adaptation: ${culturalColors}\r\n- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors\r\n- Contrast Optimization: Ensure maximum readability and visual impact`;\r\n}\r\n\r\n/**\r\n * Get typography strategy for conversion\r\n */\r\nfunction getTypographyStrategy(businessType: string, platform: Platform): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  let fontStrategy = '';\r\n  let hierarchyStrategy = '';\r\n\r\n  if (type.includes('luxury') || type.includes('premium')) {\r\n    fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';\r\n  } else if (type.includes('creative') || type.includes('design')) {\r\n    fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';\r\n  } else {\r\n    fontStrategy = 'Professional, highly readable fonts that convey trust and competence';\r\n  }\r\n\r\n  hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact\r\n- Secondary Text: Clear, readable subheadings that support the main message\r\n- Call-to-Action: Distinctive typography that stands out and drives action\r\n- Supporting Text: Clean, professional fonts for additional information`;\r\n\r\n  return `- Font Selection: ${fontStrategy}\r\n- Visual Hierarchy: ${hierarchyStrategy}\r\n- Readability: Optimized for ${platform} viewing conditions and mobile devices\r\n- Conversion Focus: Typography choices designed to guide the eye and encourage action`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA6HsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { generatePostFromProfile as generatePostFromProfileFlow } from \"@/ai/flows/generate-post-from-profile\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport {\r\n  detectAndPopulateLanguages,\r\n  getLanguageInstructionForProfile,\r\n  updateLanguageDetectionIfNeeded\r\n} from \"@/lib/services/brand-language-service\";\r\n// import { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\"; // Temporarily disabled\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\nimport { DesignGenerationService } from \"@/ai/models/services/design-generation-service\";\r\nimport type { RevoModelId } from \"@/ai/models/types/model-types\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\r\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\r\n    console.log(\"🔍 Result type:\", typeof result);\r\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n    console.error(\"❌ Error analyzing brand:\", error);\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Update language detection if needed\r\n    const profileWithLanguages = updateLanguageDetectionIfNeeded(profile);\r\n\r\n    // Generate language instructions for AI\r\n    const languageInstructions = getLanguageInstructionForProfile(profileWithLanguages);\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    const postDetails = await generatePostFromProfileFlow({\r\n      businessType: profileWithLanguages.businessType,\r\n      location: profileWithLanguages.location,\r\n      writingTone: profileWithLanguages.writingTone,\r\n      contentThemes: profileWithLanguages.contentThemes,\r\n      visualStyle: profileWithLanguages.visualStyle,\r\n      logoDataUrl: profileWithLanguages.logoDataUrl,\r\n      designExamples: effectiveDesignExamples, // Use design examples based on consistency preference\r\n      primaryColor: profileWithLanguages.primaryColor,\r\n      accentColor: profileWithLanguages.accentColor,\r\n      backgroundColor: profileWithLanguages.backgroundColor,\r\n      dayOfWeek,\r\n      currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      // Pass new detailed fields\r\n      services: servicesString,\r\n      targetAudience: profileWithLanguages.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      // Pass brand consistency preferences\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      // Pass intelligent language instructions\r\n      languageInstructions: languageInstructions,\r\n    });\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline,\r\n      callToAction: postDetails.callToAction,\r\n      // Include enhanced AI features\r\n      contentVariants: postDetails.contentVariants,\r\n      hashtagAnalysis: postDetails.hashtagAnalysis,\r\n      // Include advanced AI features\r\n      marketIntelligence: postDetails.marketIntelligence,\r\n      // Include local context features\r\n      localContext: postDetails.localContext,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    console.error(\"Error generating content:\", error);\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    console.error(\"Error generating video content:\", error);\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Error generating creative asset:\", error);\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n    console.log('🎨 Enhanced Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', finalImageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n    console.log('- Enhancements Enabled:', enableEnhancements);\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n      });\r\n\r\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\r\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\r\n\r\n    } catch (gemini25Error) {\r\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\r\n\r\n      try {\r\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\r\n      } catch (openaiError) {\r\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\r\n\r\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\r\n      }\r\n    }\r\n\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating enhanced design:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n    console.log('🎨 Gemini HD Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', imageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n\r\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n    console.log('✅ Gemini HD enhanced design generated successfully');\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error in Gemini HD design generation:', error);\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log('🎨 Generating content with artifacts...');\r\n    console.log('- Platform:', platform);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n    console.log('- Enhanced Design:', useEnhancedDesign);\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\r\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\r\n        id: a.id,\r\n        name: a.name,\r\n        type: a.type,\r\n        usageType: a.usageType,\r\n        isActive: a.isActive,\r\n        instructions: a.instructions\r\n      })));\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      console.log('🔄 Enhanced design disabled, using base content generation');\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\r\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\r\n\r\n    if (targetArtifacts.length === 0) {\r\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\r\n    } else {\r\n      console.log('🎯 Using enhanced design with artifact context');\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n          console.log('📝 Using message from exact-use artifact');\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      console.log('🎨 Applying style references from artifacts');\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n        console.log('🎨 Using artifact-inspired visual style');\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    console.log('✅ Enhanced content with artifacts generated successfully');\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating content with artifacts:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content using the new Revo model system\r\n * This action uses the proper model architecture with version-specific implementations\r\n */\r\nexport async function generateContentWithRevoModelAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  revoModel: RevoModelId,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = []\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log(`🎨 Generating content with ${revoModel} model...`);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Business Type:', profile.businessType);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n\r\n    // Handle artifacts if provided\r\n    let targetArtifacts: Artifact[] = [];\r\n    if (artifactIds.length > 0) {\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    // Prepare artifact instructions\r\n    let artifactInstructions = '';\r\n    if (targetArtifacts.length > 0) {\r\n      const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      if (exactUseArtifacts.length > 0) {\r\n        artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\\n';\r\n        exactUseArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\\n`;\r\n        });\r\n      }\r\n\r\n      if (referenceArtifacts.length > 0) {\r\n        artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\\n';\r\n        referenceArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\\n`;\r\n        });\r\n      }\r\n    }\r\n\r\n    // Use simplified Revo model generation with text validation\r\n    console.log(`🎨 Using Revo ${revoModel} with text validation...`);\r\n    console.log('🔧 DEBUG: This is the SIMPLIFIED code path');\r\n\r\n    // Generate dynamic and varied text for each design\r\n    const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);\r\n    let imageText = textVariations.selectedText;\r\n\r\n    if (revoModel === 'revo-1.0') {\r\n      console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');\r\n      console.log('🔍 Original text:', imageText);\r\n\r\n      // Simple text validation for Revo 1.0\r\n      const words = imageText.split(' ').filter(word => word.length > 0);\r\n      if (words.length > 25) {\r\n        console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);\r\n        imageText = words.slice(0, 25).join(' ');\r\n      }\r\n      console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);\r\n    }\r\n\r\n    // Use sophisticated design prompt created by 20-year veteran designer + marketer\r\n    const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);\r\n\r\n    const designResult = await generateCreativeAssetFlow({\r\n      prompt: designPrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: profile,\r\n      maskDataUrl: null\r\n    });\r\n\r\n    if (!designResult.imageUrl) {\r\n      throw new Error('Design generation failed: No image URL returned');\r\n    }\r\n\r\n    // Generate content using the standard flow for caption and hashtags\r\n    const contentResult = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      services: Array.isArray(profile.services)\r\n        ? profile.services.map(s => typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\\n')\r\n        : profile.services || '',\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\\n') : profile.keyFeatures || '',\r\n      competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\\n') : profile.competitiveAdvantages || '',\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    // Combine the design result with content result\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: new Date().toISOString(),\r\n      content: contentResult.content,\r\n      hashtags: contentResult.hashtags,\r\n      status: 'generated',\r\n      variants: [{\r\n        platform,\r\n        imageUrl: designResult.imageUrl || '',\r\n        caption: contentResult.content,\r\n        hashtags: contentResult.hashtags\r\n      }],\r\n      catchyWords: contentResult.catchyWords,\r\n      subheadline: contentResult.subheadline,\r\n      callToAction: contentResult.callToAction,\r\n      contentVariants: contentResult.contentVariants,\r\n      hashtagAnalysis: contentResult.hashtagAnalysis,\r\n      marketIntelligence: contentResult.marketIntelligence,\r\n      localContext: contentResult.localContext,\r\n      // Add Revo model metadata\r\n      revoModelUsed: revoModel,\r\n      qualityScore: 8, // Default quality score for Revo models\r\n      processingTime: Date.now() - Date.now(), // Will be calculated properly\r\n      creditsUsed: 1,\r\n      enhancementsApplied: [`Revo ${revoModel} Generation`, 'Text Validation', 'Professional Design']\r\n    };\r\n\r\n    console.log(`✅ Content generated successfully with ${revoModel}`);\r\n    console.log(`⭐ Quality Score: 8/10`);\r\n    console.log(`⚡ Processing Time: Fast generation`);\r\n    console.log(`💰 Credits Used: 1`);\r\n\r\n    return newPost;\r\n\r\n  } catch (error) {\r\n    console.error(`❌ Error generating content with ${revoModel}:`, error);\r\n    throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate professional marketing-driven text with cultural awareness\r\n * Designed by a 20-year veteran designer + 20-year marketing expert\r\n * Now deeply connected to actual brand profile information\r\n */\r\nfunction generateDynamicTextForRevo(profile: BrandProfile, revoModel: RevoModelId, platform: Platform) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n\r\n  // Generate sophisticated marketing copy using actual brand profile data\r\n  const marketingCopy = generateMarketingCopy(profile, platform);\r\n\r\n  console.log(`🎯 Generated personalized marketing copy for ${businessName}: \"${marketingCopy}\"`);\r\n\r\n  return {\r\n    selectedText: marketingCopy,\r\n    allVariations: [marketingCopy],\r\n    variationIndex: 0\r\n  };\r\n}\r\n\r\n/**\r\n * Generate sophisticated marketing copy that sells\r\n * Combines 20 years of design + marketing expertise with actual brand profile data\r\n * Now deeply personalized using real business information\r\n * LANGUAGE RESTRICTION: English only - no Arabic, Hindi, Chinese, or other non-English text\r\n */\r\nfunction generateMarketingCopy(profile: BrandProfile, platform: Platform): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract real business intelligence from profile\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Cultural and regional insights\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Generate catchy headline using actual business strengths (max 5 words)\r\n  const catchyHeadlines = [\r\n    `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,\r\n    `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,\r\n    `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,\r\n    `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,\r\n    `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`\r\n  ];\r\n\r\n  // Generate subheadline using real competitive advantages (max 14 words)\r\n  const subheadlines = [\r\n    `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,\r\n    `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,\r\n    `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,\r\n    `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,\r\n    `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`\r\n  ];\r\n\r\n  // Generate call-to-action using actual business context\r\n  const callToActions = [\r\n    `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,\r\n    `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,\r\n    `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,\r\n    `${getRandomElement(['Book', 'Schedule', 'Request'])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,\r\n    `${getRandomElement(['Start', 'Begin', 'Launch'])} Your ${businessIntelligence.journeyType} Today`\r\n  ];\r\n\r\n  // Randomly select components\r\n  const catchyWords = getRandomElement(catchyHeadlines);\r\n  const subheadline = getRandomElement(subheadlines);\r\n  const cta = getRandomElement(callToActions);\r\n\r\n  // Combine based on marketing best practices and business context\r\n  const marketingFormats = [\r\n    `${catchyWords}\\n${subheadline}\\n${cta}`,\r\n    `${catchyWords}\\n${subheadline}`,\r\n    `${catchyWords}\\n${cta}`,\r\n    `${subheadline}\\n${cta}`,\r\n    catchyWords\r\n  ];\r\n\r\n  return getRandomElement(marketingFormats);\r\n}\r\n\r\n/**\r\n * Get cultural context and local market insights\r\n */\r\nfunction getCulturalContext(location: string) {\r\n  // Default context\r\n  let context = {\r\n    localTerms: ['Premium', 'Professional', 'Expert'],\r\n    marketingStyle: 'Professional',\r\n    targetAudience: 'businesses',\r\n    localMarket: 'modern',\r\n    socialProof: 'thousands of clients',\r\n    valueProposition: 'Industry-leading',\r\n    competitiveAdvantage: 'proven expertise',\r\n    urgencyTrigger: 'Don\\'t miss out on',\r\n    actionWords: 'Connect with',\r\n    localCTA: 'Get Started',\r\n    ctaUrgency: 'Now'\r\n  };\r\n\r\n  // Location-specific cultural adaptations\r\n  if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {\r\n    context = {\r\n      localTerms: ['Premium', 'Luxury', 'Elite', 'Exclusive'],\r\n      marketingStyle: 'Luxury-focused',\r\n      targetAudience: 'discerning clients',\r\n      localMarket: 'Dubai\\'s dynamic',\r\n      socialProof: 'leading UAE businesses',\r\n      valueProposition: 'World-class',\r\n      competitiveAdvantage: 'international excellence',\r\n      urgencyTrigger: 'Seize the opportunity for',\r\n      actionWords: 'Experience',\r\n      localCTA: 'Book Your Exclusive Consultation',\r\n      ctaUrgency: 'Today'\r\n    };\r\n  } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {\r\n    context = {\r\n      localTerms: ['Bespoke', 'Tailored', 'Refined'],\r\n      marketingStyle: 'Sophisticated',\r\n      targetAudience: 'discerning professionals',\r\n      localMarket: 'London\\'s competitive',\r\n      socialProof: 'established UK enterprises',\r\n      valueProposition: 'Expertly crafted',\r\n      competitiveAdvantage: 'British excellence',\r\n      urgencyTrigger: 'Secure your',\r\n      actionWords: 'Discover',\r\n      localCTA: 'Arrange Your Consultation',\r\n      ctaUrgency: 'Promptly'\r\n    };\r\n  } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {\r\n    context = {\r\n      localTerms: ['Cutting-edge', 'Innovative', 'Game-changing'],\r\n      marketingStyle: 'Bold and direct',\r\n      targetAudience: 'ambitious professionals',\r\n      localMarket: 'NYC\\'s fast-paced',\r\n      socialProof: 'successful New York businesses',\r\n      valueProposition: 'Results-driven',\r\n      competitiveAdvantage: 'New York hustle',\r\n      urgencyTrigger: 'Don\\'t let competitors get',\r\n      actionWords: 'Dominate with',\r\n      localCTA: 'Schedule Your Strategy Session',\r\n      ctaUrgency: 'ASAP'\r\n    };\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n/**\r\n * Extract business intelligence from brand profile for personalized marketing\r\n * Analyzes actual business data to create relevant marketing copy\r\n */\r\nfunction extractBusinessIntelligence(profile: BrandProfile) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n\r\n  // Extract primary service information\r\n  const primaryService = services[0]?.name || services[0] || businessType;\r\n  const serviceDescription = services[0]?.description || '';\r\n  const targetAudience = services[0]?.targetAudience || 'businesses';\r\n  const keyFeatures = services[0]?.keyFeatures || '';\r\n  const competitiveAdvantages = services[0]?.competitiveAdvantages || '';\r\n\r\n  // Analyze description for key terms\r\n  const descriptionWords = description.toLowerCase().split(/\\s+/);\r\n  const strengthWords = extractStrengthWords(description, businessType);\r\n  const valueWords = extractValueWords(description, keyFeatures);\r\n  const benefitWords = extractBenefitWords(description, competitiveAdvantages);\r\n\r\n  // Extract competitive advantages\r\n  const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);\r\n  const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);\r\n  const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);\r\n\r\n  // Extract target audience specifics\r\n  const actualTargetAudience = extractTargetAudience(targetAudience, businessType);\r\n\r\n  // Generate contextual elements\r\n  const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);\r\n  const mainValue = extractMainValue(description, keyFeatures);\r\n  const offerType = generateOfferType(businessType, services);\r\n  const consultationType = generateConsultationType(businessType);\r\n  const journeyType = generateJourneyType(businessType, primaryService);\r\n  const urgencyTrigger = generateUrgencyTrigger(businessType, location);\r\n\r\n  // Extract outcome words from business context\r\n  const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);\r\n  const differentiators = extractDifferentiators(competitiveAdvantages, businessType);\r\n\r\n  return {\r\n    primaryService,\r\n    strengthWords,\r\n    valueWords,\r\n    benefitWords,\r\n    realCompetitiveAdvantage,\r\n    uniqueFeature,\r\n    realDifferentiator,\r\n    actualTargetAudience,\r\n    keyBenefit,\r\n    mainValue,\r\n    offerType,\r\n    consultationType,\r\n    journeyType,\r\n    urgencyTrigger,\r\n    outcomeWords,\r\n    differentiators\r\n  };\r\n}\r\n\r\n/**\r\n * Extract strength words from business description and type\r\n */\r\nfunction extractStrengthWords(description: string, businessType: string): string[] {\r\n  const strengthKeywords = ['leading', 'premium', 'expert', 'professional', 'trusted', 'innovative', 'cutting-edge', 'award-winning', 'certified', 'proven', 'reliable', 'secure', 'fast', 'efficient', 'quality', 'excellence', 'superior', 'advanced', 'specialized'];\r\n  const found = strengthKeywords.filter(word => description.toLowerCase().includes(word));\r\n\r\n  // Add business type specific strengths\r\n  const typeStrengths = getBusinessTypeStrengths(businessType);\r\n\r\n  return found.length > 0 ? found : typeStrengths;\r\n}\r\n\r\n/**\r\n * Extract value words from description and features\r\n */\r\nfunction extractValueWords(description: string, keyFeatures: string): string[] {\r\n  const valueKeywords = ['value', 'results', 'success', 'growth', 'efficiency', 'savings', 'profit', 'revenue', 'performance', 'productivity', 'quality', 'excellence', 'innovation', 'solutions', 'benefits'];\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n  const found = valueKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Excellence', 'Results', 'Success'];\r\n}\r\n\r\n/**\r\n * Extract benefit words from description and competitive advantages\r\n */\r\nfunction extractBenefitWords(description: string, competitiveAdvantages: string): string[] {\r\n  const benefitKeywords = ['success', 'growth', 'efficiency', 'savings', 'results', 'performance', 'quality', 'reliability', 'security', 'speed', 'convenience', 'expertise', 'support', 'innovation', 'excellence'];\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const found = benefitKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Success', 'Quality', 'Results'];\r\n}\r\n\r\n/**\r\n * Extract competitive advantage from actual business data\r\n */\r\nfunction extractCompetitiveAdvantage(competitiveAdvantages: string, businessType: string): string {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    // Extract first meaningful advantage\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim());\r\n    return advantages[0] || getDefaultAdvantage(businessType);\r\n  }\r\n  return getDefaultAdvantage(businessType);\r\n}\r\n\r\n/**\r\n * Extract unique feature from key features\r\n */\r\nfunction extractUniqueFeature(keyFeatures: string, businessType: string): string {\r\n  if (keyFeatures && keyFeatures.length > 10) {\r\n    const features = keyFeatures.split(',').map(s => s.trim());\r\n    return features[0] || getDefaultFeature(businessType);\r\n  }\r\n  return getDefaultFeature(businessType);\r\n}\r\n\r\n/**\r\n * Extract differentiator from competitive advantages and description\r\n */\r\nfunction extractDifferentiator(competitiveAdvantages: string, description: string): string {\r\n  const text = `${competitiveAdvantages} ${description}`.toLowerCase();\r\n\r\n  if (text.includes('24/7') || text.includes('24-7')) return '24/7 availability';\r\n  if (text.includes('fastest') || text.includes('quick') || text.includes('speed')) return 'fastest service';\r\n  if (text.includes('secure') || text.includes('security')) return 'advanced security';\r\n  if (text.includes('expert') || text.includes('experience')) return 'expert knowledge';\r\n  if (text.includes('custom') || text.includes('tailored')) return 'customized solutions';\r\n  if (text.includes('award') || text.includes('certified')) return 'award-winning service';\r\n\r\n  return 'professional excellence';\r\n}\r\n\r\n/**\r\n * Extract target audience from service data\r\n */\r\nfunction extractTargetAudience(targetAudience: string, businessType: string): string {\r\n  if (targetAudience && targetAudience.length > 5) {\r\n    return targetAudience.split(',')[0].trim() || getDefaultAudience(businessType);\r\n  }\r\n  return getDefaultAudience(businessType);\r\n}\r\n\r\n/**\r\n * Extract key benefit from service description and advantages\r\n */\r\nfunction extractKeyBenefit(serviceDescription: string, competitiveAdvantages: string): string {\r\n  const text = `${serviceDescription} ${competitiveAdvantages}`.toLowerCase();\r\n\r\n  if (text.includes('save') || text.includes('cost')) return 'cost savings';\r\n  if (text.includes('fast') || text.includes('quick') || text.includes('speed')) return 'faster results';\r\n  if (text.includes('secure') || text.includes('safety')) return 'enhanced security';\r\n  if (text.includes('grow') || text.includes('increase')) return 'business growth';\r\n  if (text.includes('efficient') || text.includes('optimize')) return 'improved efficiency';\r\n  if (text.includes('quality') || text.includes('premium')) return 'superior quality';\r\n\r\n  return 'exceptional results';\r\n}\r\n\r\n/**\r\n * Extract main value proposition\r\n */\r\nfunction extractMainValue(description: string, keyFeatures: string): string {\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n\r\n  if (text.includes('transform') || text.includes('revolutionize')) return 'transforms your business';\r\n  if (text.includes('maximize') || text.includes('optimize')) return 'maximizes your potential';\r\n  if (text.includes('accelerate') || text.includes('boost')) return 'accelerates your growth';\r\n  if (text.includes('streamline') || text.includes('simplify')) return 'streamlines your operations';\r\n  if (text.includes('enhance') || text.includes('improve')) return 'enhances your performance';\r\n\r\n  return 'delivers exceptional value';\r\n}\r\n\r\n/**\r\n * Generate business type-specific offer types\r\n */\r\nfunction generateOfferType(businessType: string, services: any[]): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Free Tasting';\r\n  if (type.includes('tech') || type.includes('software')) return 'Free Demo';\r\n  if (type.includes('health') || type.includes('medical')) return 'Free Consultation';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Free Assessment';\r\n  if (type.includes('real estate')) return 'Free Valuation';\r\n  if (type.includes('legal')) return 'Free Consultation';\r\n  if (type.includes('education') || type.includes('training')) return 'Free Trial';\r\n\r\n  return 'Free Consultation';\r\n}\r\n\r\n/**\r\n * Generate consultation types based on business\r\n */\r\nfunction generateConsultationType(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('software')) return 'Strategy Session';\r\n  if (type.includes('health') || type.includes('medical')) return 'Health Assessment';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Financial Review';\r\n  if (type.includes('real estate')) return 'Property Consultation';\r\n  if (type.includes('legal')) return 'Legal Consultation';\r\n  if (type.includes('marketing')) return 'Marketing Audit';\r\n\r\n  return 'Business Consultation';\r\n}\r\n\r\n/**\r\n * Generate journey types\r\n */\r\nfunction generateJourneyType(businessType: string, primaryService: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('digital')) return 'Digital Transformation';\r\n  if (type.includes('health') || type.includes('wellness')) return 'Wellness Journey';\r\n  if (type.includes('finance') || type.includes('investment')) return 'Financial Success';\r\n  if (type.includes('real estate')) return 'Property Investment';\r\n  if (type.includes('education')) return 'Learning Journey';\r\n\r\n  return 'Success Journey';\r\n}\r\n\r\n/**\r\n * Generate urgency triggers based on business and location\r\n */\r\nfunction generateUrgencyTrigger(businessType: string, location: string): string {\r\n  const triggers = ['Limited Time Offer', 'Act Now', 'Don\\'t Wait', 'Book Today', 'Available Now'];\r\n\r\n  if (location.toLowerCase().includes('dubai')) return 'Exclusive Dubai Offer';\r\n  if (location.toLowerCase().includes('london')) return 'Limited London Availability';\r\n  if (location.toLowerCase().includes('new york')) return 'NYC Exclusive Deal';\r\n\r\n  return getRandomElement(triggers);\r\n}\r\n\r\n/**\r\n * Get business type specific strengths\r\n */\r\nfunction getBusinessTypeStrengths(businessType: string): string[] {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return ['Premium', 'Fresh', 'Authentic'];\r\n  if (type.includes('tech') || type.includes('software')) return ['Innovative', 'Cutting-edge', 'Advanced'];\r\n  if (type.includes('health') || type.includes('medical')) return ['Trusted', 'Professional', 'Expert'];\r\n  if (type.includes('finance') || type.includes('banking')) return ['Secure', 'Reliable', 'Trusted'];\r\n  if (type.includes('real estate')) return ['Premium', 'Exclusive', 'Professional'];\r\n  if (type.includes('legal')) return ['Expert', 'Trusted', 'Professional'];\r\n  if (type.includes('education')) return ['Expert', 'Certified', 'Professional'];\r\n\r\n  return ['Professional', 'Quality', 'Trusted'];\r\n}\r\n\r\n/**\r\n * Get default competitive advantage by business type\r\n */\r\nfunction getDefaultAdvantage(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Fresh, authentic ingredients';\r\n  if (type.includes('tech') || type.includes('software')) return 'Cutting-edge technology';\r\n  if (type.includes('health') || type.includes('medical')) return 'Expert medical care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Secure financial solutions';\r\n  if (type.includes('real estate')) return 'Premium property expertise';\r\n  if (type.includes('legal')) return 'Expert legal guidance';\r\n\r\n  return 'Professional excellence';\r\n}\r\n\r\n/**\r\n * Get default feature by business type\r\n */\r\nfunction getDefaultFeature(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'farm-to-table freshness';\r\n  if (type.includes('tech') || type.includes('software')) return 'advanced automation';\r\n  if (type.includes('health') || type.includes('medical')) return 'personalized care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'secure transactions';\r\n  if (type.includes('real estate')) return 'market expertise';\r\n\r\n  return 'personalized service';\r\n}\r\n\r\n/**\r\n * Get default audience by business type\r\n */\r\nfunction getDefaultAudience(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'food enthusiasts';\r\n  if (type.includes('tech') || type.includes('software')) return 'forward-thinking businesses';\r\n  if (type.includes('health') || type.includes('medical')) return 'health-conscious individuals';\r\n  if (type.includes('finance') || type.includes('banking')) return 'smart investors';\r\n  if (type.includes('real estate')) return 'property investors';\r\n\r\n  return 'discerning clients';\r\n}\r\n\r\n/**\r\n * Extract outcome words from business context\r\n */\r\nfunction extractOutcomeWords(description: string, competitiveAdvantages: string): string[] {\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const outcomes = ['success', 'growth', 'results', 'performance', 'efficiency', 'savings', 'profit', 'revenue'];\r\n  const found = outcomes.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['success', 'results', 'growth'];\r\n}\r\n\r\n/**\r\n * Extract differentiators from competitive advantages\r\n */\r\nfunction extractDifferentiators(competitiveAdvantages: string, businessType: string): string[] {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim().split(' ')[0]);\r\n    return advantages.slice(0, 3);\r\n  }\r\n\r\n  return getBusinessTypeStrengths(businessType);\r\n}\r\n\r\n/**\r\n * Get random element from array\r\n */\r\nfunction getRandomElement<T>(array: T[]): T {\r\n  return array[Math.floor(Math.random() * array.length)];\r\n}\r\n\r\n/**\r\n * Create professional design prompt with 20 years of design + marketing expertise\r\n * Combines cultural awareness, psychology, and visual design mastery\r\n */\r\nfunction createProfessionalDesignPrompt(imageText: string, platform: Platform, profile: BrandProfile, revoModel: RevoModelId): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract business intelligence for design context\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Get cultural context for design decisions\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Industry-specific design psychology\r\n  const industryDesignPsychology = getIndustryDesignPsychology(businessType);\r\n\r\n  // Platform-specific design requirements\r\n  const platformRequirements = getPlatformDesignRequirements(platform);\r\n\r\n  // Color psychology based on business type and culture\r\n  const colorPsychology = getColorPsychology(businessType, location);\r\n\r\n  // Typography psychology for conversion\r\n  const typographyStrategy = getTypographyStrategy(businessType, platform);\r\n\r\n  return `Create an exceptional, conversion-focused ${platform} design for ${businessName} that embodies 20 years of professional design and marketing expertise.\r\n\r\nLANGUAGE REQUIREMENTS:\r\n- ALL TEXT MUST BE IN ENGLISH ONLY\r\n- NO Arabic, Hindi, Chinese, or other non-English characters\r\n- NO transliterated text or mixed languages\r\n- Use clear, professional English throughout\r\n\r\nBUSINESS INTELLIGENCE & CONTEXT:\r\n- Company: ${businessName} (${businessType})\r\n- Primary Service: ${businessIntelligence.primaryService}\r\n- Location: ${location}\r\n- Target Audience: ${businessIntelligence.actualTargetAudience}\r\n- Key Differentiator: ${businessIntelligence.realDifferentiator}\r\n- Unique Value: ${businessIntelligence.mainValue}\r\n- Competitive Advantage: ${businessIntelligence.realCompetitiveAdvantage}\r\n\r\nTEXT TO INTEGRATE: \"${imageText}\"\r\n\r\nBRAND-SPECIFIC DESIGN REQUIREMENTS:\r\n- Must communicate: ${businessIntelligence.realCompetitiveAdvantage}\r\n- Must highlight: ${businessIntelligence.uniqueFeature}\r\n- Must appeal to: ${businessIntelligence.actualTargetAudience}\r\n- Must convey: ${businessIntelligence.keyBenefit}\r\n\r\nDESIGN PSYCHOLOGY & STRATEGY:\r\n${industryDesignPsychology}\r\n\r\nVISUAL HIERARCHY & COMPOSITION:\r\n- Apply the golden ratio and rule of thirds for optimal visual flow\r\n- Create clear focal points that guide the eye to key conversion elements\r\n- Use strategic white space to enhance readability and premium feel\r\n- Implement Z-pattern or F-pattern layout for maximum engagement\r\n\r\nCOLOR STRATEGY:\r\n${colorPsychology}\r\n\r\nTYPOGRAPHY MASTERY:\r\n${typographyStrategy}\r\n\r\nCULTURAL DESIGN ADAPTATION:\r\n- ${culturalContext.localMarket} aesthetic preferences\r\n- ${culturalContext.targetAudience} visual expectations\r\n- Regional design trends and cultural symbols\r\n- Local color associations and meanings\r\n\r\nCONVERSION OPTIMIZATION:\r\n- Design elements that create urgency and desire\r\n- Visual cues that guide toward call-to-action\r\n- Trust signals through professional presentation\r\n- Emotional triggers through strategic imagery and layout\r\n\r\nPLATFORM OPTIMIZATION:\r\n${platformRequirements}\r\n\r\nTECHNICAL EXCELLENCE:\r\n- Aspect Ratio: 1:1 (perfect square)\r\n- Resolution: Ultra-high quality, print-ready standards\r\n- Text Clarity: Crystal clear, perfectly readable at all sizes\r\n- Brand Consistency: Align with professional brand standards\r\n- Mobile Optimization: Ensure perfect display on all devices\r\n\r\nFINAL QUALITY STANDARDS:\r\nThis design must look like it was created by a top-tier creative agency specifically for ${businessName}. Every element should reflect their unique value proposition: \"${businessIntelligence.realCompetitiveAdvantage}\". The design should immediately communicate their expertise in ${businessIntelligence.primaryService} while appealing directly to ${businessIntelligence.actualTargetAudience}.\r\n\r\nThe final result should be a sophisticated, professional design that drives ${businessIntelligence.actualTargetAudience} to choose ${businessName} over competitors and take immediate action.\r\n\r\nMake it absolutely irresistible for ${businessIntelligence.actualTargetAudience} and perfectly aligned with ${businessName}'s brand identity.`;\r\n}\r\n\r\n/**\r\n * Get industry-specific design psychology\r\n */\r\nfunction getIndustryDesignPsychology(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {\r\n    return `- Use warm, appetizing colors that stimulate hunger and comfort\r\n- Incorporate food photography principles with rich textures\r\n- Create cozy, inviting atmosphere through design elements\r\n- Focus on sensory appeal and mouth-watering visual presentation`;\r\n  }\r\n\r\n  if (type.includes('tech') || type.includes('software') || type.includes('digital')) {\r\n    return `- Employ clean, minimalist design with high-tech aesthetics\r\n- Use gradients and modern geometric shapes\r\n- Incorporate subtle tech-inspired elements and icons\r\n- Focus on innovation, efficiency, and cutting-edge appeal`;\r\n  }\r\n\r\n  if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {\r\n    return `- Use calming, trustworthy colors that convey safety and care\r\n- Incorporate clean, sterile design elements\r\n- Focus on professionalism, expertise, and patient comfort\r\n- Use imagery that suggests health, vitality, and well-being`;\r\n  }\r\n\r\n  if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {\r\n    return `- Employ sophisticated, conservative design elements\r\n- Use colors that convey stability, trust, and prosperity\r\n- Incorporate subtle luxury elements and professional imagery\r\n- Focus on security, growth, and financial success`;\r\n  }\r\n\r\n  if (type.includes('real estate') || type.includes('property')) {\r\n    return `- Use aspirational imagery and luxury design elements\r\n- Incorporate architectural lines and premium materials\r\n- Focus on lifestyle, investment, and dream fulfillment\r\n- Use colors that suggest stability, growth, and success`;\r\n  }\r\n\r\n  // Default professional services\r\n  return `- Use professional, trustworthy design elements\r\n- Incorporate subtle premium touches and quality indicators\r\n- Focus on expertise, reliability, and professional excellence\r\n- Use colors and imagery that convey competence and success`;\r\n}\r\n\r\n/**\r\n * Get platform-specific design requirements\r\n */\r\nfunction getPlatformDesignRequirements(platform: Platform): string {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return `- Optimize for Instagram's visual-first environment\r\n- Use bold, eye-catching elements that stand out in feeds\r\n- Incorporate Instagram-native design trends and aesthetics\r\n- Ensure design works perfectly in both feed and story formats`;\r\n\r\n    case 'Facebook':\r\n      return `- Design for Facebook's diverse, multi-generational audience\r\n- Use clear, readable elements that work across age groups\r\n- Incorporate social proof and community-focused elements\r\n- Ensure design is engaging but not overwhelming`;\r\n\r\n    case 'LinkedIn':\r\n      return `- Employ professional, business-focused design elements\r\n- Use conservative colors and sophisticated typography\r\n- Incorporate industry-specific imagery and professional symbols\r\n- Focus on credibility, expertise, and business value`;\r\n\r\n    case 'Twitter':\r\n      return `- Create concise, impactful design that communicates quickly\r\n- Use bold typography and clear visual hierarchy\r\n- Incorporate trending design elements and current aesthetics\r\n- Ensure design is optimized for rapid consumption`;\r\n\r\n    default:\r\n      return `- Create versatile design that works across multiple platforms\r\n- Use universal design principles and broad appeal\r\n- Ensure scalability and readability across different contexts\r\n- Focus on timeless, professional aesthetics`;\r\n  }\r\n}\r\n\r\n/**\r\n * Get color psychology based on business type and location\r\n */\r\nfunction getColorPsychology(businessType: string, location: string): string {\r\n  const type = businessType.toLowerCase();\r\n  const loc = location.toLowerCase();\r\n\r\n  let baseColors = '';\r\n  let culturalColors = '';\r\n\r\n  // Business type color psychology\r\n  if (type.includes('restaurant') || type.includes('food')) {\r\n    baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    baseColors = 'modern blues, teals, and purples to convey innovation and trust';\r\n  } else if (type.includes('health') || type.includes('medical')) {\r\n    baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';\r\n  } else if (type.includes('finance') || type.includes('banking')) {\r\n    baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';\r\n  } else {\r\n    baseColors = 'professional blues, grays, and accent colors to convey trust and competence';\r\n  }\r\n\r\n  // Cultural color adaptations\r\n  if (loc.includes('dubai') || loc.includes('uae')) {\r\n    culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\\'s premium market expectations';\r\n  } else if (loc.includes('london') || loc.includes('uk')) {\r\n    culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';\r\n  } else if (loc.includes('new york') || loc.includes('nyc')) {\r\n    culturalColors = 'Employ bold, confident colors that match New York\\'s dynamic business environment';\r\n  } else {\r\n    culturalColors = 'Use universally appealing professional color combinations';\r\n  }\r\n\r\n  return `- Primary Strategy: ${baseColors}\r\n- Cultural Adaptation: ${culturalColors}\r\n- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors\r\n- Contrast Optimization: Ensure maximum readability and visual impact`;\r\n}\r\n\r\n/**\r\n * Get typography strategy for conversion\r\n */\r\nfunction getTypographyStrategy(businessType: string, platform: Platform): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  let fontStrategy = '';\r\n  let hierarchyStrategy = '';\r\n\r\n  if (type.includes('luxury') || type.includes('premium')) {\r\n    fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';\r\n  } else if (type.includes('creative') || type.includes('design')) {\r\n    fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';\r\n  } else {\r\n    fontStrategy = 'Professional, highly readable fonts that convey trust and competence';\r\n  }\r\n\r\n  hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact\r\n- Secondary Text: Clear, readable subheadings that support the main message\r\n- Call-to-Action: Distinctive typography that stands out and drives action\r\n- Supporting Text: Clean, professional fonts for additional information`;\r\n\r\n  return `- Font Selection: ${fontStrategy}\r\n- Visual Hierarchy: ${hierarchyStrategy}\r\n- Readability: Optimized for ${platform} viewing conditions and mobile devices\r\n- Conversion Focus: Typography choices designed to guide the eye and encourage action`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4NsB,6BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/dashboard/post-card.tsx"], "sourcesContent": ["// src/components/dashboard/post-card.tsx\r\n\"use client\";\r\n\r\nimport * as React from 'react';\r\nimport Image from \"next/image\";\r\nimport { Facebook, Instagram, Linkedin, MoreVertical, Pen, RefreshCw, Twitter, CalendarIcon, Download, Loader2, Video, ChevronLeft, ChevronRight, ImageOff, Copy, Eye } from \"lucide-react\";\r\nimport { toPng } from 'html-to-image';\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardFooter,\r\n  CardHeader,\r\n} from \"@/components/ui/card\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport type { BrandProfile, GeneratedPost, Platform, PostVariant } from \"@/lib/types\";\r\nimport { format } from 'date-fns';\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Label } from '../ui/label';\r\nimport { Textarea } from '../ui/textarea';\r\nimport { Input } from '../ui/input';\r\nimport { generateContentAction, generateVideoContentAction } from '@/app/actions';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { cn } from '@/lib/utils';\r\nimport { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '../ui/carousel';\r\n\r\n// Helper function to validate URLs\r\nconst isValidUrl = (url: string): boolean => {\r\n  if (!url || typeof url !== 'string') {\r\n    return false;\r\n  }\r\n\r\n  // Handle compression placeholders\r\n  if (url === '[COMPRESSED_IMAGE]' || url === '[TRUNCATED]' || url.includes('[') && url.includes(']')) {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    // Check for data URLs (base64 images)\r\n    if (url.startsWith('data:')) {\r\n      return url.includes('base64,') || url.includes('charset=');\r\n    }\r\n\r\n    // Check for HTTP/HTTPS URLs\r\n    const parsedUrl = new URL(url);\r\n    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';\r\n  } catch (error) {\r\n    // Don't log compression placeholders as errors\r\n    if (!url.includes('[') || !url.includes(']')) {\r\n      console.warn('URL validation failed for:', url.substring(0, 50) + '...', error.message);\r\n    }\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Utility function to detect image format from data URL\r\n */\r\nfunction getImageFormatFromDataUrl(dataUrl: string): { format: string; extension: string } {\r\n  if (dataUrl.startsWith('data:image/svg+xml')) {\r\n    return { format: 'svg', extension: 'svg' };\r\n  } else if (dataUrl.startsWith('data:image/png;base64,')) {\r\n    return { format: 'png', extension: 'png' };\r\n  } else if (dataUrl.startsWith('data:image/jpeg;base64,') || dataUrl.startsWith('data:image/jpg;base64,')) {\r\n    return { format: 'jpeg', extension: 'jpg' };\r\n  } else if (dataUrl.startsWith('data:image/webp;base64,')) {\r\n    return { format: 'webp', extension: 'webp' };\r\n  }\r\n  return { format: 'png', extension: 'png' }; // default fallback\r\n}\r\n\r\nconst platformIcons: { [key in Platform]: React.ReactElement } = {\r\n  Facebook: <Facebook className=\"h-4 w-4\" />,\r\n  Instagram: <Instagram className=\"h-4 w-4\" />,\r\n  LinkedIn: <Linkedin className=\"h-4 w-4\" />,\r\n  Twitter: <Twitter className=\"h-4 w-4\" />,\r\n};\r\n\r\ntype PostCardProps = {\r\n  post: GeneratedPost;\r\n  brandProfile: BrandProfile;\r\n  onPostUpdated: (post: GeneratedPost) => Promise<void>;\r\n};\r\n\r\nexport function PostCard({ post, brandProfile, onPostUpdated }: PostCardProps) {\r\n  const [isEditing, setIsEditing] = React.useState(false);\r\n  const [isRegenerating, setIsRegenerating] = React.useState(false);\r\n  const [isGeneratingVideo, setIsGeneratingVideo] = React.useState(false);\r\n  const [editedContent, setEditedContent] = React.useState(post.content);\r\n  const [editedHashtags, setEditedHashtags] = React.useState(post.hashtags);\r\n  const [videoUrl, setVideoUrl] = React.useState<string | undefined>(post.videoUrl);\r\n  const [showVideoDialog, setShowVideoDialog] = React.useState(false);\r\n  const [showImagePreview, setShowImagePreview] = React.useState(false);\r\n  const [previewImageUrl, setPreviewImageUrl] = React.useState<string>('');\r\n  const [activeTab, setActiveTab] = React.useState<Platform>(post.variants[0]?.platform || 'Instagram');\r\n  const downloadRefs = React.useRef<Record<Platform, HTMLDivElement | null>>({} as Record<Platform, HTMLDivElement | null>);\r\n\r\n  // Check if this is a Revo 2.0 post (single platform)\r\n  const isRevo2Post = post.id?.startsWith('revo2-') || post.variants.length === 1;\r\n\r\n  const formattedDate = React.useMemo(() => {\r\n    try {\r\n      const date = new Date(post.date);\r\n      if (isNaN(date.getTime())) {\r\n        // If date is invalid, use current date\r\n        return format(new Date(), 'MMM d, yyyy');\r\n      }\r\n      return format(date, 'MMM d, yyyy');\r\n    } catch (error) {\r\n      // Fallback to current date if any error occurs\r\n      return format(new Date(), 'MMM d, yyyy');\r\n    }\r\n  }, [post.date]);\r\n  const { toast } = useToast();\r\n\r\n  // Platform-specific dimensions - MUST match backend Revo 2.0 generation\r\n  const getPlatformDimensions = React.useCallback((platform: Platform) => {\r\n    switch (platform.toLowerCase()) {\r\n      case 'instagram':\r\n        return { width: 1080, height: 1080, aspectClass: 'aspect-square' };\r\n      case 'facebook':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'twitter':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'linkedin':\r\n        return { width: 1200, height: 675, aspectClass: 'aspect-[16/9]' };\r\n      case 'tiktok':\r\n        return { width: 1080, height: 1920, aspectClass: 'aspect-[9/16]' };\r\n      default:\r\n        return { width: 1080, height: 1080, aspectClass: 'aspect-square' };\r\n    }\r\n  }, []);\r\n\r\n  // Copy functionality\r\n  const handleCopyCaption = React.useCallback(async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(post.content);\r\n      toast({\r\n        title: \"Caption Copied!\",\r\n        description: \"The caption has been copied to your clipboard.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Copy Failed\",\r\n        description: \"Could not copy the caption. Please try again.\",\r\n      });\r\n    }\r\n  }, [post.content, toast]);\r\n\r\n  const handleCopyHashtags = React.useCallback(async () => {\r\n    try {\r\n      const hashtagsText = typeof post.hashtags === 'string' ? post.hashtags : post.hashtags?.join(' ') || '';\r\n      await navigator.clipboard.writeText(hashtagsText);\r\n      toast({\r\n        title: \"Hashtags Copied!\",\r\n        description: \"The hashtags have been copied to your clipboard.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Copy Failed\",\r\n        description: \"Could not copy the hashtags. Please try again.\",\r\n      });\r\n    }\r\n  }, [post.hashtags, toast]);\r\n\r\n  // Image preview functionality\r\n  const handleImagePreview = React.useCallback((imageUrl: string) => {\r\n    setPreviewImageUrl(imageUrl);\r\n    setShowImagePreview(true);\r\n  }, []);\r\n\r\n  const handleDownload = React.useCallback(async () => {\r\n    const activeVariant = post.variants.find(v => v.platform === activeTab);\r\n\r\n    // First try to download the original HD image directly if URL is valid\r\n    if (activeVariant?.imageUrl && isValidUrl(activeVariant.imageUrl)) {\r\n      try {\r\n        // Check if it's a data URL (base64 encoded image)\r\n        if (activeVariant.imageUrl.startsWith('data:')) {\r\n          const { format, extension } = getImageFormatFromDataUrl(activeVariant.imageUrl);\r\n\r\n          // For social media posts, we need raster images (PNG/JPEG), not SVG\r\n          if (format === 'svg') {\r\n            console.log('🎨 Converting SVG to PNG for social media compatibility...');\r\n            // Fall through to the canvas conversion method below\r\n            // This will convert the SVG to a high-quality PNG\r\n          } else {\r\n            // Handle other data URL formats (PNG, JPEG, etc.) directly\r\n            const link = document.createElement('a');\r\n            link.href = activeVariant.imageUrl;\r\n            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n\r\n            toast({\r\n              title: \"Social Media Image Ready\",\r\n              description: `High-definition ${format.toUpperCase()} image downloaded successfully.`,\r\n            });\r\n            return;\r\n          }\r\n        } else {\r\n          // Handle regular HTTP/HTTPS URLs (not data URLs)\r\n          try {\r\n            const response = await fetch(activeVariant.imageUrl);\r\n            const blob = await response.blob();\r\n            const url = window.URL.createObjectURL(blob);\r\n\r\n            // Determine file extension based on content type\r\n            const contentType = response.headers.get('content-type') || blob.type;\r\n            let extension = 'png'; // default\r\n            if (contentType.includes('jpeg') || contentType.includes('jpg')) {\r\n              extension = 'jpg';\r\n            } else if (contentType.includes('webp')) {\r\n              extension = 'webp';\r\n            }\r\n\r\n            const link = document.createElement('a');\r\n            link.href = url;\r\n            link.download = `nevis-social-${post.id}-${activeTab}.${extension}`;\r\n            document.body.appendChild(link);\r\n            link.click();\r\n            document.body.removeChild(link);\r\n            window.URL.revokeObjectURL(url);\r\n\r\n            toast({\r\n              title: \"Social Media Image Ready\",\r\n              description: \"High-definition image downloaded successfully.\",\r\n            });\r\n            return;\r\n          } catch (error) {\r\n            console.warn('Direct download failed, falling back to canvas conversion:', error);\r\n            // Fall through to canvas conversion\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.warn('Direct HD download failed, falling back to capture method:', error);\r\n      }\r\n    }\r\n\r\n    // Fallback: Capture the displayed image with maximum quality settings\r\n    const nodeToCapture = downloadRefs.current[activeTab];\r\n    if (!nodeToCapture) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Download Failed\",\r\n        description: \"Could not find the image element to download.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Check if we're converting an SVG enhanced design\r\n      const activeVariant = post.variants.find(v => v.platform === activeTab);\r\n      const isSvgDataUrl = activeVariant?.imageUrl?.startsWith('data:image/svg+xml');\r\n      const platformDimensions = getPlatformDimensions(activeTab);\r\n\r\n      // Platform-specific optimized settings for social media posts\r\n      const socialMediaSettings = {\r\n        cacheBust: true,\r\n        canvasWidth: platformDimensions.width,\r\n        canvasHeight: platformDimensions.height,\r\n        pixelRatio: 3, // High DPI for crisp images\r\n        quality: 1.0, // Maximum quality\r\n        backgroundColor: '#ffffff', // White background for transparency\r\n        style: {\r\n          borderRadius: '0',\r\n          border: 'none',\r\n        }\r\n      };\r\n\r\n      // Enhanced settings for SVG conversion\r\n      if (isSvgDataUrl) {\r\n        socialMediaSettings.canvasWidth = platformDimensions.width;\r\n        socialMediaSettings.canvasHeight = platformDimensions.height;\r\n        socialMediaSettings.pixelRatio = 4; // Extra high DPI for SVG conversion\r\n        console.log(`🎨 Converting enhanced SVG design to ${platformDimensions.width}x${platformDimensions.height} PNG for ${activeTab}...`);\r\n      }\r\n\r\n      const dataUrl = await toPng(nodeToCapture, socialMediaSettings);\r\n\r\n      const link = document.createElement('a');\r\n      link.href = dataUrl;\r\n      link.download = `nevis-social-${post.id}-${activeTab}.png`;\r\n      link.click();\r\n\r\n      // Provide specific feedback based on content type\r\n      const successMessage = isSvgDataUrl\r\n        ? \"Enhanced design converted to PNG for social media use.\"\r\n        : \"High-definition image ready for social media posting.\";\r\n\r\n      toast({\r\n        title: \"Social Media Image Ready\",\r\n        description: successMessage,\r\n      });\r\n\r\n    } catch (err) {\r\n      console.error(err);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Download Failed\",\r\n        description: `Could not download the image. Please try again. Error: ${(err as Error).message}`,\r\n      });\r\n    }\r\n  }, [post.id, activeTab, toast]);\r\n\r\n\r\n  const handleSaveChanges = async () => {\r\n    const updatedPost = {\r\n      ...post,\r\n      content: editedContent,\r\n      hashtags: editedHashtags,\r\n      status: 'edited' as const,\r\n    };\r\n    await onPostUpdated(updatedPost);\r\n    setIsEditing(false);\r\n    toast({\r\n      title: \"Post Updated\",\r\n      description: \"Your changes have been saved.\",\r\n    });\r\n  };\r\n\r\n  const handleRegenerate = async () => {\r\n    setIsRegenerating(true);\r\n    try {\r\n      const platform = post.variants[0].platform;\r\n      const newPost = await generateContentAction(brandProfile, platform);\r\n      onPostUpdated({ ...newPost, id: post.id }); // Keep old id for replacement\r\n      toast({\r\n        title: \"Post Regenerated!\",\r\n        description: \"A new version of your post has been generated.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Regeneration Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsRegenerating(false);\r\n    }\r\n  };\r\n\r\n  const handleGenerateVideo = async () => {\r\n    if (!post.catchyWords) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Cannot Generate Video\",\r\n        description: \"The post is missing the required catchy words.\",\r\n      });\r\n      return;\r\n    }\r\n    setIsGeneratingVideo(true);\r\n    try {\r\n      const result = await generateVideoContentAction(brandProfile, post.catchyWords, post.content);\r\n      const newVideoUrl = result.videoUrl;\r\n      setVideoUrl(newVideoUrl);\r\n      await onPostUpdated({ ...post, videoUrl: newVideoUrl });\r\n      setShowVideoDialog(true);\r\n      toast({\r\n        title: \"Video Generated!\",\r\n        description: \"Your video is ready to be viewed.\",\r\n      });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Video Generation Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsGeneratingVideo(false);\r\n    }\r\n  };\r\n\r\n  const activeVariant = post.variants.find(v => v.platform === activeTab) || post.variants[0];\r\n\r\n  return (\r\n    <>\r\n  <Card className=\"flex flex-col w-full\">\r\n        <CardHeader className=\"flex-row items-center justify-between gap-4 p-4\">\r\n          <div className=\"flex items-center gap-2 text-sm font-medium text-muted-foreground\">\r\n            <CalendarIcon className=\"h-4 w-4\" />\r\n            <span>{formattedDate}</span>\r\n          </div>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button size=\"icon\" variant=\"ghost\" className=\"h-6 w-6\" disabled={isRegenerating || isGeneratingVideo}>\r\n                <MoreVertical className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuItem onClick={() => setIsEditing(true)}>\r\n                <Pen className=\"mr-2 h-4 w-4\" />\r\n                Edit Text\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleRegenerate} disabled={isRegenerating}>\r\n                {isRegenerating ? (\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                ) : (\r\n                  <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                )}\r\n                Regenerate Image\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleGenerateVideo} disabled={isGeneratingVideo}>\r\n                {isGeneratingVideo ? (\r\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                ) : (\r\n                  <Video className=\"mr-2 h-4 w-4\" />\r\n                )}\r\n                Generate Video\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={handleDownload}>\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Download Image\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </CardHeader>\r\n        <CardContent className=\"flex-grow space-y-4 p-4 pt-0\">\r\n          {isRevo2Post ? (\r\n            // Revo 2.0 single-platform layout with platform icon at top left\r\n            <div className=\"space-y-4\">\r\n              {/* Platform Icon Header - Left aligned */}\r\n              <div className=\"flex items-center justify-start p-3 bg-muted/30 rounded-lg\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  {platformIcons[post.variants[0]?.platform || 'Instagram']}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Single Image Display - Platform-specific dimensions */}\r\n              {(() => {\r\n                const variant = post.variants[0];\r\n                const dimensions = getPlatformDimensions(variant?.platform || 'Instagram');\r\n\r\n                return (\r\n                  <div className={`relative ${dimensions.aspectClass} w-full overflow-hidden`}>\r\n                    {(isRegenerating || isGeneratingVideo) && (\r\n                      <div className=\"absolute inset-0 z-10 flex items-center justify-center bg-card/80\">\r\n                        <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n                        <span className=\"sr-only\">{isRegenerating ? 'Regenerating image...' : 'Generating video...'}</span>\r\n                      </div>\r\n                    )}\r\n                    <div ref={el => (downloadRefs.current[variant?.platform || 'Instagram'] = el)} className={`relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`}>\r\n                      {variant?.imageUrl && isValidUrl(variant.imageUrl) ? (\r\n                        <div\r\n                          className=\"relative h-full w-full cursor-pointer\"\r\n                          onClick={() => handleImagePreview(variant.imageUrl)}\r\n                        >\r\n                          <Image\r\n                            alt={`Generated post image for ${variant.platform}`}\r\n                            className={cn('h-full w-full object-cover transition-opacity', (isRegenerating || isGeneratingVideo) ? 'opacity-50' : 'opacity-100')}\r\n                            height={dimensions.height}\r\n                            src={variant.imageUrl}\r\n                            data-ai-hint=\"social media post\"\r\n                            width={dimensions.width}\r\n                            crossOrigin=\"anonymous\"\r\n                            unoptimized={variant.imageUrl.startsWith('data:')} // Don't optimize data URLs\r\n                          />\r\n                          {/* Preview overlay */}\r\n                          <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100\">\r\n                            <div className=\"bg-white/90 rounded-full p-2\">\r\n                              <Eye className=\"h-5 w-5 text-gray-700\" />\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"flex h-full w-full items-center justify-center bg-muted flex-col gap-2\">\r\n                          <ImageOff className=\"h-12 w-12 text-muted-foreground\" />\r\n                          {variant?.imageUrl && !isValidUrl(variant.imageUrl) && (\r\n                            <div className=\"absolute bottom-2 left-2 right-2\">\r\n                              <div className=\"text-xs text-red-500 bg-white/90 p-2 rounded\">\r\n                                {variant.imageUrl.includes('[') && variant.imageUrl.includes(']') ? (\r\n                                  <div>\r\n                                    <p className=\"font-medium\">Image temporarily unavailable</p>\r\n                                    <p className=\"text-gray-600 mt-1\">\r\n                                      {variant.imageUrl.includes('Large image data removed')\r\n                                        ? 'Image was too large for storage. Try regenerating.'\r\n                                        : 'Image data was optimized for storage.'\r\n                                      }\r\n                                    </p>\r\n                                  </div>\r\n                                ) : (\r\n                                  <p>Invalid image URL</p>\r\n                                )}\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })()}\r\n            </div>\r\n          ) : (\r\n            // Multi-platform tab layout for Revo 1.0/1.5\r\n            <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as Platform)} className=\"w-full\">\r\n              <TabsList className=\"grid w-full grid-cols-4\">\r\n                {post.variants.map(variant => (\r\n                  <TabsTrigger key={variant.platform} value={variant.platform}>\r\n                    {platformIcons[variant.platform]}\r\n                  </TabsTrigger>\r\n                ))}\r\n              </TabsList>\r\n              {post.variants.map(variant => {\r\n                const dimensions = getPlatformDimensions(variant.platform);\r\n                return (\r\n                  <TabsContent key={variant.platform} value={variant.platform}>\r\n                    <div className={`relative ${dimensions.aspectClass} w-full overflow-hidden`}>\r\n                      {(isRegenerating || isGeneratingVideo) && (\r\n                        <div className=\"absolute inset-0 z-10 flex items-center justify-center bg-card/80\">\r\n                          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\r\n                          <span className=\"sr-only\">{isRegenerating ? 'Regenerating image...' : 'Generating video...'}</span>\r\n                        </div>\r\n                      )}\r\n                      <div ref={el => (downloadRefs.current[variant.platform] = el)} className={`relative ${dimensions.aspectClass} w-full overflow-hidden rounded-md border group`}>\r\n                        {variant.imageUrl && isValidUrl(variant.imageUrl) ? (\r\n                          <div\r\n                            className=\"relative h-full w-full cursor-pointer\"\r\n                            onClick={() => handleImagePreview(variant.imageUrl)}\r\n                          >\r\n                            <Image\r\n                              alt={`Generated post image for ${variant.platform}`}\r\n                              className={cn('h-full w-full object-cover transition-opacity', (isRegenerating || isGeneratingVideo) ? 'opacity-50' : 'opacity-100')}\r\n                              height={dimensions.height}\r\n                              src={variant.imageUrl}\r\n                              data-ai-hint=\"social media post\"\r\n                              width={dimensions.width}\r\n                              crossOrigin=\"anonymous\"\r\n                              unoptimized={variant.imageUrl.startsWith('data:')} // Don't optimize data URLs\r\n                            />\r\n                            {/* Preview overlay */}\r\n                            <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100\">\r\n                              <div className=\"bg-white/90 rounded-full p-2\">\r\n                                <Eye className=\"h-5 w-5 text-gray-700\" />\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"flex h-full w-full items-center justify-center bg-muted\">\r\n                            <ImageOff className=\"h-12 w-12 text-muted-foreground\" />\r\n                            {variant.imageUrl && !isValidUrl(variant.imageUrl) && (\r\n                              <div className=\"absolute bottom-2 left-2 right-2\">\r\n                                <p className=\"text-xs text-red-500 bg-white/90 p-1 rounded\">\r\n                                  Invalid image URL\r\n                                </p>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </TabsContent>\r\n                );\r\n              })}\r\n            </Tabs>\r\n          )}\r\n\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-start justify-between gap-2\">\r\n              <p className=\"text-sm text-foreground line-clamp-4 flex-1\">{post.content}</p>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={handleCopyCaption}\r\n                className=\"h-8 w-8 p-0 flex-shrink-0\"\r\n                title=\"Copy caption\"\r\n              >\r\n                <Copy className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n        <CardFooter className=\"p-4 pt-0\">\r\n          <div className=\"flex items-start justify-between gap-2\">\r\n            <div className=\"flex flex-wrap gap-1 flex-1\">\r\n              {post.hashtags && (() => {\r\n                // Handle both string and array formats for hashtags\r\n                const hashtagsArray = typeof post.hashtags === 'string'\r\n                  ? post.hashtags.split(\" \")\r\n                  : Array.isArray(post.hashtags)\r\n                    ? post.hashtags\r\n                    : [];\r\n\r\n                return hashtagsArray.map((tag, index) => (\r\n                  <Badge key={index} variant=\"secondary\" className=\"font-normal\">\r\n                    {tag}\r\n                  </Badge>\r\n                ));\r\n              })()}\r\n              {!post.hashtags && (\r\n                <Badge variant=\"secondary\" className=\"font-normal\">\r\n                  #enhanced #ai #design\r\n                </Badge>\r\n              )}\r\n            </div>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={handleCopyHashtags}\r\n              className=\"h-8 w-8 p-0 flex-shrink-0\"\r\n              title=\"Copy hashtags\"\r\n            >\r\n              <Copy className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </CardFooter>\r\n      </Card>\r\n\r\n      {/* Edit Post Dialog */}\r\n      <Dialog open={isEditing} onOpenChange={setIsEditing}>\r\n        <DialogContent className=\"sm:max-w-[600px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Edit Post</DialogTitle>\r\n            <DialogDescription>\r\n              Make changes to your post content and hashtags below. Click save when you're done.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"grid gap-4 py-4\">\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"content\">Content</Label>\r\n              <Textarea\r\n                id=\"content\"\r\n                value={editedContent}\r\n                onChange={(e) => setEditedContent(e.target.value)}\r\n                className=\"h-32\"\r\n              />\r\n            </div>\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"hashtags\">Hashtags</Label>\r\n              <Input\r\n                id=\"hashtags\"\r\n                value={editedHashtags}\r\n                onChange={(e) => setEditedHashtags(e.target.value)}\r\n              />\r\n            </div>\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setIsEditing(false)}>Cancel</Button>\r\n            <Button onClick={handleSaveChanges}>Save Changes</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* View Video Dialog */}\r\n      <Dialog open={showVideoDialog} onOpenChange={setShowVideoDialog}>\r\n        <DialogContent className=\"sm:max-w-[600px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Generated Video</DialogTitle>\r\n            <DialogDescription>\r\n              Here is the video generated for your post. You can download it from here.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"my-4\">\r\n            {videoUrl ? (\r\n              <video controls autoPlay src={videoUrl} className=\"w-full rounded-md\" />\r\n            ) : (\r\n              <p>No video available.</p>\r\n            )}\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowVideoDialog(false)}>Close</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Image Preview Modal */}\r\n      <Dialog open={showImagePreview} onOpenChange={setShowImagePreview}>\r\n        <DialogContent className=\"sm:max-w-4xl max-h-[90vh] p-2\">\r\n          <DialogHeader className=\"pb-2\">\r\n            <DialogTitle>Image Preview</DialogTitle>\r\n            <DialogDescription>\r\n              Click and drag to pan, scroll to zoom\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"flex items-center justify-center max-h-[70vh] overflow-hidden\">\r\n            {previewImageUrl && (\r\n              <img\r\n                src={previewImageUrl}\r\n                alt=\"Post image preview\"\r\n                className=\"max-w-full max-h-full object-contain rounded-lg\"\r\n              />\r\n            )}\r\n          </div>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowImagePreview(false)}>\r\n              Close\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;AAGzC;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAMA;AAQA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AArCA;;;;;;;;;;;;;;;;;;AAwCA,mCAAmC;AACnC,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACnC,OAAO;IACT;IAEA,kCAAkC;IAClC,IAAI,QAAQ,wBAAwB,QAAQ,iBAAiB,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM;QACnG,OAAO;IACT;IAEA,IAAI;QACF,sCAAsC;QACtC,IAAI,IAAI,UAAU,CAAC,UAAU;YAC3B,OAAO,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC;QACjD;QAEA,4BAA4B;QAC5B,MAAM,YAAY,IAAI,IAAI;QAC1B,OAAO,UAAU,QAAQ,KAAK,WAAW,UAAU,QAAQ,KAAK;IAClE,EAAE,OAAO,OAAO;QACd,+CAA+C;QAC/C,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM;YAC5C,QAAQ,IAAI,CAAC,8BAA8B,IAAI,SAAS,CAAC,GAAG,MAAM,OAAO,MAAM,OAAO;QACxF;QACA,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,0BAA0B,OAAe;IAChD,IAAI,QAAQ,UAAU,CAAC,uBAAuB;QAC5C,OAAO;YAAE,QAAQ;YAAO,WAAW;QAAM;IAC3C,OAAO,IAAI,QAAQ,UAAU,CAAC,2BAA2B;QACvD,OAAO;YAAE,QAAQ;YAAO,WAAW;QAAM;IAC3C,OAAO,IAAI,QAAQ,UAAU,CAAC,8BAA8B,QAAQ,UAAU,CAAC,2BAA2B;QACxG,OAAO;YAAE,QAAQ;YAAQ,WAAW;QAAM;IAC5C,OAAO,IAAI,QAAQ,UAAU,CAAC,4BAA4B;QACxD,OAAO;YAAE,QAAQ;YAAQ,WAAW;QAAO;IAC7C;IACA,OAAO;QAAE,QAAQ;QAAO,WAAW;IAAM,GAAG,mBAAmB;AACjE;AAEA,MAAM,gBAA2D;IAC/D,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,yBAAW,6LAAC,+MAAA,CAAA,YAAS;QAAC,WAAU;;;;;;IAChC,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC9B,uBAAS,6LAAC,2MAAA,CAAA,UAAO;QAAC,WAAU;;;;;;AAC9B;AAQO,SAAS,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,EAAiB;;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,KAAK,OAAO;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,KAAK,QAAQ;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAsB,KAAK,QAAQ;IAChF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAU;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAY,KAAK,QAAQ,CAAC,EAAE,EAAE,YAAY;IACzF,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAA2C,CAAC;IAE5E,qDAAqD;IACrD,MAAM,cAAc,KAAK,EAAE,EAAE,WAAW,aAAa,KAAK,QAAQ,CAAC,MAAM,KAAK;IAE9E,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;2CAAE;YAClC,IAAI;gBACF,MAAM,OAAO,IAAI,KAAK,KAAK,IAAI;gBAC/B,IAAI,MAAM,KAAK,OAAO,KAAK;oBACzB,uCAAuC;oBACvC,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;gBAC5B;gBACA,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACtB,EAAE,OAAO,OAAO;gBACd,+CAA+C;gBAC/C,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;YAC5B;QACF;0CAAG;QAAC,KAAK,IAAI;KAAC;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,wEAAwE;IACxE,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;uDAAE,CAAC;YAC/C,OAAQ,SAAS,WAAW;gBAC1B,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAM,aAAa;oBAAgB;gBACnE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAK,aAAa;oBAAgB;gBAClE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAK,aAAa;oBAAgB;gBAClE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAK,aAAa;oBAAgB;gBAClE,KAAK;oBACH,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAM,aAAa;oBAAgB;gBACnE;oBACE,OAAO;wBAAE,OAAO;wBAAM,QAAQ;wBAAM,aAAa;oBAAgB;YACrE;QACF;sDAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;mDAAE;YAC1C,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;gBAChD,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;YACF;QACF;kDAAG;QAAC,KAAK,OAAO;QAAE;KAAM;IAExB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;oDAAE;YAC3C,IAAI;gBACF,MAAM,eAAe,OAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,GAAG,KAAK,QAAQ,EAAE,KAAK,QAAQ;gBACrG,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACpC,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;YACF;QACF;mDAAG;QAAC,KAAK,QAAQ;QAAE;KAAM;IAEzB,8BAA8B;IAC9B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;oDAAE,CAAC;YAC5C,mBAAmB;YACnB,oBAAoB;QACtB;mDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAAE;YACvC,MAAM,gBAAgB,KAAK,QAAQ,CAAC,IAAI;sEAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;;YAE7D,uEAAuE;YACvE,IAAI,eAAe,YAAY,WAAW,cAAc,QAAQ,GAAG;gBACjE,IAAI;oBACF,kDAAkD;oBAClD,IAAI,cAAc,QAAQ,CAAC,UAAU,CAAC,UAAU;wBAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,0BAA0B,cAAc,QAAQ;wBAE9E,oEAAoE;wBACpE,IAAI,WAAW,OAAO;4BACpB,QAAQ,GAAG,CAAC;wBACZ,qDAAqD;wBACrD,kDAAkD;wBACpD,OAAO;4BACL,2DAA2D;4BAC3D,MAAM,OAAO,SAAS,aAAa,CAAC;4BACpC,KAAK,IAAI,GAAG,cAAc,QAAQ;4BAClC,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW;4BACnE,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC1B,KAAK,KAAK;4BACV,SAAS,IAAI,CAAC,WAAW,CAAC;4BAE1B,MAAM;gCACJ,OAAO;gCACP,aAAa,CAAC,gBAAgB,EAAE,OAAO,WAAW,GAAG,+BAA+B,CAAC;4BACvF;4BACA;wBACF;oBACF,OAAO;wBACL,iDAAiD;wBACjD,IAAI;4BACF,MAAM,WAAW,MAAM,MAAM,cAAc,QAAQ;4BACnD,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;4BAEvC,iDAAiD;4BACjD,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,IAAI;4BACrE,IAAI,YAAY,OAAO,UAAU;4BACjC,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,QAAQ;gCAC/D,YAAY;4BACd,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;gCACvC,YAAY;4BACd;4BAEA,MAAM,OAAO,SAAS,aAAa,CAAC;4BACpC,KAAK,IAAI,GAAG;4BACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW;4BACnE,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC1B,KAAK,KAAK;4BACV,SAAS,IAAI,CAAC,WAAW,CAAC;4BAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;4BAE3B,MAAM;gCACJ,OAAO;gCACP,aAAa;4BACf;4BACA;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,IAAI,CAAC,8DAA8D;wBAC3E,oCAAoC;wBACtC;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,8DAA8D;gBAC7E;YACF;YAEA,sEAAsE;YACtE,MAAM,gBAAgB,aAAa,OAAO,CAAC,UAAU;YACrD,IAAI,CAAC,eAAe;gBAClB,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;gBACA;YACF;YAEA,IAAI;gBACF,mDAAmD;gBACnD,MAAM,gBAAgB,KAAK,QAAQ,CAAC,IAAI;0EAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;;gBAC7D,MAAM,eAAe,eAAe,UAAU,WAAW;gBACzD,MAAM,qBAAqB,sBAAsB;gBAEjD,8DAA8D;gBAC9D,MAAM,sBAAsB;oBAC1B,WAAW;oBACX,aAAa,mBAAmB,KAAK;oBACrC,cAAc,mBAAmB,MAAM;oBACvC,YAAY;oBACZ,SAAS;oBACT,iBAAiB;oBACjB,OAAO;wBACL,cAAc;wBACd,QAAQ;oBACV;gBACF;gBAEA,uCAAuC;gBACvC,IAAI,cAAc;oBAChB,oBAAoB,WAAW,GAAG,mBAAmB,KAAK;oBAC1D,oBAAoB,YAAY,GAAG,mBAAmB,MAAM;oBAC5D,oBAAoB,UAAU,GAAG,GAAG,oCAAoC;oBACxE,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,mBAAmB,KAAK,CAAC,CAAC,EAAE,mBAAmB,MAAM,CAAC,SAAS,EAAE,UAAU,GAAG,CAAC;gBACrI;gBAEA,MAAM,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,QAAK,AAAD,EAAE,eAAe;gBAE3C,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,QAAQ,GAAG,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC;gBAC1D,KAAK,KAAK;gBAEV,kDAAkD;gBAClD,MAAM,iBAAiB,eACnB,2DACA;gBAEJ,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YAEF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC;gBACd,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa,CAAC,uDAAuD,EAAE,AAAC,IAAc,OAAO,EAAE;gBACjG;YACF;QACF;+CAAG;QAAC,KAAK,EAAE;QAAE;QAAW;KAAM;IAG9B,MAAM,oBAAoB;QACxB,MAAM,cAAc;YAClB,GAAG,IAAI;YACP,SAAS;YACT,UAAU;YACV,QAAQ;QACV;QACA,MAAM,cAAc;QACpB,aAAa;QACb,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,KAAK,QAAQ,CAAC,EAAE,CAAC,QAAQ;YAC1C,MAAM,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;YAC1D,cAAc;gBAAE,GAAG,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC,IAAI,8BAA8B;YAC1E,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,KAAK,WAAW,EAAE;YACrB,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QACA,qBAAqB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,6BAA0B,AAAD,EAAE,cAAc,KAAK,WAAW,EAAE,KAAK,OAAO;YAC5F,MAAM,cAAc,OAAO,QAAQ;YACnC,YAAY;YACZ,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAY;YACrD,mBAAmB;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,gBAAgB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,cAAc,KAAK,QAAQ,CAAC,EAAE;IAE3F,qBACE;;0BACF,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACV,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;kDAAM;;;;;;;;;;;;0CAET,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,SAAQ;4CAAQ,WAAU;4CAAU,UAAU,kBAAkB;sDAClF,cAAA,6LAAC,6NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,aAAa;;kEAC5C,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAkB,UAAU;;oDACpD,+BACC,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACrB;;;;;;;0DAGJ,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAqB,UAAU;;oDACvD,kCACC,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDACjB;;;;;;;0DAGJ,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,cACC,iEAAiE;0CACjE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,aAAa,CAAC,KAAK,QAAQ,CAAC,EAAE,EAAE,YAAY,YAAY;;;;;;;;;;;oCAK5D,CAAC;wCACA,MAAM,UAAU,KAAK,QAAQ,CAAC,EAAE;wCAChC,MAAM,aAAa,sBAAsB,SAAS,YAAY;wCAE9D,qBACE,6LAAC;4CAAI,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,uBAAuB,CAAC;;gDACxE,CAAC,kBAAkB,iBAAiB,mBACnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAAW,iBAAiB,0BAA0B;;;;;;;;;;;;8DAG1E,6LAAC;oDAAI,KAAK,CAAA,KAAO,aAAa,OAAO,CAAC,SAAS,YAAY,YAAY,GAAG;oDAAK,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,+CAA+C,CAAC;8DAC1K,SAAS,YAAY,WAAW,QAAQ,QAAQ,kBAC/C,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM,mBAAmB,QAAQ,QAAQ;;0EAElD,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK,CAAC,yBAAyB,EAAE,QAAQ,QAAQ,EAAE;gEACnD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD,AAAC,kBAAkB,oBAAqB,eAAe;gEACtH,QAAQ,WAAW,MAAM;gEACzB,KAAK,QAAQ,QAAQ;gEACrB,gBAAa;gEACb,OAAO,WAAW,KAAK;gEACvB,aAAY;gEACZ,aAAa,QAAQ,QAAQ,CAAC,UAAU,CAAC;;;;;;0EAG3C,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;6EAKrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,SAAS,YAAY,CAAC,WAAW,QAAQ,QAAQ,mBAChD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,CAAC,QAAQ,CAAC,qBAC3D,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAc;;;;;;0FAC3B,6LAAC;gFAAE,WAAU;0FACV,QAAQ,QAAQ,CAAC,QAAQ,CAAC,8BACvB,uDACA;;;;;;;;;;;6FAKR,6LAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAUvB,CAAC;;;;;;uCAGH,6CAA6C;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,OAAO;gCAAW,eAAe,CAAC,IAAM,aAAa;gCAAgB,WAAU;;kDACnF,6LAAC,mIAAA,CAAA,WAAQ;wCAAC,WAAU;kDACjB,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,wBACjB,6LAAC,mIAAA,CAAA,cAAW;gDAAwB,OAAO,QAAQ,QAAQ;0DACxD,aAAa,CAAC,QAAQ,QAAQ,CAAC;+CADhB,QAAQ,QAAQ;;;;;;;;;;oCAKrC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA;wCACjB,MAAM,aAAa,sBAAsB,QAAQ,QAAQ;wCACzD,qBACE,6LAAC,mIAAA,CAAA,cAAW;4CAAwB,OAAO,QAAQ,QAAQ;sDACzD,cAAA,6LAAC;gDAAI,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,uBAAuB,CAAC;;oDACxE,CAAC,kBAAkB,iBAAiB,mBACnC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;gEAAK,WAAU;0EAAW,iBAAiB,0BAA0B;;;;;;;;;;;;kEAG1E,6LAAC;wDAAI,KAAK,CAAA,KAAO,aAAa,OAAO,CAAC,QAAQ,QAAQ,CAAC,GAAG;wDAAK,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,CAAC,+CAA+C,CAAC;kEAC1J,QAAQ,QAAQ,IAAI,WAAW,QAAQ,QAAQ,kBAC9C,6LAAC;4DACC,WAAU;4DACV,SAAS,IAAM,mBAAmB,QAAQ,QAAQ;;8EAElD,6LAAC,gIAAA,CAAA,UAAK;oEACJ,KAAK,CAAC,yBAAyB,EAAE,QAAQ,QAAQ,EAAE;oEACnD,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD,AAAC,kBAAkB,oBAAqB,eAAe;oEACtH,QAAQ,WAAW,MAAM;oEACzB,KAAK,QAAQ,QAAQ;oEACrB,gBAAa;oEACb,OAAO,WAAW,KAAK;oEACvB,aAAY;oEACZ,aAAa,QAAQ,QAAQ,CAAC,UAAU,CAAC;;;;;;8EAG3C,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;iFAKrB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,QAAQ,QAAQ,IAAI,CAAC,WAAW,QAAQ,QAAQ,mBAC/C,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;kFAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CApCxD,QAAQ,QAAQ;;;;;oCA+CtC;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA+C,KAAK,OAAO;;;;;;sDACxE,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKxB,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,QAAQ,IAAI,CAAC;4CACjB,oDAAoD;4CACpD,MAAM,gBAAgB,OAAO,KAAK,QAAQ,KAAK,WAC3C,KAAK,QAAQ,CAAC,KAAK,CAAC,OACpB,MAAM,OAAO,CAAC,KAAK,QAAQ,IACzB,KAAK,QAAQ,GACb,EAAE;4CAER,OAAO,cAAc,GAAG,CAAC,CAAC,KAAK,sBAC7B,6LAAC,oIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAY,WAAU;8DAC9C;mDADS;;;;;wCAIhB,CAAC;wCACA,CAAC,KAAK,QAAQ,kBACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAc;;;;;;;;;;;;8CAKvD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAW,cAAc;0BACrC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;sCAIvD,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,aAAa;8CAAQ;;;;;;8CAC9D,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;0BAM1C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAiB,cAAc;0BAC3C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACZ,yBACC,6LAAC;gCAAM,QAAQ;gCAAC,QAAQ;gCAAC,KAAK;gCAAU,WAAU;;;;;qDAElD,6LAAC;0CAAE;;;;;;;;;;;sCAGP,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,mBAAmB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;0BAM1E,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACZ,iCACC,6LAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;;;;;;sCAIhB,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,oBAAoB;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;GAnmBgB;;QA6BI,+HAAA,CAAA,WAAQ;;;KA7BZ", "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { generatePostFromProfile as generatePostFromProfileFlow } from \"@/ai/flows/generate-post-from-profile\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport {\r\n  detectAndPopulateLanguages,\r\n  getLanguageInstructionForProfile,\r\n  updateLanguageDetectionIfNeeded\r\n} from \"@/lib/services/brand-language-service\";\r\n// import { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\"; // Temporarily disabled\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\nimport { DesignGenerationService } from \"@/ai/models/services/design-generation-service\";\r\nimport type { RevoModelId } from \"@/ai/models/types/model-types\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\r\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\r\n    console.log(\"🔍 Result type:\", typeof result);\r\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n    console.error(\"❌ Error analyzing brand:\", error);\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Update language detection if needed\r\n    const profileWithLanguages = updateLanguageDetectionIfNeeded(profile);\r\n\r\n    // Generate language instructions for AI\r\n    const languageInstructions = getLanguageInstructionForProfile(profileWithLanguages);\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    const postDetails = await generatePostFromProfileFlow({\r\n      businessType: profileWithLanguages.businessType,\r\n      location: profileWithLanguages.location,\r\n      writingTone: profileWithLanguages.writingTone,\r\n      contentThemes: profileWithLanguages.contentThemes,\r\n      visualStyle: profileWithLanguages.visualStyle,\r\n      logoDataUrl: profileWithLanguages.logoDataUrl,\r\n      designExamples: effectiveDesignExamples, // Use design examples based on consistency preference\r\n      primaryColor: profileWithLanguages.primaryColor,\r\n      accentColor: profileWithLanguages.accentColor,\r\n      backgroundColor: profileWithLanguages.backgroundColor,\r\n      dayOfWeek,\r\n      currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      // Pass new detailed fields\r\n      services: servicesString,\r\n      targetAudience: profileWithLanguages.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      // Pass brand consistency preferences\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      // Pass intelligent language instructions\r\n      languageInstructions: languageInstructions,\r\n    });\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline,\r\n      callToAction: postDetails.callToAction,\r\n      // Include enhanced AI features\r\n      contentVariants: postDetails.contentVariants,\r\n      hashtagAnalysis: postDetails.hashtagAnalysis,\r\n      // Include advanced AI features\r\n      marketIntelligence: postDetails.marketIntelligence,\r\n      // Include local context features\r\n      localContext: postDetails.localContext,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    console.error(\"Error generating content:\", error);\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    console.error(\"Error generating video content:\", error);\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Error generating creative asset:\", error);\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n    console.log('🎨 Enhanced Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', finalImageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n    console.log('- Enhancements Enabled:', enableEnhancements);\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n      });\r\n\r\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\r\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\r\n\r\n    } catch (gemini25Error) {\r\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\r\n\r\n      try {\r\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\r\n      } catch (openaiError) {\r\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\r\n\r\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\r\n      }\r\n    }\r\n\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating enhanced design:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n    console.log('🎨 Gemini HD Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', imageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n\r\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n    console.log('✅ Gemini HD enhanced design generated successfully');\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error in Gemini HD design generation:', error);\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log('🎨 Generating content with artifacts...');\r\n    console.log('- Platform:', platform);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n    console.log('- Enhanced Design:', useEnhancedDesign);\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\r\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\r\n        id: a.id,\r\n        name: a.name,\r\n        type: a.type,\r\n        usageType: a.usageType,\r\n        isActive: a.isActive,\r\n        instructions: a.instructions\r\n      })));\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      console.log('🔄 Enhanced design disabled, using base content generation');\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\r\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\r\n\r\n    if (targetArtifacts.length === 0) {\r\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\r\n    } else {\r\n      console.log('🎯 Using enhanced design with artifact context');\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n          console.log('📝 Using message from exact-use artifact');\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      console.log('🎨 Applying style references from artifacts');\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n        console.log('🎨 Using artifact-inspired visual style');\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    console.log('✅ Enhanced content with artifacts generated successfully');\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating content with artifacts:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content using the new Revo model system\r\n * This action uses the proper model architecture with version-specific implementations\r\n */\r\nexport async function generateContentWithRevoModelAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  revoModel: RevoModelId,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = []\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log(`🎨 Generating content with ${revoModel} model...`);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Business Type:', profile.businessType);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n\r\n    // Handle artifacts if provided\r\n    let targetArtifacts: Artifact[] = [];\r\n    if (artifactIds.length > 0) {\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    // Prepare artifact instructions\r\n    let artifactInstructions = '';\r\n    if (targetArtifacts.length > 0) {\r\n      const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      if (exactUseArtifacts.length > 0) {\r\n        artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\\n';\r\n        exactUseArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\\n`;\r\n        });\r\n      }\r\n\r\n      if (referenceArtifacts.length > 0) {\r\n        artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\\n';\r\n        referenceArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\\n`;\r\n        });\r\n      }\r\n    }\r\n\r\n    // Use simplified Revo model generation with text validation\r\n    console.log(`🎨 Using Revo ${revoModel} with text validation...`);\r\n    console.log('🔧 DEBUG: This is the SIMPLIFIED code path');\r\n\r\n    // Generate dynamic and varied text for each design\r\n    const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);\r\n    let imageText = textVariations.selectedText;\r\n\r\n    if (revoModel === 'revo-1.0') {\r\n      console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');\r\n      console.log('🔍 Original text:', imageText);\r\n\r\n      // Simple text validation for Revo 1.0\r\n      const words = imageText.split(' ').filter(word => word.length > 0);\r\n      if (words.length > 25) {\r\n        console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);\r\n        imageText = words.slice(0, 25).join(' ');\r\n      }\r\n      console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);\r\n    }\r\n\r\n    // Use sophisticated design prompt created by 20-year veteran designer + marketer\r\n    const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);\r\n\r\n    const designResult = await generateCreativeAssetFlow({\r\n      prompt: designPrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: profile,\r\n      maskDataUrl: null\r\n    });\r\n\r\n    if (!designResult.imageUrl) {\r\n      throw new Error('Design generation failed: No image URL returned');\r\n    }\r\n\r\n    // Generate content using the standard flow for caption and hashtags\r\n    const contentResult = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      services: Array.isArray(profile.services)\r\n        ? profile.services.map(s => typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\\n')\r\n        : profile.services || '',\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\\n') : profile.keyFeatures || '',\r\n      competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\\n') : profile.competitiveAdvantages || '',\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    // Combine the design result with content result\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: new Date().toISOString(),\r\n      content: contentResult.content,\r\n      hashtags: contentResult.hashtags,\r\n      status: 'generated',\r\n      variants: [{\r\n        platform,\r\n        imageUrl: designResult.imageUrl || '',\r\n        caption: contentResult.content,\r\n        hashtags: contentResult.hashtags\r\n      }],\r\n      catchyWords: contentResult.catchyWords,\r\n      subheadline: contentResult.subheadline,\r\n      callToAction: contentResult.callToAction,\r\n      contentVariants: contentResult.contentVariants,\r\n      hashtagAnalysis: contentResult.hashtagAnalysis,\r\n      marketIntelligence: contentResult.marketIntelligence,\r\n      localContext: contentResult.localContext,\r\n      // Add Revo model metadata\r\n      revoModelUsed: revoModel,\r\n      qualityScore: 8, // Default quality score for Revo models\r\n      processingTime: Date.now() - Date.now(), // Will be calculated properly\r\n      creditsUsed: 1,\r\n      enhancementsApplied: [`Revo ${revoModel} Generation`, 'Text Validation', 'Professional Design']\r\n    };\r\n\r\n    console.log(`✅ Content generated successfully with ${revoModel}`);\r\n    console.log(`⭐ Quality Score: 8/10`);\r\n    console.log(`⚡ Processing Time: Fast generation`);\r\n    console.log(`💰 Credits Used: 1`);\r\n\r\n    return newPost;\r\n\r\n  } catch (error) {\r\n    console.error(`❌ Error generating content with ${revoModel}:`, error);\r\n    throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate professional marketing-driven text with cultural awareness\r\n * Designed by a 20-year veteran designer + 20-year marketing expert\r\n * Now deeply connected to actual brand profile information\r\n */\r\nfunction generateDynamicTextForRevo(profile: BrandProfile, revoModel: RevoModelId, platform: Platform) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n\r\n  // Generate sophisticated marketing copy using actual brand profile data\r\n  const marketingCopy = generateMarketingCopy(profile, platform);\r\n\r\n  console.log(`🎯 Generated personalized marketing copy for ${businessName}: \"${marketingCopy}\"`);\r\n\r\n  return {\r\n    selectedText: marketingCopy,\r\n    allVariations: [marketingCopy],\r\n    variationIndex: 0\r\n  };\r\n}\r\n\r\n/**\r\n * Generate sophisticated marketing copy that sells\r\n * Combines 20 years of design + marketing expertise with actual brand profile data\r\n * Now deeply personalized using real business information\r\n * LANGUAGE RESTRICTION: English only - no Arabic, Hindi, Chinese, or other non-English text\r\n */\r\nfunction generateMarketingCopy(profile: BrandProfile, platform: Platform): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract real business intelligence from profile\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Cultural and regional insights\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Generate catchy headline using actual business strengths (max 5 words)\r\n  const catchyHeadlines = [\r\n    `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,\r\n    `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,\r\n    `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,\r\n    `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,\r\n    `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`\r\n  ];\r\n\r\n  // Generate subheadline using real competitive advantages (max 14 words)\r\n  const subheadlines = [\r\n    `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,\r\n    `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,\r\n    `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,\r\n    `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,\r\n    `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`\r\n  ];\r\n\r\n  // Generate call-to-action using actual business context\r\n  const callToActions = [\r\n    `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,\r\n    `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,\r\n    `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,\r\n    `${getRandomElement(['Book', 'Schedule', 'Request'])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,\r\n    `${getRandomElement(['Start', 'Begin', 'Launch'])} Your ${businessIntelligence.journeyType} Today`\r\n  ];\r\n\r\n  // Randomly select components\r\n  const catchyWords = getRandomElement(catchyHeadlines);\r\n  const subheadline = getRandomElement(subheadlines);\r\n  const cta = getRandomElement(callToActions);\r\n\r\n  // Combine based on marketing best practices and business context\r\n  const marketingFormats = [\r\n    `${catchyWords}\\n${subheadline}\\n${cta}`,\r\n    `${catchyWords}\\n${subheadline}`,\r\n    `${catchyWords}\\n${cta}`,\r\n    `${subheadline}\\n${cta}`,\r\n    catchyWords\r\n  ];\r\n\r\n  return getRandomElement(marketingFormats);\r\n}\r\n\r\n/**\r\n * Get cultural context and local market insights\r\n */\r\nfunction getCulturalContext(location: string) {\r\n  // Default context\r\n  let context = {\r\n    localTerms: ['Premium', 'Professional', 'Expert'],\r\n    marketingStyle: 'Professional',\r\n    targetAudience: 'businesses',\r\n    localMarket: 'modern',\r\n    socialProof: 'thousands of clients',\r\n    valueProposition: 'Industry-leading',\r\n    competitiveAdvantage: 'proven expertise',\r\n    urgencyTrigger: 'Don\\'t miss out on',\r\n    actionWords: 'Connect with',\r\n    localCTA: 'Get Started',\r\n    ctaUrgency: 'Now'\r\n  };\r\n\r\n  // Location-specific cultural adaptations\r\n  if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {\r\n    context = {\r\n      localTerms: ['Premium', 'Luxury', 'Elite', 'Exclusive'],\r\n      marketingStyle: 'Luxury-focused',\r\n      targetAudience: 'discerning clients',\r\n      localMarket: 'Dubai\\'s dynamic',\r\n      socialProof: 'leading UAE businesses',\r\n      valueProposition: 'World-class',\r\n      competitiveAdvantage: 'international excellence',\r\n      urgencyTrigger: 'Seize the opportunity for',\r\n      actionWords: 'Experience',\r\n      localCTA: 'Book Your Exclusive Consultation',\r\n      ctaUrgency: 'Today'\r\n    };\r\n  } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {\r\n    context = {\r\n      localTerms: ['Bespoke', 'Tailored', 'Refined'],\r\n      marketingStyle: 'Sophisticated',\r\n      targetAudience: 'discerning professionals',\r\n      localMarket: 'London\\'s competitive',\r\n      socialProof: 'established UK enterprises',\r\n      valueProposition: 'Expertly crafted',\r\n      competitiveAdvantage: 'British excellence',\r\n      urgencyTrigger: 'Secure your',\r\n      actionWords: 'Discover',\r\n      localCTA: 'Arrange Your Consultation',\r\n      ctaUrgency: 'Promptly'\r\n    };\r\n  } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {\r\n    context = {\r\n      localTerms: ['Cutting-edge', 'Innovative', 'Game-changing'],\r\n      marketingStyle: 'Bold and direct',\r\n      targetAudience: 'ambitious professionals',\r\n      localMarket: 'NYC\\'s fast-paced',\r\n      socialProof: 'successful New York businesses',\r\n      valueProposition: 'Results-driven',\r\n      competitiveAdvantage: 'New York hustle',\r\n      urgencyTrigger: 'Don\\'t let competitors get',\r\n      actionWords: 'Dominate with',\r\n      localCTA: 'Schedule Your Strategy Session',\r\n      ctaUrgency: 'ASAP'\r\n    };\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n/**\r\n * Extract business intelligence from brand profile for personalized marketing\r\n * Analyzes actual business data to create relevant marketing copy\r\n */\r\nfunction extractBusinessIntelligence(profile: BrandProfile) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n\r\n  // Extract primary service information\r\n  const primaryService = services[0]?.name || services[0] || businessType;\r\n  const serviceDescription = services[0]?.description || '';\r\n  const targetAudience = services[0]?.targetAudience || 'businesses';\r\n  const keyFeatures = services[0]?.keyFeatures || '';\r\n  const competitiveAdvantages = services[0]?.competitiveAdvantages || '';\r\n\r\n  // Analyze description for key terms\r\n  const descriptionWords = description.toLowerCase().split(/\\s+/);\r\n  const strengthWords = extractStrengthWords(description, businessType);\r\n  const valueWords = extractValueWords(description, keyFeatures);\r\n  const benefitWords = extractBenefitWords(description, competitiveAdvantages);\r\n\r\n  // Extract competitive advantages\r\n  const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);\r\n  const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);\r\n  const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);\r\n\r\n  // Extract target audience specifics\r\n  const actualTargetAudience = extractTargetAudience(targetAudience, businessType);\r\n\r\n  // Generate contextual elements\r\n  const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);\r\n  const mainValue = extractMainValue(description, keyFeatures);\r\n  const offerType = generateOfferType(businessType, services);\r\n  const consultationType = generateConsultationType(businessType);\r\n  const journeyType = generateJourneyType(businessType, primaryService);\r\n  const urgencyTrigger = generateUrgencyTrigger(businessType, location);\r\n\r\n  // Extract outcome words from business context\r\n  const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);\r\n  const differentiators = extractDifferentiators(competitiveAdvantages, businessType);\r\n\r\n  return {\r\n    primaryService,\r\n    strengthWords,\r\n    valueWords,\r\n    benefitWords,\r\n    realCompetitiveAdvantage,\r\n    uniqueFeature,\r\n    realDifferentiator,\r\n    actualTargetAudience,\r\n    keyBenefit,\r\n    mainValue,\r\n    offerType,\r\n    consultationType,\r\n    journeyType,\r\n    urgencyTrigger,\r\n    outcomeWords,\r\n    differentiators\r\n  };\r\n}\r\n\r\n/**\r\n * Extract strength words from business description and type\r\n */\r\nfunction extractStrengthWords(description: string, businessType: string): string[] {\r\n  const strengthKeywords = ['leading', 'premium', 'expert', 'professional', 'trusted', 'innovative', 'cutting-edge', 'award-winning', 'certified', 'proven', 'reliable', 'secure', 'fast', 'efficient', 'quality', 'excellence', 'superior', 'advanced', 'specialized'];\r\n  const found = strengthKeywords.filter(word => description.toLowerCase().includes(word));\r\n\r\n  // Add business type specific strengths\r\n  const typeStrengths = getBusinessTypeStrengths(businessType);\r\n\r\n  return found.length > 0 ? found : typeStrengths;\r\n}\r\n\r\n/**\r\n * Extract value words from description and features\r\n */\r\nfunction extractValueWords(description: string, keyFeatures: string): string[] {\r\n  const valueKeywords = ['value', 'results', 'success', 'growth', 'efficiency', 'savings', 'profit', 'revenue', 'performance', 'productivity', 'quality', 'excellence', 'innovation', 'solutions', 'benefits'];\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n  const found = valueKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Excellence', 'Results', 'Success'];\r\n}\r\n\r\n/**\r\n * Extract benefit words from description and competitive advantages\r\n */\r\nfunction extractBenefitWords(description: string, competitiveAdvantages: string): string[] {\r\n  const benefitKeywords = ['success', 'growth', 'efficiency', 'savings', 'results', 'performance', 'quality', 'reliability', 'security', 'speed', 'convenience', 'expertise', 'support', 'innovation', 'excellence'];\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const found = benefitKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Success', 'Quality', 'Results'];\r\n}\r\n\r\n/**\r\n * Extract competitive advantage from actual business data\r\n */\r\nfunction extractCompetitiveAdvantage(competitiveAdvantages: string, businessType: string): string {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    // Extract first meaningful advantage\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim());\r\n    return advantages[0] || getDefaultAdvantage(businessType);\r\n  }\r\n  return getDefaultAdvantage(businessType);\r\n}\r\n\r\n/**\r\n * Extract unique feature from key features\r\n */\r\nfunction extractUniqueFeature(keyFeatures: string, businessType: string): string {\r\n  if (keyFeatures && keyFeatures.length > 10) {\r\n    const features = keyFeatures.split(',').map(s => s.trim());\r\n    return features[0] || getDefaultFeature(businessType);\r\n  }\r\n  return getDefaultFeature(businessType);\r\n}\r\n\r\n/**\r\n * Extract differentiator from competitive advantages and description\r\n */\r\nfunction extractDifferentiator(competitiveAdvantages: string, description: string): string {\r\n  const text = `${competitiveAdvantages} ${description}`.toLowerCase();\r\n\r\n  if (text.includes('24/7') || text.includes('24-7')) return '24/7 availability';\r\n  if (text.includes('fastest') || text.includes('quick') || text.includes('speed')) return 'fastest service';\r\n  if (text.includes('secure') || text.includes('security')) return 'advanced security';\r\n  if (text.includes('expert') || text.includes('experience')) return 'expert knowledge';\r\n  if (text.includes('custom') || text.includes('tailored')) return 'customized solutions';\r\n  if (text.includes('award') || text.includes('certified')) return 'award-winning service';\r\n\r\n  return 'professional excellence';\r\n}\r\n\r\n/**\r\n * Extract target audience from service data\r\n */\r\nfunction extractTargetAudience(targetAudience: string, businessType: string): string {\r\n  if (targetAudience && targetAudience.length > 5) {\r\n    return targetAudience.split(',')[0].trim() || getDefaultAudience(businessType);\r\n  }\r\n  return getDefaultAudience(businessType);\r\n}\r\n\r\n/**\r\n * Extract key benefit from service description and advantages\r\n */\r\nfunction extractKeyBenefit(serviceDescription: string, competitiveAdvantages: string): string {\r\n  const text = `${serviceDescription} ${competitiveAdvantages}`.toLowerCase();\r\n\r\n  if (text.includes('save') || text.includes('cost')) return 'cost savings';\r\n  if (text.includes('fast') || text.includes('quick') || text.includes('speed')) return 'faster results';\r\n  if (text.includes('secure') || text.includes('safety')) return 'enhanced security';\r\n  if (text.includes('grow') || text.includes('increase')) return 'business growth';\r\n  if (text.includes('efficient') || text.includes('optimize')) return 'improved efficiency';\r\n  if (text.includes('quality') || text.includes('premium')) return 'superior quality';\r\n\r\n  return 'exceptional results';\r\n}\r\n\r\n/**\r\n * Extract main value proposition\r\n */\r\nfunction extractMainValue(description: string, keyFeatures: string): string {\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n\r\n  if (text.includes('transform') || text.includes('revolutionize')) return 'transforms your business';\r\n  if (text.includes('maximize') || text.includes('optimize')) return 'maximizes your potential';\r\n  if (text.includes('accelerate') || text.includes('boost')) return 'accelerates your growth';\r\n  if (text.includes('streamline') || text.includes('simplify')) return 'streamlines your operations';\r\n  if (text.includes('enhance') || text.includes('improve')) return 'enhances your performance';\r\n\r\n  return 'delivers exceptional value';\r\n}\r\n\r\n/**\r\n * Generate business type-specific offer types\r\n */\r\nfunction generateOfferType(businessType: string, services: any[]): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Free Tasting';\r\n  if (type.includes('tech') || type.includes('software')) return 'Free Demo';\r\n  if (type.includes('health') || type.includes('medical')) return 'Free Consultation';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Free Assessment';\r\n  if (type.includes('real estate')) return 'Free Valuation';\r\n  if (type.includes('legal')) return 'Free Consultation';\r\n  if (type.includes('education') || type.includes('training')) return 'Free Trial';\r\n\r\n  return 'Free Consultation';\r\n}\r\n\r\n/**\r\n * Generate consultation types based on business\r\n */\r\nfunction generateConsultationType(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('software')) return 'Strategy Session';\r\n  if (type.includes('health') || type.includes('medical')) return 'Health Assessment';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Financial Review';\r\n  if (type.includes('real estate')) return 'Property Consultation';\r\n  if (type.includes('legal')) return 'Legal Consultation';\r\n  if (type.includes('marketing')) return 'Marketing Audit';\r\n\r\n  return 'Business Consultation';\r\n}\r\n\r\n/**\r\n * Generate journey types\r\n */\r\nfunction generateJourneyType(businessType: string, primaryService: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('digital')) return 'Digital Transformation';\r\n  if (type.includes('health') || type.includes('wellness')) return 'Wellness Journey';\r\n  if (type.includes('finance') || type.includes('investment')) return 'Financial Success';\r\n  if (type.includes('real estate')) return 'Property Investment';\r\n  if (type.includes('education')) return 'Learning Journey';\r\n\r\n  return 'Success Journey';\r\n}\r\n\r\n/**\r\n * Generate urgency triggers based on business and location\r\n */\r\nfunction generateUrgencyTrigger(businessType: string, location: string): string {\r\n  const triggers = ['Limited Time Offer', 'Act Now', 'Don\\'t Wait', 'Book Today', 'Available Now'];\r\n\r\n  if (location.toLowerCase().includes('dubai')) return 'Exclusive Dubai Offer';\r\n  if (location.toLowerCase().includes('london')) return 'Limited London Availability';\r\n  if (location.toLowerCase().includes('new york')) return 'NYC Exclusive Deal';\r\n\r\n  return getRandomElement(triggers);\r\n}\r\n\r\n/**\r\n * Get business type specific strengths\r\n */\r\nfunction getBusinessTypeStrengths(businessType: string): string[] {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return ['Premium', 'Fresh', 'Authentic'];\r\n  if (type.includes('tech') || type.includes('software')) return ['Innovative', 'Cutting-edge', 'Advanced'];\r\n  if (type.includes('health') || type.includes('medical')) return ['Trusted', 'Professional', 'Expert'];\r\n  if (type.includes('finance') || type.includes('banking')) return ['Secure', 'Reliable', 'Trusted'];\r\n  if (type.includes('real estate')) return ['Premium', 'Exclusive', 'Professional'];\r\n  if (type.includes('legal')) return ['Expert', 'Trusted', 'Professional'];\r\n  if (type.includes('education')) return ['Expert', 'Certified', 'Professional'];\r\n\r\n  return ['Professional', 'Quality', 'Trusted'];\r\n}\r\n\r\n/**\r\n * Get default competitive advantage by business type\r\n */\r\nfunction getDefaultAdvantage(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Fresh, authentic ingredients';\r\n  if (type.includes('tech') || type.includes('software')) return 'Cutting-edge technology';\r\n  if (type.includes('health') || type.includes('medical')) return 'Expert medical care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Secure financial solutions';\r\n  if (type.includes('real estate')) return 'Premium property expertise';\r\n  if (type.includes('legal')) return 'Expert legal guidance';\r\n\r\n  return 'Professional excellence';\r\n}\r\n\r\n/**\r\n * Get default feature by business type\r\n */\r\nfunction getDefaultFeature(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'farm-to-table freshness';\r\n  if (type.includes('tech') || type.includes('software')) return 'advanced automation';\r\n  if (type.includes('health') || type.includes('medical')) return 'personalized care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'secure transactions';\r\n  if (type.includes('real estate')) return 'market expertise';\r\n\r\n  return 'personalized service';\r\n}\r\n\r\n/**\r\n * Get default audience by business type\r\n */\r\nfunction getDefaultAudience(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'food enthusiasts';\r\n  if (type.includes('tech') || type.includes('software')) return 'forward-thinking businesses';\r\n  if (type.includes('health') || type.includes('medical')) return 'health-conscious individuals';\r\n  if (type.includes('finance') || type.includes('banking')) return 'smart investors';\r\n  if (type.includes('real estate')) return 'property investors';\r\n\r\n  return 'discerning clients';\r\n}\r\n\r\n/**\r\n * Extract outcome words from business context\r\n */\r\nfunction extractOutcomeWords(description: string, competitiveAdvantages: string): string[] {\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const outcomes = ['success', 'growth', 'results', 'performance', 'efficiency', 'savings', 'profit', 'revenue'];\r\n  const found = outcomes.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['success', 'results', 'growth'];\r\n}\r\n\r\n/**\r\n * Extract differentiators from competitive advantages\r\n */\r\nfunction extractDifferentiators(competitiveAdvantages: string, businessType: string): string[] {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim().split(' ')[0]);\r\n    return advantages.slice(0, 3);\r\n  }\r\n\r\n  return getBusinessTypeStrengths(businessType);\r\n}\r\n\r\n/**\r\n * Get random element from array\r\n */\r\nfunction getRandomElement<T>(array: T[]): T {\r\n  return array[Math.floor(Math.random() * array.length)];\r\n}\r\n\r\n/**\r\n * Create professional design prompt with 20 years of design + marketing expertise\r\n * Combines cultural awareness, psychology, and visual design mastery\r\n */\r\nfunction createProfessionalDesignPrompt(imageText: string, platform: Platform, profile: BrandProfile, revoModel: RevoModelId): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract business intelligence for design context\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Get cultural context for design decisions\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Industry-specific design psychology\r\n  const industryDesignPsychology = getIndustryDesignPsychology(businessType);\r\n\r\n  // Platform-specific design requirements\r\n  const platformRequirements = getPlatformDesignRequirements(platform);\r\n\r\n  // Color psychology based on business type and culture\r\n  const colorPsychology = getColorPsychology(businessType, location);\r\n\r\n  // Typography psychology for conversion\r\n  const typographyStrategy = getTypographyStrategy(businessType, platform);\r\n\r\n  return `Create an exceptional, conversion-focused ${platform} design for ${businessName} that embodies 20 years of professional design and marketing expertise.\r\n\r\nLANGUAGE REQUIREMENTS:\r\n- ALL TEXT MUST BE IN ENGLISH ONLY\r\n- NO Arabic, Hindi, Chinese, or other non-English characters\r\n- NO transliterated text or mixed languages\r\n- Use clear, professional English throughout\r\n\r\nBUSINESS INTELLIGENCE & CONTEXT:\r\n- Company: ${businessName} (${businessType})\r\n- Primary Service: ${businessIntelligence.primaryService}\r\n- Location: ${location}\r\n- Target Audience: ${businessIntelligence.actualTargetAudience}\r\n- Key Differentiator: ${businessIntelligence.realDifferentiator}\r\n- Unique Value: ${businessIntelligence.mainValue}\r\n- Competitive Advantage: ${businessIntelligence.realCompetitiveAdvantage}\r\n\r\nTEXT TO INTEGRATE: \"${imageText}\"\r\n\r\nBRAND-SPECIFIC DESIGN REQUIREMENTS:\r\n- Must communicate: ${businessIntelligence.realCompetitiveAdvantage}\r\n- Must highlight: ${businessIntelligence.uniqueFeature}\r\n- Must appeal to: ${businessIntelligence.actualTargetAudience}\r\n- Must convey: ${businessIntelligence.keyBenefit}\r\n\r\nDESIGN PSYCHOLOGY & STRATEGY:\r\n${industryDesignPsychology}\r\n\r\nVISUAL HIERARCHY & COMPOSITION:\r\n- Apply the golden ratio and rule of thirds for optimal visual flow\r\n- Create clear focal points that guide the eye to key conversion elements\r\n- Use strategic white space to enhance readability and premium feel\r\n- Implement Z-pattern or F-pattern layout for maximum engagement\r\n\r\nCOLOR STRATEGY:\r\n${colorPsychology}\r\n\r\nTYPOGRAPHY MASTERY:\r\n${typographyStrategy}\r\n\r\nCULTURAL DESIGN ADAPTATION:\r\n- ${culturalContext.localMarket} aesthetic preferences\r\n- ${culturalContext.targetAudience} visual expectations\r\n- Regional design trends and cultural symbols\r\n- Local color associations and meanings\r\n\r\nCONVERSION OPTIMIZATION:\r\n- Design elements that create urgency and desire\r\n- Visual cues that guide toward call-to-action\r\n- Trust signals through professional presentation\r\n- Emotional triggers through strategic imagery and layout\r\n\r\nPLATFORM OPTIMIZATION:\r\n${platformRequirements}\r\n\r\nTECHNICAL EXCELLENCE:\r\n- Aspect Ratio: 1:1 (perfect square)\r\n- Resolution: Ultra-high quality, print-ready standards\r\n- Text Clarity: Crystal clear, perfectly readable at all sizes\r\n- Brand Consistency: Align with professional brand standards\r\n- Mobile Optimization: Ensure perfect display on all devices\r\n\r\nFINAL QUALITY STANDARDS:\r\nThis design must look like it was created by a top-tier creative agency specifically for ${businessName}. Every element should reflect their unique value proposition: \"${businessIntelligence.realCompetitiveAdvantage}\". The design should immediately communicate their expertise in ${businessIntelligence.primaryService} while appealing directly to ${businessIntelligence.actualTargetAudience}.\r\n\r\nThe final result should be a sophisticated, professional design that drives ${businessIntelligence.actualTargetAudience} to choose ${businessName} over competitors and take immediate action.\r\n\r\nMake it absolutely irresistible for ${businessIntelligence.actualTargetAudience} and perfectly aligned with ${businessName}'s brand identity.`;\r\n}\r\n\r\n/**\r\n * Get industry-specific design psychology\r\n */\r\nfunction getIndustryDesignPsychology(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {\r\n    return `- Use warm, appetizing colors that stimulate hunger and comfort\r\n- Incorporate food photography principles with rich textures\r\n- Create cozy, inviting atmosphere through design elements\r\n- Focus on sensory appeal and mouth-watering visual presentation`;\r\n  }\r\n\r\n  if (type.includes('tech') || type.includes('software') || type.includes('digital')) {\r\n    return `- Employ clean, minimalist design with high-tech aesthetics\r\n- Use gradients and modern geometric shapes\r\n- Incorporate subtle tech-inspired elements and icons\r\n- Focus on innovation, efficiency, and cutting-edge appeal`;\r\n  }\r\n\r\n  if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {\r\n    return `- Use calming, trustworthy colors that convey safety and care\r\n- Incorporate clean, sterile design elements\r\n- Focus on professionalism, expertise, and patient comfort\r\n- Use imagery that suggests health, vitality, and well-being`;\r\n  }\r\n\r\n  if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {\r\n    return `- Employ sophisticated, conservative design elements\r\n- Use colors that convey stability, trust, and prosperity\r\n- Incorporate subtle luxury elements and professional imagery\r\n- Focus on security, growth, and financial success`;\r\n  }\r\n\r\n  if (type.includes('real estate') || type.includes('property')) {\r\n    return `- Use aspirational imagery and luxury design elements\r\n- Incorporate architectural lines and premium materials\r\n- Focus on lifestyle, investment, and dream fulfillment\r\n- Use colors that suggest stability, growth, and success`;\r\n  }\r\n\r\n  // Default professional services\r\n  return `- Use professional, trustworthy design elements\r\n- Incorporate subtle premium touches and quality indicators\r\n- Focus on expertise, reliability, and professional excellence\r\n- Use colors and imagery that convey competence and success`;\r\n}\r\n\r\n/**\r\n * Get platform-specific design requirements\r\n */\r\nfunction getPlatformDesignRequirements(platform: Platform): string {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return `- Optimize for Instagram's visual-first environment\r\n- Use bold, eye-catching elements that stand out in feeds\r\n- Incorporate Instagram-native design trends and aesthetics\r\n- Ensure design works perfectly in both feed and story formats`;\r\n\r\n    case 'Facebook':\r\n      return `- Design for Facebook's diverse, multi-generational audience\r\n- Use clear, readable elements that work across age groups\r\n- Incorporate social proof and community-focused elements\r\n- Ensure design is engaging but not overwhelming`;\r\n\r\n    case 'LinkedIn':\r\n      return `- Employ professional, business-focused design elements\r\n- Use conservative colors and sophisticated typography\r\n- Incorporate industry-specific imagery and professional symbols\r\n- Focus on credibility, expertise, and business value`;\r\n\r\n    case 'Twitter':\r\n      return `- Create concise, impactful design that communicates quickly\r\n- Use bold typography and clear visual hierarchy\r\n- Incorporate trending design elements and current aesthetics\r\n- Ensure design is optimized for rapid consumption`;\r\n\r\n    default:\r\n      return `- Create versatile design that works across multiple platforms\r\n- Use universal design principles and broad appeal\r\n- Ensure scalability and readability across different contexts\r\n- Focus on timeless, professional aesthetics`;\r\n  }\r\n}\r\n\r\n/**\r\n * Get color psychology based on business type and location\r\n */\r\nfunction getColorPsychology(businessType: string, location: string): string {\r\n  const type = businessType.toLowerCase();\r\n  const loc = location.toLowerCase();\r\n\r\n  let baseColors = '';\r\n  let culturalColors = '';\r\n\r\n  // Business type color psychology\r\n  if (type.includes('restaurant') || type.includes('food')) {\r\n    baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    baseColors = 'modern blues, teals, and purples to convey innovation and trust';\r\n  } else if (type.includes('health') || type.includes('medical')) {\r\n    baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';\r\n  } else if (type.includes('finance') || type.includes('banking')) {\r\n    baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';\r\n  } else {\r\n    baseColors = 'professional blues, grays, and accent colors to convey trust and competence';\r\n  }\r\n\r\n  // Cultural color adaptations\r\n  if (loc.includes('dubai') || loc.includes('uae')) {\r\n    culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\\'s premium market expectations';\r\n  } else if (loc.includes('london') || loc.includes('uk')) {\r\n    culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';\r\n  } else if (loc.includes('new york') || loc.includes('nyc')) {\r\n    culturalColors = 'Employ bold, confident colors that match New York\\'s dynamic business environment';\r\n  } else {\r\n    culturalColors = 'Use universally appealing professional color combinations';\r\n  }\r\n\r\n  return `- Primary Strategy: ${baseColors}\r\n- Cultural Adaptation: ${culturalColors}\r\n- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors\r\n- Contrast Optimization: Ensure maximum readability and visual impact`;\r\n}\r\n\r\n/**\r\n * Get typography strategy for conversion\r\n */\r\nfunction getTypographyStrategy(businessType: string, platform: Platform): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  let fontStrategy = '';\r\n  let hierarchyStrategy = '';\r\n\r\n  if (type.includes('luxury') || type.includes('premium')) {\r\n    fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';\r\n  } else if (type.includes('creative') || type.includes('design')) {\r\n    fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';\r\n  } else {\r\n    fontStrategy = 'Professional, highly readable fonts that convey trust and competence';\r\n  }\r\n\r\n  hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact\r\n- Secondary Text: Clear, readable subheadings that support the main message\r\n- Call-to-Action: Distinctive typography that stands out and drives action\r\n- Supporting Text: Clean, professional fonts for additional information`;\r\n\r\n  return `- Font Selection: ${fontStrategy}\r\n- Visual Hierarchy: ${hierarchyStrategy}\r\n- Readability: Optimized for ${platform} viewing conditions and mobile devices\r\n- Conversion Focus: Typography choices designed to guide the eye and encourage action`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqcsB,qCAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/actions.ts"], "sourcesContent": ["// src/app/actions.ts\r\n\"use server\";\r\n\r\nimport { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from \"@/ai/flows/analyze-brand\";\r\nimport { generatePostFromProfile as generatePostFromProfileFlow } from \"@/ai/flows/generate-post-from-profile\";\r\nimport { generateVideoPost as generateVideoPostFlow } from \"@/ai/flows/generate-video-post\";\r\nimport { generateCreativeAsset as generateCreativeAssetFlow } from \"@/ai/flows/generate-creative-asset\";\r\nimport type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from \"@/lib/types\";\r\nimport { artifactsService } from \"@/lib/services/artifacts-service\";\r\nimport type { Artifact } from \"@/lib/types/artifacts\";\r\nimport {\r\n  detectAndPopulateLanguages,\r\n  getLanguageInstructionForProfile,\r\n  updateLanguageDetectionIfNeeded\r\n} from \"@/lib/services/brand-language-service\";\r\n// import { generateEnhancedDesign } from \"@/ai/gemini-2.5-design\"; // Temporarily disabled\r\nimport { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from \"@/app/actions/revo-2-actions\";\r\nimport { DesignGenerationService } from \"@/ai/models/services/design-generation-service\";\r\nimport type { RevoModelId } from \"@/ai/models/types/model-types\";\r\n\r\n\r\n// --- AI Flow Actions ---\r\n\r\ntype AnalysisResult = {\r\n  success: true;\r\n  data: BrandAnalysisResult;\r\n} | {\r\n  success: false;\r\n  error: string;\r\n  errorType: 'blocked' | 'timeout' | 'error';\r\n};\r\n\r\nexport async function analyzeBrandAction(\r\n  websiteUrl: string,\r\n  designImageUris: string[],\r\n): Promise<AnalysisResult> {\r\n  try {\r\n    console.log(\"🔍 Starting brand analysis for URL:\", websiteUrl);\r\n    console.log(\"🖼️ Design images count:\", designImageUris.length);\r\n\r\n    // Validate URL format\r\n    if (!websiteUrl || !websiteUrl.trim()) {\r\n      return {\r\n        success: false,\r\n        error: \"Website URL is required\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    // Ensure URL has protocol\r\n    let validUrl = websiteUrl.trim();\r\n    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {\r\n      validUrl = 'https://' + validUrl;\r\n    }\r\n\r\n    const result = await analyzeBrandFlow({\r\n      websiteUrl: validUrl,\r\n      designImageUris: designImageUris || []\r\n    });\r\n\r\n    console.log(\"✅ Brand analysis result:\", JSON.stringify(result, null, 2));\r\n    console.log(\"🔍 Result type:\", typeof result);\r\n    console.log(\"🔍 Result keys:\", result ? Object.keys(result) : \"No result\");\r\n\r\n    if (!result) {\r\n      return {\r\n        success: false,\r\n        error: \"Analysis returned empty result\",\r\n        errorType: 'error'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: result\r\n    };\r\n  } catch (error) {\r\n    console.error(\"❌ Error analyzing brand:\", error);\r\n\r\n    // Return structured error response instead of throwing\r\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error occurred\";\r\n\r\n    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else if (errorMessage.includes('timeout')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website analysis timed out. Please try again or check if the website is accessible.\",\r\n        errorType: 'timeout'\r\n      };\r\n    } else if (errorMessage.includes('CORS')) {\r\n      return {\r\n        success: false,\r\n        error: \"Website blocks automated access. This is common for security reasons.\",\r\n        errorType: 'blocked'\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: `Analysis failed: ${errorMessage}`,\r\n        errorType: 'error'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nconst getAspectRatioForPlatform = (platform: Platform): string => {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return '1:1'; // Square\r\n    case 'Facebook':\r\n      return '16:9'; // Landscape - Facebook posts are landscape format\r\n    case 'Twitter':\r\n      return '16:9'; // Landscape\r\n    case 'LinkedIn':\r\n      return '16:9'; // Landscape - LinkedIn posts are landscape format\r\n    default:\r\n      return '1:1';\r\n  }\r\n}\r\n\r\nexport async function generateContentAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean }\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    const today = new Date();\r\n    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });\r\n    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });\r\n\r\n    // Update language detection if needed\r\n    const profileWithLanguages = updateLanguageDetectionIfNeeded(profile);\r\n\r\n    // Generate language instructions for AI\r\n    const languageInstructions = getLanguageInstructionForProfile(profileWithLanguages);\r\n\r\n    // Apply brand consistency logic\r\n    const effectiveDesignExamples = brandConsistency?.strictConsistency\r\n      ? (profile.designExamples || [])\r\n      : []; // Don't use design examples if not strict consistency\r\n\r\n    // Convert arrays to newline-separated strings for AI processing\r\n    const keyFeaturesString = Array.isArray(profile.keyFeatures)\r\n      ? profile.keyFeatures.join('\\n')\r\n      : profile.keyFeatures || '';\r\n\r\n    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)\r\n      ? profile.competitiveAdvantages.join('\\n')\r\n      : profile.competitiveAdvantages || '';\r\n\r\n    // Convert services array to newline-separated string\r\n    const servicesString = Array.isArray(profile.services)\r\n      ? profile.services.map(service =>\r\n        typeof service === 'object' && service.name\r\n          ? `${service.name}: ${service.description || ''}`\r\n          : service\r\n      ).join('\\n')\r\n      : profile.services || '';\r\n\r\n\r\n\r\n    const postDetails = await generatePostFromProfileFlow({\r\n      businessType: profileWithLanguages.businessType,\r\n      location: profileWithLanguages.location,\r\n      writingTone: profileWithLanguages.writingTone,\r\n      contentThemes: profileWithLanguages.contentThemes,\r\n      visualStyle: profileWithLanguages.visualStyle,\r\n      logoDataUrl: profileWithLanguages.logoDataUrl,\r\n      designExamples: effectiveDesignExamples, // Use design examples based on consistency preference\r\n      primaryColor: profileWithLanguages.primaryColor,\r\n      accentColor: profileWithLanguages.accentColor,\r\n      backgroundColor: profileWithLanguages.backgroundColor,\r\n      dayOfWeek,\r\n      currentDate,\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      // Pass new detailed fields\r\n      services: servicesString,\r\n      targetAudience: profileWithLanguages.targetAudience,\r\n      keyFeatures: keyFeaturesString,\r\n      competitiveAdvantages: competitiveAdvantagesString,\r\n      // Pass brand consistency preferences\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n      // Pass intelligent language instructions\r\n      languageInstructions: languageInstructions,\r\n    });\r\n\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: today.toISOString(),\r\n      content: postDetails.content,\r\n      hashtags: postDetails.hashtags,\r\n      status: 'generated',\r\n      variants: postDetails.variants,\r\n      catchyWords: postDetails.catchyWords,\r\n      subheadline: postDetails.subheadline,\r\n      callToAction: postDetails.callToAction,\r\n      // Include enhanced AI features\r\n      contentVariants: postDetails.contentVariants,\r\n      hashtagAnalysis: postDetails.hashtagAnalysis,\r\n      // Include advanced AI features\r\n      marketIntelligence: postDetails.marketIntelligence,\r\n      // Include local context features\r\n      localContext: postDetails.localContext,\r\n    };\r\n\r\n    return newPost;\r\n  } catch (error) {\r\n    console.error(\"Error generating content:\", error);\r\n    throw new Error(\"Failed to generate content. Please try again later.\");\r\n  }\r\n}\r\n\r\nexport async function generateVideoContentAction(\r\n  profile: BrandProfile,\r\n  catchyWords: string,\r\n  postContent: string,\r\n): Promise<{ videoUrl: string }> {\r\n  try {\r\n    const result = await generateVideoPostFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      visualStyle: profile.visualStyle,\r\n      imageText: catchyWords, // Use catchyWords as imageText for video generation\r\n      postContent: postContent,\r\n    });\r\n    return { videoUrl: result.videoUrl };\r\n  } catch (error) {\r\n    console.error(\"Error generating video content:\", error);\r\n    // Pass the specific error message from the flow to the client\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n\r\nexport async function generateCreativeAssetAction(\r\n  prompt: string,\r\n  outputType: 'image' | 'video',\r\n  referenceAssetUrl: string | null,\r\n  useBrandProfile: boolean,\r\n  brandProfile: BrandProfile | null,\r\n  maskDataUrl: string | null | undefined,\r\n  aspectRatio: '16:9' | '9:16' | undefined\r\n): Promise<CreativeAsset> {\r\n  try {\r\n    const result = await generateCreativeAssetFlow({\r\n      prompt,\r\n      outputType,\r\n      referenceAssetUrl,\r\n      useBrandProfile,\r\n      brandProfile: useBrandProfile ? brandProfile : null,\r\n      maskDataUrl,\r\n      aspectRatio,\r\n    });\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Error generating creative asset:\", error);\r\n    // Always pass the specific error message from the flow to the client.\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\nexport async function generateEnhancedDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },\r\n  brandProfile?: BrandProfile,\r\n  enableEnhancements: boolean = true,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactInstructions?: string\r\n): Promise<{\r\n  imageUrl: string;\r\n  qualityScore: number;\r\n  enhancementsApplied: string[];\r\n  processingTime: number;\r\n}> {\r\n  const startTime = Date.now();\r\n  const enhancementsApplied: string[] = [];\r\n\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for enhanced design generation');\r\n    }\r\n\r\n    // Handle both old string format and new object format\r\n    let finalImageText: string;\r\n    if (typeof imageText === 'string') {\r\n      finalImageText = imageText;\r\n    } else {\r\n      // Combine catchy words, subheadline, and call-to-action\r\n      const components = [imageText.catchyWords];\r\n      if (imageText.subheadline && imageText.subheadline.trim()) {\r\n        components.push(imageText.subheadline.trim());\r\n      }\r\n      if (imageText.callToAction && imageText.callToAction.trim()) {\r\n        components.push(imageText.callToAction.trim());\r\n      }\r\n      finalImageText = components.join('\\n');\r\n    }\r\n\r\n    console.log('🎨 Enhanced Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', finalImageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n    console.log('- Enhancements Enabled:', enableEnhancements);\r\n\r\n    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD\r\n    let result;\r\n\r\n    try {\r\n      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');\r\n\r\n      result = await generateEnhancedDesign({\r\n        businessType,\r\n        platform,\r\n        visualStyle,\r\n        imageText: finalImageText,\r\n        brandProfile,\r\n        brandConsistency,\r\n        artifactInstructions,\r\n      });\r\n\r\n      console.log('✅ Gemini 2.5 enhanced design generated successfully');\r\n      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);\r\n      console.log(`⚡ Processing Time: ${result.processingTime}ms`);\r\n\r\n    } catch (gemini25Error) {\r\n      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);\r\n\r\n      try {\r\n        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');\r\n        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');\r\n\r\n        result = await generateEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');\r\n      } catch (openaiError) {\r\n        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);\r\n\r\n        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n        result = await generateGeminiHDEnhancedDesignWithFallback({\r\n          businessType,\r\n          platform,\r\n          visualStyle,\r\n          imageText: finalImageText,\r\n          brandProfile,\r\n          brandConsistency,\r\n          artifactInstructions,\r\n        });\r\n\r\n        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');\r\n      }\r\n    }\r\n\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      imageUrl: result.imageUrl,\r\n      qualityScore: result.qualityScore,\r\n      enhancementsApplied: result.enhancementsApplied,\r\n      processingTime: result.processingTime\r\n    };\r\n\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating enhanced design:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate enhanced design specifically using Gemini 2.0 Flash HD\r\n * This action forces the use of Gemini HD for maximum quality\r\n */\r\nexport async function generateGeminiHDDesignAction(\r\n  businessType: string,\r\n  platform: string,\r\n  visualStyle: string,\r\n  imageText: string,\r\n  brandProfile: BrandProfile,\r\n  brandConsistency?: {\r\n    strictConsistency: boolean;\r\n    followBrandColors: boolean;\r\n  },\r\n  artifactInstructions?: string\r\n): Promise<PostVariant> {\r\n  try {\r\n    if (!brandProfile) {\r\n      throw new Error('Brand profile is required for Gemini HD design generation');\r\n    }\r\n\r\n    console.log('🎨 Gemini HD Design Generation Started');\r\n    console.log('- Business Type:', businessType);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Visual Style:', visualStyle);\r\n    console.log('- Image Text:', imageText);\r\n    console.log('- Brand Profile:', brandProfile.businessName);\r\n\r\n    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');\r\n    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');\r\n\r\n    const result = await generateGeminiHDEnhancedDesignWithFallback({\r\n      businessType,\r\n      platform,\r\n      visualStyle,\r\n      imageText,\r\n      brandProfile,\r\n      brandConsistency,\r\n      artifactInstructions,\r\n    });\r\n\r\n    console.log('✅ Gemini HD enhanced design generated successfully');\r\n    console.log('🔗 Image URL:', result.imageUrl);\r\n    console.log('⭐ Quality Score:', result.qualityScore);\r\n    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);\r\n\r\n    return {\r\n      platform,\r\n      imageUrl: result.imageUrl,\r\n      caption: imageText,\r\n      hashtags: [],\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Error in Gemini HD design generation:', error);\r\n    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content with artifact references (Enhanced)\r\n */\r\nexport async function generateContentWithArtifactsAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = [],\r\n  useEnhancedDesign: boolean = true\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log('🎨 Generating content with artifacts...');\r\n    console.log('- Platform:', platform);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n    console.log('- Enhanced Design:', useEnhancedDesign);\r\n\r\n    // Get active artifacts if no specific artifacts provided\r\n    let targetArtifacts: Artifact[] = [];\r\n\r\n    if (artifactIds.length > 0) {\r\n      // Use specified artifacts\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts, prioritizing exact-use\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      console.log('🔍 Active artifacts found:', activeArtifacts.length);\r\n      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({\r\n        id: a.id,\r\n        name: a.name,\r\n        type: a.type,\r\n        usageType: a.usageType,\r\n        isActive: a.isActive,\r\n        instructions: a.instructions\r\n      })));\r\n\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      // Prioritize exact-use artifacts\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      // Track usage for active artifacts\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));\r\n\r\n    // Generate base content first\r\n    const basePost = await generateContentAction(profile, platform, brandConsistency);\r\n\r\n    // If enhanced design is disabled, return base content\r\n    if (!useEnhancedDesign) {\r\n      console.log('🔄 Enhanced design disabled, using base content generation');\r\n      return basePost;\r\n    }\r\n\r\n    // Enhanced design is enabled - always use enhanced generation regardless of artifacts\r\n    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');\r\n    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);\r\n\r\n    if (targetArtifacts.length === 0) {\r\n      console.log('✨ No artifacts provided - using enhanced design without artifact context');\r\n    } else {\r\n      console.log('🎯 Using enhanced design with artifact context');\r\n    }\r\n\r\n    // Separate exact-use and reference artifacts\r\n    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n    // Create enhanced image text structure from post components\r\n    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {\r\n      catchyWords: basePost.catchyWords || 'Engaging Content',\r\n      subheadline: basePost.subheadline,\r\n      callToAction: basePost.callToAction\r\n    };\r\n    let enhancedContent = basePost.content;\r\n\r\n    // Collect usage instructions from artifacts\r\n    const artifactInstructions = targetArtifacts\r\n      .filter(a => a.instructions && a.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Collect text overlay instructions from text artifacts\r\n    const textOverlayInstructions = exactUseArtifacts\r\n      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())\r\n      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)\r\n      .join('\\n');\r\n\r\n    // Process exact-use artifacts first (higher priority)\r\n    if (exactUseArtifacts.length > 0) {\r\n      const primaryExactUse = exactUseArtifacts[0];\r\n\r\n      // Use text overlay if available\r\n      if (primaryExactUse.textOverlay) {\r\n        if (primaryExactUse.textOverlay.headline) {\r\n          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;\r\n          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);\r\n        }\r\n\r\n        if (primaryExactUse.textOverlay.message) {\r\n          enhancedContent = primaryExactUse.textOverlay.message;\r\n          console.log('📝 Using message from exact-use artifact');\r\n        }\r\n\r\n        // Use CTA from artifact if available\r\n        if (primaryExactUse.textOverlay.cta) {\r\n          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;\r\n          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Process reference artifacts for style guidance\r\n    const activeDirectives = referenceArtifacts.flatMap(artifact =>\r\n      artifact.directives.filter(directive => directive.active)\r\n    );\r\n\r\n    // Apply style reference directives\r\n    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');\r\n    let visualStyleOverride = profile.visualStyle || 'modern';\r\n    if (styleDirectives.length > 0) {\r\n      console.log('🎨 Applying style references from artifacts');\r\n      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);\r\n      if (primaryStyleDirective) {\r\n        visualStyleOverride = 'artifact-inspired';\r\n        console.log('🎨 Using artifact-inspired visual style');\r\n      }\r\n    }\r\n\r\n    // Combine all instructions\r\n    const allInstructions = [artifactInstructions, textOverlayInstructions]\r\n      .filter(Boolean)\r\n      .join('\\n');\r\n\r\n    // Generate enhanced design with artifact context\r\n    const enhancedResult = await generateEnhancedDesignAction(\r\n      profile.businessType || 'business',\r\n      platform.toLowerCase(),\r\n      visualStyleOverride,\r\n      enhancedImageText,\r\n      profile,\r\n      true,\r\n      brandConsistency,\r\n      allInstructions || undefined\r\n    );\r\n\r\n    // Create enhanced post with artifact metadata\r\n    const enhancedPost: GeneratedPost = {\r\n      ...basePost,\r\n      id: Date.now().toString(),\r\n      variants: [{\r\n        platform: platform,\r\n        imageUrl: enhancedResult.imageUrl\r\n      }],\r\n      content: targetArtifacts.length > 0\r\n        ? `${enhancedContent}\\n\\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`\r\n        : `${enhancedContent}\\n\\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,\r\n      date: new Date().toISOString(),\r\n      // Add artifact metadata\r\n      metadata: {\r\n        ...basePost.metadata,\r\n        referencedArtifacts: targetArtifacts.map(a => ({\r\n          id: a.id,\r\n          name: a.name,\r\n          type: a.type,\r\n          category: a.category\r\n        })),\r\n        activeDirectives: activeDirectives.map(d => ({\r\n          id: d.id,\r\n          type: d.type,\r\n          label: d.label,\r\n          priority: d.priority\r\n        }))\r\n      }\r\n    };\r\n\r\n    console.log('✅ Enhanced content with artifacts generated successfully');\r\n    return enhancedPost;\r\n\r\n  } catch (error) {\r\n    console.error(\"Error generating content with artifacts:\", error);\r\n    throw new Error((error as Error).message);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate content using the new Revo model system\r\n * This action uses the proper model architecture with version-specific implementations\r\n */\r\nexport async function generateContentWithRevoModelAction(\r\n  profile: BrandProfile,\r\n  platform: Platform,\r\n  revoModel: RevoModelId,\r\n  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },\r\n  artifactIds: string[] = []\r\n): Promise<GeneratedPost> {\r\n  try {\r\n    console.log(`🎨 Generating content with ${revoModel} model...`);\r\n    console.log('- Platform:', platform);\r\n    console.log('- Business Type:', profile.businessType);\r\n    console.log('- Artifacts:', artifactIds.length);\r\n\r\n    // Handle artifacts if provided\r\n    let targetArtifacts: Artifact[] = [];\r\n    if (artifactIds.length > 0) {\r\n      for (const artifactId of artifactIds) {\r\n        const artifact = artifactsService.getArtifact(artifactId);\r\n        if (artifact) {\r\n          targetArtifacts.push(artifact);\r\n          await artifactsService.trackUsage(artifactId, 'quick-content');\r\n        }\r\n      }\r\n    } else {\r\n      // Use active artifacts\r\n      const activeArtifacts = artifactsService.getActiveArtifacts();\r\n      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');\r\n      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];\r\n\r\n      for (const artifact of targetArtifacts) {\r\n        await artifactsService.trackUsage(artifact.id, 'quick-content');\r\n      }\r\n    }\r\n\r\n    // Prepare artifact instructions\r\n    let artifactInstructions = '';\r\n    if (targetArtifacts.length > 0) {\r\n      const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');\r\n      const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');\r\n\r\n      if (exactUseArtifacts.length > 0) {\r\n        artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\\n';\r\n        exactUseArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\\n`;\r\n        });\r\n      }\r\n\r\n      if (referenceArtifacts.length > 0) {\r\n        artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\\n';\r\n        referenceArtifacts.forEach(artifact => {\r\n          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\\n`;\r\n        });\r\n      }\r\n    }\r\n\r\n    // Use simplified Revo model generation with text validation\r\n    console.log(`🎨 Using Revo ${revoModel} with text validation...`);\r\n    console.log('🔧 DEBUG: This is the SIMPLIFIED code path');\r\n\r\n    // Generate dynamic and varied text for each design\r\n    const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);\r\n    let imageText = textVariations.selectedText;\r\n\r\n    if (revoModel === 'revo-1.0') {\r\n      console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');\r\n      console.log('🔍 Original text:', imageText);\r\n\r\n      // Simple text validation for Revo 1.0\r\n      const words = imageText.split(' ').filter(word => word.length > 0);\r\n      if (words.length > 25) {\r\n        console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);\r\n        imageText = words.slice(0, 25).join(' ');\r\n      }\r\n      console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);\r\n    }\r\n\r\n    // Use sophisticated design prompt created by 20-year veteran designer + marketer\r\n    const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);\r\n\r\n    const designResult = await generateCreativeAssetFlow({\r\n      prompt: designPrompt,\r\n      outputType: 'image',\r\n      referenceAssetUrl: null,\r\n      useBrandProfile: true,\r\n      brandProfile: profile,\r\n      maskDataUrl: null\r\n    });\r\n\r\n    if (!designResult.imageUrl) {\r\n      throw new Error('Design generation failed: No image URL returned');\r\n    }\r\n\r\n    // Generate content using the standard flow for caption and hashtags\r\n    const contentResult = await generatePostFromProfileFlow({\r\n      businessType: profile.businessType,\r\n      location: profile.location,\r\n      writingTone: profile.writingTone,\r\n      contentThemes: profile.contentThemes,\r\n      visualStyle: profile.visualStyle,\r\n      logoDataUrl: profile.logoDataUrl,\r\n      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],\r\n      primaryColor: profile.primaryColor,\r\n      accentColor: profile.accentColor,\r\n      backgroundColor: profile.backgroundColor,\r\n      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),\r\n      currentDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),\r\n      variants: [{\r\n        platform: platform,\r\n        aspectRatio: getAspectRatioForPlatform(platform),\r\n      }],\r\n      services: Array.isArray(profile.services)\r\n        ? profile.services.map(s => typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\\n')\r\n        : profile.services || '',\r\n      targetAudience: profile.targetAudience,\r\n      keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\\n') : profile.keyFeatures || '',\r\n      competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\\n') : profile.competitiveAdvantages || '',\r\n      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },\r\n    });\r\n\r\n    // Combine the design result with content result\r\n    const newPost: GeneratedPost = {\r\n      id: new Date().toISOString(),\r\n      date: new Date().toISOString(),\r\n      content: contentResult.content,\r\n      hashtags: contentResult.hashtags,\r\n      status: 'generated',\r\n      variants: [{\r\n        platform,\r\n        imageUrl: designResult.imageUrl || '',\r\n        caption: contentResult.content,\r\n        hashtags: contentResult.hashtags\r\n      }],\r\n      catchyWords: contentResult.catchyWords,\r\n      subheadline: contentResult.subheadline,\r\n      callToAction: contentResult.callToAction,\r\n      contentVariants: contentResult.contentVariants,\r\n      hashtagAnalysis: contentResult.hashtagAnalysis,\r\n      marketIntelligence: contentResult.marketIntelligence,\r\n      localContext: contentResult.localContext,\r\n      // Add Revo model metadata\r\n      revoModelUsed: revoModel,\r\n      qualityScore: 8, // Default quality score for Revo models\r\n      processingTime: Date.now() - Date.now(), // Will be calculated properly\r\n      creditsUsed: 1,\r\n      enhancementsApplied: [`Revo ${revoModel} Generation`, 'Text Validation', 'Professional Design']\r\n    };\r\n\r\n    console.log(`✅ Content generated successfully with ${revoModel}`);\r\n    console.log(`⭐ Quality Score: 8/10`);\r\n    console.log(`⚡ Processing Time: Fast generation`);\r\n    console.log(`💰 Credits Used: 1`);\r\n\r\n    return newPost;\r\n\r\n  } catch (error) {\r\n    console.error(`❌ Error generating content with ${revoModel}:`, error);\r\n    throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Generate professional marketing-driven text with cultural awareness\r\n * Designed by a 20-year veteran designer + 20-year marketing expert\r\n * Now deeply connected to actual brand profile information\r\n */\r\nfunction generateDynamicTextForRevo(profile: BrandProfile, revoModel: RevoModelId, platform: Platform) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n\r\n  // Generate sophisticated marketing copy using actual brand profile data\r\n  const marketingCopy = generateMarketingCopy(profile, platform);\r\n\r\n  console.log(`🎯 Generated personalized marketing copy for ${businessName}: \"${marketingCopy}\"`);\r\n\r\n  return {\r\n    selectedText: marketingCopy,\r\n    allVariations: [marketingCopy],\r\n    variationIndex: 0\r\n  };\r\n}\r\n\r\n/**\r\n * Generate sophisticated marketing copy that sells\r\n * Combines 20 years of design + marketing expertise with actual brand profile data\r\n * Now deeply personalized using real business information\r\n * LANGUAGE RESTRICTION: English only - no Arabic, Hindi, Chinese, or other non-English text\r\n */\r\nfunction generateMarketingCopy(profile: BrandProfile, platform: Platform): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract real business intelligence from profile\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Cultural and regional insights\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Generate catchy headline using actual business strengths (max 5 words)\r\n  const catchyHeadlines = [\r\n    `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,\r\n    `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,\r\n    `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,\r\n    `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,\r\n    `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`\r\n  ];\r\n\r\n  // Generate subheadline using real competitive advantages (max 14 words)\r\n  const subheadlines = [\r\n    `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,\r\n    `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,\r\n    `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,\r\n    `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,\r\n    `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`\r\n  ];\r\n\r\n  // Generate call-to-action using actual business context\r\n  const callToActions = [\r\n    `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,\r\n    `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,\r\n    `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,\r\n    `${getRandomElement(['Book', 'Schedule', 'Request'])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,\r\n    `${getRandomElement(['Start', 'Begin', 'Launch'])} Your ${businessIntelligence.journeyType} Today`\r\n  ];\r\n\r\n  // Randomly select components\r\n  const catchyWords = getRandomElement(catchyHeadlines);\r\n  const subheadline = getRandomElement(subheadlines);\r\n  const cta = getRandomElement(callToActions);\r\n\r\n  // Combine based on marketing best practices and business context\r\n  const marketingFormats = [\r\n    `${catchyWords}\\n${subheadline}\\n${cta}`,\r\n    `${catchyWords}\\n${subheadline}`,\r\n    `${catchyWords}\\n${cta}`,\r\n    `${subheadline}\\n${cta}`,\r\n    catchyWords\r\n  ];\r\n\r\n  return getRandomElement(marketingFormats);\r\n}\r\n\r\n/**\r\n * Get cultural context and local market insights\r\n */\r\nfunction getCulturalContext(location: string) {\r\n  // Default context\r\n  let context = {\r\n    localTerms: ['Premium', 'Professional', 'Expert'],\r\n    marketingStyle: 'Professional',\r\n    targetAudience: 'businesses',\r\n    localMarket: 'modern',\r\n    socialProof: 'thousands of clients',\r\n    valueProposition: 'Industry-leading',\r\n    competitiveAdvantage: 'proven expertise',\r\n    urgencyTrigger: 'Don\\'t miss out on',\r\n    actionWords: 'Connect with',\r\n    localCTA: 'Get Started',\r\n    ctaUrgency: 'Now'\r\n  };\r\n\r\n  // Location-specific cultural adaptations\r\n  if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {\r\n    context = {\r\n      localTerms: ['Premium', 'Luxury', 'Elite', 'Exclusive'],\r\n      marketingStyle: 'Luxury-focused',\r\n      targetAudience: 'discerning clients',\r\n      localMarket: 'Dubai\\'s dynamic',\r\n      socialProof: 'leading UAE businesses',\r\n      valueProposition: 'World-class',\r\n      competitiveAdvantage: 'international excellence',\r\n      urgencyTrigger: 'Seize the opportunity for',\r\n      actionWords: 'Experience',\r\n      localCTA: 'Book Your Exclusive Consultation',\r\n      ctaUrgency: 'Today'\r\n    };\r\n  } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {\r\n    context = {\r\n      localTerms: ['Bespoke', 'Tailored', 'Refined'],\r\n      marketingStyle: 'Sophisticated',\r\n      targetAudience: 'discerning professionals',\r\n      localMarket: 'London\\'s competitive',\r\n      socialProof: 'established UK enterprises',\r\n      valueProposition: 'Expertly crafted',\r\n      competitiveAdvantage: 'British excellence',\r\n      urgencyTrigger: 'Secure your',\r\n      actionWords: 'Discover',\r\n      localCTA: 'Arrange Your Consultation',\r\n      ctaUrgency: 'Promptly'\r\n    };\r\n  } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {\r\n    context = {\r\n      localTerms: ['Cutting-edge', 'Innovative', 'Game-changing'],\r\n      marketingStyle: 'Bold and direct',\r\n      targetAudience: 'ambitious professionals',\r\n      localMarket: 'NYC\\'s fast-paced',\r\n      socialProof: 'successful New York businesses',\r\n      valueProposition: 'Results-driven',\r\n      competitiveAdvantage: 'New York hustle',\r\n      urgencyTrigger: 'Don\\'t let competitors get',\r\n      actionWords: 'Dominate with',\r\n      localCTA: 'Schedule Your Strategy Session',\r\n      ctaUrgency: 'ASAP'\r\n    };\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\n/**\r\n * Extract business intelligence from brand profile for personalized marketing\r\n * Analyzes actual business data to create relevant marketing copy\r\n */\r\nfunction extractBusinessIntelligence(profile: BrandProfile) {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n  const location = profile.location || '';\r\n\r\n  // Extract primary service information\r\n  const primaryService = services[0]?.name || services[0] || businessType;\r\n  const serviceDescription = services[0]?.description || '';\r\n  const targetAudience = services[0]?.targetAudience || 'businesses';\r\n  const keyFeatures = services[0]?.keyFeatures || '';\r\n  const competitiveAdvantages = services[0]?.competitiveAdvantages || '';\r\n\r\n  // Analyze description for key terms\r\n  const descriptionWords = description.toLowerCase().split(/\\s+/);\r\n  const strengthWords = extractStrengthWords(description, businessType);\r\n  const valueWords = extractValueWords(description, keyFeatures);\r\n  const benefitWords = extractBenefitWords(description, competitiveAdvantages);\r\n\r\n  // Extract competitive advantages\r\n  const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);\r\n  const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);\r\n  const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);\r\n\r\n  // Extract target audience specifics\r\n  const actualTargetAudience = extractTargetAudience(targetAudience, businessType);\r\n\r\n  // Generate contextual elements\r\n  const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);\r\n  const mainValue = extractMainValue(description, keyFeatures);\r\n  const offerType = generateOfferType(businessType, services);\r\n  const consultationType = generateConsultationType(businessType);\r\n  const journeyType = generateJourneyType(businessType, primaryService);\r\n  const urgencyTrigger = generateUrgencyTrigger(businessType, location);\r\n\r\n  // Extract outcome words from business context\r\n  const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);\r\n  const differentiators = extractDifferentiators(competitiveAdvantages, businessType);\r\n\r\n  return {\r\n    primaryService,\r\n    strengthWords,\r\n    valueWords,\r\n    benefitWords,\r\n    realCompetitiveAdvantage,\r\n    uniqueFeature,\r\n    realDifferentiator,\r\n    actualTargetAudience,\r\n    keyBenefit,\r\n    mainValue,\r\n    offerType,\r\n    consultationType,\r\n    journeyType,\r\n    urgencyTrigger,\r\n    outcomeWords,\r\n    differentiators\r\n  };\r\n}\r\n\r\n/**\r\n * Extract strength words from business description and type\r\n */\r\nfunction extractStrengthWords(description: string, businessType: string): string[] {\r\n  const strengthKeywords = ['leading', 'premium', 'expert', 'professional', 'trusted', 'innovative', 'cutting-edge', 'award-winning', 'certified', 'proven', 'reliable', 'secure', 'fast', 'efficient', 'quality', 'excellence', 'superior', 'advanced', 'specialized'];\r\n  const found = strengthKeywords.filter(word => description.toLowerCase().includes(word));\r\n\r\n  // Add business type specific strengths\r\n  const typeStrengths = getBusinessTypeStrengths(businessType);\r\n\r\n  return found.length > 0 ? found : typeStrengths;\r\n}\r\n\r\n/**\r\n * Extract value words from description and features\r\n */\r\nfunction extractValueWords(description: string, keyFeatures: string): string[] {\r\n  const valueKeywords = ['value', 'results', 'success', 'growth', 'efficiency', 'savings', 'profit', 'revenue', 'performance', 'productivity', 'quality', 'excellence', 'innovation', 'solutions', 'benefits'];\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n  const found = valueKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Excellence', 'Results', 'Success'];\r\n}\r\n\r\n/**\r\n * Extract benefit words from description and competitive advantages\r\n */\r\nfunction extractBenefitWords(description: string, competitiveAdvantages: string): string[] {\r\n  const benefitKeywords = ['success', 'growth', 'efficiency', 'savings', 'results', 'performance', 'quality', 'reliability', 'security', 'speed', 'convenience', 'expertise', 'support', 'innovation', 'excellence'];\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const found = benefitKeywords.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['Success', 'Quality', 'Results'];\r\n}\r\n\r\n/**\r\n * Extract competitive advantage from actual business data\r\n */\r\nfunction extractCompetitiveAdvantage(competitiveAdvantages: string, businessType: string): string {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    // Extract first meaningful advantage\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim());\r\n    return advantages[0] || getDefaultAdvantage(businessType);\r\n  }\r\n  return getDefaultAdvantage(businessType);\r\n}\r\n\r\n/**\r\n * Extract unique feature from key features\r\n */\r\nfunction extractUniqueFeature(keyFeatures: string, businessType: string): string {\r\n  if (keyFeatures && keyFeatures.length > 10) {\r\n    const features = keyFeatures.split(',').map(s => s.trim());\r\n    return features[0] || getDefaultFeature(businessType);\r\n  }\r\n  return getDefaultFeature(businessType);\r\n}\r\n\r\n/**\r\n * Extract differentiator from competitive advantages and description\r\n */\r\nfunction extractDifferentiator(competitiveAdvantages: string, description: string): string {\r\n  const text = `${competitiveAdvantages} ${description}`.toLowerCase();\r\n\r\n  if (text.includes('24/7') || text.includes('24-7')) return '24/7 availability';\r\n  if (text.includes('fastest') || text.includes('quick') || text.includes('speed')) return 'fastest service';\r\n  if (text.includes('secure') || text.includes('security')) return 'advanced security';\r\n  if (text.includes('expert') || text.includes('experience')) return 'expert knowledge';\r\n  if (text.includes('custom') || text.includes('tailored')) return 'customized solutions';\r\n  if (text.includes('award') || text.includes('certified')) return 'award-winning service';\r\n\r\n  return 'professional excellence';\r\n}\r\n\r\n/**\r\n * Extract target audience from service data\r\n */\r\nfunction extractTargetAudience(targetAudience: string, businessType: string): string {\r\n  if (targetAudience && targetAudience.length > 5) {\r\n    return targetAudience.split(',')[0].trim() || getDefaultAudience(businessType);\r\n  }\r\n  return getDefaultAudience(businessType);\r\n}\r\n\r\n/**\r\n * Extract key benefit from service description and advantages\r\n */\r\nfunction extractKeyBenefit(serviceDescription: string, competitiveAdvantages: string): string {\r\n  const text = `${serviceDescription} ${competitiveAdvantages}`.toLowerCase();\r\n\r\n  if (text.includes('save') || text.includes('cost')) return 'cost savings';\r\n  if (text.includes('fast') || text.includes('quick') || text.includes('speed')) return 'faster results';\r\n  if (text.includes('secure') || text.includes('safety')) return 'enhanced security';\r\n  if (text.includes('grow') || text.includes('increase')) return 'business growth';\r\n  if (text.includes('efficient') || text.includes('optimize')) return 'improved efficiency';\r\n  if (text.includes('quality') || text.includes('premium')) return 'superior quality';\r\n\r\n  return 'exceptional results';\r\n}\r\n\r\n/**\r\n * Extract main value proposition\r\n */\r\nfunction extractMainValue(description: string, keyFeatures: string): string {\r\n  const text = `${description} ${keyFeatures}`.toLowerCase();\r\n\r\n  if (text.includes('transform') || text.includes('revolutionize')) return 'transforms your business';\r\n  if (text.includes('maximize') || text.includes('optimize')) return 'maximizes your potential';\r\n  if (text.includes('accelerate') || text.includes('boost')) return 'accelerates your growth';\r\n  if (text.includes('streamline') || text.includes('simplify')) return 'streamlines your operations';\r\n  if (text.includes('enhance') || text.includes('improve')) return 'enhances your performance';\r\n\r\n  return 'delivers exceptional value';\r\n}\r\n\r\n/**\r\n * Generate business type-specific offer types\r\n */\r\nfunction generateOfferType(businessType: string, services: any[]): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Free Tasting';\r\n  if (type.includes('tech') || type.includes('software')) return 'Free Demo';\r\n  if (type.includes('health') || type.includes('medical')) return 'Free Consultation';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Free Assessment';\r\n  if (type.includes('real estate')) return 'Free Valuation';\r\n  if (type.includes('legal')) return 'Free Consultation';\r\n  if (type.includes('education') || type.includes('training')) return 'Free Trial';\r\n\r\n  return 'Free Consultation';\r\n}\r\n\r\n/**\r\n * Generate consultation types based on business\r\n */\r\nfunction generateConsultationType(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('software')) return 'Strategy Session';\r\n  if (type.includes('health') || type.includes('medical')) return 'Health Assessment';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Financial Review';\r\n  if (type.includes('real estate')) return 'Property Consultation';\r\n  if (type.includes('legal')) return 'Legal Consultation';\r\n  if (type.includes('marketing')) return 'Marketing Audit';\r\n\r\n  return 'Business Consultation';\r\n}\r\n\r\n/**\r\n * Generate journey types\r\n */\r\nfunction generateJourneyType(businessType: string, primaryService: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('tech') || type.includes('digital')) return 'Digital Transformation';\r\n  if (type.includes('health') || type.includes('wellness')) return 'Wellness Journey';\r\n  if (type.includes('finance') || type.includes('investment')) return 'Financial Success';\r\n  if (type.includes('real estate')) return 'Property Investment';\r\n  if (type.includes('education')) return 'Learning Journey';\r\n\r\n  return 'Success Journey';\r\n}\r\n\r\n/**\r\n * Generate urgency triggers based on business and location\r\n */\r\nfunction generateUrgencyTrigger(businessType: string, location: string): string {\r\n  const triggers = ['Limited Time Offer', 'Act Now', 'Don\\'t Wait', 'Book Today', 'Available Now'];\r\n\r\n  if (location.toLowerCase().includes('dubai')) return 'Exclusive Dubai Offer';\r\n  if (location.toLowerCase().includes('london')) return 'Limited London Availability';\r\n  if (location.toLowerCase().includes('new york')) return 'NYC Exclusive Deal';\r\n\r\n  return getRandomElement(triggers);\r\n}\r\n\r\n/**\r\n * Get business type specific strengths\r\n */\r\nfunction getBusinessTypeStrengths(businessType: string): string[] {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return ['Premium', 'Fresh', 'Authentic'];\r\n  if (type.includes('tech') || type.includes('software')) return ['Innovative', 'Cutting-edge', 'Advanced'];\r\n  if (type.includes('health') || type.includes('medical')) return ['Trusted', 'Professional', 'Expert'];\r\n  if (type.includes('finance') || type.includes('banking')) return ['Secure', 'Reliable', 'Trusted'];\r\n  if (type.includes('real estate')) return ['Premium', 'Exclusive', 'Professional'];\r\n  if (type.includes('legal')) return ['Expert', 'Trusted', 'Professional'];\r\n  if (type.includes('education')) return ['Expert', 'Certified', 'Professional'];\r\n\r\n  return ['Professional', 'Quality', 'Trusted'];\r\n}\r\n\r\n/**\r\n * Get default competitive advantage by business type\r\n */\r\nfunction getDefaultAdvantage(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'Fresh, authentic ingredients';\r\n  if (type.includes('tech') || type.includes('software')) return 'Cutting-edge technology';\r\n  if (type.includes('health') || type.includes('medical')) return 'Expert medical care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'Secure financial solutions';\r\n  if (type.includes('real estate')) return 'Premium property expertise';\r\n  if (type.includes('legal')) return 'Expert legal guidance';\r\n\r\n  return 'Professional excellence';\r\n}\r\n\r\n/**\r\n * Get default feature by business type\r\n */\r\nfunction getDefaultFeature(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'farm-to-table freshness';\r\n  if (type.includes('tech') || type.includes('software')) return 'advanced automation';\r\n  if (type.includes('health') || type.includes('medical')) return 'personalized care';\r\n  if (type.includes('finance') || type.includes('banking')) return 'secure transactions';\r\n  if (type.includes('real estate')) return 'market expertise';\r\n\r\n  return 'personalized service';\r\n}\r\n\r\n/**\r\n * Get default audience by business type\r\n */\r\nfunction getDefaultAudience(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food')) return 'food enthusiasts';\r\n  if (type.includes('tech') || type.includes('software')) return 'forward-thinking businesses';\r\n  if (type.includes('health') || type.includes('medical')) return 'health-conscious individuals';\r\n  if (type.includes('finance') || type.includes('banking')) return 'smart investors';\r\n  if (type.includes('real estate')) return 'property investors';\r\n\r\n  return 'discerning clients';\r\n}\r\n\r\n/**\r\n * Extract outcome words from business context\r\n */\r\nfunction extractOutcomeWords(description: string, competitiveAdvantages: string): string[] {\r\n  const text = `${description} ${competitiveAdvantages}`.toLowerCase();\r\n  const outcomes = ['success', 'growth', 'results', 'performance', 'efficiency', 'savings', 'profit', 'revenue'];\r\n  const found = outcomes.filter(word => text.includes(word));\r\n\r\n  return found.length > 0 ? found : ['success', 'results', 'growth'];\r\n}\r\n\r\n/**\r\n * Extract differentiators from competitive advantages\r\n */\r\nfunction extractDifferentiators(competitiveAdvantages: string, businessType: string): string[] {\r\n  if (competitiveAdvantages && competitiveAdvantages.length > 10) {\r\n    const advantages = competitiveAdvantages.split(',').map(s => s.trim().split(' ')[0]);\r\n    return advantages.slice(0, 3);\r\n  }\r\n\r\n  return getBusinessTypeStrengths(businessType);\r\n}\r\n\r\n/**\r\n * Get random element from array\r\n */\r\nfunction getRandomElement<T>(array: T[]): T {\r\n  return array[Math.floor(Math.random() * array.length)];\r\n}\r\n\r\n/**\r\n * Create professional design prompt with 20 years of design + marketing expertise\r\n * Combines cultural awareness, psychology, and visual design mastery\r\n */\r\nfunction createProfessionalDesignPrompt(imageText: string, platform: Platform, profile: BrandProfile, revoModel: RevoModelId): string {\r\n  const businessName = profile.businessName || 'Your Business';\r\n  const businessType = profile.businessType || 'Professional Services';\r\n  const location = profile.location || '';\r\n  const description = profile.description || '';\r\n  const services = Array.isArray(profile.services) ? profile.services : [];\r\n\r\n  // Extract business intelligence for design context\r\n  const businessIntelligence = extractBusinessIntelligence(profile);\r\n\r\n  // Get cultural context for design decisions\r\n  const culturalContext = getCulturalContext(location);\r\n\r\n  // Industry-specific design psychology\r\n  const industryDesignPsychology = getIndustryDesignPsychology(businessType);\r\n\r\n  // Platform-specific design requirements\r\n  const platformRequirements = getPlatformDesignRequirements(platform);\r\n\r\n  // Color psychology based on business type and culture\r\n  const colorPsychology = getColorPsychology(businessType, location);\r\n\r\n  // Typography psychology for conversion\r\n  const typographyStrategy = getTypographyStrategy(businessType, platform);\r\n\r\n  return `Create an exceptional, conversion-focused ${platform} design for ${businessName} that embodies 20 years of professional design and marketing expertise.\r\n\r\nLANGUAGE REQUIREMENTS:\r\n- ALL TEXT MUST BE IN ENGLISH ONLY\r\n- NO Arabic, Hindi, Chinese, or other non-English characters\r\n- NO transliterated text or mixed languages\r\n- Use clear, professional English throughout\r\n\r\nBUSINESS INTELLIGENCE & CONTEXT:\r\n- Company: ${businessName} (${businessType})\r\n- Primary Service: ${businessIntelligence.primaryService}\r\n- Location: ${location}\r\n- Target Audience: ${businessIntelligence.actualTargetAudience}\r\n- Key Differentiator: ${businessIntelligence.realDifferentiator}\r\n- Unique Value: ${businessIntelligence.mainValue}\r\n- Competitive Advantage: ${businessIntelligence.realCompetitiveAdvantage}\r\n\r\nTEXT TO INTEGRATE: \"${imageText}\"\r\n\r\nBRAND-SPECIFIC DESIGN REQUIREMENTS:\r\n- Must communicate: ${businessIntelligence.realCompetitiveAdvantage}\r\n- Must highlight: ${businessIntelligence.uniqueFeature}\r\n- Must appeal to: ${businessIntelligence.actualTargetAudience}\r\n- Must convey: ${businessIntelligence.keyBenefit}\r\n\r\nDESIGN PSYCHOLOGY & STRATEGY:\r\n${industryDesignPsychology}\r\n\r\nVISUAL HIERARCHY & COMPOSITION:\r\n- Apply the golden ratio and rule of thirds for optimal visual flow\r\n- Create clear focal points that guide the eye to key conversion elements\r\n- Use strategic white space to enhance readability and premium feel\r\n- Implement Z-pattern or F-pattern layout for maximum engagement\r\n\r\nCOLOR STRATEGY:\r\n${colorPsychology}\r\n\r\nTYPOGRAPHY MASTERY:\r\n${typographyStrategy}\r\n\r\nCULTURAL DESIGN ADAPTATION:\r\n- ${culturalContext.localMarket} aesthetic preferences\r\n- ${culturalContext.targetAudience} visual expectations\r\n- Regional design trends and cultural symbols\r\n- Local color associations and meanings\r\n\r\nCONVERSION OPTIMIZATION:\r\n- Design elements that create urgency and desire\r\n- Visual cues that guide toward call-to-action\r\n- Trust signals through professional presentation\r\n- Emotional triggers through strategic imagery and layout\r\n\r\nPLATFORM OPTIMIZATION:\r\n${platformRequirements}\r\n\r\nTECHNICAL EXCELLENCE:\r\n- Aspect Ratio: 1:1 (perfect square)\r\n- Resolution: Ultra-high quality, print-ready standards\r\n- Text Clarity: Crystal clear, perfectly readable at all sizes\r\n- Brand Consistency: Align with professional brand standards\r\n- Mobile Optimization: Ensure perfect display on all devices\r\n\r\nFINAL QUALITY STANDARDS:\r\nThis design must look like it was created by a top-tier creative agency specifically for ${businessName}. Every element should reflect their unique value proposition: \"${businessIntelligence.realCompetitiveAdvantage}\". The design should immediately communicate their expertise in ${businessIntelligence.primaryService} while appealing directly to ${businessIntelligence.actualTargetAudience}.\r\n\r\nThe final result should be a sophisticated, professional design that drives ${businessIntelligence.actualTargetAudience} to choose ${businessName} over competitors and take immediate action.\r\n\r\nMake it absolutely irresistible for ${businessIntelligence.actualTargetAudience} and perfectly aligned with ${businessName}'s brand identity.`;\r\n}\r\n\r\n/**\r\n * Get industry-specific design psychology\r\n */\r\nfunction getIndustryDesignPsychology(businessType: string): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {\r\n    return `- Use warm, appetizing colors that stimulate hunger and comfort\r\n- Incorporate food photography principles with rich textures\r\n- Create cozy, inviting atmosphere through design elements\r\n- Focus on sensory appeal and mouth-watering visual presentation`;\r\n  }\r\n\r\n  if (type.includes('tech') || type.includes('software') || type.includes('digital')) {\r\n    return `- Employ clean, minimalist design with high-tech aesthetics\r\n- Use gradients and modern geometric shapes\r\n- Incorporate subtle tech-inspired elements and icons\r\n- Focus on innovation, efficiency, and cutting-edge appeal`;\r\n  }\r\n\r\n  if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {\r\n    return `- Use calming, trustworthy colors that convey safety and care\r\n- Incorporate clean, sterile design elements\r\n- Focus on professionalism, expertise, and patient comfort\r\n- Use imagery that suggests health, vitality, and well-being`;\r\n  }\r\n\r\n  if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {\r\n    return `- Employ sophisticated, conservative design elements\r\n- Use colors that convey stability, trust, and prosperity\r\n- Incorporate subtle luxury elements and professional imagery\r\n- Focus on security, growth, and financial success`;\r\n  }\r\n\r\n  if (type.includes('real estate') || type.includes('property')) {\r\n    return `- Use aspirational imagery and luxury design elements\r\n- Incorporate architectural lines and premium materials\r\n- Focus on lifestyle, investment, and dream fulfillment\r\n- Use colors that suggest stability, growth, and success`;\r\n  }\r\n\r\n  // Default professional services\r\n  return `- Use professional, trustworthy design elements\r\n- Incorporate subtle premium touches and quality indicators\r\n- Focus on expertise, reliability, and professional excellence\r\n- Use colors and imagery that convey competence and success`;\r\n}\r\n\r\n/**\r\n * Get platform-specific design requirements\r\n */\r\nfunction getPlatformDesignRequirements(platform: Platform): string {\r\n  switch (platform) {\r\n    case 'Instagram':\r\n      return `- Optimize for Instagram's visual-first environment\r\n- Use bold, eye-catching elements that stand out in feeds\r\n- Incorporate Instagram-native design trends and aesthetics\r\n- Ensure design works perfectly in both feed and story formats`;\r\n\r\n    case 'Facebook':\r\n      return `- Design for Facebook's diverse, multi-generational audience\r\n- Use clear, readable elements that work across age groups\r\n- Incorporate social proof and community-focused elements\r\n- Ensure design is engaging but not overwhelming`;\r\n\r\n    case 'LinkedIn':\r\n      return `- Employ professional, business-focused design elements\r\n- Use conservative colors and sophisticated typography\r\n- Incorporate industry-specific imagery and professional symbols\r\n- Focus on credibility, expertise, and business value`;\r\n\r\n    case 'Twitter':\r\n      return `- Create concise, impactful design that communicates quickly\r\n- Use bold typography and clear visual hierarchy\r\n- Incorporate trending design elements and current aesthetics\r\n- Ensure design is optimized for rapid consumption`;\r\n\r\n    default:\r\n      return `- Create versatile design that works across multiple platforms\r\n- Use universal design principles and broad appeal\r\n- Ensure scalability and readability across different contexts\r\n- Focus on timeless, professional aesthetics`;\r\n  }\r\n}\r\n\r\n/**\r\n * Get color psychology based on business type and location\r\n */\r\nfunction getColorPsychology(businessType: string, location: string): string {\r\n  const type = businessType.toLowerCase();\r\n  const loc = location.toLowerCase();\r\n\r\n  let baseColors = '';\r\n  let culturalColors = '';\r\n\r\n  // Business type color psychology\r\n  if (type.includes('restaurant') || type.includes('food')) {\r\n    baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    baseColors = 'modern blues, teals, and purples to convey innovation and trust';\r\n  } else if (type.includes('health') || type.includes('medical')) {\r\n    baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';\r\n  } else if (type.includes('finance') || type.includes('banking')) {\r\n    baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';\r\n  } else {\r\n    baseColors = 'professional blues, grays, and accent colors to convey trust and competence';\r\n  }\r\n\r\n  // Cultural color adaptations\r\n  if (loc.includes('dubai') || loc.includes('uae')) {\r\n    culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\\'s premium market expectations';\r\n  } else if (loc.includes('london') || loc.includes('uk')) {\r\n    culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';\r\n  } else if (loc.includes('new york') || loc.includes('nyc')) {\r\n    culturalColors = 'Employ bold, confident colors that match New York\\'s dynamic business environment';\r\n  } else {\r\n    culturalColors = 'Use universally appealing professional color combinations';\r\n  }\r\n\r\n  return `- Primary Strategy: ${baseColors}\r\n- Cultural Adaptation: ${culturalColors}\r\n- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors\r\n- Contrast Optimization: Ensure maximum readability and visual impact`;\r\n}\r\n\r\n/**\r\n * Get typography strategy for conversion\r\n */\r\nfunction getTypographyStrategy(businessType: string, platform: Platform): string {\r\n  const type = businessType.toLowerCase();\r\n\r\n  let fontStrategy = '';\r\n  let hierarchyStrategy = '';\r\n\r\n  if (type.includes('luxury') || type.includes('premium')) {\r\n    fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';\r\n  } else if (type.includes('tech') || type.includes('digital')) {\r\n    fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';\r\n  } else if (type.includes('creative') || type.includes('design')) {\r\n    fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';\r\n  } else {\r\n    fontStrategy = 'Professional, highly readable fonts that convey trust and competence';\r\n  }\r\n\r\n  hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact\r\n- Secondary Text: Clear, readable subheadings that support the main message\r\n- Call-to-Action: Distinctive typography that stands out and drives action\r\n- Supporting Text: Clean, professional fonts for additional information`;\r\n\r\n  return `- Font Selection: ${fontStrategy}\r\n- Visual Hierarchy: ${hierarchyStrategy}\r\n- Readability: Optimized for ${platform} viewing conditions and mobile devices\r\n- Conversion Focus: Typography choices designed to guide the eye and encourage action`;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAyoBsB,qCAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-brand-profiles.ts"], "sourcesContent": ["// Hook for managing brand profiles with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { brandProfileFirebaseService } from '@/lib/firebase/services/brand-profile-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\nexport interface BrandProfilesState {\r\n  profiles: CompleteBrandProfile[];\r\n  currentProfile: CompleteBrandProfile | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useBrandProfiles() {\r\n  const userId = useUserId();\r\n  const [state, setState] = useState<BrandProfilesState>({\r\n    profiles: [],\r\n    currentProfile: null,\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load brand profiles\r\n  const loadProfiles = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, profiles: [], currentProfile: null }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n\r\n      // Try to load from Firestore, fallback to localStorage\r\n      let profiles: CompleteBrandProfile[] = [];\r\n      try {\r\n        profiles = await brandProfileFirebaseService.getUserBrandProfiles(userId);\r\n      } catch (firebaseError) {\r\n        console.log('🔄 Firebase unavailable, using localStorage fallback');\r\n        // Fallback to localStorage\r\n        const stored = localStorage.getItem('completeBrandProfile');\r\n        if (stored) {\r\n          const profile = JSON.parse(stored);\r\n          profiles = [profile];\r\n        }\r\n      }\r\n\r\n      const currentProfile = profiles.length > 0 ? profiles[0] : null;\r\n\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles,\r\n        currentProfile,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load profiles',\r\n      }));\r\n    }\r\n  }, [userId]);\r\n\r\n  // Save brand profile\r\n  const saveProfile = useCallback(async (profile: CompleteBrandProfile): Promise<string> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to save profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      const profileId = await brandProfileFirebaseService.saveBrandProfile(profile, userId);\r\n\r\n      // Reload profiles to get the updated list\r\n      await loadProfiles();\r\n\r\n      setState(prev => ({ ...prev, saving: false }));\r\n      return profileId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, loadProfiles]);\r\n\r\n  // Update brand profile\r\n  const updateProfile = useCallback(async (\r\n    profileId: string,\r\n    updates: Partial<CompleteBrandProfile>\r\n  ): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to update profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n\r\n      await brandProfileFirebaseService.updateBrandProfile(profileId, updates);\r\n\r\n      // Update local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.map(p =>\r\n          p.id === profileId ? { ...p, ...updates } : p\r\n        ),\r\n        currentProfile: prev.currentProfile?.id === profileId\r\n          ? { ...prev.currentProfile, ...updates }\r\n          : prev.currentProfile,\r\n        saving: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to update profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Delete brand profile\r\n  const deleteProfile = useCallback(async (profileId: string): Promise<void> => {\r\n    if (!userId) {\r\n      throw new Error('User must be authenticated to delete profile');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, error: null }));\r\n\r\n      await brandProfileFirebaseService.delete(profileId);\r\n\r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        profiles: prev.profiles.filter(p => p.id !== profileId),\r\n        currentProfile: prev.currentProfile?.id === profileId ? null : prev.currentProfile,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        error: error instanceof Error ? error.message : 'Failed to delete profile',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId]);\r\n\r\n  // Set current profile\r\n  const setCurrentProfile = useCallback((profile: CompleteBrandProfile | null) => {\r\n    console.log('🎯 setCurrentProfile called with:', profile?.businessName || 'null');\r\n    setState(prev => {\r\n      console.log('📊 Previous current profile:', prev.currentProfile?.businessName || 'none');\r\n      return { ...prev, currentProfile: profile };\r\n    });\r\n  }, []);\r\n\r\n  // Get profile by ID\r\n  const getProfileById = useCallback(async (profileId: string): Promise<CompleteBrandProfile | null> => {\r\n    try {\r\n      return await brandProfileFirebaseService.getBrandProfileById(profileId);\r\n    } catch (error) {\r\n      console.error('Failed to get profile by ID:', error);\r\n      return null;\r\n    }\r\n  }, []);\r\n\r\n  // Load profiles when userId changes\r\n  useEffect(() => {\r\n    loadProfiles();\r\n  }, [loadProfiles]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = brandProfileFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (profiles) => {\r\n        console.log('🔄 Real-time profiles update received:', profiles.length, 'profiles');\r\n        setState(prev => {\r\n          // Preserve the current profile if it still exists in the updated profiles\r\n          let preservedCurrentProfile = prev.currentProfile;\r\n\r\n          if (prev.currentProfile) {\r\n            // Check if current profile still exists in the updated list\r\n            const stillExists = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n            if (!stillExists) {\r\n              console.log('⚠️ Current profile no longer exists, clearing selection');\r\n              preservedCurrentProfile = null;\r\n            } else {\r\n              // Update with the latest version of the current profile\r\n              const updatedProfile = profiles.find(p => p.id === (prev.currentProfile as any)?.id);\r\n              if (updatedProfile) {\r\n                console.log('✅ Current profile updated with latest data:', updatedProfile.businessName);\r\n                preservedCurrentProfile = updatedProfile;\r\n              }\r\n            }\r\n          }\r\n\r\n          // Only auto-select first profile if there's no current profile at all AND this is the initial load\r\n          const finalCurrentProfile = preservedCurrentProfile ||\r\n            (!prev.currentProfile && profiles.length > 0 ? profiles[0] : null);\r\n\r\n          if (finalCurrentProfile && !prev.currentProfile) {\r\n            console.log('🎯 Auto-selecting first profile on initial load:', finalCurrentProfile.businessName);\r\n          }\r\n\r\n          return {\r\n            ...prev,\r\n            profiles,\r\n            currentProfile: finalCurrentProfile,\r\n          };\r\n        });\r\n      },\r\n      { orderBy: 'updatedAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId]);\r\n\r\n  return {\r\n    ...state,\r\n    saveProfile,\r\n    updateProfile,\r\n    deleteProfile,\r\n    setCurrentProfile,\r\n    getProfileById,\r\n    reload: loadProfiles,\r\n  };\r\n}\r\n\r\n// Hook for getting the current/latest brand profile\r\nexport function useCurrentBrandProfile() {\r\n  const { currentProfile, loading, error } = useBrandProfiles();\r\n\r\n  return {\r\n    profile: currentProfile,\r\n    loading,\r\n    error,\r\n  };\r\n}\r\n\r\n// Hook for checking if user has a complete brand profile\r\nexport function useHasCompleteBrandProfile(): boolean {\r\n  const { currentProfile, loading } = useBrandProfiles();\r\n\r\n  if (loading || !currentProfile) return false;\r\n\r\n  // Check if profile has required fields\r\n  const requiredFields = [\r\n    'businessName',\r\n    'businessType',\r\n    'location',\r\n    'description',\r\n    'services',\r\n  ];\r\n\r\n  return requiredFields.every(field => {\r\n    const value = currentProfile[field as keyof CompleteBrandProfile];\r\n    return value && (\r\n      typeof value === 'string' ? value.trim().length > 0 :\r\n        Array.isArray(value) ? value.length > 0 :\r\n          true\r\n    );\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAClD;AACA;AACA;;;;;AAWO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,UAAU,EAAE;QACZ,gBAAgB;QAChB,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAC/B,IAAI,CAAC,QAAQ;gBACX;kEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAO,UAAU,EAAE;4BAAE,gBAAgB;wBAAK,CAAC;;gBACjF;YACF;YAEA,IAAI;gBACF;kEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAM,OAAO;wBAAK,CAAC;;gBAEzD,uDAAuD;gBACvD,IAAI,WAAmC,EAAE;gBACzC,IAAI;oBACF,WAAW,MAAM,oKAAA,CAAA,8BAA2B,CAAC,oBAAoB,CAAC;gBACpE,EAAE,OAAO,eAAe;oBACtB,QAAQ,GAAG,CAAC;oBACZ,2BAA2B;oBAC3B,MAAM,SAAS,aAAa,OAAO,CAAC;oBACpC,IAAI,QAAQ;wBACV,MAAM,UAAU,KAAK,KAAK,CAAC;wBAC3B,WAAW;4BAAC;yBAAQ;oBACtB;gBACF;gBAEA,MAAM,iBAAiB,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;gBAE3D;kEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP;4BACA;4BACA,SAAS;wBACX,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;kEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;YACH;QACF;qDAAG;QAAC;KAAO;IAEX,qBAAqB;IACrB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACrC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;iEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,YAAY,MAAM,oKAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAAC,SAAS;gBAE9E,0CAA0C;gBAC1C,MAAM;gBAEN;iEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAAM,CAAC;;gBAC5C,OAAO;YACT,EAAE,OAAO,OAAO;gBACd;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;oDAAG;QAAC;QAAQ;KAAa;IAEzB,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAChC,WACA;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;mEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,oKAAA,CAAA,8BAA2B,CAAC,kBAAkB,CAAC,WAAW;gBAEhE,oCAAoC;gBACpC;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU,KAAK,QAAQ,CAAC,GAAG;+EAAC,CAAA,IAC1B,EAAE,EAAE,KAAK,YAAY;wCAAE,GAAG,CAAC;wCAAE,GAAG,OAAO;oCAAC,IAAI;;4BAE9C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YACxC;gCAAE,GAAG,KAAK,cAAc;gCAAE,GAAG,OAAO;4BAAC,IACrC,KAAK,cAAc;4BACvB,QAAQ;wBACV,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;sDAAG;QAAC;KAAO;IAEX,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YACvC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;mEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,OAAO;wBAAK,CAAC;;gBAE1C,MAAM,oKAAA,CAAA,8BAA2B,CAAC,MAAM,CAAC;gBAEzC,qBAAqB;gBACrB;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU,KAAK,QAAQ,CAAC,MAAM;+EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;4BAC7C,gBAAgB,KAAK,cAAc,EAAE,OAAO,YAAY,OAAO,KAAK,cAAc;wBACpF,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;mEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;sDAAG;QAAC;KAAO;IAEX,sBAAsB;IACtB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACrC,QAAQ,GAAG,CAAC,qCAAqC,SAAS,gBAAgB;YAC1E;mEAAS,CAAA;oBACP,QAAQ,GAAG,CAAC,gCAAgC,KAAK,cAAc,EAAE,gBAAgB;oBACjF,OAAO;wBAAE,GAAG,IAAI;wBAAE,gBAAgB;oBAAQ;gBAC5C;;QACF;0DAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO;YACxC,IAAI;gBACF,OAAO,MAAM,oKAAA,CAAA,8BAA2B,CAAC,mBAAmB,CAAC;YAC/D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO;YACT;QACF;uDAAG,EAAE;IAEL,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAa;IAEjB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,MAAM,cAAc,oKAAA,CAAA,8BAA2B,CAAC,qBAAqB,CACnE;0DACA,CAAC;oBACC,QAAQ,GAAG,CAAC,0CAA0C,SAAS,MAAM,EAAE;oBACvE;kEAAS,CAAA;4BACP,0EAA0E;4BAC1E,IAAI,0BAA0B,KAAK,cAAc;4BAEjD,IAAI,KAAK,cAAc,EAAE;gCACvB,4DAA4D;gCAC5D,MAAM,cAAc,SAAS,IAAI;0FAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;;gCAC9E,IAAI,CAAC,aAAa;oCAChB,QAAQ,GAAG,CAAC;oCACZ,0BAA0B;gCAC5B,OAAO;oCACL,wDAAwD;oCACxD,MAAM,iBAAiB,SAAS,IAAI;iGAAC,CAAA,IAAK,EAAE,EAAE,KAAM,KAAK,cAAc,EAAU;;oCACjF,IAAI,gBAAgB;wCAClB,QAAQ,GAAG,CAAC,+CAA+C,eAAe,YAAY;wCACtF,0BAA0B;oCAC5B;gCACF;4BACF;4BAEA,mGAAmG;4BACnG,MAAM,sBAAsB,2BAC1B,CAAC,CAAC,KAAK,cAAc,IAAI,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG,IAAI;4BAEnE,IAAI,uBAAuB,CAAC,KAAK,cAAc,EAAE;gCAC/C,QAAQ,GAAG,CAAC,oDAAoD,oBAAoB,YAAY;4BAClG;4BAEA,OAAO;gCACL,GAAG,IAAI;gCACP;gCACA,gBAAgB;4BAClB;wBACF;;gBACF;yDACA;gBAAE,SAAS;gBAAa,gBAAgB;YAAO;YAGjD,OAAO;QACT;qCAAG;QAAC;KAAO;IAEX,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;GA5NgB;;QACC,0IAAA,CAAA,YAAS;;;AA8NnB,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG;IAE3C,OAAO;QACL,SAAS;QACT;QACA;IACF;AACF;IARgB;;QAC6B;;;AAUtC,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG;IAEpC,IAAI,WAAW,CAAC,gBAAgB,OAAO;IAEvC,uCAAuC;IACvC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,eAAe,KAAK,CAAC,CAAA;QAC1B,MAAM,QAAQ,cAAc,CAAC,MAAoC;QACjE,OAAO,SAAS,CACd,OAAO,UAAU,WAAW,MAAM,IAAI,GAAG,MAAM,GAAG,IAChD,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG,IACpC,IACN;IACF;AACF;IAtBgB;;QACsB", "debugId": null}}, {"offset": {"line": 1926, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/hooks/use-generated-posts.ts"], "sourcesContent": ["// Hook for managing generated posts with Firestore\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { generatedPostFirebaseService } from '@/lib/firebase/services/generated-post-service';\r\nimport { useUserId } from './use-firebase-auth';\r\nimport { useCurrentBrandProfile } from './use-brand-profiles';\r\nimport type { GeneratedPost, Platform } from '@/lib/types';\r\n\r\nexport interface GeneratedPostsState {\r\n  posts: GeneratedPost[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  saving: boolean;\r\n}\r\n\r\nexport function useGeneratedPosts(limit: number = 10) {\r\n  const userId = useUserId();\r\n  const { profile: currentProfile } = useCurrentBrandProfile();\r\n  const [state, setState] = useState<GeneratedPostsState>({\r\n    posts: [],\r\n    loading: true,\r\n    error: null,\r\n    saving: false,\r\n  });\r\n\r\n  // Load generated posts\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId) {\r\n      setState(prev => ({ ...prev, loading: false, posts: [] }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      \r\n      const posts = await generatedPostFirebaseService.getUserGeneratedPosts(userId, { limit });\r\n      \r\n      setState(prev => ({\r\n        ...prev,\r\n        posts,\r\n        loading: false,\r\n      }));\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to load posts',\r\n      }));\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Save generated post\r\n  const savePost = useCallback(async (post: GeneratedPost): Promise<string> => {\r\n    if (!userId || !currentProfile) {\r\n      throw new Error('User must be authenticated and have a brand profile to save posts');\r\n    }\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, saving: true, error: null }));\r\n      \r\n      const postId = await generatedPostFirebaseService.saveGeneratedPost(post, userId, currentProfile.id);\r\n      \r\n      // Add to local state optimistically\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: [{ ...post, id: postId }, ...prev.posts].slice(0, limit),\r\n        saving: false,\r\n      }));\r\n      \r\n      return postId;\r\n    } catch (error) {\r\n      setState(prev => ({\r\n        ...prev,\r\n        saving: false,\r\n        error: error instanceof Error ? error.message : 'Failed to save post',\r\n      }));\r\n      throw error;\r\n    }\r\n  }, [userId, currentProfile, limit]);\r\n\r\n  // Update post analytics\r\n  const updatePostAnalytics = useCallback(async (\r\n    postId: string,\r\n    analytics: {\r\n      views?: number;\r\n      likes?: number;\r\n      shares?: number;\r\n      comments?: number;\r\n      qualityScore?: number;\r\n      engagementPrediction?: number;\r\n      brandAlignmentScore?: number;\r\n    }\r\n  ): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.updatePostAnalytics(postId, analytics);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, ...analytics }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to update post analytics:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Update post status\r\n  const updatePostStatus = useCallback(async (\r\n    postId: string,\r\n    status: 'generated' | 'edited' | 'posted'\r\n  ): Promise<void> => {\r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      const publishedAt = status === 'posted' ? new Date() : undefined;\r\n      \r\n      await generatedPostFirebaseService.updatePostStatus(postId, firestoreStatus, undefined, publishedAt);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.map(post => \r\n          post.id === postId \r\n            ? { ...post, status }\r\n            : post\r\n        ),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to update post status:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Delete post\r\n  const deletePost = useCallback(async (postId: string): Promise<void> => {\r\n    try {\r\n      await generatedPostFirebaseService.delete(postId);\r\n      \r\n      // Update local state\r\n      setState(prev => ({\r\n        ...prev,\r\n        posts: prev.posts.filter(post => post.id !== postId),\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to delete post:', error);\r\n      throw error;\r\n    }\r\n  }, []);\r\n\r\n  // Get posts by platform\r\n  const getPostsByPlatform = useCallback(async (platform: Platform): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      return await generatedPostFirebaseService.getUserGeneratedPosts(userId, { platform, limit });\r\n    } catch (error) {\r\n      console.error('Failed to get posts by platform:', error);\r\n      return [];\r\n    }\r\n  }, [userId, limit]);\r\n\r\n  // Get posts by status\r\n  const getPostsByStatus = useCallback(async (status: 'generated' | 'edited' | 'posted'): Promise<GeneratedPost[]> => {\r\n    if (!userId) return [];\r\n    \r\n    try {\r\n      const firestoreStatus = status === 'posted' ? 'published' : 'draft';\r\n      return await generatedPostFirebaseService.getPostsByStatus(userId, firestoreStatus);\r\n    } catch (error) {\r\n      console.error('Failed to get posts by status:', error);\r\n      return [];\r\n    }\r\n  }, [userId]);\r\n\r\n  // Load posts when dependencies change\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  // Set up real-time listener\r\n  useEffect(() => {\r\n    if (!userId) return;\r\n\r\n    const unsubscribe = generatedPostFirebaseService.onUserDocumentsChange(\r\n      userId,\r\n      (posts) => {\r\n        setState(prev => ({\r\n          ...prev,\r\n          posts: posts.slice(0, limit),\r\n        }));\r\n      },\r\n      { limit, orderBy: 'createdAt', orderDirection: 'desc' }\r\n    );\r\n\r\n    return unsubscribe;\r\n  }, [userId, limit]);\r\n\r\n  return {\r\n    ...state,\r\n    savePost,\r\n    updatePostAnalytics,\r\n    updatePostStatus,\r\n    deletePost,\r\n    getPostsByPlatform,\r\n    getPostsByStatus,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for getting posts for a specific brand profile\r\nexport function useGeneratedPostsForBrand(brandProfileId: string, limit: number = 10) {\r\n  const userId = useUserId();\r\n  const [posts, setPosts] = useState<GeneratedPost[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const loadPosts = useCallback(async () => {\r\n    if (!userId || !brandProfileId) {\r\n      setPosts([]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const brandPosts = await generatedPostFirebaseService.getRecentPostsForBrand(\r\n        userId, \r\n        brandProfileId, \r\n        limit\r\n      );\r\n      \r\n      setPosts(brandPosts);\r\n      setLoading(false);\r\n    } catch (err) {\r\n      setError(err instanceof Error ? err.message : 'Failed to load posts');\r\n      setLoading(false);\r\n    }\r\n  }, [userId, brandProfileId, limit]);\r\n\r\n  useEffect(() => {\r\n    loadPosts();\r\n  }, [loadPosts]);\r\n\r\n  return {\r\n    posts,\r\n    loading,\r\n    error,\r\n    reload: loadPosts,\r\n  };\r\n}\r\n\r\n// Hook for post statistics\r\nexport function usePostStatistics() {\r\n  const { posts } = useGeneratedPosts(100); // Get more posts for statistics\r\n  \r\n  const statistics = {\r\n    total: posts.length,\r\n    byPlatform: posts.reduce((acc, post) => {\r\n      acc[post.platform] = (acc[post.platform] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<Platform, number>),\r\n    byStatus: posts.reduce((acc, post) => {\r\n      acc[post.status] = (acc[post.status] || 0) + 1;\r\n      return acc;\r\n    }, {} as Record<string, number>),\r\n    averageQuality: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.qualityScore || 0), 0) / posts.length \r\n      : 0,\r\n    averageEngagement: posts.length > 0 \r\n      ? posts.reduce((sum, post) => sum + (post.engagementPrediction || 0), 0) / posts.length \r\n      : 0,\r\n  };\r\n\r\n  return statistics;\r\n}\r\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;;AACnD;AACA;AACA;AACA;;;;;;AAUO,SAAS,kBAAkB,QAAgB,EAAE;;IAClD,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,yBAAsB,AAAD;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,OAAO,EAAE;QACT,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,uBAAuB;IACvB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC5B,IAAI,CAAC,QAAQ;gBACX;gEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAO,OAAO,EAAE;wBAAC,CAAC;;gBACxD;YACF;YAEA,IAAI;gBACF;gEAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,SAAS;4BAAM,OAAO;wBAAK,CAAC;;gBAEzD,MAAM,QAAQ,MAAM,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;oBAAE;gBAAM;gBAEvF;gEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP;4BACA,SAAS;wBACX,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd;gEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,SAAS;4BACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;YACH;QACF;mDAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YAClC,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI;gBACF;+DAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;4BAAM,OAAO;wBAAK,CAAC;;gBAExD,MAAM,SAAS,MAAM,qKAAA,CAAA,+BAA4B,CAAC,iBAAiB,CAAC,MAAM,QAAQ,eAAe,EAAE;gBAEnG,oCAAoC;gBACpC;+DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO;gCAAC;oCAAE,GAAG,IAAI;oCAAE,IAAI;gCAAO;mCAAM,KAAK,KAAK;6BAAC,CAAC,KAAK,CAAC,GAAG;4BACzD,QAAQ;wBACV,CAAC;;gBAED,OAAO;YACT,EAAE,OAAO,OAAO;gBACd;+DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBACD,MAAM;YACR;QACF;kDAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OACtC,QACA;YAUA,IAAI;gBACF,MAAM,qKAAA,CAAA,+BAA4B,CAAC,mBAAmB,CAAC,QAAQ;gBAE/D,qBAAqB;gBACrB;0EAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,GAAG;sFAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wCAAE,GAAG,IAAI;wCAAE,GAAG,SAAS;oCAAC,IACxB;;wBAER,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;YACR;QACF;6DAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OACnC,QACA;YAEA,IAAI;gBACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;gBAC5D,MAAM,cAAc,WAAW,WAAW,IAAI,SAAS;gBAEvD,MAAM,qKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ,iBAAiB,WAAW;gBAExF,qBAAqB;gBACrB;uEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,GAAG;mFAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SACR;wCAAE,GAAG,IAAI;wCAAE;oCAAO,IAClB;;wBAER,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM;YACR;QACF;0DAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO;YACpC,IAAI;gBACF,MAAM,qKAAA,CAAA,+BAA4B,CAAC,MAAM,CAAC;gBAE1C,qBAAqB;gBACrB;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,OAAO,KAAK,KAAK,CAAC,MAAM;6EAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;wBAC/C,CAAC;;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM;YACR;QACF;oDAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,OAAO;YAC5C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,IAAI;gBACF,OAAO,MAAM,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC,QAAQ;oBAAE;oBAAU;gBAAM;YAC5F,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,OAAO,EAAE;YACX;QACF;4DAAG;QAAC;QAAQ;KAAM;IAElB,sBAAsB;IACtB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,OAAO;YAC1C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAEtB,IAAI;gBACF,MAAM,kBAAkB,WAAW,WAAW,cAAc;gBAC5D,OAAO,MAAM,qKAAA,CAAA,+BAA4B,CAAC,gBAAgB,CAAC,QAAQ;YACrE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO,EAAE;YACX;QACF;0DAAG;QAAC;KAAO;IAEX,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAU;IAEd,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,QAAQ;YAEb,MAAM,cAAc,qKAAA,CAAA,+BAA4B,CAAC,qBAAqB,CACpE;2DACA,CAAC;oBACC;mEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,OAAO,MAAM,KAAK,CAAC,GAAG;4BACxB,CAAC;;gBACH;0DACA;gBAAE;gBAAO,SAAS;gBAAa,gBAAgB;YAAO;YAGxD,OAAO;QACT;sCAAG;QAAC;QAAQ;KAAM;IAElB,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;QACA;QACA;QACA,QAAQ;IACV;AACF;GApMgB;;QACC,0IAAA,CAAA,YAAS;QACY,2IAAA,CAAA,yBAAsB;;;AAqMrD,SAAS,0BAA0B,cAAsB,EAAE,QAAgB,EAAE;;IAClF,MAAM,SAAS,CAAA,GAAA,0IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAC5B,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAC9B,SAAS,EAAE;gBACX,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,aAAa,MAAM,qKAAA,CAAA,+BAA4B,CAAC,sBAAsB,CAC1E,QACA,gBACA;gBAGF,SAAS;gBACT,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC9C,WAAW;YACb;QACF;2DAAG;QAAC;QAAQ;QAAgB;KAAM;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR;QACF;8CAAG;QAAC;KAAU;IAEd,OAAO;QACL;QACA;QACA;QACA,QAAQ;IACV;AACF;IAzCgB;;QACC,0IAAA,CAAA,YAAS;;;AA2CnB,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,kBAAkB,MAAM,gCAAgC;IAE1E,MAAM,aAAa;QACjB,OAAO,MAAM,MAAM;QACnB,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK;YAC7B,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;YACjD,OAAO;QACT,GAAG,CAAC;QACJ,UAAU,MAAM,MAAM,CAAC,CAAC,KAAK;YAC3B,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;YAC7C,OAAO;QACT,GAAG,CAAC;QACJ,gBAAgB,MAAM,MAAM,GAAG,IAC3B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GAC7E;QACJ,mBAAmB,MAAM,MAAM,GAAG,IAC9B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,oBAAoB,IAAI,CAAC,GAAG,KAAK,MAAM,MAAM,GACrF;IACN;IAEA,OAAO;AACT;IAtBgB;;QACI", "debugId": null}}, {"offset": {"line": 2272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/dashboard/content-calendar.tsx"], "sourcesContent": ["// src/components/dashboard/content-calendar.tsx\r\n\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Loader2, Facebook, Instagram, Linkedin, Twitter, Settings, Palette, Sparkles } from \"lucide-react\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { PostCard } from \"@/components/dashboard/post-card\";\r\nimport { generateContentAction, generateEnhancedDesignAction, generateContentWithArtifactsAction, generateContentWithRevoModelAction } from \"@/app/actions\";\r\n\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { useGeneratedPosts } from \"@/hooks/use-generated-posts\";\r\nimport { useFirebaseAuth } from \"@/hooks/use-firebase-auth\";\r\nimport type { BrandProfile, GeneratedPost, Platform, BrandConsistencyPreferences } from \"@/lib/types\";\r\n\r\ntype RevoModel = 'revo-1.0' | 'revo-1.5';\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\";\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { ArtifactSelector } from \"@/components/artifacts/artifact-selector\";\r\n\r\ntype ContentCalendarProps = {\r\n  brandProfile: BrandProfile;\r\n  posts: GeneratedPost[];\r\n  onPostGenerated: (post: GeneratedPost) => void;\r\n  onPostUpdated: (post: GeneratedPost) => Promise<void>;\r\n};\r\n\r\nconst platforms: { name: Platform; icon: React.ElementType }[] = [\r\n  { name: 'Instagram', icon: Instagram },\r\n  { name: 'Facebook', icon: Facebook },\r\n  { name: 'Twitter', icon: Twitter },\r\n  { name: 'LinkedIn', icon: Linkedin },\r\n];\r\n\r\nexport function ContentCalendar({ brandProfile, posts, onPostGenerated, onPostUpdated }: ContentCalendarProps) {\r\n  const [isGenerating, setIsGenerating] = React.useState<Platform | null>(null);\r\n  const { toast } = useToast();\r\n  const { user } = useFirebaseAuth();\r\n  const { savePost, saving } = useGeneratedPosts();\r\n\r\n  // Brand consistency preferences - default to consistent if design examples exist\r\n  const [brandConsistency, setBrandConsistency] = React.useState<BrandConsistencyPreferences>({\r\n    strictConsistency: !!(brandProfile.designExamples && brandProfile.designExamples.length > 0), // Auto-check if design examples exist\r\n    followBrandColors: true, // Always follow brand colors\r\n  });\r\n\r\n  // Revo model selection\r\n  const [selectedRevoModel, setSelectedRevoModel] = React.useState<RevoModel>('revo-1.5');\r\n\r\n  // Artifact selection for content generation\r\n  const [selectedArtifacts, setSelectedArtifacts] = React.useState<string[]>([]);\r\n\r\n  // Save preferences to localStorage\r\n  React.useEffect(() => {\r\n    const savedPreferences = localStorage.getItem('brandConsistencyPreferences');\r\n    if (savedPreferences) {\r\n      setBrandConsistency(JSON.parse(savedPreferences));\r\n    }\r\n\r\n    const savedRevoModel = localStorage.getItem('selectedRevoModel');\r\n    if (savedRevoModel) {\r\n      setSelectedRevoModel(savedRevoModel as RevoModel);\r\n    }\r\n  }, []);\r\n\r\n  React.useEffect(() => {\r\n    localStorage.setItem('brandConsistencyPreferences', JSON.stringify(brandConsistency));\r\n  }, [brandConsistency]);\r\n\r\n  React.useEffect(() => {\r\n    localStorage.setItem('selectedRevoModel', selectedRevoModel);\r\n  }, [selectedRevoModel]);\r\n\r\n  const handleGenerateClick = async (platform: Platform) => {\r\n    setIsGenerating(platform);\r\n    try {\r\n      console.log('🚀 Starting content generation for platform:', platform);\r\n      console.log('👤 User authenticated:', !!user);\r\n      console.log('🏢 Brand profile:', brandProfile?.businessName);\r\n\r\n      let newPost;\r\n\r\n      // Check if artifacts are enabled (simple toggle approach)\r\n      const artifactsEnabled = selectedArtifacts.length > 0;\r\n\r\n      // Use the new Revo model system for all versions\r\n      if (selectedRevoModel === 'revo-1.0' || selectedRevoModel === 'revo-1.5' || selectedRevoModel === 'revo-2.0') {\r\n        console.log(`🎨 Using new Revo model system with ${selectedRevoModel}`);\r\n        // Use the new Revo model system\r\n        newPost = await generateContentWithRevoModelAction(\r\n          brandProfile,\r\n          platform,\r\n          selectedRevoModel as 'revo-1.0' | 'revo-1.5' | 'revo-2.0',\r\n          brandConsistency,\r\n          [] // Empty array - let the action use active artifacts from artifacts service\r\n        );\r\n      } else {\r\n        // Fallback to old system for any other cases\r\n        const useEnhancedGeneration = artifactsEnabled || selectedRevoModel === 'revo-1.5';\r\n\r\n        if (useEnhancedGeneration) {\r\n          console.log(`✨ Using enhanced generation with ${selectedRevoModel} model`);\r\n          // Use artifact-enhanced generation - will automatically use active artifacts from artifacts page\r\n          newPost = await generateContentWithArtifactsAction(\r\n            brandProfile,\r\n            platform,\r\n            brandConsistency,\r\n            [], // Empty array - let the action use active artifacts from artifacts service\r\n            selectedRevoModel === 'revo-1.5' // Enhanced design for Revo 1.5\r\n          );\r\n        } else {\r\n          console.log(`📝 Using standard content generation with ${selectedRevoModel} model`);\r\n          // Use standard content generation\r\n          newPost = await generateContentAction(brandProfile, platform, brandConsistency);\r\n        }\r\n      }\r\n\r\n      console.log('📄 Generated post:', newPost.content.substring(0, 100) + '...');\r\n\r\n      // Save to Firestore database first\r\n      try {\r\n        console.log('💾 Saving post to Firestore database...');\r\n        const postId = await savePost(newPost);\r\n        console.log('✅ Post saved to Firestore with ID:', postId);\r\n\r\n        // Update the post with the Firestore ID\r\n        const savedPost = { ...newPost, id: postId };\r\n        onPostGenerated(savedPost);\r\n      } catch (saveError) {\r\n        console.error('❌ Failed to save to Firestore, falling back to localStorage:', saveError);\r\n        // Fallback to localStorage if Firestore fails\r\n        onPostGenerated(newPost);\r\n      }\r\n\r\n      // Dynamic toast message based on generation type\r\n      let title = \"Content Generated!\";\r\n      let description = `A new ${platform} post has been saved to your database.`;\r\n\r\n      if (selectedArtifacts.length > 0) {\r\n        title = \"Content Generated with References! 📎\";\r\n        description = `A new ${platform} post using ${selectedArtifacts.length} reference${selectedArtifacts.length !== 1 ? 's' : ''} has been saved.`;\r\n      } else if (selectedRevoModel === 'revo-1.5') {\r\n        title = \"Enhanced Content Generated! ✨\";\r\n        description = `A new enhanced ${platform} post with ${selectedRevoModel} has been saved.`;\r\n      } else {\r\n        title = \"Content Generated! 🚀\";\r\n        description = `A new ${platform} post with ${selectedRevoModel} has been saved.`;\r\n      }\r\n\r\n      toast({ title, description });\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Generation Failed\",\r\n        description: (error as Error).message,\r\n      });\r\n    } finally {\r\n      setIsGenerating(null);\r\n    }\r\n  };\r\n\r\n  // Ensure this component is always full-bleed inside the app shell and does not cause horizontal overflow.\r\n  return (\r\n    <div className=\"w-full max-w-[100vw] box-border overflow-x-hidden\">\r\n      <div className=\"w-full px-6 py-10 lg:py-16 lg:px-12\">\r\n        <div className=\"w-full box-border space-y-6\">\r\n          {/* Compact Brand Consistency Controls */}\r\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Settings className=\"h-4 w-4 text-blue-600\" />\r\n                <span className=\"font-medium text-sm\">Brand Consistency</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Palette className=\"h-3 w-3 text-gray-500\" />\r\n                  <span className=\"text-xs text-gray-600\">Strict</span>\r\n                  <Switch\r\n                    checked={brandConsistency.strictConsistency}\r\n                    onCheckedChange={(checked) =>\r\n                      setBrandConsistency(prev => ({ ...prev, strictConsistency: checked }))\r\n                    }\r\n                  />\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Sparkles className=\"h-3 w-3 text-gray-500\" />\r\n                  <span className=\"text-xs text-gray-600\">Colors</span>\r\n                  <Switch\r\n                    checked={brandConsistency.followBrandColors}\r\n                    onCheckedChange={(checked) =>\r\n                      setBrandConsistency(prev => ({ ...prev, followBrandColors: checked }))\r\n                    }\r\n                  />\r\n                </div>\r\n                <Separator orientation=\"vertical\" className=\"h-4\" />\r\n                <div className=\"flex items-center gap-2\">\r\n                  <span className=\"text-xs text-gray-600\">AI Model:</span>\r\n                  <select\r\n                    value={selectedRevoModel}\r\n                    onChange={(e) => setSelectedRevoModel(e.target.value as RevoModel)}\r\n                    className=\"appearance-none bg-white border border-gray-300 rounded-md px-3 py-1 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  >\r\n                    <option value=\"revo-1.0\">Revo 1.0</option>\r\n                    <option value=\"revo-1.5\">Revo 1.5</option>\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <p className=\"text-xs text-gray-500 mt-2\">\r\n              {selectedRevoModel === 'revo-1.5'\r\n                ? `✨ ${selectedRevoModel}: Enhanced AI with professional design principles + ${brandConsistency.strictConsistency ? \"strict consistency\" : \"brand colors\"}`\r\n                : selectedRevoModel === 'revo-1.0'\r\n                  ? `🚀 ${selectedRevoModel}: Standard reliable AI + ${brandConsistency.strictConsistency ? \"strict consistency\" : \"brand colors\"}`\r\n                  : `🌟 ${selectedRevoModel}: Next-generation AI (coming soon)`\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {/* Simple Artifacts Toggle */}\r\n          <div className=\"mb-6\">\r\n            <Card>\r\n              <CardContent className=\"p-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"space-y-1\">\r\n                    <Label className=\"text-sm font-medium\">Use Artifacts</Label>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Enable to use your uploaded reference materials and exact-use content\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Switch\r\n                      checked={selectedArtifacts.length > 0}\r\n                      onCheckedChange={(checked) => {\r\n                        if (checked) {\r\n                          // Enable artifacts - this will use active artifacts from the artifacts page\r\n                          setSelectedArtifacts(['active']);\r\n                        } else {\r\n                          // Disable artifacts\r\n                          setSelectedArtifacts([]);\r\n                        }\r\n                      }}\r\n                    />\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => window.open('/artifacts', '_blank')}\r\n                      className=\"text-xs\"\r\n                    >\r\n                      Manage\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                {selectedArtifacts.length > 0 && (\r\n                  <div className=\"mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md\">\r\n                    <p className=\"text-xs text-blue-700\">\r\n                      ✓ Artifacts enabled - Content will use your reference materials and exact-use items from the Artifacts page\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col items-start gap-4 md:flex-row md:items-center md:justify-between\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold tracking-tight font-headline\">Content Calendar</h1>\r\n              <p className=\"text-muted-foreground\">\r\n                Here's your generated content. Click a post to edit or regenerate.\r\n              </p>\r\n            </div>\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button disabled={!!isGenerating}>\r\n                  {isGenerating ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                      Generating for {isGenerating}...\r\n                    </>\r\n                  ) : (\r\n                    \"✨ Generate New Post\"\r\n                  )}\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent>\r\n                {platforms.map((p) => (\r\n                  <DropdownMenuItem key={p.name} onClick={() => handleGenerateClick(p.name)} disabled={!!isGenerating}>\r\n                    <p.icon className=\"mr-2 h-4 w-4\" />\r\n                    <span>{p.name}</span>\r\n                  </DropdownMenuItem>\r\n                ))}\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n\r\n          {posts.length > 0 ? (\r\n            <div className=\"grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 w-full max-w-none\">\r\n              {posts.map((post) => (\r\n                <PostCard\r\n                  key={post.id}\r\n                  post={post}\r\n                  brandProfile={brandProfile}\r\n                  onPostUpdated={onPostUpdated}\r\n                />\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-muted-foreground/30 bg-card p-12 text-center w-full\">\r\n              <h3 className=\"text-xl font-semibold\">Your calendar is empty</h3>\r\n              <p className=\"text-muted-foreground mt-2\">\r\n                Click the \"Generate\" button to create your first social media post!\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AAGhD;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;;;AA4BA,MAAM,YAA2D;IAC/D;QAAE,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;IAAC;IACrC;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACnC;QAAE,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;IAAC;IACjC;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;IAAC;CACpC;AAEM,SAAS,gBAAgB,EAAE,YAAY,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAwB;;IAC3G,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAkB;IACxE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;IAC/B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD;IAE7C,iFAAiF;IACjF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAA8B;QAC1F,mBAAmB,CAAC,CAAC,CAAC,aAAa,cAAc,IAAI,aAAa,cAAc,CAAC,MAAM,GAAG,CAAC;QAC3F,mBAAmB;IACrB;IAEA,uBAAuB;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAY;IAE5E,4CAA4C;IAC5C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAW,EAAE;IAE7E,mCAAmC;IACnC,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,MAAM,mBAAmB,aAAa,OAAO,CAAC;YAC9C,IAAI,kBAAkB;gBACpB,oBAAoB,KAAK,KAAK,CAAC;YACjC;YAEA,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,gBAAgB;gBAClB,qBAAqB;YACvB;QACF;oCAAG,EAAE;IAEL,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,aAAa,OAAO,CAAC,+BAA+B,KAAK,SAAS,CAAC;QACrE;oCAAG;QAAC;KAAiB;IAErB,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,aAAa,OAAO,CAAC,qBAAqB;QAC5C;oCAAG;QAAC;KAAkB;IAEtB,MAAM,sBAAsB,OAAO;QACjC,gBAAgB;QAChB,IAAI;YACF,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,QAAQ,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,QAAQ,GAAG,CAAC,qBAAqB,cAAc;YAE/C,IAAI;YAEJ,0DAA0D;YAC1D,MAAM,mBAAmB,kBAAkB,MAAM,GAAG;YAEpD,iDAAiD;YACjD,IAAI,sBAAsB,cAAc,sBAAsB,cAAc,sBAAsB,YAAY;gBAC5G,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,mBAAmB;gBACtE,gCAAgC;gBAChC,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,qCAAkC,AAAD,EAC/C,cACA,UACA,mBACA,kBACA,EAAE,CAAC,2EAA2E;;YAElF,OAAO;gBACL,6CAA6C;gBAC7C,MAAM,wBAAwB,oBAAoB,sBAAsB;gBAExE,IAAI,uBAAuB;oBACzB,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,kBAAkB,MAAM,CAAC;oBACzE,iGAAiG;oBACjG,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,qCAAkC,AAAD,EAC/C,cACA,UACA,kBACA,EAAE,EACF,sBAAsB,WAAW,+BAA+B;;gBAEpE,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,kBAAkB,MAAM,CAAC;oBAClF,kCAAkC;oBAClC,UAAU,MAAM,CAAA,GAAA,qJAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,UAAU;gBAChE;YACF;YAEA,QAAQ,GAAG,CAAC,sBAAsB,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO;YAEtE,mCAAmC;YACnC,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,SAAS,MAAM,SAAS;gBAC9B,QAAQ,GAAG,CAAC,sCAAsC;gBAElD,wCAAwC;gBACxC,MAAM,YAAY;oBAAE,GAAG,OAAO;oBAAE,IAAI;gBAAO;gBAC3C,gBAAgB;YAClB,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,gEAAgE;gBAC9E,8CAA8C;gBAC9C,gBAAgB;YAClB;YAEA,iDAAiD;YACjD,IAAI,QAAQ;YACZ,IAAI,cAAc,CAAC,MAAM,EAAE,SAAS,sCAAsC,CAAC;YAE3E,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,QAAQ;gBACR,cAAc,CAAC,MAAM,EAAE,SAAS,YAAY,EAAE,kBAAkB,MAAM,CAAC,UAAU,EAAE,kBAAkB,MAAM,KAAK,IAAI,MAAM,GAAG,gBAAgB,CAAC;YAChJ,OAAO,IAAI,sBAAsB,YAAY;gBAC3C,QAAQ;gBACR,cAAc,CAAC,eAAe,EAAE,SAAS,WAAW,EAAE,kBAAkB,gBAAgB,CAAC;YAC3F,OAAO;gBACL,QAAQ;gBACR,cAAc,CAAC,MAAM,EAAE,SAAS,WAAW,EAAE,kBAAkB,gBAAgB,CAAC;YAClF;YAEA,MAAM;gBAAE;gBAAO;YAAY;QAC7B,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa,AAAC,MAAgB,OAAO;YACvC;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,0GAA0G;IAC1G,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,iBAAiB,iBAAiB;wDAC3C,iBAAiB,CAAC,UAChB,oBAAoB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB;gEAAQ,CAAC;;;;;;;;;;;;0DAI1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,iBAAiB,iBAAiB;wDAC3C,iBAAiB,CAAC,UAChB,oBAAoB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB;gEAAQ,CAAC;;;;;;;;;;;;0DAI1E,6LAAC,wIAAA,CAAA,YAAS;gDAAC,aAAY;gDAAW,WAAU;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACpD,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,6LAAC;gEAAO,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKjC,6LAAC;gCAAE,WAAU;0CACV,sBAAsB,aACnB,CAAC,EAAE,EAAE,kBAAkB,oDAAoD,EAAE,iBAAiB,iBAAiB,GAAG,uBAAuB,gBAAgB,GACzJ,sBAAsB,aACpB,CAAC,GAAG,EAAE,kBAAkB,yBAAyB,EAAE,iBAAiB,iBAAiB,GAAG,uBAAuB,gBAAgB,GAC/H,CAAC,GAAG,EAAE,kBAAkB,kCAAkC,CAAC;;;;;;;;;;;;kCAMrE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAI/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,kBAAkB,MAAM,GAAG;wDACpC,iBAAiB,CAAC;4DAChB,IAAI,SAAS;gEACX,4EAA4E;gEAC5E,qBAAqB;oEAAC;iEAAS;4DACjC,OAAO;gEACL,oBAAoB;gEACpB,qBAAqB,EAAE;4DACzB;wDACF;;;;;;kEAEF,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,OAAO,IAAI,CAAC,cAAc;wDACzC,WAAU;kEACX;;;;;;;;;;;;;;;;;;oCAKJ,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,UAAU,CAAC,CAAC;sDACjB,6BACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;oDACjC;oDAAa;;+DAG/B;;;;;;;;;;;kDAIN,6LAAC,+IAAA,CAAA,sBAAmB;kDACjB,UAAU,GAAG,CAAC,CAAC,kBACd,6LAAC,+IAAA,CAAA,mBAAgB;gDAAc,SAAS,IAAM,oBAAoB,EAAE,IAAI;gDAAG,UAAU,CAAC,CAAC;;kEACrF,6LAAC,EAAE,IAAI;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAM,EAAE,IAAI;;;;;;;+CAFQ,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;oBASpC,MAAM,MAAM,GAAG,kBACd,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,kJAAA,CAAA,WAAQ;gCAEP,MAAM;gCACN,cAAc;gCACd,eAAe;+BAHV,KAAK,EAAE;;;;;;;;;6CAQlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GA3RgB;;QAEI,+HAAA,CAAA,WAAQ;QACT,0IAAA,CAAA,kBAAe;QACH,4IAAA,CAAA,oBAAiB;;;KAJhC", "debugId": null}}, {"offset": {"line": 2964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/components/layout/unified-brand-layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { UnifiedBrandProvider, useUnifiedBrand, useBrandChangeListener } from '@/contexts/unified-brand-context';\r\nimport type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';\r\n\r\ninterface UnifiedBrandLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n// Inner component that uses the unified brand context\r\nfunction UnifiedBrandLayoutContent({ children }: UnifiedBrandLayoutProps) {\r\n  const { currentBrand, loading, error } = useUnifiedBrand();\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Listen for brand changes and log them\r\n  useBrandChangeListener((brand) => {\r\n    console.log('🔄 Brand changed in layout:', brand?.businessName || brand?.name || 'none');\r\n    \r\n    // Mark as initialized once we have a brand or finished loading\r\n    if (!isInitialized && (!loading || brand)) {\r\n      setIsInitialized(true);\r\n    }\r\n  });\r\n\r\n  // Show loading state while initializing\r\n  if (!isInitialized && loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading brand profiles...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if there's an error\r\n  if (error) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n            <span className=\"text-red-600 text-2xl\">⚠️</span>\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-red-900 mb-2\">Error Loading Brands</h2>\r\n          <p className=\"text-red-600 mb-4\">{error}</p>\r\n          <button \r\n            onClick={() => window.location.reload()} \r\n            className=\"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\r\n          >\r\n            Retry\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"unified-brand-layout\">\r\n      {/* Debug info in development */}\r\n      {process.env.NODE_ENV === 'development' && (\r\n        <div className=\"fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl\">\r\n          <div>🔥 Unified Brand System</div>\r\n          <div>Brand: {currentBrand?.businessName || currentBrand?.name || 'None'}</div>\r\n          <div>ID: {currentBrand?.id || 'None'}</div>\r\n        </div>\r\n      )}\r\n      \r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Main layout component that provides the unified brand context\r\nexport function UnifiedBrandLayout({ children }: UnifiedBrandLayoutProps) {\r\n  return (\r\n    <UnifiedBrandProvider>\r\n      <UnifiedBrandLayoutContent>\r\n        {children}\r\n      </UnifiedBrandLayoutContent>\r\n    </UnifiedBrandProvider>\r\n  );\r\n}\r\n\r\n// Hook to make any component brand-aware\r\nexport function useBrandAware() {\r\n  const { currentBrand, selectBrand, loading } = useUnifiedBrand();\r\n  \r\n  return {\r\n    currentBrand,\r\n    selectBrand,\r\n    loading,\r\n    isReady: !loading && currentBrand !== null,\r\n    brandId: currentBrand?.id || null,\r\n    brandName: currentBrand?.businessName || currentBrand?.name || null,\r\n  };\r\n}\r\n\r\n// Higher-order component to make any component brand-aware\r\nexport function withBrandAware<P extends object>(\r\n  Component: React.ComponentType<P & { brand: CompleteBrandProfile | null }>\r\n) {\r\n  return function BrandAwareComponent(props: P) {\r\n    const { currentBrand } = useUnifiedBrand();\r\n    \r\n    return <Component {...props} brand={currentBrand} />;\r\n  };\r\n}\r\n\r\n// Component to show brand-specific content\r\ninterface BrandContentProps {\r\n  children: (brand: CompleteBrandProfile) => React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function BrandContent({ children, fallback }: BrandContentProps) {\r\n  const { currentBrand, loading } = useUnifiedBrand();\r\n  \r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  if (!currentBrand) {\r\n    return fallback || (\r\n      <div className=\"text-center p-8\">\r\n        <div className=\"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n          <span className=\"text-gray-400 text-2xl\">🏢</span>\r\n        </div>\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Brand Selected</h3>\r\n        <p className=\"text-gray-600\">Please select a brand to continue.</p>\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  return <>{children(currentBrand)}</>;\r\n}\r\n\r\n// Component to conditionally render content based on brand\r\ninterface ConditionalBrandContentProps {\r\n  brandId?: string;\r\n  brandName?: string;\r\n  children: React.ReactNode;\r\n  fallback?: React.ReactNode;\r\n}\r\n\r\nexport function ConditionalBrandContent({ \r\n  brandId, \r\n  brandName, \r\n  children, \r\n  fallback \r\n}: ConditionalBrandContentProps) {\r\n  const { currentBrand } = useUnifiedBrand();\r\n  \r\n  const shouldRender = \r\n    (!brandId || currentBrand?.id === brandId) &&\r\n    (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);\r\n  \r\n  if (shouldRender) {\r\n    return <>{children}</>;\r\n  }\r\n  \r\n  return fallback || null;\r\n}\r\n\r\n// Hook to get brand-scoped data with automatic updates\r\nexport function useBrandScopedData<T>(\r\n  feature: string,\r\n  defaultValue: T,\r\n  loader?: (brandId: string) => T | Promise<T>\r\n): [T, (data: T) => void, boolean] {\r\n  const { currentBrand, getBrandStorage } = useUnifiedBrand();\r\n  const [data, setData] = useState<T>(defaultValue);\r\n  const [loading, setLoading] = useState(false);\r\n  \r\n  // Load data when brand changes\r\n  useEffect(() => {\r\n    if (!currentBrand?.id) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    const storage = getBrandStorage(feature);\r\n    if (!storage) {\r\n      setData(defaultValue);\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    \r\n    try {\r\n      if (loader) {\r\n        // Use custom loader\r\n        const result = loader(currentBrand.id);\r\n        if (result instanceof Promise) {\r\n          result.then(loadedData => {\r\n            setData(loadedData);\r\n            setLoading(false);\r\n          }).catch(error => {\r\n            console.error(`Failed to load ${feature} data:`, error);\r\n            setData(defaultValue);\r\n            setLoading(false);\r\n          });\r\n        } else {\r\n          setData(result);\r\n          setLoading(false);\r\n        }\r\n      } else {\r\n        // Use storage\r\n        const storedData = storage.getItem<T>();\r\n        setData(storedData || defaultValue);\r\n        setLoading(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Failed to load ${feature} data:`, error);\r\n      setData(defaultValue);\r\n      setLoading(false);\r\n    }\r\n  }, [currentBrand?.id, feature, defaultValue, loader, getBrandStorage]);\r\n  \r\n  // Save data function\r\n  const saveData = (newData: T) => {\r\n    setData(newData);\r\n    \r\n    if (currentBrand?.id) {\r\n      const storage = getBrandStorage(feature);\r\n      if (storage) {\r\n        storage.setItem(newData);\r\n      }\r\n    }\r\n  };\r\n  \r\n  return [data, saveData, loading];\r\n}\r\n\r\n// Component to display brand switching status\r\nexport function BrandSwitchingStatus() {\r\n  const { loading, currentBrand } = useUnifiedBrand();\r\n  const [switching, setSwitching] = useState(false);\r\n  \r\n  useBrandChangeListener((brand) => {\r\n    setSwitching(true);\r\n    const timer = setTimeout(() => setSwitching(false), 1000);\r\n    return () => clearTimeout(timer);\r\n  });\r\n  \r\n  if (!switching && !loading) return null;\r\n  \r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\r\n        <span className=\"text-sm\">\r\n          {switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AA6DO;;AA3DP;AACA;;;AAHA;;;AAUA,sDAAsD;AACtD,SAAS,0BAA0B,EAAE,QAAQ,EAA2B;;IACtE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wCAAwC;IACxC,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD;4DAAE,CAAC;YACtB,QAAQ,GAAG,CAAC,+BAA+B,OAAO,gBAAgB,OAAO,QAAQ;YAEjF,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB,CAAC,CAAC,WAAW,KAAK,GAAG;gBACzC,iBAAiB;YACnB;QACF;;IAEA,wCAAwC;IACxC,IAAI,CAAC,iBAAiB,SAAS;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,uCAAuC;IACvC,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;kCAE1C,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,oDAAyB,+BACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAI;;;;;;kCACL,6LAAC;;4BAAI;4BAAQ,cAAc,gBAAgB,cAAc,QAAQ;;;;;;;kCACjE,6LAAC;;4BAAI;4BAAK,cAAc,MAAM;;;;;;;;;;;;;YAIjC;;;;;;;AAGP;GA7DS;;QACkC,kJAAA,CAAA,kBAAe;QAIxD,kJAAA,CAAA,yBAAsB;;;KALf;AAgEF,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,qBACE,6LAAC,kJAAA,CAAA,uBAAoB;kBACnB,cAAA,6LAAC;sBACE;;;;;;;;;;;AAIT;MARgB;AAWT,SAAS;;IACd,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAE7D,OAAO;QACL;QACA;QACA;QACA,SAAS,CAAC,WAAW,iBAAiB;QACtC,SAAS,cAAc,MAAM;QAC7B,WAAW,cAAc,gBAAgB,cAAc,QAAQ;IACjE;AACF;IAXgB;;QACiC,kJAAA,CAAA,kBAAe;;;AAazD,SAAS,eACd,SAA0E;;IAE1E,UAAO,SAAS,oBAAoB,KAAQ;;QAC1C,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;QAEvC,qBAAO,6LAAC;YAAW,GAAG,KAAK;YAAE,OAAO;;;;;;IACtC;;YAH2B,kJAAA,CAAA,kBAAe;;;AAI5C;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAqB;;IACpE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAEhD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,0BACL,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAyB;;;;;;;;;;;8BAE3C,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBAAO;kBAAG,SAAS;;AACrB;IAxBgB;;QACoB,kJAAA,CAAA,kBAAe;;;MADnC;AAkCT,SAAS,wBAAwB,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,QAAQ,EACqB;;IAC7B,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAEvC,MAAM,eACJ,CAAC,CAAC,WAAW,cAAc,OAAO,OAAO,KACzC,CAAC,CAAC,aAAa,cAAc,iBAAiB,aAAa,cAAc,SAAS,SAAS;IAE7F,IAAI,cAAc;QAChB,qBAAO;sBAAG;;IACZ;IAEA,OAAO,YAAY;AACrB;IAjBgB;;QAMW,kJAAA,CAAA,kBAAe;;;MAN1B;AAoBT,SAAS,mBACd,OAAe,EACf,YAAe,EACf,MAA4C;;IAE5C,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,cAAc,IAAI;gBACrB,QAAQ;gBACR;YACF;YAEA,MAAM,UAAU,gBAAgB;YAChC,IAAI,CAAC,SAAS;gBACZ,QAAQ;gBACR;YACF;YAEA,WAAW;YAEX,IAAI;gBACF,IAAI,QAAQ;oBACV,oBAAoB;oBACpB,MAAM,SAAS,OAAO,aAAa,EAAE;oBACrC,IAAI,kBAAkB,SAAS;wBAC7B,OAAO,IAAI;4DAAC,CAAA;gCACV,QAAQ;gCACR,WAAW;4BACb;2DAAG,KAAK;4DAAC,CAAA;gCACP,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,EAAE;gCACjD,QAAQ;gCACR,WAAW;4BACb;;oBACF,OAAO;wBACL,QAAQ;wBACR,WAAW;oBACb;gBACF,OAAO;oBACL,cAAc;oBACd,MAAM,aAAa,QAAQ,OAAO;oBAClC,QAAQ,cAAc;oBACtB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,EAAE;gBACjD,QAAQ;gBACR,WAAW;YACb;QACF;uCAAG;QAAC,cAAc;QAAI;QAAS;QAAc;QAAQ;KAAgB;IAErE,qBAAqB;IACrB,MAAM,WAAW,CAAC;QAChB,QAAQ;QAER,IAAI,cAAc,IAAI;YACpB,MAAM,UAAU,gBAAgB;YAChC,IAAI,SAAS;gBACX,QAAQ,OAAO,CAAC;YAClB;QACF;IACF;IAEA,OAAO;QAAC;QAAM;QAAU;KAAQ;AAClC;IAnEgB;;QAK4B,kJAAA,CAAA,kBAAe;;;AAiEpD,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD;uDAAE,CAAC;YACtB,aAAa;YACb,MAAM,QAAQ;qEAAW,IAAM,aAAa;oEAAQ;YACpD;+DAAO,IAAM,aAAa;;QAC5B;;IAEA,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAK,WAAU;8BACb,YAAY,CAAC,aAAa,EAAE,cAAc,gBAAgB,cAAc,KAAK,GAAG,CAAC,GAAG;;;;;;;;;;;;;;;;;AAK/F;IAtBgB;;QACoB,kJAAA,CAAA,kBAAe;QAGjD,kJAAA,CAAA,yBAAsB;;;MAJR", "debugId": null}}, {"offset": {"line": 3433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nevisai/Nevis/src/app/quick-content/page.tsx"], "sourcesContent": ["// src/app/content-calendar/page.tsx\r\n\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { SidebarInset, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { ContentCalendar } from \"@/components/dashboard/content-calendar\";\r\n// TODO: Re-enable once ActiveArtifactsIndicator is properly set up\r\n// import { ActiveArtifactsIndicator } from \"@/components/artifacts/active-artifacts-indicator\";\r\nimport type { BrandProfile, GeneratedPost } from \"@/lib/types\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { User, PanelLeftClose, PanelLeftOpen } from \"lucide-react\";\r\nimport { useUnifiedBrand, useBrandStorage, useBrandChangeListener } from \"@/contexts/unified-brand-context\";\r\nimport { UnifiedBrandLayout, BrandContent, BrandSwitchingStatus } from \"@/components/layout/unified-brand-layout\";\r\nimport { STORAGE_FEATURES, getStorageUsage, cleanupAllStorage } from \"@/lib/services/brand-scoped-storage\";\r\n\r\nconst MAX_POSTS_TO_STORE = 5; // Reduced to prevent storage issues\r\n\r\n// Brand-scoped storage cleanup utility\r\nconst cleanupBrandScopedStorage = (brandStorage: any) => {\r\n  try {\r\n    const posts = brandStorage.getItem() || [];\r\n\r\n    // Fix invalid dates in existing posts\r\n    const fixedPosts = posts.map((post: GeneratedPost) => {\r\n      if (!post.date || isNaN(new Date(post.date).getTime())) {\r\n        return {\r\n          ...post,\r\n          date: new Date().toISOString()\r\n        };\r\n      }\r\n      return post;\r\n    });\r\n\r\n    if (fixedPosts.length > 5) {\r\n      // Keep only the 5 most recent posts\r\n      const recentPosts = fixedPosts.slice(0, 5);\r\n      brandStorage.setItem(recentPosts);\r\n      return recentPosts;\r\n    } else {\r\n      // Save the fixed posts back\r\n      brandStorage.setItem(fixedPosts);\r\n      return fixedPosts;\r\n    }\r\n  } catch (error) {\r\n    console.warn('Brand-scoped storage cleanup failed:', error);\r\n  }\r\n  return null;\r\n};\r\n\r\nfunction QuickContentPage() {\r\n  const { currentBrand, brands, loading: brandLoading, selectBrand } = useUnifiedBrand();\r\n  const postsStorage = useBrandStorage(STORAGE_FEATURES.QUICK_CONTENT);\r\n  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n  const { open: sidebarOpen, toggleSidebar } = useSidebar();\r\n\r\n  // Inline brand restoration function\r\n  const forceBrandRestore = React.useCallback(() => {\r\n    try {\r\n      // Try to restore from full brand data first\r\n      const savedBrandData = localStorage.getItem('currentBrandData');\r\n      if (savedBrandData) {\r\n        const parsedData = JSON.parse(savedBrandData);\r\n        console.log('🔄 Attempting to restore brand from full data:', parsedData.businessName || parsedData.name);\r\n\r\n        // Find matching brand in current brands list\r\n        const matchingBrand = brands.find(b => b.id === parsedData.id);\r\n        if (matchingBrand) {\r\n          console.log('✅ Found matching brand in brands list, using fresh data');\r\n          selectBrand(matchingBrand);\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // Fallback to brand ID restoration\r\n      const savedBrandId = localStorage.getItem('selectedBrandId');\r\n      if (savedBrandId && brands.length > 0) {\r\n        const savedBrand = brands.find(b => b.id === savedBrandId);\r\n        if (savedBrand) {\r\n          console.log('🔄 Restored brand from ID:', savedBrand.businessName || savedBrand.name);\r\n          selectBrand(savedBrand);\r\n          return true;\r\n        }\r\n      }\r\n\r\n      return false;\r\n    } catch (error) {\r\n      console.error('Failed to restore brand from storage:', error);\r\n      return false;\r\n    }\r\n  }, [brands, selectBrand]);\r\n\r\n  // Load posts when brand changes using unified brand system\r\n  useBrandChangeListener(React.useCallback((brand) => {\r\n    const brandName = brand?.businessName || brand?.name || 'none';\r\n    console.log('🔄 Quick Content: brand changed to:', brandName);\r\n\r\n    if (!brand) {\r\n      setGeneratedPosts([]);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      if (postsStorage) {\r\n        const posts = postsStorage.getItem<GeneratedPost[]>() || [];\r\n\r\n        // Check if any posts have invalid dates\r\n        const hasInvalidDates = posts.some((post: GeneratedPost) =>\r\n          !post.date || isNaN(new Date(post.date).getTime())\r\n        );\r\n\r\n        if (hasInvalidDates) {\r\n          console.warn('Found posts with invalid dates, clearing brand storage...');\r\n          postsStorage.removeItem();\r\n          setGeneratedPosts([]);\r\n        } else {\r\n          setGeneratedPosts(posts);\r\n        }\r\n\r\n        console.log(`✅ Loaded ${posts.length} posts for brand ${brandName}`);\r\n      } else {\r\n        setGeneratedPosts([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to load posts for brand:', brandName, error);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Failed to load data\",\r\n        description: \"Could not read your posts data. It might be corrupted.\",\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [postsStorage, toast]));\r\n\r\n  // Enhanced brand selection logic with persistence recovery\r\n  useEffect(() => {\r\n    console.log('🔍 Enhanced brand selection check:', {\r\n      brandLoading,\r\n      brandsCount: brands.length,\r\n      currentBrand: currentBrand?.businessName || currentBrand?.name || 'null',\r\n      postsStorageAvailable: !!postsStorage\r\n    });\r\n\r\n    if (!brandLoading) {\r\n      // Add a small delay to ensure brands have time to load\r\n      const timer = setTimeout(() => {\r\n        if (brands.length === 0) {\r\n          // No brands exist, redirect to brand setup\r\n          console.log('🔄 Quick Content: No brands found, redirecting to brand setup');\r\n          try { router.prefetch('/brand-profile'); } catch { }\r\n          router.push('/brand-profile');\r\n        } else if (brands.length > 0 && !currentBrand) {\r\n          // Try to restore from persistence first\r\n          console.log('🔧 Attempting brand restoration from persistence...');\r\n          const restored = forceBrandRestore();\r\n\r\n          if (!restored) {\r\n            // If restoration failed, auto-select the first brand\r\n            console.log('🎯 Auto-selecting first available brand:', brands[0].businessName || brands[0].name);\r\n            selectBrand(brands[0]);\r\n          }\r\n        }\r\n      }, 1000); // 1 second delay\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [currentBrand, brands.length, brandLoading, router, selectBrand, forceBrandRestore]);\r\n\r\n\r\n  // Inline image persistence function\r\n  const persistImageUrl = async (url: string): Promise<string> => {\r\n    try {\r\n      // If it's already a data URL and not too large, keep it\r\n      if (url.startsWith('data:')) {\r\n        const sizeInBytes = Math.round((url.length * 3) / 4);\r\n        if (sizeInBytes <= 500 * 1024) { // 500KB limit\r\n          console.log('📸 Keeping small data URL as-is');\r\n          return url;\r\n        }\r\n      }\r\n\r\n      // If it's a blob URL or HTTP URL, try to fetch and convert\r\n      if (url.startsWith('blob:') || url.startsWith('http')) {\r\n        console.log('🔄 Converting URL to persistent data URL:', url.substring(0, 50) + '...');\r\n\r\n        const response = await fetch(url);\r\n        if (!response.ok) throw new Error(`HTTP ${response.status}`);\r\n\r\n        const blob = await response.blob();\r\n\r\n        // If blob is too large, skip persistence\r\n        if (blob.size > 1024 * 1024) { // 1MB limit\r\n          console.warn('⚠️ Image too large, keeping original URL');\r\n          return url;\r\n        }\r\n\r\n        // Convert to data URL\r\n        return new Promise((resolve, reject) => {\r\n          const reader = new FileReader();\r\n          reader.onload = () => resolve(reader.result as string);\r\n          reader.onerror = () => reject(new Error('Failed to read blob'));\r\n          reader.readAsDataURL(blob);\r\n        });\r\n      }\r\n\r\n      // Return original URL if we can't process it\r\n      return url;\r\n    } catch (error) {\r\n      console.warn('⚠️ Failed to persist image URL:', error);\r\n      return url; // Return original URL on failure\r\n    }\r\n  };\r\n\r\n  const handlePostGenerated = async (post: GeneratedPost) => {\r\n    console.log('📝 Processing generated post...');\r\n\r\n    // Simple approach: try to persist images but don't block if it fails\r\n    let processedPost = { ...post };\r\n\r\n    try {\r\n      // Persist main image URL if exists\r\n      if (post.imageUrl) {\r\n        const persistedUrl = await persistImageUrl(post.imageUrl);\r\n        processedPost.imageUrl = persistedUrl;\r\n      }\r\n\r\n      // Persist variant image URLs\r\n      if (post.variants && post.variants.length > 0) {\r\n        const persistedVariants = await Promise.all(\r\n          post.variants.map(async (variant) => {\r\n            if (variant.imageUrl) {\r\n              const persistedUrl = await persistImageUrl(variant.imageUrl);\r\n              return { ...variant, imageUrl: persistedUrl };\r\n            }\r\n            return variant;\r\n          })\r\n        );\r\n        processedPost.variants = persistedVariants;\r\n      }\r\n\r\n      console.log('✅ Image persistence completed');\r\n    } catch (error) {\r\n      console.warn('⚠️ Image persistence failed, using original post:', error);\r\n      processedPost = post; // Use original post if persistence fails\r\n    }\r\n\r\n    // Add the processed post and slice the array to only keep the most recent ones\r\n    const newPosts = [processedPost, ...generatedPosts].slice(0, MAX_POSTS_TO_STORE);\r\n    setGeneratedPosts(newPosts);\r\n\r\n    if (!postsStorage) {\r\n      console.warn('No posts storage available for current brand - keeping in memory only');\r\n      toast({\r\n        title: \"Storage Unavailable\",\r\n        description: \"Post generated but couldn't be saved. Please select a brand.\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // The BrandScopedStorage now handles quota management automatically\r\n      postsStorage.setItem(newPosts);\r\n      console.log(`💾 Saved ${newPosts.length} posts for brand ${currentBrand?.businessName || currentBrand?.name}`);\r\n    } catch (error) {\r\n      console.error('Storage error in handlePostGenerated:', error);\r\n\r\n      // Show user-friendly error message\r\n      toast({\r\n        title: \"Storage Issue\",\r\n        description: \"Post generated successfully but couldn't be saved. Storage may be full.\",\r\n        variant: \"destructive\",\r\n      });\r\n\r\n      // Keep the post in memory even if storage fails\r\n      console.log('Post kept in memory despite storage failure');\r\n    }\r\n  };\r\n\r\n  // Debug function to clear all posts for current brand\r\n  const clearAllPosts = () => {\r\n    if (!postsStorage) {\r\n      console.warn('No posts storage available for current brand');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      postsStorage.removeItem();\r\n      setGeneratedPosts([]);\r\n      toast({\r\n        title: \"Posts Cleared\",\r\n        description: `All stored posts have been cleared for ${currentBrand?.businessName || currentBrand?.name}.`,\r\n      });\r\n      console.log(`🗑️ Cleared all posts for brand ${currentBrand?.businessName || currentBrand?.name}`);\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Clear Failed\",\r\n        description: \"Could not clear stored posts.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handlePostUpdated = async (updatedPost: GeneratedPost) => {\r\n    if (!postsStorage) {\r\n      console.warn('No posts storage available for current brand');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const updatedPosts = generatedPosts.map((post) =>\r\n        post.id === updatedPost.id ? updatedPost : post\r\n      );\r\n      setGeneratedPosts(updatedPosts);\r\n\r\n      // Check storage size before saving\r\n      const postsData = JSON.stringify(updatedPosts);\r\n      const maxSize = 5 * 1024 * 1024; // 5MB limit\r\n\r\n      if (postsData.length > maxSize) {\r\n        // If too large, keep fewer posts\r\n        const reducedPosts = updatedPosts.slice(0, Math.max(1, Math.floor(MAX_POSTS_TO_STORE / 2)));\r\n        postsStorage.setItem(reducedPosts);\r\n        setGeneratedPosts(reducedPosts);\r\n\r\n        toast({\r\n          title: \"Storage Optimized\",\r\n          description: \"Reduced stored posts to prevent storage overflow. Some older posts were removed.\",\r\n        });\r\n      } else {\r\n        postsStorage.setItem(updatedPosts);\r\n      }\r\n\r\n      console.log(`💾 Updated post for brand ${currentBrand?.businessName || currentBrand?.name}`);\r\n    } catch (error) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Failed to update post\",\r\n        description: \"Unable to save post updates. Your browser storage may be full.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <SidebarInset key={currentBrand?.id || 'no-brand'} fullWidth>\r\n      <header className=\"flex h-14 items-center justify-between gap-4 border-b bg-card px-4 lg:h-[60px] lg:px-6\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={toggleSidebar}\r\n            className=\"h-8 w-8\"\r\n            title={sidebarOpen ? \"Hide sidebar for full-screen mode\" : \"Show sidebar\"}\r\n          >\r\n            {sidebarOpen ? (\r\n              <PanelLeftClose className=\"h-4 w-4\" />\r\n            ) : (\r\n              <PanelLeftOpen className=\"h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            {sidebarOpen ? \"Sidebar visible\" : \"Full-screen mode\"}\r\n          </span>\r\n        </div>\r\n\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"secondary\" size=\"icon\" className=\"rounded-full\">\r\n              <Avatar>\r\n                <AvatarImage src=\"https://placehold.co/40x40.png\" alt=\"User\" data-ai-hint=\"user avatar\" />\r\n                <AvatarFallback><User /></AvatarFallback>\r\n              </Avatar>\r\n              <span className=\"sr-only\">Toggle user menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuLabel>My Account</DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem\r\n              onClick={() => {\r\n                if (postsStorage) {\r\n                  const cleaned = cleanupBrandScopedStorage(postsStorage);\r\n                  if (cleaned) {\r\n                    setGeneratedPosts(cleaned);\r\n                    toast({\r\n                      title: \"Storage Cleaned\",\r\n                      description: `Removed older posts for ${currentBrand?.businessName || currentBrand?.name}.`,\r\n                    });\r\n                  } else {\r\n                    toast({\r\n                      title: \"Storage Clean\",\r\n                      description: \"Storage is already optimized.\",\r\n                    });\r\n                  }\r\n                } else {\r\n                  toast({\r\n                    variant: \"destructive\",\r\n                    title: \"No Brand Selected\",\r\n                    description: \"Please select a brand first.\",\r\n                  });\r\n                }\r\n              }}\r\n            >\r\n              Clear Old Posts\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </header>\r\n      <main className=\"flex-1 overflow-auto\">\r\n        <div className=\"min-h-full bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <div className=\"max-w-7xl mx-auto\">\r\n              {isLoading || brandLoading ? (\r\n                <div className=\"flex w-full min-h-[300px] items-center justify-center\">\r\n                  <div className=\"w-full max-w-3xl text-center\">\r\n                    <p>Loading Quick Content...</p>\r\n                  </div>\r\n                </div>\r\n              ) : !currentBrand ? (\r\n                <div className=\"flex flex-col items-center justify-center min-h-[400px] space-y-4\">\r\n                  <h2 className=\"text-xl font-semibold\">Select a Brand</h2>\r\n                  <p className=\"text-muted-foreground text-center\">\r\n                    Please select a brand to start generating content.\r\n                  </p>\r\n                  {brands.length > 0 ? (\r\n                    <div className=\"flex flex-wrap gap-2\">\r\n                      {brands.map((brand) => (\r\n                        <Button\r\n                          key={brand.id}\r\n                          onClick={() => selectBrand(brand)}\r\n                          variant=\"outline\"\r\n                        >\r\n                          {brand.businessName || brand.name}\r\n                        </Button>\r\n                      ))}\r\n                    </div>\r\n                  ) : (\r\n                    <Button onMouseEnter={() => router.prefetch('/brand-profile')} onFocus={() => router.prefetch('/brand-profile')} onClick={() => router.push('/brand-profile')}>\r\n                      Create Brand Profile\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {/* TODO: Re-enable Active Artifacts Indicator once component is set up */}\r\n                  {/* <ActiveArtifactsIndicator\r\n              onArtifactDeactivate={() => {\r\n                // Refresh content when artifacts are deactivated\r\n                console.log('Artifact deactivated, content generation will use updated active artifacts');\r\n              }}\r\n              onManageArtifacts={() => {\r\n                // Navigate to artifacts page\r\n                window.open('/artifacts', '_blank');\r\n              }}\r\n            /> */}\r\n\r\n                  {/* Content Calendar */}\r\n                  {/* Map unified CompleteBrandProfile to the simplified BrandProfile expected by ContentCalendar */}\r\n                  {currentBrand && (\r\n                    <ContentCalendar\r\n                      brandProfile={{\r\n                        businessName: currentBrand.businessName,\r\n                        businessType: currentBrand.businessType || '',\r\n                        location: currentBrand.location || '',\r\n                        logoDataUrl: currentBrand.logoDataUrl || '',\r\n                        visualStyle: currentBrand.visualStyle || '',\r\n                        writingTone: currentBrand.writingTone || '',\r\n                        contentThemes: currentBrand.contentThemes || '',\r\n                        websiteUrl: currentBrand.websiteUrl || '',\r\n                        description: currentBrand.description || '',\r\n                        // Convert services array to newline-separated string to match BrandProfile.services\r\n                        services: Array.isArray((currentBrand as any).services)\r\n                          ? (currentBrand as any).services.map((s: any) => s.name).join('\\n')\r\n                          : (currentBrand as any).services || '',\r\n                        targetAudience: currentBrand.targetAudience || '',\r\n                        keyFeatures: currentBrand.keyFeatures || '',\r\n                        competitiveAdvantages: currentBrand.competitiveAdvantages || '',\r\n                        contactInfo: {\r\n                          phone: currentBrand.contactPhone || '',\r\n                          email: currentBrand.contactEmail || '',\r\n                          address: currentBrand.contactAddress || '',\r\n                        },\r\n                        socialMedia: {\r\n                          facebook: currentBrand.facebookUrl || '',\r\n                          instagram: currentBrand.instagramUrl || '',\r\n                          twitter: currentBrand.twitterUrl || '',\r\n                          linkedin: currentBrand.linkedinUrl || '',\r\n                        },\r\n                        primaryColor: currentBrand.primaryColor || undefined,\r\n                        accentColor: currentBrand.accentColor || undefined,\r\n                        backgroundColor: currentBrand.backgroundColor || undefined,\r\n                        designExamples: currentBrand.designExamples || [],\r\n                      }}\r\n                      posts={generatedPosts}\r\n                      onPostGenerated={handlePostGenerated}\r\n                      onPostUpdated={handlePostUpdated}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </SidebarInset>\r\n  );\r\n}\r\n\r\nfunction QuickContentPageWithUnifiedBrand() {\r\n  return (\r\n    <UnifiedBrandLayout>\r\n      <QuickContentPage />\r\n      <BrandSwitchingStatus />\r\n    </UnifiedBrandLayout>\r\n  );\r\n}\r\n\r\nexport default QuickContentPageWithUnifiedBrand;\r\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AAGpC;AAEA;AAQA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAxBA;;;;;;;;;;;;;;AA0BA,MAAM,qBAAqB,GAAG,oCAAoC;AAElE,uCAAuC;AACvC,MAAM,4BAA4B,CAAC;IACjC,IAAI;QACF,MAAM,QAAQ,aAAa,OAAO,MAAM,EAAE;QAE1C,sCAAsC;QACtC,MAAM,aAAa,MAAM,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,KAAK;gBACtD,OAAO;oBACL,GAAG,IAAI;oBACP,MAAM,IAAI,OAAO,WAAW;gBAC9B;YACF;YACA,OAAO;QACT;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,oCAAoC;YACpC,MAAM,cAAc,WAAW,KAAK,CAAC,GAAG;YACxC,aAAa,OAAO,CAAC;YACrB,OAAO;QACT,OAAO;YACL,4BAA4B;YAC5B,aAAa,OAAO,CAAC;YACrB,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,wCAAwC;IACvD;IACA,OAAO;AACT;AAEA,SAAS;;IACP,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,YAAY,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACnF,MAAM,eAAe,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD,EAAE,uJAAA,CAAA,mBAAgB,CAAC,aAAa;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,MAAM,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAEtD,oCAAoC;IACpC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;2DAAE;YAC1C,IAAI;gBACF,4CAA4C;gBAC5C,MAAM,iBAAiB,aAAa,OAAO,CAAC;gBAC5C,IAAI,gBAAgB;oBAClB,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,QAAQ,GAAG,CAAC,kDAAkD,WAAW,YAAY,IAAI,WAAW,IAAI;oBAExG,6CAA6C;oBAC7C,MAAM,gBAAgB,OAAO,IAAI;yFAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;;oBAC7D,IAAI,eAAe;wBACjB,QAAQ,GAAG,CAAC;wBACZ,YAAY;wBACZ,OAAO;oBACT;gBACF;gBAEA,mCAAmC;gBACnC,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,gBAAgB,OAAO,MAAM,GAAG,GAAG;oBACrC,MAAM,aAAa,OAAO,IAAI;sFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;oBAC7C,IAAI,YAAY;wBACd,QAAQ,GAAG,CAAC,8BAA8B,WAAW,YAAY,IAAI,WAAW,IAAI;wBACpF,YAAY;wBACZ,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,OAAO;YACT;QACF;0DAAG;QAAC;QAAQ;KAAY;IAExB,2DAA2D;IAC3D,CAAA,GAAA,kJAAA,CAAA,yBAAsB,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;+DAAE,CAAC;YACxC,MAAM,YAAY,OAAO,gBAAgB,OAAO,QAAQ;YACxD,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,IAAI,CAAC,OAAO;gBACV,kBAAkB,EAAE;gBACpB,aAAa;gBACb;YACF;YAEA,aAAa;YAEb,IAAI;gBACF,IAAI,cAAc;oBAChB,MAAM,QAAQ,aAAa,OAAO,MAAuB,EAAE;oBAE3D,wCAAwC;oBACxC,MAAM,kBAAkB,MAAM,IAAI;+FAAC,CAAC,OAClC,CAAC,KAAK,IAAI,IAAI,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO;;oBAGjD,IAAI,iBAAiB;wBACnB,QAAQ,IAAI,CAAC;wBACb,aAAa,UAAU;wBACvB,kBAAkB,EAAE;oBACtB,OAAO;wBACL,kBAAkB;oBACpB;oBAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,iBAAiB,EAAE,WAAW;gBACrE,OAAO;oBACL,kBAAkB,EAAE;gBACtB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC,WAAW;gBAC5D,MAAM;oBACJ,SAAS;oBACT,OAAO;oBACP,aAAa;gBACf;YACF,SAAU;gBACR,aAAa;YACf;QACF;8DAAG;QAAC;QAAc;KAAM;IAExB,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,QAAQ,GAAG,CAAC,sCAAsC;gBAChD;gBACA,aAAa,OAAO,MAAM;gBAC1B,cAAc,cAAc,gBAAgB,cAAc,QAAQ;gBAClE,uBAAuB,CAAC,CAAC;YAC3B;YAEA,IAAI,CAAC,cAAc;gBACjB,uDAAuD;gBACvD,MAAM,QAAQ;wDAAW;wBACvB,IAAI,OAAO,MAAM,KAAK,GAAG;4BACvB,2CAA2C;4BAC3C,QAAQ,GAAG,CAAC;4BACZ,IAAI;gCAAE,OAAO,QAAQ,CAAC;4BAAmB,EAAE,OAAM,CAAE;4BACnD,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,OAAO,MAAM,GAAG,KAAK,CAAC,cAAc;4BAC7C,wCAAwC;4BACxC,QAAQ,GAAG,CAAC;4BACZ,MAAM,WAAW;4BAEjB,IAAI,CAAC,UAAU;gCACb,qDAAqD;gCACrD,QAAQ,GAAG,CAAC,4CAA4C,MAAM,CAAC,EAAE,CAAC,YAAY,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI;gCAChG,YAAY,MAAM,CAAC,EAAE;4BACvB;wBACF;oBACF;uDAAG,OAAO,iBAAiB;gBAE3B;kDAAO,IAAM,aAAa;;YAC5B;QACF;qCAAG;QAAC;QAAc,OAAO,MAAM;QAAE;QAAc;QAAQ;QAAa;KAAkB;IAGtF,oCAAoC;IACpC,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,wDAAwD;YACxD,IAAI,IAAI,UAAU,CAAC,UAAU;gBAC3B,MAAM,cAAc,KAAK,KAAK,CAAC,AAAC,IAAI,MAAM,GAAG,IAAK;gBAClD,IAAI,eAAe,MAAM,MAAM;oBAC7B,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACT;YACF;YAEA,2DAA2D;YAC3D,IAAI,IAAI,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,SAAS;gBACrD,QAAQ,GAAG,CAAC,6CAA6C,IAAI,SAAS,CAAC,GAAG,MAAM;gBAEhF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;gBAE3D,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,yCAAyC;gBACzC,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM;oBAC3B,QAAQ,IAAI,CAAC;oBACb,OAAO;gBACT;gBAEA,sBAAsB;gBACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;oBAC3B,MAAM,SAAS,IAAI;oBACnB,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;oBAC3C,OAAO,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;oBACxC,OAAO,aAAa,CAAC;gBACvB;YACF;YAEA,6CAA6C;YAC7C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,mCAAmC;YAChD,OAAO,KAAK,iCAAiC;QAC/C;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,QAAQ,GAAG,CAAC;QAEZ,qEAAqE;QACrE,IAAI,gBAAgB;YAAE,GAAG,IAAI;QAAC;QAE9B,IAAI;YACF,mCAAmC;YACnC,IAAI,KAAK,QAAQ,EAAE;gBACjB,MAAM,eAAe,MAAM,gBAAgB,KAAK,QAAQ;gBACxD,cAAc,QAAQ,GAAG;YAC3B;YAEA,6BAA6B;YAC7B,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC7C,MAAM,oBAAoB,MAAM,QAAQ,GAAG,CACzC,KAAK,QAAQ,CAAC,GAAG,CAAC,OAAO;oBACvB,IAAI,QAAQ,QAAQ,EAAE;wBACpB,MAAM,eAAe,MAAM,gBAAgB,QAAQ,QAAQ;wBAC3D,OAAO;4BAAE,GAAG,OAAO;4BAAE,UAAU;wBAAa;oBAC9C;oBACA,OAAO;gBACT;gBAEF,cAAc,QAAQ,GAAG;YAC3B;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,qDAAqD;YAClE,gBAAgB,MAAM,yCAAyC;QACjE;QAEA,+EAA+E;QAC/E,MAAM,WAAW;YAAC;eAAkB;SAAe,CAAC,KAAK,CAAC,GAAG;QAC7D,kBAAkB;QAElB,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC;YACb,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI;YACF,oEAAoE;YACpE,aAAa,OAAO,CAAC;YACrB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,iBAAiB,EAAE,cAAc,gBAAgB,cAAc,MAAM;QAC/G,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YAEvD,mCAAmC;YACnC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YAEA,gDAAgD;YAChD,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,sDAAsD;IACtD,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,aAAa,UAAU;YACvB,kBAAkB,EAAE;YACpB,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,uCAAuC,EAAE,cAAc,gBAAgB,cAAc,KAAK,CAAC,CAAC;YAC5G;YACA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,cAAc,gBAAgB,cAAc,MAAM;QACnG,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,cAAc;YACjB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,MAAM,eAAe,eAAe,GAAG,CAAC,CAAC,OACvC,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;YAE7C,kBAAkB;YAElB,mCAAmC;YACnC,MAAM,YAAY,KAAK,SAAS,CAAC;YACjC,MAAM,UAAU,IAAI,OAAO,MAAM,YAAY;YAE7C,IAAI,UAAU,MAAM,GAAG,SAAS;gBAC9B,iCAAiC;gBACjC,MAAM,eAAe,aAAa,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,qBAAqB;gBACvF,aAAa,OAAO,CAAC;gBACrB,kBAAkB;gBAElB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,aAAa,OAAO,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,cAAc,gBAAgB,cAAc,MAAM;QAC7F,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,eAAY;QAAsC,SAAS;;0BAC1D,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAO,cAAc,sCAAsC;0CAE1D,4BACC,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;yDAE1B,6LAAC,+NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAG7B,6LAAC;gCAAK,WAAU;0CACb,cAAc,oBAAoB;;;;;;;;;;;;kCAIvC,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,MAAK;oCAAO,WAAU;;sDAChD,6LAAC,qIAAA,CAAA,SAAM;;8DACL,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAI;oDAAiC,KAAI;oDAAO,gBAAa;;;;;;8DAC1E,6LAAC,qIAAA,CAAA,iBAAc;8DAAC,cAAA,6LAAC,qMAAA,CAAA,OAAI;;;;;;;;;;;;;;;;sDAEvB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAM;;kDACzB,6LAAC,+IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kDACtB,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS;4CACP,IAAI,cAAc;gDAChB,MAAM,UAAU,0BAA0B;gDAC1C,IAAI,SAAS;oDACX,kBAAkB;oDAClB,MAAM;wDACJ,OAAO;wDACP,aAAa,CAAC,wBAAwB,EAAE,cAAc,gBAAgB,cAAc,KAAK,CAAC,CAAC;oDAC7F;gDACF,OAAO;oDACL,MAAM;wDACJ,OAAO;wDACP,aAAa;oDACf;gDACF;4CACF,OAAO;gDACL,MAAM;oDACJ,SAAS;oDACT,OAAO;oDACP,aAAa;gDACf;4CACF;wCACF;kDACD;;;;;;;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,aAAa,6BACZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;kDAAE;;;;;;;;;;;;;;;uCAGL,CAAC,6BACH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;oCAGhD,OAAO,MAAM,GAAG,kBACf,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAS,IAAM,YAAY;gDAC3B,SAAQ;0DAEP,MAAM,YAAY,IAAI,MAAM,IAAI;+CAJ5B,MAAM,EAAE;;;;;;;;;6DASnB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,cAAc,IAAM,OAAO,QAAQ,CAAC;wCAAmB,SAAS,IAAM,OAAO,QAAQ,CAAC;wCAAmB,SAAS,IAAM,OAAO,IAAI,CAAC;kDAAmB;;;;;;;;;;;qDAMnK,6LAAC;gCAAI,WAAU;0CAeZ,8BACC,6LAAC,yJAAA,CAAA,kBAAe;oCACd,cAAc;wCACZ,cAAc,aAAa,YAAY;wCACvC,cAAc,aAAa,YAAY,IAAI;wCAC3C,UAAU,aAAa,QAAQ,IAAI;wCACnC,aAAa,aAAa,WAAW,IAAI;wCACzC,aAAa,aAAa,WAAW,IAAI;wCACzC,aAAa,aAAa,WAAW,IAAI;wCACzC,eAAe,aAAa,aAAa,IAAI;wCAC7C,YAAY,aAAa,UAAU,IAAI;wCACvC,aAAa,aAAa,WAAW,IAAI;wCACzC,oFAAoF;wCACpF,UAAU,MAAM,OAAO,CAAC,AAAC,aAAqB,QAAQ,IAClD,AAAC,aAAqB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAW,EAAE,IAAI,EAAE,IAAI,CAAC,QAC5D,AAAC,aAAqB,QAAQ,IAAI;wCACtC,gBAAgB,aAAa,cAAc,IAAI;wCAC/C,aAAa,aAAa,WAAW,IAAI;wCACzC,uBAAuB,aAAa,qBAAqB,IAAI;wCAC7D,aAAa;4CACX,OAAO,aAAa,YAAY,IAAI;4CACpC,OAAO,aAAa,YAAY,IAAI;4CACpC,SAAS,aAAa,cAAc,IAAI;wCAC1C;wCACA,aAAa;4CACX,UAAU,aAAa,WAAW,IAAI;4CACtC,WAAW,aAAa,YAAY,IAAI;4CACxC,SAAS,aAAa,UAAU,IAAI;4CACpC,UAAU,aAAa,WAAW,IAAI;wCACxC;wCACA,cAAc,aAAa,YAAY,IAAI;wCAC3C,aAAa,aAAa,WAAW,IAAI;wCACzC,iBAAiB,aAAa,eAAe,IAAI;wCACjD,gBAAgB,aAAa,cAAc,IAAI,EAAE;oCACnD;oCACA,OAAO;oCACP,iBAAiB;oCACjB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAvJd,cAAc,MAAM;;;;;AAkK3C;GA/cS;;QAC8D,kJAAA,CAAA,kBAAe;QAC/D,kJAAA,CAAA,kBAAe;QAGrB,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,WAAQ;QACmB,sIAAA,CAAA,aAAU;QAuCvD,kJAAA,CAAA,yBAAsB;;;KA9Cf;AAidT,SAAS;IACP,qBACE,6LAAC,6JAAA,CAAA,qBAAkB;;0BACjB,6LAAC;;;;;0BACD,6LAAC,6JAAA,CAAA,uBAAoB;;;;;;;;;;;AAG3B;MAPS;uCASM", "debugId": null}}]}
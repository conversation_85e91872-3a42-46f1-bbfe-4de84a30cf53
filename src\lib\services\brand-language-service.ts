/**
 * Brand Language Service
 * Automatically detects and manages language settings for brand profiles
 */

import { 
  detectLanguagesForLocation, 
  generateLanguageInstruction,
  type LocationLanguageMapping 
} from './language-detection-service';
import type { BrandProfile, CompleteBrandProfile } from '@/lib/types';

/**
 * Detect and populate language information for a brand profile
 */
export function detectAndPopulateLanguages<T extends BrandProfile | CompleteBrandProfile>(
  profile: T
): T {
  if (!profile.location) {
    console.warn('Brand Language Service: No location provided, skipping language detection');
    return profile;
  }

  const languageMapping = detectLanguagesForLocation(profile.location);
  
  const detectedLanguages = {
    primary: {
      code: languageMapping.primaryLanguage.code,
      name: languageMapping.primaryLanguage.name,
      nativeName: languageMapping.primaryLanguage.nativeName,
      isRTL: languageMapping.primaryLanguage.isRTL
    },
    secondary: languageMapping.secondaryLanguages?.map(lang => ({
      code: lang.code,
      name: lang.name,
      nativeName: lang.nativeName,
      isRTL: lang.isRTL
    })),
    region: languageMapping.region,
    lastDetected: new Date().toISOString()
  };

  console.log(`Brand Language Service: Detected languages for "${profile.location}":`, {
    primary: detectedLanguages.primary.name,
    secondary: detectedLanguages.secondary?.map(l => l.name).join(', ') || 'None',
    region: detectedLanguages.region
  });

  return {
    ...profile,
    detectedLanguages
  };
}

/**
 * Check if language detection needs to be updated
 */
export function shouldUpdateLanguageDetection(profile: BrandProfile | CompleteBrandProfile): boolean {
  // Always update if no language detection exists
  if (!profile.detectedLanguages) {
    return true;
  }

  // Update if location has changed (we can't track this directly, but we can check if detection is old)
  const lastDetected = profile.detectedLanguages.lastDetected;
  if (!lastDetected) {
    return true;
  }

  // Update if detection is older than 30 days (in case our mapping improves)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  return new Date(lastDetected) < thirtyDaysAgo;
}

/**
 * Get language instruction for AI content generation
 */
export function getLanguageInstructionForProfile(profile: BrandProfile | CompleteBrandProfile): string {
  // If we have detected languages, use them
  if (profile.detectedLanguages) {
    const primary = profile.detectedLanguages.primary;
    const secondary = profile.detectedLanguages.secondary || [];
    
    let instruction = `LANGUAGE REQUIREMENTS FOR ${profile.location.toUpperCase()}:\n`;
    instruction += `PRIMARY LANGUAGE: Use ${primary.name} (${primary.nativeName}) as the main language for content generation.\n`;
    
    if (secondary.length > 0) {
      const secondaryNames = secondary.map(lang => `${lang.name} (${lang.nativeName})`).join(', ');
      instruction += `SECONDARY LANGUAGES: ${secondaryNames} may be used when culturally appropriate.\n`;
    }
    
    if (primary.isRTL) {
      instruction += `RTL SUPPORT: This language uses right-to-left text direction.\n`;
    }
    
    instruction += `REGION: Content is for ${profile.location} in the ${profile.detectedLanguages.region} region.\n`;
    instruction += `CRITICAL: Only use languages appropriate for this specific location (${profile.location}). Do not use random or incorrect languages like Arabic for non-Arabic countries.\n`;
    instruction += `QUALITY: Ensure all text is clear, professional, and grammatically correct in the chosen language.`;
    
    return instruction;
  }

  // Fallback to location-based detection
  return generateLanguageInstruction(profile.location || 'Global');
}

/**
 * Validate that content matches the expected language for a profile
 */
export function validateContentLanguage(
  content: string, 
  profile: BrandProfile | CompleteBrandProfile
): {
  isValid: boolean;
  expectedLanguage: string;
  detectedIssues: string[];
} {
  const issues: string[] = [];
  
  if (!profile.detectedLanguages) {
    return {
      isValid: true,
      expectedLanguage: 'Unknown',
      detectedIssues: ['No language detection available']
    };
  }

  const expectedLang = profile.detectedLanguages.primary;
  
  // Basic validation - check for common issues
  
  // Check for Arabic characters when not expected
  if (!expectedLang.isRTL && /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(content)) {
    issues.push(`Contains Arabic characters but location "${profile.location}" does not use Arabic`);
  }
  
  // Check for Chinese characters when not expected
  if (expectedLang.code !== 'zh' && expectedLang.code !== 'zh-CN' && expectedLang.code !== 'zh-TW' && 
      /[\u4e00-\u9fff\u3400-\u4dbf\u20000-\u2a6df\u2a700-\u2b73f\u2b740-\u2b81f\u2b820-\u2ceaf]/.test(content)) {
    issues.push(`Contains Chinese characters but location "${profile.location}" does not use Chinese`);
  }
  
  // Check for Hindi characters when not expected
  if (expectedLang.code !== 'hi' && /[\u0900-\u097F]/.test(content)) {
    issues.push(`Contains Hindi characters but location "${profile.location}" does not use Hindi`);
  }
  
  // Check for corrupted or gibberish text
  if (/[^\w\s\-.,!?@#$%^&*()+={}[\]|\\:";'<>\/~`]/.test(content.replace(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\u4e00-\u9fff\u3400-\u4dbf\u0900-\u097F]/g, ''))) {
    issues.push('Contains potentially corrupted or unreadable characters');
  }

  return {
    isValid: issues.length === 0,
    expectedLanguage: expectedLang.name,
    detectedIssues: issues
  };
}

/**
 * Auto-update language detection for existing profiles
 */
export function updateLanguageDetectionIfNeeded<T extends BrandProfile | CompleteBrandProfile>(
  profile: T
): T {
  if (shouldUpdateLanguageDetection(profile)) {
    console.log(`Brand Language Service: Updating language detection for "${profile.businessName}"`);
    return detectAndPopulateLanguages(profile);
  }
  
  return profile;
}

/**
 * Get supported languages summary for a profile
 */
export function getLanguagesSummary(profile: BrandProfile | CompleteBrandProfile): string {
  if (!profile.detectedLanguages) {
    return 'Language detection not available';
  }

  const primary = profile.detectedLanguages.primary.name;
  const secondary = profile.detectedLanguages.secondary?.map(l => l.name).join(', ');
  
  let summary = `Primary: ${primary}`;
  if (secondary) {
    summary += `, Secondary: ${secondary}`;
  }
  summary += ` (${profile.detectedLanguages.region})`;
  
  return summary;
}

/**
 * Export language detection data for debugging
 */
export function exportLanguageDetectionData(profile: BrandProfile | CompleteBrandProfile) {
  return {
    location: profile.location,
    detectedLanguages: profile.detectedLanguages,
    languageInstruction: getLanguageInstructionForProfile(profile),
    summary: getLanguagesSummary(profile),
    needsUpdate: shouldUpdateLanguageDetection(profile)
  };
}

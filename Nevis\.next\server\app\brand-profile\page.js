const CHUNK_PUBLIC_PATH = "server/app/brand-profile/page.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_a17f26a9._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__292a8cba._.js");
runtime.loadChunk("server/chunks/ssr/[externals]_next_dist_compiled_@vercel_og_index_node_d5c80dd1.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__7d074629._.js");
runtime.loadChunk("server/chunks/ssr/src_app_layout_tsx_41fb020b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/_76336d53._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__fd036d4e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_759434b3._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@genkit-ai_core_lib_b4b8dea8._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_lib_9973ecc6._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ajv_dist_e6327f72._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod-to-json-schema_dist_cfb46992._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_core_build_esm_ac2e01f7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_semantic-conventions_build_esm_17a93919._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_resources_build_esm_eadd61b9._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_sdk-metrics_build_esm_2bc910b7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_sdk-trace-base_build_esm_ffb73884._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_protobufjs_c6ba58b0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_otlp-transformer_build_esm_7b4c78bd._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@grpc_grpc-js_cc2986af._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_handlebars_097a2825._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_yaml_dist_5cbea403._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@genkit-ai_ai_lib_3128529a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@google_generative-ai_dist_6df4b23c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_web-streams-polyfill_dist_ponyfill_es2018_8f4a41dd.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_371ac9f5._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_2e40c601._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/brand-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/actions/revo-2-actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/ai/flows/generate-creative-asset.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/brand-profile/page { METADATA_0 => \"[project]/src/app/icon--metadata.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/brand-profile/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/brand-profile/page { METADATA_0 => \"[project]/src/app/icon--metadata.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/brand-profile/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;

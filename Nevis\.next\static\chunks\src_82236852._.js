(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/studio/chat-avatar.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-avatar.tsx
__turbopack_context__.s({
    "ChatAvatar": (()=>ChatAvatar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-client] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-client] (ecmascript)");
;
;
;
function ChatAvatar({ role }) {
    if (role === 'user') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
            className: "h-8 w-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                    className: "h-4 w-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-avatar.tsx",
                    lineNumber: 14,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-avatar.tsx",
                lineNumber: 13,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-avatar.tsx",
            lineNumber: 12,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
        className: "h-8 w-8 bg-primary text-primary-foreground",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-avatar.tsx",
                lineNumber: 23,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-avatar.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-avatar.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
_c = ChatAvatar;
var _c;
__turbopack_context__.k.register(_c, "ChatAvatar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/chat-messages.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-messages.tsx
__turbopack_context__.s({
    "ChatMessages": (()=>ChatMessages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-client] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-client] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tooltip.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
function ChatMessages({ messages, isLoading, onSetReferenceAsset, onEditImage }) {
    _s();
    const scrollableContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatMessages.useEffect": ()=>{
            if (scrollableContainerRef.current) {
                scrollableContainerRef.current.scrollTop = scrollableContainerRef.current.scrollHeight;
            }
        }
    }["ChatMessages.useEffect"], [
        messages,
        isLoading
    ]);
    const handleDownload = async (url, type)=>{
        if (!url) {
            toast({
                variant: 'destructive',
                title: 'Download Failed',
                description: 'No asset URL found.'
            });
            return;
        }
        try {
            // Download the original HD file directly to preserve quality
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            const fileExtension = type === 'image' ? 'png' : 'mp4';
            link.download = `nevis-hd-${type}-${Date.now()}.${fileExtension}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);
            toast({
                title: 'HD Download Complete',
                description: `High-definition ${type} downloaded successfully.`
            });
        } catch (error) {
            console.error('Download failed:', error);
            toast({
                variant: 'destructive',
                title: 'Download Failed',
                description: `Could not download the ${type}. Please try again.`
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: scrollableContainerRef,
        className: "flex-1 overflow-y-auto p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mx-auto max-w-3xl space-y-6",
                children: [
                    messages.map((message, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('flex items-start gap-4', message.role === 'user' && 'justify-end'),
                            children: [
                                message.role === 'assistant' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                    role: "assistant"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 79,
                                    columnNumber: 48
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('max-w-[80%] space-y-2 rounded-lg p-3', message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'),
                                    children: [
                                        message.role === 'user' && message.imageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative w-48 h-48 overflow-hidden rounded-md border",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                src: message.imageUrl,
                                                alt: "User upload preview",
                                                layout: "fill",
                                                objectFit: "cover"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                lineNumber: 89,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 88,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "whitespace-pre-wrap text-sm",
                                            children: message.content
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 93,
                                            columnNumber: 17
                                        }, this),
                                        message.role === 'assistant' && message.imageUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "group relative w-full max-w-sm overflow-hidden rounded-md border",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    src: message.imageUrl,
                                                    alt: "Generated image",
                                                    width: 512,
                                                    height: 512,
                                                    className: "w-full h-auto object-contain",
                                                    crossOrigin: "anonymous"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 98,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onEditImage(message.imageUrl),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 108,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Edit Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 109,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 102,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 101,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Edit with Inpainting"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 112,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 100,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onSetReferenceAsset(message.imageUrl, 'image'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 122,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Refine Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 123,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 116,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 115,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Refine this image (new prompt)"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 126,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 114,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>handleDownload(message.imageUrl, 'image'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 136,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Download Image"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 137,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 130,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 129,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Download image"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 140,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 128,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 99,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 97,
                                            columnNumber: 19
                                        }, this),
                                        message.role === 'assistant' && message.videoUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "group relative w-full max-w-sm overflow-hidden rounded-md border",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                                    controls: true,
                                                    autoPlay: true,
                                                    src: message.videoUrl,
                                                    className: "w-full"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 149,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute top-2 right-2 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>onSetReferenceAsset(message.videoUrl, 'video'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 159,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Refine Video"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 160,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 153,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 152,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Refine this video (new prompt)"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 163,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 151,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                                    asChild: true,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        variant: "secondary",
                                                                        size: "icon",
                                                                        className: "h-8 w-8",
                                                                        onClick: ()=>handleDownload(message.videoUrl, 'video'),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                                className: "h-4 w-4"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 173,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "sr-only",
                                                                                children: "Download Video"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                                lineNumber: 174,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                        lineNumber: 167,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 166,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                                    children: "Download video"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                                    lineNumber: 177,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                            lineNumber: 165,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                                    lineNumber: 150,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                                            lineNumber: 148,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 80,
                                    columnNumber: 15
                                }, this),
                                message.role === 'user' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                    role: "user"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-messages.tsx",
                                    lineNumber: 184,
                                    columnNumber: 43
                                }, this)
                            ]
                        }, index, true, {
                            fileName: "[project]/src/components/studio/chat-messages.tsx",
                            lineNumber: 78,
                            columnNumber: 13
                        }, this)),
                    isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatAvatar"], {
                                role: "assistant"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                lineNumber: 190,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2 rounded-lg bg-muted p-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                        className: "h-5 w-5 animate-spin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                        lineNumber: 192,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm",
                                        children: "Generating..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                                        lineNumber: 193,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/chat-messages.tsx",
                                lineNumber: 191,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/chat-messages.tsx",
                        lineNumber: 189,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/studio/chat-messages.tsx",
                lineNumber: 76,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/studio/chat-messages.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-messages.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
}
_s(ChatMessages, "KRbjU4Xhyo/ff6LNIhRbDr74M+M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = ChatMessages;
var _c;
__turbopack_context__.k.register(_c, "ChatMessages");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 8,
        columnNumber: 7
    }, this);
});
_c1 = Textarea;
Textarea.displayName = 'Textarea';
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Textarea$React.forwardRef");
__turbopack_context__.k.register(_c1, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/label.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const labelVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");
const Label = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(labelVariants(), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/label.tsx",
        lineNumber: 18,
        columnNumber: 3
    }, this));
_c1 = Label;
Label.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Label$React.forwardRef");
__turbopack_context__.k.register(_c1, "Label");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/switch.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Switch": (()=>Switch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-switch/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
const Switch = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input", className),
        ...props,
        ref: ref,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Thumb"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")
        }, void 0, false, {
            fileName: "[project]/src/components/ui/switch.tsx",
            lineNumber: 20,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/switch.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
_c1 = Switch;
Switch.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$switch$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Switch$React.forwardRef");
__turbopack_context__.k.register(_c1, "Switch");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/radio-group.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "RadioGroup": (()=>RadioGroup),
    "RadioGroupItem": (()=>RadioGroupItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-radio-group/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle.js [app-client] (ecmascript) <export default as Circle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const RadioGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("grid gap-2", className),
        ...props,
        ref: ref
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radio-group.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
});
_c1 = RadioGroup;
RadioGroup.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
const RadioGroupItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c2 = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Indicator"], {
            className: "flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__["Circle"], {
                className: "h-2.5 w-2.5 fill-current text-current"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/radio-group.tsx",
                lineNumber: 37,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/radio-group.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radio-group.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
});
_c3 = RadioGroupItem;
RadioGroupItem.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Item"].displayName;
;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "RadioGroup$React.forwardRef");
__turbopack_context__.k.register(_c1, "RadioGroup");
__turbopack_context__.k.register(_c2, "RadioGroupItem$React.forwardRef");
__turbopack_context__.k.register(_c3, "RadioGroupItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/revo-model-selector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "REVO_MODELS": (()=>REVO_MODELS),
    "RevoModelSelector": (()=>RevoModelSelector)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-client] (ecmascript) <export default as Check>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sparkles.js [app-client] (ecmascript) <export default as Sparkles>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-client] (ecmascript) <export default as Zap>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rocket$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Rocket$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rocket.js [app-client] (ecmascript) <export default as Rocket>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
const REVO_MODELS = [
    {
        id: 'revo-1.0',
        name: 'Revo 1.0',
        description: 'Standard Model - Stable Foundation',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Zap$3e$__["Zap"],
        badge: 'Stable',
        badgeVariant: 'secondary',
        features: [
            'Reliable AI Engine',
            '1:1 Images',
            'Core Features',
            'Proven Performance'
        ],
        status: 'stable'
    },
    {
        id: 'revo-1.5',
        name: 'Revo 1.5',
        description: 'Enhanced Model - Advanced Features',
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sparkles$3e$__["Sparkles"],
        badge: 'Enhanced',
        badgeVariant: 'default',
        features: [
            'Advanced AI Engine',
            'Superior Quality',
            'Enhanced Design',
            'Smart Optimizations'
        ],
        status: 'enhanced'
    }
];
function RevoModelSelector({ selectedModel, onModelChange, disabled = false, className }) {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const currentModel = REVO_MODELS.find((model)=>model.id === selectedModel) || REVO_MODELS[0];
    const CurrentIcon = currentModel.icon;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
        open: isOpen,
        onOpenChange: setIsOpen,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                asChild: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    variant: "outline",
                    disabled: disabled,
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("min-w-[180px] justify-between", className),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CurrentIcon, {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 84,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: currentModel.name
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 85,
                                    columnNumber: 13
                                }, this),
                                currentModel.badge && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: currentModel.badgeVariant,
                                    className: "text-xs",
                                    children: currentModel.badge
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 87,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 83,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                            className: "h-4 w-4 opacity-50"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                    lineNumber: 75,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                lineNumber: 74,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                align: "start",
                className: "w-80",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rocket$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Rocket$3e$__["Rocket"], {
                                className: "w-4 h-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                lineNumber: 98,
                                columnNumber: 11
                            }, this),
                            "Select Revo Model"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 97,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 101,
                        columnNumber: 9
                    }, this),
                    REVO_MODELS.map((model)=>{
                        const IconComponent = model.icon;
                        const isSelected = selectedModel === model.id;
                        const isAvailable = model.status !== 'development';
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuItem"], {
                            onClick: ()=>{
                                if (isAvailable) {
                                    onModelChange(model.id);
                                    setIsOpen(false);
                                }
                            },
                            disabled: !isAvailable,
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col items-start gap-2 p-4 cursor-pointer", !isAvailable && "opacity-50 cursor-not-allowed"),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(IconComponent, {
                                                    className: "h-4 w-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 125,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: model.name
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 126,
                                                    columnNumber: 19
                                                }, this),
                                                model.badge && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                    variant: model.badgeVariant,
                                                    className: "text-xs",
                                                    children: model.badge
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                                    lineNumber: 128,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 124,
                                            columnNumber: 17
                                        }, this),
                                        isSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Check$3e$__["Check"], {
                                            className: "h-4 w-4 text-green-600"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 133,
                                            columnNumber: 32
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 123,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-muted-foreground",
                                    children: model.description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 136,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap gap-1",
                                    children: model.features.map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "outline",
                                            className: "text-xs",
                                            children: feature
                                        }, index, false, {
                                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                            lineNumber: 142,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                                    lineNumber: 140,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, model.id, true, {
                            fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                            lineNumber: 109,
                            columnNumber: 13
                        }, this);
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuSeparator"], {}, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 151,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 text-xs text-muted-foreground",
                        children: "Each Revo model offers different capabilities and features"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                        lineNumber: 152,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/revo-model-selector.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/revo-model-selector.tsx",
        lineNumber: 73,
        columnNumber: 5
    }, this);
}
_s(RevoModelSelector, "+sus0Lb0ewKHdwiUhiTAJFoFyQ0=");
_c = RevoModelSelector;
;
var _c;
__turbopack_context__.k.register(_c, "RevoModelSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/chat-input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-input.tsx
__turbopack_context__.s({
    "ChatInput": (()=>ChatInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$paperclip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Paperclip$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/paperclip.js [app-client] (ecmascript) <export default as Paperclip>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-client] (ecmascript) <export default as Send>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/image.js [app-client] (ecmascript) <export default as Image>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/video.js [app-client] (ecmascript) <export default as Video>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-client] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-client] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tooltip.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/switch.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/radio-group.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$revo$2d$model$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/revo-model-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
function ChatInput({ input, setInput, handleSubmit, isLoading, imagePreview, setImagePreview, setImageDataUrl, useBrandProfile, setUseBrandProfile, outputType, setOutputType, handleImageUpload, isBrandProfileAvailable, onEditImage, aspectRatio, setAspectRatio, selectedRevoModel, setSelectedRevoModel, userCredits }) {
    _s();
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const handleKeyDown = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const form = e.currentTarget.form;
            if (form) {
                form.requestSubmit();
            }
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatInput.useEffect": ()=>{
            if (inputRef.current) {
                inputRef.current.style.height = 'auto';
                inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
            }
        }
    }["ChatInput.useEffect"], [
        input
    ]);
    const handleRemoveImage = ()=>{
        setImagePreview(null);
        setImageDataUrl(null);
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative mt-auto w-full border-t",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            onSubmit: handleSubmit,
            className: "p-4 space-y-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        imagePreview && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "group absolute bottom-full mb-2 w-24 h-24",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: imagePreview,
                                    alt: "Image preview",
                                    layout: "fill",
                                    objectFit: "cover",
                                    className: "rounded-md"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 93,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-md",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                    asChild: true,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "ghost",
                                                        size: "icon",
                                                        className: "h-8 w-8 text-white hover:bg-white/20 hover:text-white",
                                                        onClick: ()=>onEditImage(imagePreview),
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "sr-only",
                                                                children: "Edit image"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                                lineNumber: 105,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                                lineNumber: 106,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                                        lineNumber: 98,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 97,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                    children: "Edit this image"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 109,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 96,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 95,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 94,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "button",
                                    variant: "ghost",
                                    size: "icon",
                                    className: "absolute -right-2 -top-2 h-6 w-6 rounded-full bg-background",
                                    onClick: handleRemoveImage,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Remove image"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 120,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                            className: "h-4 w-4"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 121,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 113,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 92,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                            ref: inputRef,
                            value: input,
                            onChange: (e)=>setInput(e.target.value),
                            onKeyDown: handleKeyDown,
                            placeholder: "Describe the image or video you want to create...",
                            className: "pr-20 resize-none min-h-[4rem] max-h-40",
                            rows: 1,
                            disabled: isLoading
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                asChild: true,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                    type: "button",
                                                    size: "icon",
                                                    variant: "ghost",
                                                    onClick: ()=>fileInputRef.current?.click(),
                                                    disabled: isLoading,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$paperclip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Paperclip$3e$__["Paperclip"], {}, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 140,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: "Attach image"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 141,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 139,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 138,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                children: "Attach a reference image"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 144,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 137,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "file",
                                    ref: fileInputRef,
                                    className: "hidden",
                                    accept: "image/*",
                                    onChange: handleImageUpload
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 147,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    type: "submit",
                                    size: "icon",
                                    variant: "ghost",
                                    disabled: isLoading || !input,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__["Send"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 155,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Send message"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 156,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 154,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/chat-input.tsx",
                    lineNumber: 90,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col sm:flex-row items-center justify-between gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$switch$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Switch"], {
                                    id: "brand-profile-switch",
                                    checked: useBrandProfile,
                                    onCheckedChange: setUseBrandProfile,
                                    disabled: !isBrandProfileAvailable
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 163,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    htmlFor: "brand-profile-switch",
                                    children: "Apply Brand Profile"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 169,
                                    columnNumber: 13
                                }, this),
                                !isBrandProfileAvailable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                                                asChild: true,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-xs text-muted-foreground",
                                                    children: "(No profile found)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 173,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: "Go to Brand Profile to set one up."
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/chat-input.tsx",
                                                lineNumber: 176,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 172,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 171,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 162,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "AI Model:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 185,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$revo$2d$model$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RevoModelSelector"], {
                                    selectedModel: selectedRevoModel,
                                    onModelChange: setSelectedRevoModel,
                                    disabled: !isBrandProfileAvailable || outputType !== 'image',
                                    showCredits: true,
                                    userCredits: userCredits
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 186,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 184,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {
                            orientation: "vertical",
                            className: "h-6 hidden sm:block"
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 195,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "Output Type:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 198,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroup"], {
                                    value: outputType,
                                    onValueChange: (v)=>setOutputType(v),
                                    className: "flex items-center space-x-4",
                                    disabled: isLoading,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "image",
                                                    id: "r-image"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 201,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-image",
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__["Image"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 202,
                                                            columnNumber: 78
                                                        }, this),
                                                        " Image"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 202,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 200,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "video",
                                                    id: "r-video"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-video",
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Video$3e$__["Video"], {
                                                            className: "h-4 w-4"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                                            lineNumber: 206,
                                                            columnNumber: 78
                                                        }, this),
                                                        " Video"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 206,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 204,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 199,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 197,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center space-x-4", outputType === 'video' ? 'opacity-100' : 'opacity-0'),
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                    children: "Aspect Ratio:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 212,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroup"], {
                                    value: aspectRatio,
                                    onValueChange: (v)=>setAspectRatio(v),
                                    className: "flex items-center space-x-4",
                                    disabled: isLoading || outputType !== 'video',
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "16:9",
                                                    id: "r-16-9"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 215,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-16-9",
                                                    className: "flex items-center gap-2",
                                                    children: "16:9 (Sound)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 216,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 214,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                                    value: "9:16",
                                                    id: "r-9-16"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 219,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                                    htmlFor: "r-9-16",
                                                    className: "flex items-center gap-2",
                                                    children: "9:16 (No Sound)"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/studio/chat-input.tsx",
                                            lineNumber: 218,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/studio/chat-input.tsx",
                                    lineNumber: 213,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isLoading || !input,
                            children: isLoading ? 'Generating...' : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-input.tsx",
                                        lineNumber: 226,
                                        columnNumber: 46
                                    }, this),
                                    " Generate"
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/chat-input.tsx",
                            lineNumber: 225,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/chat-input.tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/studio/chat-input.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/studio/chat-input.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
_s(ChatInput, "kyX8tf4W/Ccr5InjDYL8qSDZnvE=");
_c = ChatInput;
var _c;
__turbopack_context__.k.register(_c, "ChatInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:ef11a5 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f5984969144514912b21f6a991662f103d7b9ea01":"generateCreativeAssetAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateCreativeAssetAction": (()=>generateCreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f5984969144514912b21f6a991662f103d7b9ea01", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/data:d3c1bc [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f55dc402a959547c09759f8ea54339b9fcdcd1f9b":"generateEnhancedDesignAction"},"src/app/actions.ts",""] */ __turbopack_context__.s({
    "generateEnhancedDesignAction": (()=>generateEnhancedDesignAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateEnhancedDesignAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("7f55dc402a959547c09759f8ea54339b9fcdcd1f9b", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateEnhancedDesignAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/actions/data:6eb872 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"70e9cf46b5f4301aa97f030c1535b69f9f2cc39636":"generateRevo2CreativeAssetAction"},"src/app/actions/revo-2-actions.ts",""] */ __turbopack_context__.s({
    "generateRevo2CreativeAssetAction": (()=>generateRevo2CreativeAssetAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateRevo2CreativeAssetAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("70e9cf46b5f4301aa97f030c1535b69f9f2cc39636", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateRevo2CreativeAssetAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/chat-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/chat-layout.tsx
__turbopack_context__.s({
    "ChatLayout": (()=>ChatLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$messages$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-messages.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$wrap$2d$balancer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-wrap-balancer/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bot.js [app-client] (ecmascript) <export default as Bot>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ef11a5__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:ef11a5 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$d3c1bc__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:d3c1bc [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$data$3a$6eb872__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/actions/data:6eb872 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
function ChatLayout({ brandProfile, onEditImage }) {
    _s();
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [input, setInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [imagePreview, setImagePreview] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [imageDataUrl, setImageDataUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [useBrandProfile, setUseBrandProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!!brandProfile);
    const [outputType, setOutputType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('image');
    const [aspectRatio, setAspectRatio] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('16:9');
    const [selectedRevoModel, setSelectedRevoModel] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('revo-1.5');
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatLayout.useEffect": ()=>{
            setUseBrandProfile(!!brandProfile);
        }
    }["ChatLayout.useEffect"], [
        brandProfile
    ]);
    const handleImageUpload = (event)=>{
        const file = event.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = ()=>{
                const dataUrl = reader.result;
                setImagePreview(dataUrl);
                setImageDataUrl(dataUrl);
            };
            reader.readAsDataURL(file);
        }
    };
    const handleSetReferenceAsset = (url, type)=>{
        if (url) {
            setOutputType(type);
            setImagePreview(url); // Using imagePreview for both image and video previews in the input area.
            setImageDataUrl(url);
        }
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!input.trim()) {
            toast({
                variant: 'destructive',
                title: 'Input Required',
                description: 'Please describe the image or video you want to create.'
            });
            return;
        }
        ;
        const newUserMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: input,
            // For simplicity, we just show the preview, which could be an image data URL for a video.
            imageUrl: imagePreview
        };
        setMessages([
            ...messages,
            newUserMessage
        ]);
        const currentInput = input;
        const currentImageDataUrl = imageDataUrl;
        setInput('');
        setImagePreview(null);
        setImageDataUrl(null);
        setIsLoading(true);
        try {
            let result;
            let aiResponse;
            if (selectedRevoModel === 'revo-2.0' && outputType === 'image' && brandProfile) {
                // Use Revo 2.0 next-generation AI for images with brand profile
                const revo2Result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$actions$2f$data$3a$6eb872__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateRevo2CreativeAssetAction"])(currentInput, brandProfile, {
                    platform: 'Instagram',
                    aspectRatio: '1:1',
                    style: 'photographic',
                    quality: 'ultra',
                    mood: 'professional'
                });
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: `🌟 ${selectedRevoModel} Revolutionary AI Generated!\n\nQuality Score: ${revo2Result.qualityScore}/10\nEnhancements: ${revo2Result.enhancementsApplied.join(', ')}\nProcessing Time: ${revo2Result.processingTime}ms\n\nThis image was created using our next-generation AI engine with revolutionary capabilities and ultra-high quality results.`,
                    imageUrl: revo2Result.imageUrl
                };
            } else if (selectedRevoModel === 'revo-1.5' && outputType === 'image' && brandProfile) {
                // Use enhanced design generation for images with brand profile
                const enhancedResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$d3c1bc__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateEnhancedDesignAction"])(brandProfile.businessType || 'business', 'Instagram', brandProfile.visualStyle || 'modern', currentInput, brandProfile, true, {
                    strictConsistency: true,
                    followBrandColors: true
                } // Enable all enhancements for Creative Studio
                );
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: `✨ ${selectedRevoModel} Enhanced Design Generated!\n\nQuality Score: ${enhancedResult.qualityScore}/10\nEnhancements: ${enhancedResult.enhancementsApplied.join(', ')}\nProcessing Time: ${enhancedResult.processingTime}ms\n\nThis design uses professional design principles, platform optimization, and quality validation for superior results.`,
                    imageUrl: enhancedResult.imageUrl
                };
            } else {
                // Use standard creative asset generation
                console.log(`🚀 Using ${selectedRevoModel} for standard generation`);
                result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ef11a5__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"])(currentInput, outputType, currentImageDataUrl, useBrandProfile, brandProfile, null, outputType === 'video' ? aspectRatio : undefined);
                aiResponse = {
                    id: (Date.now() + 1).toString(),
                    role: 'assistant',
                    content: result.aiExplanation,
                    imageUrl: result.imageUrl,
                    videoUrl: result.videoUrl
                };
            }
            setMessages((prevMessages)=>[
                    ...prevMessages,
                    aiResponse
                ]);
        } catch (error) {
            const errorResponse = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: `Sorry, I ran into an error: ${error.message}`
            };
            setMessages((prevMessages)=>[
                    ...prevMessages,
                    errorResponse
                ]);
            toast({
                variant: 'destructive',
                title: 'Generation Failed',
                description: error.message
            });
        } finally{
            setIsLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative flex h-full flex-col",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 overflow-y-auto",
                children: messages.length === 0 && !isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex h-full flex-col items-center justify-center text-center p-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                        className: "max-w-2xl w-full",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bot$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bot$3e$__["Bot"], {
                                    className: "mx-auto h-12 w-12 text-primary mb-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 176,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold font-headline",
                                    children: "Creative Studio"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 177,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-muted-foreground mt-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$wrap$2d$balancer$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        children: "Welcome to your AI-powered creative partner. Describe the ad you want, upload an image to edit, or start from scratch."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/chat-layout.tsx",
                                        lineNumber: 179,
                                        columnNumber: 37
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                                    lineNumber: 178,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/studio/chat-layout.tsx",
                            lineNumber: 175,
                            columnNumber: 29
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/studio/chat-layout.tsx",
                        lineNumber: 174,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                    lineNumber: 173,
                    columnNumber: 21
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$messages$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatMessages"], {
                    messages: messages,
                    isLoading: isLoading,
                    onSetReferenceAsset: handleSetReferenceAsset,
                    onEditImage: onEditImage
                }, void 0, false, {
                    fileName: "[project]/src/components/studio/chat-layout.tsx",
                    lineNumber: 187,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-layout.tsx",
                lineNumber: 171,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatInput"], {
                input: input,
                setInput: setInput,
                handleSubmit: handleSubmit,
                isLoading: isLoading,
                imagePreview: imagePreview,
                setImagePreview: setImagePreview,
                setImageDataUrl: setImageDataUrl,
                useBrandProfile: useBrandProfile,
                setUseBrandProfile: setUseBrandProfile,
                outputType: outputType,
                setOutputType: setOutputType,
                handleImageUpload: handleImageUpload,
                isBrandProfileAvailable: !!brandProfile,
                onEditImage: onEditImage,
                aspectRatio: aspectRatio,
                setAspectRatio: setAspectRatio,
                selectedRevoModel: selectedRevoModel,
                setSelectedRevoModel: setSelectedRevoModel
            }, void 0, false, {
                fileName: "[project]/src/components/studio/chat-layout.tsx",
                lineNumber: 196,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/studio/chat-layout.tsx",
        lineNumber: 170,
        columnNumber: 9
    }, this);
}
_s(ChatLayout, "xXrZMHM3mjOHkCi/dOQ9dzRZpb8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = ChatLayout;
var _c;
__turbopack_context__.k.register(_c, "ChatLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/slider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Slider": (()=>Slider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slider/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
const Slider = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative flex w-full touch-none select-none items-center", className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Track"], {
                className: "relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"], {
                    className: "absolute h-full bg-primary"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/slider.tsx",
                    lineNumber: 21,
                    columnNumber: 7
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/slider.tsx",
                lineNumber: 20,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Thumb"], {
                className: "block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/slider.tsx",
                lineNumber: 23,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/slider.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
_c1 = Slider;
Slider.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Slider$React.forwardRef");
__turbopack_context__.k.register(_c1, "Slider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/studio/image-editor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/components/studio/image-editor.tsx
__turbopack_context__.s({
    "ImageEditor": (()=>ImageEditor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand.js [app-client] (ecmascript) <export default as Wand>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/brush.js [app-client] (ecmascript) <export default as Brush>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eraser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eraser$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eraser.js [app-client] (ecmascript) <export default as Eraser>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/undo.js [app-client] (ecmascript) <export default as Undo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/redo.js [app-client] (ecmascript) <export default as Redo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rectangle$2d$horizontal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RectangleHorizontal$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rectangle-horizontal.js [app-client] (ecmascript) <export default as RectangleHorizontal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$slider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/slider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/label.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-toast.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ef11a5__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/data:ef11a5 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
function ImageEditor({ imageUrl, onClose, brandProfile }) {
    _s();
    const imageCanvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const drawingCanvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isDrawing, setIsDrawing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [brushSize, setBrushSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(40);
    const [tool, setTool] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('brush');
    const [prompt, setPrompt] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // History for drawing actions (masking)
    const [drawHistory, setDrawHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [drawHistoryIndex, setDrawHistoryIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(-1);
    // History for generated images
    const [imageHistory, setImageHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        imageUrl
    ]);
    const [imageHistoryIndex, setImageHistoryIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const [rectStart, setRectStart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const currentImageUrl = imageHistory[imageHistoryIndex];
    // Function to draw the main image onto its canvas
    const drawImage = (url)=>{
        const imageCanvas = imageCanvasRef.current;
        const drawingCanvas = drawingCanvasRef.current;
        if (!imageCanvas || !drawingCanvas) return;
        const imageCtx = imageCanvas.getContext('2d');
        const drawingCtx = drawingCanvas.getContext('2d');
        if (!imageCtx || !drawingCtx) return;
        const image = new window.Image();
        image.crossOrigin = "anonymous";
        image.src = url;
        image.onload = ()=>{
            imageCanvas.width = image.naturalWidth;
            imageCanvas.height = image.naturalHeight;
            drawingCanvas.width = image.naturalWidth;
            drawingCanvas.height = image.naturalHeight;
            imageCtx.drawImage(image, 0, 0);
            // Clear drawing canvas and reset its history
            drawingCtx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);
            const initialImageData = drawingCtx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);
            setDrawHistory([
                initialImageData
            ]);
            setDrawHistoryIndex(0);
        };
        image.onerror = ()=>{
            toast({
                variant: 'destructive',
                title: "Error loading image",
                description: "Could not load the image for editing."
            });
        };
    };
    // Redraw the image whenever the currentImageUrl changes (from undo/redo or new generation)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ImageEditor.useEffect": ()=>{
            if (currentImageUrl) {
                drawImage(currentImageUrl);
            }
        }
    }["ImageEditor.useEffect"], [
        currentImageUrl
    ]);
    const getDrawingContext = ()=>drawingCanvasRef.current?.getContext('2d', {
            willReadFrequently: true
        });
    const saveToDrawHistory = ()=>{
        const drawingCtx = getDrawingContext();
        if (!drawingCtx || !drawingCanvasRef.current) return;
        const newHistory = drawHistory.slice(0, drawHistoryIndex + 1);
        newHistory.push(drawingCtx.getImageData(0, 0, drawingCanvasRef.current.width, drawingCanvasRef.current.height));
        setDrawHistory(newHistory);
        setDrawHistoryIndex(newHistory.length - 1);
    };
    const handleDrawUndo = ()=>{
        if (drawHistoryIndex > 0) {
            const newIndex = drawHistoryIndex - 1;
            setDrawHistoryIndex(newIndex);
            const drawingCtx = getDrawingContext();
            if (drawingCtx) {
                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);
            }
        }
    };
    const handleDrawRedo = ()=>{
        if (drawHistoryIndex < drawHistory.length - 1) {
            const newIndex = drawHistoryIndex + 1;
            setDrawHistoryIndex(newIndex);
            const drawingCtx = getDrawingContext();
            if (drawingCtx) {
                drawingCtx.putImageData(drawHistory[newIndex], 0, 0);
            }
        }
    };
    const handleGenerationUndo = ()=>{
        if (imageHistoryIndex > 0) {
            setImageHistoryIndex((prev)=>prev - 1);
        }
    };
    const getMousePos = (canvas, evt)=>{
        const rect = canvas.getBoundingClientRect();
        return {
            x: (evt.clientX - rect.left) / rect.width * canvas.width,
            y: (evt.clientY - rect.top) / rect.height * canvas.height
        };
    };
    const startDrawing = (e)=>{
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        const { x, y } = getMousePos(canvas, e);
        setIsDrawing(true);
        if (tool === 'brush' || tool === 'eraser') {
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineWidth = brushSize;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.globalCompositeOperation = tool === 'brush' ? 'source-over' : 'destination-out';
        } else if (tool === 'rect') {
            setRectStart({
                x,
                y
            });
        }
    };
    const draw = (e)=>{
        if (!isDrawing) return;
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        const { x, y } = getMousePos(canvas, e);
        if (tool === 'brush' || tool === 'eraser') {
            ctx.lineTo(x, y);
            ctx.stroke();
        } else if (tool === 'rect' && rectStart) {
            // Restore previous state to draw rect preview
            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.lineWidth = 2;
            ctx.strokeRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);
        }
    };
    const stopDrawing = (e)=>{
        if (!isDrawing) return;
        const canvas = drawingCanvasRef.current;
        const ctx = getDrawingContext();
        if (!ctx || !canvas) return;
        if (tool === 'brush' || tool === 'eraser') {
            ctx.closePath();
        } else if (tool === 'rect' && rectStart) {
            // Restore canvas before drawing final rect
            ctx.putImageData(drawHistory[drawHistoryIndex], 0, 0);
            const { x, y } = getMousePos(canvas, e);
            ctx.globalCompositeOperation = 'source-over';
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(rectStart.x, rectStart.y, x - rectStart.x, y - rectStart.y);
            setRectStart(null);
        }
        setIsDrawing(false);
        saveToDrawHistory();
    };
    const getMaskDataUrl = ()=>{
        const drawingCanvas = drawingCanvasRef.current;
        if (!drawingCanvas) return '';
        const ctx = getDrawingContext();
        if (!ctx) return '';
        const originalImageData = ctx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height);
        const originalData = originalImageData.data;
        const maskCanvas = document.createElement('canvas');
        maskCanvas.width = drawingCanvas.width;
        maskCanvas.height = drawingCanvas.height;
        const maskCtx = maskCanvas.getContext('2d');
        if (!maskCtx) return '';
        const maskImageData = maskCtx.createImageData(drawingCanvas.width, drawingCanvas.height);
        const maskData = maskImageData.data;
        for(let i = 0; i < originalData.length; i += 4){
            if (originalData[i + 3] > 0) {
                maskData[i] = 0; // R (black)
                maskData[i + 1] = 0; // G
                maskData[i + 2] = 0; // B
                maskData[i + 3] = 255; // A (opaque)
            } else {
                maskData[i] = 255; // R (white)
                maskData[i + 1] = 255; // G
                maskData[i + 2] = 255; // B
                maskData[i + 3] = 255; // A (opaque)
            }
        }
        maskCtx.putImageData(maskImageData, 0, 0);
        return maskCanvas.toDataURL('image/png');
    };
    const handleGenerate = async ()=>{
        if (!prompt.trim()) {
            toast({
                variant: 'destructive',
                title: "Prompt is required",
                description: "Please describe what you want to change."
            });
            return;
        }
        setIsLoading(true);
        const maskDataUrl = getMaskDataUrl();
        if (!maskDataUrl) {
            toast({
                variant: 'destructive',
                title: "Mask Error",
                description: "Could not generate the mask data."
            });
            setIsLoading(false);
            return;
        }
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$data$3a$ef11a5__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAssetAction"])(prompt, 'image', currentImageUrl, !!brandProfile, brandProfile, maskDataUrl);
            if (result.imageUrl) {
                const newHistory = imageHistory.slice(0, imageHistoryIndex + 1);
                newHistory.push(result.imageUrl);
                setImageHistory(newHistory);
                setImageHistoryIndex(newHistory.length - 1);
                toast({
                    title: "Image Updated!",
                    description: result.aiExplanation
                });
            } else {
                toast({
                    variant: 'destructive',
                    title: "Generation Failed",
                    description: "The AI did not return an image."
                });
            }
        } catch (error) {
            toast({
                variant: 'destructive',
                title: "Generation Failed",
                description: error.message
            });
        } finally{
            setIsLoading(false);
            setPrompt("");
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-full w-full bg-background",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-64 flex-shrink-0 border-r bg-card p-4 flex flex-col gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-xl font-bold",
                                children: "Image Editor"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 282,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "ghost",
                                size: "icon",
                                onClick: onClose,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/studio/image-editor.tsx",
                                    lineNumber: 283,
                                    columnNumber: 75
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 283,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 281,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "History"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 287,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        onClick: handleDrawUndo,
                                        disabled: drawHistoryIndex <= 0,
                                        children: [
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                                lineNumber: 289,
                                                columnNumber: 110
                                            }, this),
                                            " Undo Mask"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 289,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        onClick: handleDrawRedo,
                                        disabled: drawHistoryIndex >= drawHistory.length - 1,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__["Redo"], {
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                                lineNumber: 290,
                                                columnNumber: 130
                                            }, this),
                                            " Redo Mask"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 290,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 288,
                                columnNumber: 22
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                onClick: handleGenerationUndo,
                                disabled: imageHistoryIndex <= 0,
                                className: "w-full",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                                        className: "mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 293,
                                        columnNumber: 25
                                    }, this),
                                    " Undo Generation"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 292,
                                columnNumber: 22
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 286,
                        columnNumber: 18
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                children: "Tool"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 298,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-3 gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'brush' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('brush'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$brush$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Brush$3e$__["Brush"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 300,
                                            columnNumber: 119
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 300,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'rect' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('rect'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rectangle$2d$horizontal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RectangleHorizontal$3e$__["RectangleHorizontal"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 301,
                                            columnNumber: 117
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 301,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: tool === 'eraser' ? 'secondary' : 'outline',
                                        onClick: ()=>setTool('eraser'),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eraser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eraser$3e$__["Eraser"], {}, void 0, false, {
                                            fileName: "[project]/src/components/studio/image-editor.tsx",
                                            lineNumber: 302,
                                            columnNumber: 121
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/studio/image-editor.tsx",
                                        lineNumber: 302,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 299,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 297,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                htmlFor: "brush-size",
                                children: [
                                    "Brush Size: ",
                                    brushSize,
                                    "px"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 307,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$slider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slider"], {
                                id: "brush-size",
                                min: 5,
                                max: 100,
                                value: [
                                    brushSize
                                ],
                                onValueChange: (v)=>setBrushSize(v[0])
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 308,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 306,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 310,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2 flex-1 flex flex-col",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"], {
                                htmlFor: "inpaint-prompt",
                                children: "Edit Prompt"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 312,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                id: "inpaint-prompt",
                                placeholder: "e.g., 'add sunglasses'",
                                value: prompt,
                                onChange: (e)=>setPrompt(e.target.value),
                                disabled: isLoading
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 313,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 311,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: handleGenerate,
                        disabled: isLoading || !prompt,
                        children: [
                            isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                className: "mr-2 animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 322,
                                columnNumber: 34
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wand$3e$__["Wand"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/studio/image-editor.tsx",
                                lineNumber: 322,
                                columnNumber: 77
                            }, this),
                            "Generate"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/studio/image-editor.tsx",
                        lineNumber: 321,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/studio/image-editor.tsx",
                lineNumber: 280,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex items-center justify-center p-4 overflow-hidden bg-muted/20",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                            ref: imageCanvasRef,
                            className: "max-w-full max-h-full object-contain rounded-md shadow-lg"
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/image-editor.tsx",
                            lineNumber: 329,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
                            ref: drawingCanvasRef,
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("absolute top-0 left-0 max-w-full max-h-full object-contain cursor-crosshair"),
                            onMouseDown: startDrawing,
                            onMouseMove: draw,
                            onMouseUp: stopDrawing,
                            onMouseLeave: stopDrawing
                        }, void 0, false, {
                            fileName: "[project]/src/components/studio/image-editor.tsx",
                            lineNumber: 333,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/studio/image-editor.tsx",
                    lineNumber: 328,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/studio/image-editor.tsx",
                lineNumber: 327,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/studio/image-editor.tsx",
        lineNumber: 278,
        columnNumber: 9
    }, this);
}
_s(ImageEditor, "0rwuOkHx93pNP4sRWLWjPl87Qes=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$toast$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"]
    ];
});
_c = ImageEditor;
var _c;
__turbopack_context__.k.register(_c, "ImageEditor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/unified-brand-layout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BrandContent": (()=>BrandContent),
    "BrandSwitchingStatus": (()=>BrandSwitchingStatus),
    "ConditionalBrandContent": (()=>ConditionalBrandContent),
    "UnifiedBrandLayout": (()=>UnifiedBrandLayout),
    "useBrandAware": (()=>useBrandAware),
    "useBrandScopedData": (()=>useBrandScopedData),
    "withBrandAware": (()=>withBrandAware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
'use client';
;
;
// Inner component that uses the unified brand context
function UnifiedBrandLayoutContent({ children }) {
    _s();
    const { currentBrand, loading, error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Listen for brand changes and log them
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "UnifiedBrandLayoutContent.useBrandChangeListener": (brand)=>{
            console.log('🔄 Brand changed in layout:', brand?.businessName || brand?.name || 'none');
            // Mark as initialized once we have a brand or finished loading
            if (!isInitialized && (!loading || brand)) {
                setIsInitialized(true);
            }
        }
    }["UnifiedBrandLayoutContent.useBrandChangeListener"]);
    // Show loading state while initializing
    if (!isInitialized && loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 31,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Loading brand profiles..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 32,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 30,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this);
    }
    // Show error state if there's an error
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-red-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-600 text-2xl",
                            children: "⚠️"
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                            lineNumber: 44,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 43,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-red-900 mb-2",
                        children: "Error Loading Brands"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-red-600 mb-4",
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 47,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>window.location.reload(),
                        className: "px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",
                        children: "Retry"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 48,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 42,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 41,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "unified-brand-layout",
        children: [
            ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 right-0 z-50 bg-black bg-opacity-75 text-white text-xs p-2 rounded-bl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: "🔥 Unified Brand System"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 64,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "Brand: ",
                            currentBrand?.businessName || currentBrand?.name || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            "ID: ",
                            currentBrand?.id || 'None'
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
_s(UnifiedBrandLayoutContent, "fCqT7M8oQD2mKm1cC+OOL+jMWzQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c = UnifiedBrandLayoutContent;
function UnifiedBrandLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnifiedBrandProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(UnifiedBrandLayoutContent, {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 79,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, this);
}
_c1 = UnifiedBrandLayout;
function useBrandAware() {
    _s1();
    const { currentBrand, selectBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    return {
        currentBrand,
        selectBrand,
        loading,
        isReady: !loading && currentBrand !== null,
        brandId: currentBrand?.id || null,
        brandName: currentBrand?.businessName || currentBrand?.name || null
    };
}
_s1(useBrandAware, "w47bVP/loHCLTWfG0SMBg7xbNQg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
function withBrandAware(Component) {
    var _s = __turbopack_context__.k.signature();
    return _s(function BrandAwareComponent(props) {
        _s();
        const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props,
            brand: currentBrand
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 107,
            columnNumber: 12
        }, this);
    }, "MN56VmfrkrwEAMHnmgJmh3z4nas=", false, function() {
        return [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
        ];
    });
}
function BrandContent({ children, fallback }) {
    _s2();
    const { currentBrand, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center p-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"
            }, void 0, false, {
                fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                lineNumber: 123,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 122,
            columnNumber: 7
        }, this);
    }
    if (!currentBrand) {
        return fallback || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-center p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-gray-400 text-2xl",
                        children: "🏢"
                    }, void 0, false, {
                        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 131,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    className: "text-lg font-semibold text-gray-900 mb-2",
                    children: "No Brand Selected"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-gray-600",
                    children: "Please select a brand to continue."
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 135,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 130,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children(currentBrand)
    }, void 0, false);
}
_s2(BrandContent, "SDXm9VlEhbnzCURAKLqu5+JG9ko=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
_c2 = BrandContent;
function ConditionalBrandContent({ brandId, brandName, children, fallback }) {
    _s3();
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const shouldRender = (!brandId || currentBrand?.id === brandId) && (!brandName || currentBrand?.businessName === brandName || currentBrand?.name === brandName);
    if (shouldRender) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    return fallback || null;
}
_s3(ConditionalBrandContent, "MN56VmfrkrwEAMHnmgJmh3z4nas=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
_c3 = ConditionalBrandContent;
function useBrandScopedData(feature, defaultValue, loader) {
    _s4();
    const { currentBrand, getBrandStorage } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useBrandScopedData.useEffect": ()=>{
            if (!currentBrand?.id) {
                setData(defaultValue);
                return;
            }
            const storage = getBrandStorage(feature);
            if (!storage) {
                setData(defaultValue);
                return;
            }
            setLoading(true);
            try {
                if (loader) {
                    // Use custom loader
                    const result = loader(currentBrand.id);
                    if (result instanceof Promise) {
                        result.then({
                            "useBrandScopedData.useEffect": (loadedData)=>{
                                setData(loadedData);
                                setLoading(false);
                            }
                        }["useBrandScopedData.useEffect"]).catch({
                            "useBrandScopedData.useEffect": (error)=>{
                                console.error(`Failed to load ${feature} data:`, error);
                                setData(defaultValue);
                                setLoading(false);
                            }
                        }["useBrandScopedData.useEffect"]);
                    } else {
                        setData(result);
                        setLoading(false);
                    }
                } else {
                    // Use storage
                    const storedData = storage.getItem();
                    setData(storedData || defaultValue);
                    setLoading(false);
                }
            } catch (error) {
                console.error(`Failed to load ${feature} data:`, error);
                setData(defaultValue);
                setLoading(false);
            }
        }
    }["useBrandScopedData.useEffect"], [
        currentBrand?.id,
        feature,
        defaultValue,
        loader,
        getBrandStorage
    ]);
    // Save data function
    const saveData = (newData)=>{
        setData(newData);
        if (currentBrand?.id) {
            const storage = getBrandStorage(feature);
            if (storage) {
                storage.setItem(newData);
            }
        }
    };
    return [
        data,
        saveData,
        loading
    ];
}
_s4(useBrandScopedData, "ZdiOY6elsL57Z0m55fnPycGGj2E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"]
    ];
});
function BrandSwitchingStatus() {
    _s5();
    const { loading, currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [switching, setSwitching] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "BrandSwitchingStatus.useBrandChangeListener": (brand)=>{
            setSwitching(true);
            const timer = setTimeout({
                "BrandSwitchingStatus.useBrandChangeListener.timer": ()=>setSwitching(false)
            }["BrandSwitchingStatus.useBrandChangeListener.timer"], 1000);
            return ({
                "BrandSwitchingStatus.useBrandChangeListener": ()=>clearTimeout(timer)
            })["BrandSwitchingStatus.useBrandChangeListener"];
        }
    }["BrandSwitchingStatus.useBrandChangeListener"]);
    if (!switching && !loading) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-2",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm",
                    children: switching ? `Switching to ${currentBrand?.businessName || currentBrand?.name}...` : 'Loading...'
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
                    lineNumber: 257,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
            lineNumber: 255,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/unified-brand-layout.tsx",
        lineNumber: 254,
        columnNumber: 5
    }, this);
}
_s5(BrandSwitchingStatus, "sajUo/soq/Xgt0zh0dhNkAG60WA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c4 = BrandSwitchingStatus;
var _c, _c1, _c2, _c3, _c4;
__turbopack_context__.k.register(_c, "UnifiedBrandLayoutContent");
__turbopack_context__.k.register(_c1, "UnifiedBrandLayout");
__turbopack_context__.k.register(_c2, "BrandContent");
__turbopack_context__.k.register(_c3, "ConditionalBrandContent");
__turbopack_context__.k.register(_c4, "BrandSwitchingStatus");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/creative-studio/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/app/creative-studio/page.tsx
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/dropdown-menu.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sidebar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/chat-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-client] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-client] (ecmascript) <export default as Palette>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$image$2d$editor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/studio/image-editor.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/unified-brand-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/unified-brand-layout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/brand-scoped-storage.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
function CreativeStudioPageContent() {
    _s();
    const { currentBrand } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"])();
    const [editorImage, setEditorImage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const creativeStudioStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandStorage"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$brand$2d$scoped$2d$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_FEATURES"].CREATIVE_STUDIO);
    // Load Creative Studio data when brand changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"])({
        "CreativeStudioPageContent.useBrandChangeListener": (brand)=>{
            if (brand) {
                console.log(`🎨 Creative Studio: brand changed to: ${brand.businessName || brand.name}`);
                // Load any Creative Studio specific data for this brand
                if (creativeStudioStorage) {
                    const studioData = creativeStudioStorage.getItem();
                    if (studioData) {
                        console.log(`📂 Loaded Creative Studio data for brand ${brand.businessName || brand.name}`);
                    // Apply any saved studio settings here
                    }
                }
            } else {
                console.log('🎨 Creative Studio: no brand selected');
            }
        }
    }["CreativeStudioPageContent.useBrandChangeListener"]);
    // Convert CompleteBrandProfile to BrandProfile for compatibility
    const brandProfile = currentBrand ? {
        businessName: currentBrand.businessName,
        businessType: currentBrand.businessType,
        location: currentBrand.location,
        description: currentBrand.description,
        targetAudience: currentBrand.targetAudience,
        keyFeatures: currentBrand.keyFeatures,
        competitiveAdvantages: currentBrand.competitiveAdvantages,
        visualStyle: currentBrand.visualStyle,
        writingTone: currentBrand.writingTone,
        contentThemes: currentBrand.contentThemes,
        primaryColor: currentBrand.primaryColor,
        accentColor: currentBrand.accentColor,
        backgroundColor: currentBrand.backgroundColor,
        logoDataUrl: currentBrand.logoDataUrl,
        websiteUrl: currentBrand.websiteUrl,
        socialMedia: {
            facebook: currentBrand.facebookUrl,
            instagram: currentBrand.instagramUrl,
            twitter: currentBrand.twitterUrl,
            linkedin: currentBrand.linkedinUrl
        },
        contactInfo: {
            phone: currentBrand.contactPhone,
            email: currentBrand.contactEmail,
            address: currentBrand.contactAddress
        }
    } : null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SidebarInset"], {
        fullWidth: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "flex h-14 items-center justify-between border-b bg-card px-4 lg:h-[60px] lg:px-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {}, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenu"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuTrigger"], {
                                asChild: true,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "secondary",
                                    size: "icon",
                                    className: "rounded-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                                    src: "https://placehold.co/40x40.png",
                                                    alt: "User",
                                                    "data-ai-hint": "user avatar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                                    lineNumber: 84,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {}, void 0, false, {
                                                        fileName: "[project]/src/app/creative-studio/page.tsx",
                                                        lineNumber: 89,
                                                        columnNumber: 33
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                                    lineNumber: 89,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/creative-studio/page.tsx",
                                            lineNumber: 83,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "sr-only",
                                            children: "Toggle user menu"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/creative-studio/page.tsx",
                                            lineNumber: 91,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                    lineNumber: 82,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 81,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuContent"], {
                                align: "end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$dropdown$2d$menu$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DropdownMenuLabel"], {
                                    children: "My Account"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/creative-studio/page.tsx",
                                    lineNumber: 95,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 78,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1 overflow-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container mx-auto px-4 py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "max-w-7xl mx-auto",
                            children: editorImage ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$image$2d$editor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ImageEditor"], {
                                imageUrl: editorImage,
                                onClose: ()=>setEditorImage(null),
                                brandProfile: brandProfile
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 104,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$studio$2f$chat$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChatLayout"], {
                                brandProfile: brandProfile,
                                onEditImage: setEditorImage
                            }, void 0, false, {
                                fileName: "[project]/src/app/creative-studio/page.tsx",
                                lineNumber: 110,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/creative-studio/page.tsx",
                            lineNumber: 102,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/creative-studio/page.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 99,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 77,
        columnNumber: 5
    }, this);
}
_s(CreativeStudioPageContent, "Mq/JsPR2x0mG04vUD+yEheI7FsQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUnifiedBrand"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandStorage"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$unified$2d$brand$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBrandChangeListener"]
    ];
});
_c = CreativeStudioPageContent;
function CreativeStudioPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrandContent"], {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Palette$3e$__["Palette"], {
                            className: "w-8 h-8 text-gray-400"
                        }, void 0, false, {
                            fileName: "[project]/src/app/creative-studio/page.tsx",
                            lineNumber: 129,
                            columnNumber: 13
                        }, void 0)
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 128,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-gray-900 mb-2",
                        children: "No Brand Selected"
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 131,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: "Please select a brand to access the Creative Studio."
                    }, void 0, false, {
                        fileName: "[project]/src/app/creative-studio/page.tsx",
                        lineNumber: 132,
                        columnNumber: 11
                    }, void 0)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 127,
                columnNumber: 9
            }, void 0)
        }, void 0, false, {
            fileName: "[project]/src/app/creative-studio/page.tsx",
            lineNumber: 126,
            columnNumber: 7
        }, void 0),
        children: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CreativeStudioPageContent, {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 136,
                columnNumber: 14
            }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 125,
        columnNumber: 5
    }, this);
}
_c1 = CreativeStudioPage;
function CreativeStudioPageWithUnifiedBrand() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnifiedBrandLayout"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CreativeStudioPage, {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 144,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$unified$2d$brand$2d$layout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BrandSwitchingStatus"], {}, void 0, false, {
                fileName: "[project]/src/app/creative-studio/page.tsx",
                lineNumber: 145,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/creative-studio/page.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
_c2 = CreativeStudioPageWithUnifiedBrand;
const __TURBOPACK__default__export__ = CreativeStudioPageWithUnifiedBrand;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "CreativeStudioPageContent");
__turbopack_context__.k.register(_c1, "CreativeStudioPage");
__turbopack_context__.k.register(_c2, "CreativeStudioPageWithUnifiedBrand");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_82236852._.js.map
// src/app/actions.ts
"use server";

import { analyzeBrand as analyzeBrandFlow, BrandAnalysisResult } from "@/ai/flows/analyze-brand";
import { generatePostFromProfile as generatePostFromProfileFlow } from "@/ai/flows/generate-post-from-profile";
import { generateVideoPost as generateVideoPostFlow } from "@/ai/flows/generate-video-post";
import { generateCreativeAsset as generateCreativeAssetFlow } from "@/ai/flows/generate-creative-asset";
import type { BrandProfile, GeneratedPost, Platform, CreativeAsset } from "@/lib/types";
import { artifactsService } from "@/lib/services/artifacts-service";
import type { Artifact } from "@/lib/types/artifacts";
// Temporarily commented out for build fix
// import {
//   detectAndPopulateLanguages,
//   getLanguageInstructionForProfile,
//   updateLanguageDetectionIfNeeded
// } from "@/lib/services/brand-language-service";
// import { generateEnhancedDesign } from "@/ai/gemini-2.5-design"; // Temporarily disabled
import { generateRevo2ContentAction, generateRevo2CreativeAssetAction } from "@/app/actions/revo-2-actions";
import { DesignGenerationService } from "@/ai/models/services/design-generation-service";
import type { RevoModelId } from "@/ai/models/types/model-types";


// --- AI Flow Actions ---

type AnalysisResult = {
  success: true;
  data: BrandAnalysisResult;
} | {
  success: false;
  error: string;
  errorType: 'blocked' | 'timeout' | 'error';
};

export async function analyzeBrandAction(
  websiteUrl: string,
  designImageUris: string[],
): Promise<AnalysisResult> {
  try {
    console.log("🔍 Starting brand analysis for URL:", websiteUrl);
    console.log("🖼️ Design images count:", designImageUris.length);

    // Validate URL format
    if (!websiteUrl || !websiteUrl.trim()) {
      return {
        success: false,
        error: "Website URL is required",
        errorType: 'error'
      };
    }

    // Ensure URL has protocol
    let validUrl = websiteUrl.trim();
    if (!validUrl.startsWith('http://') && !validUrl.startsWith('https://')) {
      validUrl = 'https://' + validUrl;
    }

    const result = await analyzeBrandFlow({
      websiteUrl: validUrl,
      designImageUris: designImageUris || []
    });

    console.log("✅ Brand analysis result:", JSON.stringify(result, null, 2));
    console.log("🔍 Result type:", typeof result);
    console.log("🔍 Result keys:", result ? Object.keys(result) : "No result");

    if (!result) {
      return {
        success: false,
        error: "Analysis returned empty result",
        errorType: 'error'
      };
    }

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error("❌ Error analyzing brand:", error);

    // Return structured error response instead of throwing
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

    if (errorMessage.includes('fetch') || errorMessage.includes('403') || errorMessage.includes('blocked')) {
      return {
        success: false,
        error: "Website blocks automated access. This is common for security reasons.",
        errorType: 'blocked'
      };
    } else if (errorMessage.includes('timeout')) {
      return {
        success: false,
        error: "Website analysis timed out. Please try again or check if the website is accessible.",
        errorType: 'timeout'
      };
    } else if (errorMessage.includes('CORS')) {
      return {
        success: false,
        error: "Website blocks automated access. This is common for security reasons.",
        errorType: 'blocked'
      };
    } else {
      return {
        success: false,
        error: `Analysis failed: ${errorMessage}`,
        errorType: 'error'
      };
    }
  }
}

const getAspectRatioForPlatform = (platform: Platform): string => {
  switch (platform) {
    case 'Instagram':
      return '1:1'; // Square
    case 'Facebook':
      return '16:9'; // Landscape - Facebook posts are landscape format
    case 'Twitter':
      return '16:9'; // Landscape
    case 'LinkedIn':
      return '16:9'; // Landscape - LinkedIn posts are landscape format
    default:
      return '1:1';
  }
}

export async function generateContentAction(
  profile: BrandProfile,
  platform: Platform,
  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean }
): Promise<GeneratedPost> {
  try {
    const today = new Date();
    const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });
    const currentDate = today.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

    // Temporarily commented out for build fix
    // const profileWithLanguages = updateLanguageDetectionIfNeeded(profile);
    // const languageInstructions = getLanguageInstructionForProfile(profileWithLanguages);
    const profileWithLanguages = profile;
    const languageInstructions = `Use appropriate language for ${profile.location || 'the business location'}`;

    // Apply brand consistency logic
    const effectiveDesignExamples = brandConsistency?.strictConsistency
      ? (profile.designExamples || [])
      : []; // Don't use design examples if not strict consistency

    // Convert arrays to newline-separated strings for AI processing
    const keyFeaturesString = Array.isArray(profile.keyFeatures)
      ? profile.keyFeatures.join('\n')
      : profile.keyFeatures || '';

    const competitiveAdvantagesString = Array.isArray(profile.competitiveAdvantages)
      ? profile.competitiveAdvantages.join('\n')
      : profile.competitiveAdvantages || '';

    // Convert services array to newline-separated string
    const servicesString = Array.isArray(profile.services)
      ? profile.services.map(service =>
        typeof service === 'object' && service.name
          ? `${service.name}: ${service.description || ''}`
          : service
      ).join('\n')
      : profile.services || '';



    const postDetails = await generatePostFromProfileFlow({
      businessType: profileWithLanguages.businessType,
      location: profileWithLanguages.location,
      writingTone: profileWithLanguages.writingTone,
      contentThemes: profileWithLanguages.contentThemes,
      visualStyle: profileWithLanguages.visualStyle,
      logoDataUrl: profileWithLanguages.logoDataUrl,
      designExamples: effectiveDesignExamples, // Use design examples based on consistency preference
      primaryColor: profileWithLanguages.primaryColor,
      accentColor: profileWithLanguages.accentColor,
      backgroundColor: profileWithLanguages.backgroundColor,
      dayOfWeek,
      currentDate,
      variants: [{
        platform: platform,
        aspectRatio: getAspectRatioForPlatform(platform),
      }],
      // Pass new detailed fields
      services: servicesString,
      targetAudience: profileWithLanguages.targetAudience,
      keyFeatures: keyFeaturesString,
      competitiveAdvantages: competitiveAdvantagesString,
      // Pass brand consistency preferences
      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },
      // Pass intelligent language instructions
      languageInstructions: languageInstructions,
    });

    const newPost: GeneratedPost = {
      id: new Date().toISOString(),
      date: today.toISOString(),
      content: postDetails.content,
      hashtags: postDetails.hashtags,
      status: 'generated',
      variants: postDetails.variants,
      catchyWords: postDetails.catchyWords,
      subheadline: postDetails.subheadline,
      callToAction: postDetails.callToAction,
      // Include enhanced AI features
      contentVariants: postDetails.contentVariants,
      hashtagAnalysis: postDetails.hashtagAnalysis,
      // Include advanced AI features
      marketIntelligence: postDetails.marketIntelligence,
      // Include local context features
      localContext: postDetails.localContext,
    };

    return newPost;
  } catch (error) {
    console.error("Error generating content:", error);
    throw new Error("Failed to generate content. Please try again later.");
  }
}

export async function generateVideoContentAction(
  profile: BrandProfile,
  catchyWords: string,
  postContent: string,
): Promise<{ videoUrl: string }> {
  try {
    const result = await generateVideoPostFlow({
      businessType: profile.businessType,
      location: profile.location,
      visualStyle: profile.visualStyle,
      imageText: catchyWords, // Use catchyWords as imageText for video generation
      postContent: postContent,
    });
    return { videoUrl: result.videoUrl };
  } catch (error) {
    console.error("Error generating video content:", error);
    // Pass the specific error message from the flow to the client
    throw new Error((error as Error).message);
  }
}


export async function generateCreativeAssetAction(
  prompt: string,
  outputType: 'image' | 'video',
  referenceAssetUrl: string | null,
  useBrandProfile: boolean,
  brandProfile: BrandProfile | null,
  maskDataUrl: string | null | undefined,
  aspectRatio: '16:9' | '9:16' | undefined
): Promise<CreativeAsset> {
  try {
    const result = await generateCreativeAssetFlow({
      prompt,
      outputType,
      referenceAssetUrl,
      useBrandProfile,
      brandProfile: useBrandProfile ? brandProfile : null,
      maskDataUrl,
      aspectRatio,
    });
    return result;
  } catch (error) {
    console.error("Error generating creative asset:", error);
    // Always pass the specific error message from the flow to the client.
    throw new Error((error as Error).message);
  }
}

export async function generateEnhancedDesignAction(
  businessType: string,
  platform: string,
  visualStyle: string,
  imageText: string | { catchyWords: string; subheadline?: string; callToAction?: string },
  brandProfile?: BrandProfile,
  enableEnhancements: boolean = true,
  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },
  artifactInstructions?: string
): Promise<{
  imageUrl: string;
  qualityScore: number;
  enhancementsApplied: string[];
  processingTime: number;
}> {
  const startTime = Date.now();
  const enhancementsApplied: string[] = [];

  try {
    if (!brandProfile) {
      throw new Error('Brand profile is required for enhanced design generation');
    }

    // Handle both old string format and new object format
    let finalImageText: string;
    if (typeof imageText === 'string') {
      finalImageText = imageText;
    } else {
      // Combine catchy words, subheadline, and call-to-action
      const components = [imageText.catchyWords];
      if (imageText.subheadline && imageText.subheadline.trim()) {
        components.push(imageText.subheadline.trim());
      }
      if (imageText.callToAction && imageText.callToAction.trim()) {
        components.push(imageText.callToAction.trim());
      }
      finalImageText = components.join('\n');
    }

    console.log('🎨 Enhanced Design Generation Started');
    console.log('- Business Type:', businessType);
    console.log('- Platform:', platform);
    console.log('- Visual Style:', visualStyle);
    console.log('- Image Text:', finalImageText);
    console.log('- Brand Profile:', brandProfile.businessName);
    console.log('- Enhancements Enabled:', enableEnhancements);

    // Try Gemini 2.5 first (best quality), then fallback to OpenAI, then Gemini 2.0 HD
    let result;

    try {
      console.log('🚀 Using Gemini 2.5 Pro for superior design generation...');

      result = await generateEnhancedDesign({
        businessType,
        platform,
        visualStyle,
        imageText: finalImageText,
        brandProfile,
        brandConsistency,
        artifactInstructions,
      });

      console.log('✅ Gemini 2.5 enhanced design generated successfully');
      console.log(`🎯 Quality Score: ${result.qualityScore}/10`);
      console.log(`⚡ Processing Time: ${result.processingTime}ms`);

    } catch (gemini25Error) {
      console.warn('⚠️ Gemini 2.5 generation failed, falling back to OpenAI:', gemini25Error);

      try {
        console.log('🚀 Using OpenAI GPT-Image 1 for enhanced design generation...');
        const { generateEnhancedDesignWithFallback } = await import('@/ai/openai-enhanced-design');

        result = await generateEnhancedDesignWithFallback({
          businessType,
          platform,
          visualStyle,
          imageText: finalImageText,
          brandProfile,
          brandConsistency,
          artifactInstructions,
        });

        console.log('✅ OpenAI GPT-Image 1 enhanced design generated successfully');
      } catch (openaiError) {
        console.warn('⚠️ OpenAI generation also failed, falling back to Gemini 2.0 HD:', openaiError);

        console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');
        const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');

        result = await generateGeminiHDEnhancedDesignWithFallback({
          businessType,
          platform,
          visualStyle,
          imageText: finalImageText,
          brandProfile,
          brandConsistency,
          artifactInstructions,
        });

        console.log('✅ Gemini 2.0 HD enhanced design generated successfully');
      }
    }

    console.log('🔗 Image URL:', result.imageUrl);
    console.log('⭐ Quality Score:', result.qualityScore);
    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);

    return {
      imageUrl: result.imageUrl,
      qualityScore: result.qualityScore,
      enhancementsApplied: result.enhancementsApplied,
      processingTime: result.processingTime
    };


  } catch (error) {
    console.error("Error generating enhanced design:", error);
    throw new Error((error as Error).message);
  }
}

/**
 * Generate enhanced design specifically using Gemini 2.0 Flash HD
 * This action forces the use of Gemini HD for maximum quality
 */
export async function generateGeminiHDDesignAction(
  businessType: string,
  platform: string,
  visualStyle: string,
  imageText: string,
  brandProfile: BrandProfile,
  brandConsistency?: {
    strictConsistency: boolean;
    followBrandColors: boolean;
  },
  artifactInstructions?: string
): Promise<PostVariant> {
  try {
    if (!brandProfile) {
      throw new Error('Brand profile is required for Gemini HD design generation');
    }

    console.log('🎨 Gemini HD Design Generation Started');
    console.log('- Business Type:', businessType);
    console.log('- Platform:', platform);
    console.log('- Visual Style:', visualStyle);
    console.log('- Image Text:', imageText);
    console.log('- Brand Profile:', brandProfile.businessName);

    console.log('🚀 Using Gemini 2.0 Flash HD for enhanced design generation...');
    const { generateGeminiHDEnhancedDesignWithFallback } = await import('@/ai/gemini-hd-enhanced-design');

    const result = await generateGeminiHDEnhancedDesignWithFallback({
      businessType,
      platform,
      visualStyle,
      imageText,
      brandProfile,
      brandConsistency,
      artifactInstructions,
    });

    console.log('✅ Gemini HD enhanced design generated successfully');
    console.log('🔗 Image URL:', result.imageUrl);
    console.log('⭐ Quality Score:', result.qualityScore);
    console.log('🎯 Enhancements Applied:', result.enhancementsApplied);

    return {
      platform,
      imageUrl: result.imageUrl,
      caption: imageText,
      hashtags: [],
    };
  } catch (error) {
    console.error('❌ Error in Gemini HD design generation:', error);
    throw new Error(`Gemini HD design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate content with artifact references (Enhanced)
 */
export async function generateContentWithArtifactsAction(
  profile: BrandProfile,
  platform: Platform,
  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },
  artifactIds: string[] = [],
  useEnhancedDesign: boolean = true
): Promise<GeneratedPost> {
  try {
    console.log('🎨 Generating content with artifacts...');
    console.log('- Platform:', platform);
    console.log('- Artifacts:', artifactIds.length);
    console.log('- Enhanced Design:', useEnhancedDesign);

    // Get active artifacts if no specific artifacts provided
    let targetArtifacts: Artifact[] = [];

    if (artifactIds.length > 0) {
      // Use specified artifacts
      for (const artifactId of artifactIds) {
        const artifact = artifactsService.getArtifact(artifactId);
        if (artifact) {
          targetArtifacts.push(artifact);
          await artifactsService.trackUsage(artifactId, 'quick-content');
        }
      }
    } else {
      // Use active artifacts, prioritizing exact-use
      const activeArtifacts = artifactsService.getActiveArtifacts();
      console.log('🔍 Active artifacts found:', activeArtifacts.length);
      console.log('📋 Active artifacts details:', activeArtifacts.map(a => ({
        id: a.id,
        name: a.name,
        type: a.type,
        usageType: a.usageType,
        isActive: a.isActive,
        instructions: a.instructions
      })));

      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');
      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');

      // Prioritize exact-use artifacts
      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];

      // Track usage for active artifacts
      for (const artifact of targetArtifacts) {
        await artifactsService.trackUsage(artifact.id, 'quick-content');
      }
    }

    console.log('📎 Using artifacts:', targetArtifacts.map(a => `${a.name} (${a.usageType})`));

    // Generate base content first
    const basePost = await generateContentAction(profile, platform, brandConsistency);

    // If enhanced design is disabled, return base content
    if (!useEnhancedDesign) {
      console.log('🔄 Enhanced design disabled, using base content generation');
      return basePost;
    }

    // Enhanced design is enabled - always use enhanced generation regardless of artifacts
    console.log('🎨 Enhanced design enabled - proceeding with enhanced generation');
    console.log(`📊 Artifacts available: ${targetArtifacts.length}`);

    if (targetArtifacts.length === 0) {
      console.log('✨ No artifacts provided - using enhanced design without artifact context');
    } else {
      console.log('🎯 Using enhanced design with artifact context');
    }

    // Separate exact-use and reference artifacts
    const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');
    const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');

    // Create enhanced image text structure from post components
    let enhancedImageText: { catchyWords: string; subheadline?: string; callToAction?: string } = {
      catchyWords: basePost.catchyWords || 'Engaging Content',
      subheadline: basePost.subheadline,
      callToAction: basePost.callToAction
    };
    let enhancedContent = basePost.content;

    // Collect usage instructions from artifacts
    const artifactInstructions = targetArtifacts
      .filter(a => a.instructions && a.instructions.trim())
      .map(a => `- ${a.name}: ${a.instructions}`)
      .join('\n');

    // Collect text overlay instructions from text artifacts
    const textOverlayInstructions = exactUseArtifacts
      .filter(a => a.textOverlay?.instructions && a.textOverlay.instructions.trim())
      .map(a => `- ${a.name}: ${a.textOverlay.instructions}`)
      .join('\n');

    // Process exact-use artifacts first (higher priority)
    if (exactUseArtifacts.length > 0) {
      const primaryExactUse = exactUseArtifacts[0];

      // Use text overlay if available
      if (primaryExactUse.textOverlay) {
        if (primaryExactUse.textOverlay.headline) {
          enhancedImageText.catchyWords = primaryExactUse.textOverlay.headline;
          console.log('📝 Using headline from exact-use artifact:', enhancedImageText.catchyWords);
        }

        if (primaryExactUse.textOverlay.message) {
          enhancedContent = primaryExactUse.textOverlay.message;
          console.log('📝 Using message from exact-use artifact');
        }

        // Use CTA from artifact if available
        if (primaryExactUse.textOverlay.cta) {
          enhancedImageText.callToAction = primaryExactUse.textOverlay.cta;
          console.log('📝 Using CTA from exact-use artifact:', enhancedImageText.callToAction);
        }
      }
    }

    // Process reference artifacts for style guidance
    const activeDirectives = referenceArtifacts.flatMap(artifact =>
      artifact.directives.filter(directive => directive.active)
    );

    // Apply style reference directives
    const styleDirectives = activeDirectives.filter(d => d.type === 'style-reference');
    let visualStyleOverride = profile.visualStyle || 'modern';
    if (styleDirectives.length > 0) {
      console.log('🎨 Applying style references from artifacts');
      const primaryStyleDirective = styleDirectives.find(d => d.priority >= 7);
      if (primaryStyleDirective) {
        visualStyleOverride = 'artifact-inspired';
        console.log('🎨 Using artifact-inspired visual style');
      }
    }

    // Combine all instructions
    const allInstructions = [artifactInstructions, textOverlayInstructions]
      .filter(Boolean)
      .join('\n');

    // Generate enhanced design with artifact context
    const enhancedResult = await generateEnhancedDesignAction(
      profile.businessType || 'business',
      platform.toLowerCase(),
      visualStyleOverride,
      enhancedImageText,
      profile,
      true,
      brandConsistency,
      allInstructions || undefined
    );

    // Create enhanced post with artifact metadata
    const enhancedPost: GeneratedPost = {
      ...basePost,
      id: Date.now().toString(),
      variants: [{
        platform: platform,
        imageUrl: enhancedResult.imageUrl
      }],
      content: targetArtifacts.length > 0
        ? `${enhancedContent}\n\n✨ Enhanced with AI+ using ${targetArtifacts.length} reference${targetArtifacts.length !== 1 ? 's' : ''} (Quality: ${enhancedResult.qualityScore}/10)`
        : `${enhancedContent}\n\n✨ Enhanced with AI+ Design Generation (Quality: ${enhancedResult.qualityScore}/10)`,
      date: new Date().toISOString(),
      // Add artifact metadata
      metadata: {
        ...basePost.metadata,
        referencedArtifacts: targetArtifacts.map(a => ({
          id: a.id,
          name: a.name,
          type: a.type,
          category: a.category
        })),
        activeDirectives: activeDirectives.map(d => ({
          id: d.id,
          type: d.type,
          label: d.label,
          priority: d.priority
        }))
      }
    };

    console.log('✅ Enhanced content with artifacts generated successfully');
    return enhancedPost;

  } catch (error) {
    console.error("Error generating content with artifacts:", error);
    throw new Error((error as Error).message);
  }
}

/**
 * Generate content using the new Revo model system
 * This action uses the proper model architecture with version-specific implementations
 */
export async function generateContentWithRevoModelAction(
  profile: BrandProfile,
  platform: Platform,
  revoModel: RevoModelId,
  brandConsistency?: { strictConsistency: boolean; followBrandColors: boolean },
  artifactIds: string[] = []
): Promise<GeneratedPost> {
  try {
    console.log(`🎨 Generating content with ${revoModel} model...`);
    console.log('- Platform:', platform);
    console.log('- Business Type:', profile.businessType);
    console.log('- Artifacts:', artifactIds.length);

    // Handle artifacts if provided
    let targetArtifacts: Artifact[] = [];
    if (artifactIds.length > 0) {
      for (const artifactId of artifactIds) {
        const artifact = artifactsService.getArtifact(artifactId);
        if (artifact) {
          targetArtifacts.push(artifact);
          await artifactsService.trackUsage(artifactId, 'quick-content');
        }
      }
    } else {
      // Use active artifacts
      const activeArtifacts = artifactsService.getActiveArtifacts();
      const exactUseArtifacts = activeArtifacts.filter(a => a.usageType === 'exact-use');
      const referenceArtifacts = activeArtifacts.filter(a => a.usageType === 'reference');
      targetArtifacts = [...exactUseArtifacts, ...referenceArtifacts.slice(0, 3)];

      for (const artifact of targetArtifacts) {
        await artifactsService.trackUsage(artifact.id, 'quick-content');
      }
    }

    // Prepare artifact instructions
    let artifactInstructions = '';
    if (targetArtifacts.length > 0) {
      const exactUseArtifacts = targetArtifacts.filter(a => a.usageType === 'exact-use');
      const referenceArtifacts = targetArtifacts.filter(a => a.usageType === 'reference');

      if (exactUseArtifacts.length > 0) {
        artifactInstructions += 'EXACT USE ARTIFACTS (use exactly as specified):\n';
        exactUseArtifacts.forEach(artifact => {
          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use this content exactly'}\n`;
        });
      }

      if (referenceArtifacts.length > 0) {
        artifactInstructions += 'REFERENCE ARTIFACTS (use as inspiration):\n';
        referenceArtifacts.forEach(artifact => {
          artifactInstructions += `- ${artifact.name}: ${artifact.instructions || 'Use as style reference'}\n`;
        });
      }
    }

    // Use simplified Revo model generation with text validation
    console.log(`🎨 Using Revo ${revoModel} with text validation...`);
    console.log('🔧 DEBUG: This is the SIMPLIFIED code path');

    // Generate dynamic and varied text for each design
    const textVariations = generateDynamicTextForRevo(profile, revoModel, platform);
    let imageText = textVariations.selectedText;

    if (revoModel === 'revo-1.0') {
      console.log('🎨 Revo 1.0: Applying strict 25-word text validation...');
      console.log('🔍 Original text:', imageText);

      // Simple text validation for Revo 1.0
      const words = imageText.split(' ').filter(word => word.length > 0);
      if (words.length > 25) {
        console.log(`⚠️ Revo 1.0: Text exceeds 25 words (${words.length}), truncating...`);
        imageText = words.slice(0, 25).join(' ');
      }
      console.log(`✅ Revo 1.0: Final text (${imageText.split(' ').length} words):`, imageText);
    }

    // Use sophisticated design prompt created by 20-year veteran designer + marketer
    const designPrompt = createProfessionalDesignPrompt(imageText, platform, profile, revoModel);

    const designResult = await generateCreativeAssetFlow({
      prompt: designPrompt,
      outputType: 'image',
      referenceAssetUrl: null,
      useBrandProfile: true,
      brandProfile: profile,
      maskDataUrl: null
    });

    if (!designResult.imageUrl) {
      throw new Error('Design generation failed: No image URL returned');
    }

    // Generate content using the standard flow for caption and hashtags
    const contentResult = await generatePostFromProfileFlow({
      businessType: profile.businessType,
      location: profile.location,
      writingTone: profile.writingTone,
      contentThemes: profile.contentThemes,
      visualStyle: profile.visualStyle,
      logoDataUrl: profile.logoDataUrl,
      designExamples: brandConsistency?.strictConsistency ? (profile.designExamples || []) : [],
      primaryColor: profile.primaryColor,
      accentColor: profile.accentColor,
      backgroundColor: profile.backgroundColor,
      dayOfWeek: new Date().toLocaleDateString('en-US', { weekday: 'long' }),
      currentDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
      variants: [{
        platform: platform,
        aspectRatio: getAspectRatioForPlatform(platform),
      }],
      services: Array.isArray(profile.services)
        ? profile.services.map(s => typeof s === 'object' ? `${s.name}: ${s.description || ''}` : s).join('\n')
        : profile.services || '',
      targetAudience: profile.targetAudience,
      keyFeatures: Array.isArray(profile.keyFeatures) ? profile.keyFeatures.join('\n') : profile.keyFeatures || '',
      competitiveAdvantages: Array.isArray(profile.competitiveAdvantages) ? profile.competitiveAdvantages.join('\n') : profile.competitiveAdvantages || '',
      brandConsistency: brandConsistency || { strictConsistency: false, followBrandColors: true },
    });

    // Combine the design result with content result
    const newPost: GeneratedPost = {
      id: new Date().toISOString(),
      date: new Date().toISOString(),
      content: contentResult.content,
      hashtags: contentResult.hashtags,
      status: 'generated',
      variants: [{
        platform,
        imageUrl: designResult.imageUrl || '',
        caption: contentResult.content,
        hashtags: contentResult.hashtags
      }],
      catchyWords: contentResult.catchyWords,
      subheadline: contentResult.subheadline,
      callToAction: contentResult.callToAction,
      contentVariants: contentResult.contentVariants,
      hashtagAnalysis: contentResult.hashtagAnalysis,
      marketIntelligence: contentResult.marketIntelligence,
      localContext: contentResult.localContext,
      // Add Revo model metadata
      revoModelUsed: revoModel,
      qualityScore: 8, // Default quality score for Revo models
      processingTime: Date.now() - Date.now(), // Will be calculated properly
      creditsUsed: 1,
      enhancementsApplied: [`Revo ${revoModel} Generation`, 'Text Validation', 'Professional Design']
    };

    console.log(`✅ Content generated successfully with ${revoModel}`);
    console.log(`⭐ Quality Score: 8/10`);
    console.log(`⚡ Processing Time: Fast generation`);
    console.log(`💰 Credits Used: 1`);

    return newPost;

  } catch (error) {
    console.error(`❌ Error generating content with ${revoModel}:`, error);
    throw new Error(`Failed to generate content with ${revoModel}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate professional marketing-driven text with cultural awareness
 * Designed by a 20-year veteran designer + 20-year marketing expert
 * Now deeply connected to actual brand profile information
 */
function generateDynamicTextForRevo(profile: BrandProfile, revoModel: RevoModelId, platform: Platform) {
  const businessName = profile.businessName || 'Your Business';
  const businessType = profile.businessType || 'Professional Services';
  const services = Array.isArray(profile.services) ? profile.services : [];
  const location = profile.location || '';
  const description = profile.description || '';

  // Generate sophisticated marketing copy using actual brand profile data
  const marketingCopy = generateMarketingCopy(profile, platform);

  console.log(`🎯 Generated personalized marketing copy for ${businessName}: "${marketingCopy}"`);

  return {
    selectedText: marketingCopy,
    allVariations: [marketingCopy],
    variationIndex: 0
  };
}

/**
 * Generate sophisticated marketing copy that sells
 * Combines 20 years of design + marketing expertise with actual brand profile data
 * Now deeply personalized using real business information
 * LANGUAGE RESTRICTION: English only - no Arabic, Hindi, Chinese, or other non-English text
 */
function generateMarketingCopy(profile: BrandProfile, platform: Platform): string {
  const businessName = profile.businessName || 'Your Business';
  const businessType = profile.businessType || 'Professional Services';
  const location = profile.location || '';
  const description = profile.description || '';
  const services = Array.isArray(profile.services) ? profile.services : [];

  // Extract real business intelligence from profile
  const businessIntelligence = extractBusinessIntelligence(profile);

  // Cultural and regional insights
  const culturalContext = getCulturalContext(location);

  // Generate catchy headline using actual business strengths (max 5 words)
  const catchyHeadlines = [
    `${getRandomElement(businessIntelligence.strengthWords)} ${businessName}`,
    `${businessName} ${getRandomElement(businessIntelligence.valueWords)}`,
    `${getRandomElement(culturalContext.localTerms)} ${businessIntelligence.primaryService}`,
    `${businessName} Delivers ${getRandomElement(businessIntelligence.benefitWords)}`,
    `${getRandomElement(businessIntelligence.differentiators)} ${businessName}`
  ];

  // Generate subheadline using real competitive advantages (max 14 words)
  const subheadlines = [
    `${businessIntelligence.realCompetitiveAdvantage} for ${businessIntelligence.actualTargetAudience} in ${location}`,
    `Join ${culturalContext.socialProof} who trust ${businessName} for ${businessIntelligence.keyBenefit}`,
    `${culturalContext.valueProposition} ${businessIntelligence.primaryService} with ${businessIntelligence.uniqueFeature}`,
    `Experience ${businessIntelligence.realDifferentiator} that drives ${getRandomElement(businessIntelligence.outcomeWords)} for your business`,
    `${culturalContext.urgencyTrigger} ${businessIntelligence.primaryService} that ${businessIntelligence.mainValue}`
  ];

  // Generate call-to-action using actual business context
  const callToActions = [
    `${culturalContext.actionWords} ${businessName} ${culturalContext.ctaUrgency}`,
    `Get Your ${businessIntelligence.offerType} ${culturalContext.ctaUrgency}`,
    `${culturalContext.localCTA} - ${businessIntelligence.urgencyTrigger}`,
    `${getRandomElement(['Book', 'Schedule', 'Request'])} Your ${businessIntelligence.consultationType} ${culturalContext.ctaUrgency}`,
    `${getRandomElement(['Start', 'Begin', 'Launch'])} Your ${businessIntelligence.journeyType} Today`
  ];

  // Randomly select components
  const catchyWords = getRandomElement(catchyHeadlines);
  const subheadline = getRandomElement(subheadlines);
  const cta = getRandomElement(callToActions);

  // Combine based on marketing best practices and business context
  const marketingFormats = [
    `${catchyWords}\n${subheadline}\n${cta}`,
    `${catchyWords}\n${subheadline}`,
    `${catchyWords}\n${cta}`,
    `${subheadline}\n${cta}`,
    catchyWords
  ];

  return getRandomElement(marketingFormats);
}

/**
 * Get cultural context and local market insights
 */
function getCulturalContext(location: string) {
  // Default context
  let context = {
    localTerms: ['Premium', 'Professional', 'Expert'],
    marketingStyle: 'Professional',
    targetAudience: 'businesses',
    localMarket: 'modern',
    socialProof: 'thousands of clients',
    valueProposition: 'Industry-leading',
    competitiveAdvantage: 'proven expertise',
    urgencyTrigger: 'Don\'t miss out on',
    actionWords: 'Connect with',
    localCTA: 'Get Started',
    ctaUrgency: 'Now'
  };

  // Location-specific cultural adaptations
  if (location.toLowerCase().includes('dubai') || location.toLowerCase().includes('uae')) {
    context = {
      localTerms: ['Premium', 'Luxury', 'Elite', 'Exclusive'],
      marketingStyle: 'Luxury-focused',
      targetAudience: 'discerning clients',
      localMarket: 'Dubai\'s dynamic',
      socialProof: 'leading UAE businesses',
      valueProposition: 'World-class',
      competitiveAdvantage: 'international excellence',
      urgencyTrigger: 'Seize the opportunity for',
      actionWords: 'Experience',
      localCTA: 'Book Your Exclusive Consultation',
      ctaUrgency: 'Today'
    };
  } else if (location.toLowerCase().includes('london') || location.toLowerCase().includes('uk')) {
    context = {
      localTerms: ['Bespoke', 'Tailored', 'Refined'],
      marketingStyle: 'Sophisticated',
      targetAudience: 'discerning professionals',
      localMarket: 'London\'s competitive',
      socialProof: 'established UK enterprises',
      valueProposition: 'Expertly crafted',
      competitiveAdvantage: 'British excellence',
      urgencyTrigger: 'Secure your',
      actionWords: 'Discover',
      localCTA: 'Arrange Your Consultation',
      ctaUrgency: 'Promptly'
    };
  } else if (location.toLowerCase().includes('new york') || location.toLowerCase().includes('nyc')) {
    context = {
      localTerms: ['Cutting-edge', 'Innovative', 'Game-changing'],
      marketingStyle: 'Bold and direct',
      targetAudience: 'ambitious professionals',
      localMarket: 'NYC\'s fast-paced',
      socialProof: 'successful New York businesses',
      valueProposition: 'Results-driven',
      competitiveAdvantage: 'New York hustle',
      urgencyTrigger: 'Don\'t let competitors get',
      actionWords: 'Dominate with',
      localCTA: 'Schedule Your Strategy Session',
      ctaUrgency: 'ASAP'
    };
  }

  return context;
}

/**
 * Extract business intelligence from brand profile for personalized marketing
 * Analyzes actual business data to create relevant marketing copy
 */
function extractBusinessIntelligence(profile: BrandProfile) {
  const businessName = profile.businessName || 'Your Business';
  const businessType = profile.businessType || 'Professional Services';
  const description = profile.description || '';
  const services = Array.isArray(profile.services) ? profile.services : [];
  const location = profile.location || '';

  // Extract primary service information
  const primaryService = services[0]?.name || services[0] || businessType;
  const serviceDescription = services[0]?.description || '';
  const targetAudience = services[0]?.targetAudience || 'businesses';
  const keyFeatures = services[0]?.keyFeatures || '';
  const competitiveAdvantages = services[0]?.competitiveAdvantages || '';

  // Analyze description for key terms
  const descriptionWords = description.toLowerCase().split(/\s+/);
  const strengthWords = extractStrengthWords(description, businessType);
  const valueWords = extractValueWords(description, keyFeatures);
  const benefitWords = extractBenefitWords(description, competitiveAdvantages);

  // Extract competitive advantages
  const realCompetitiveAdvantage = extractCompetitiveAdvantage(competitiveAdvantages, businessType);
  const uniqueFeature = extractUniqueFeature(keyFeatures, businessType);
  const realDifferentiator = extractDifferentiator(competitiveAdvantages, description);

  // Extract target audience specifics
  const actualTargetAudience = extractTargetAudience(targetAudience, businessType);

  // Generate contextual elements
  const keyBenefit = extractKeyBenefit(serviceDescription, competitiveAdvantages);
  const mainValue = extractMainValue(description, keyFeatures);
  const offerType = generateOfferType(businessType, services);
  const consultationType = generateConsultationType(businessType);
  const journeyType = generateJourneyType(businessType, primaryService);
  const urgencyTrigger = generateUrgencyTrigger(businessType, location);

  // Extract outcome words from business context
  const outcomeWords = extractOutcomeWords(description, competitiveAdvantages);
  const differentiators = extractDifferentiators(competitiveAdvantages, businessType);

  return {
    primaryService,
    strengthWords,
    valueWords,
    benefitWords,
    realCompetitiveAdvantage,
    uniqueFeature,
    realDifferentiator,
    actualTargetAudience,
    keyBenefit,
    mainValue,
    offerType,
    consultationType,
    journeyType,
    urgencyTrigger,
    outcomeWords,
    differentiators
  };
}

/**
 * Extract strength words from business description and type
 */
function extractStrengthWords(description: string, businessType: string): string[] {
  const strengthKeywords = ['leading', 'premium', 'expert', 'professional', 'trusted', 'innovative', 'cutting-edge', 'award-winning', 'certified', 'proven', 'reliable', 'secure', 'fast', 'efficient', 'quality', 'excellence', 'superior', 'advanced', 'specialized'];
  const found = strengthKeywords.filter(word => description.toLowerCase().includes(word));

  // Add business type specific strengths
  const typeStrengths = getBusinessTypeStrengths(businessType);

  return found.length > 0 ? found : typeStrengths;
}

/**
 * Extract value words from description and features
 */
function extractValueWords(description: string, keyFeatures: string): string[] {
  const valueKeywords = ['value', 'results', 'success', 'growth', 'efficiency', 'savings', 'profit', 'revenue', 'performance', 'productivity', 'quality', 'excellence', 'innovation', 'solutions', 'benefits'];
  const text = `${description} ${keyFeatures}`.toLowerCase();
  const found = valueKeywords.filter(word => text.includes(word));

  return found.length > 0 ? found : ['Excellence', 'Results', 'Success'];
}

/**
 * Extract benefit words from description and competitive advantages
 */
function extractBenefitWords(description: string, competitiveAdvantages: string): string[] {
  const benefitKeywords = ['success', 'growth', 'efficiency', 'savings', 'results', 'performance', 'quality', 'reliability', 'security', 'speed', 'convenience', 'expertise', 'support', 'innovation', 'excellence'];
  const text = `${description} ${competitiveAdvantages}`.toLowerCase();
  const found = benefitKeywords.filter(word => text.includes(word));

  return found.length > 0 ? found : ['Success', 'Quality', 'Results'];
}

/**
 * Extract competitive advantage from actual business data
 */
function extractCompetitiveAdvantage(competitiveAdvantages: string, businessType: string): string {
  if (competitiveAdvantages && competitiveAdvantages.length > 10) {
    // Extract first meaningful advantage
    const advantages = competitiveAdvantages.split(',').map(s => s.trim());
    return advantages[0] || getDefaultAdvantage(businessType);
  }
  return getDefaultAdvantage(businessType);
}

/**
 * Extract unique feature from key features
 */
function extractUniqueFeature(keyFeatures: string, businessType: string): string {
  if (keyFeatures && keyFeatures.length > 10) {
    const features = keyFeatures.split(',').map(s => s.trim());
    return features[0] || getDefaultFeature(businessType);
  }
  return getDefaultFeature(businessType);
}

/**
 * Extract differentiator from competitive advantages and description
 */
function extractDifferentiator(competitiveAdvantages: string, description: string): string {
  const text = `${competitiveAdvantages} ${description}`.toLowerCase();

  if (text.includes('24/7') || text.includes('24-7')) return '24/7 availability';
  if (text.includes('fastest') || text.includes('quick') || text.includes('speed')) return 'fastest service';
  if (text.includes('secure') || text.includes('security')) return 'advanced security';
  if (text.includes('expert') || text.includes('experience')) return 'expert knowledge';
  if (text.includes('custom') || text.includes('tailored')) return 'customized solutions';
  if (text.includes('award') || text.includes('certified')) return 'award-winning service';

  return 'professional excellence';
}

/**
 * Extract target audience from service data
 */
function extractTargetAudience(targetAudience: string, businessType: string): string {
  if (targetAudience && targetAudience.length > 5) {
    return targetAudience.split(',')[0].trim() || getDefaultAudience(businessType);
  }
  return getDefaultAudience(businessType);
}

/**
 * Extract key benefit from service description and advantages
 */
function extractKeyBenefit(serviceDescription: string, competitiveAdvantages: string): string {
  const text = `${serviceDescription} ${competitiveAdvantages}`.toLowerCase();

  if (text.includes('save') || text.includes('cost')) return 'cost savings';
  if (text.includes('fast') || text.includes('quick') || text.includes('speed')) return 'faster results';
  if (text.includes('secure') || text.includes('safety')) return 'enhanced security';
  if (text.includes('grow') || text.includes('increase')) return 'business growth';
  if (text.includes('efficient') || text.includes('optimize')) return 'improved efficiency';
  if (text.includes('quality') || text.includes('premium')) return 'superior quality';

  return 'exceptional results';
}

/**
 * Extract main value proposition
 */
function extractMainValue(description: string, keyFeatures: string): string {
  const text = `${description} ${keyFeatures}`.toLowerCase();

  if (text.includes('transform') || text.includes('revolutionize')) return 'transforms your business';
  if (text.includes('maximize') || text.includes('optimize')) return 'maximizes your potential';
  if (text.includes('accelerate') || text.includes('boost')) return 'accelerates your growth';
  if (text.includes('streamline') || text.includes('simplify')) return 'streamlines your operations';
  if (text.includes('enhance') || text.includes('improve')) return 'enhances your performance';

  return 'delivers exceptional value';
}

/**
 * Generate business type-specific offer types
 */
function generateOfferType(businessType: string, services: any[]): string {
  const type = businessType.toLowerCase();

  if (type.includes('restaurant') || type.includes('food')) return 'Free Tasting';
  if (type.includes('tech') || type.includes('software')) return 'Free Demo';
  if (type.includes('health') || type.includes('medical')) return 'Free Consultation';
  if (type.includes('finance') || type.includes('banking')) return 'Free Assessment';
  if (type.includes('real estate')) return 'Free Valuation';
  if (type.includes('legal')) return 'Free Consultation';
  if (type.includes('education') || type.includes('training')) return 'Free Trial';

  return 'Free Consultation';
}

/**
 * Generate consultation types based on business
 */
function generateConsultationType(businessType: string): string {
  const type = businessType.toLowerCase();

  if (type.includes('tech') || type.includes('software')) return 'Strategy Session';
  if (type.includes('health') || type.includes('medical')) return 'Health Assessment';
  if (type.includes('finance') || type.includes('banking')) return 'Financial Review';
  if (type.includes('real estate')) return 'Property Consultation';
  if (type.includes('legal')) return 'Legal Consultation';
  if (type.includes('marketing')) return 'Marketing Audit';

  return 'Business Consultation';
}

/**
 * Generate journey types
 */
function generateJourneyType(businessType: string, primaryService: string): string {
  const type = businessType.toLowerCase();

  if (type.includes('tech') || type.includes('digital')) return 'Digital Transformation';
  if (type.includes('health') || type.includes('wellness')) return 'Wellness Journey';
  if (type.includes('finance') || type.includes('investment')) return 'Financial Success';
  if (type.includes('real estate')) return 'Property Investment';
  if (type.includes('education')) return 'Learning Journey';

  return 'Success Journey';
}

/**
 * Generate urgency triggers based on business and location
 */
function generateUrgencyTrigger(businessType: string, location: string): string {
  const triggers = ['Limited Time Offer', 'Act Now', 'Don\'t Wait', 'Book Today', 'Available Now'];

  if (location.toLowerCase().includes('dubai')) return 'Exclusive Dubai Offer';
  if (location.toLowerCase().includes('london')) return 'Limited London Availability';
  if (location.toLowerCase().includes('new york')) return 'NYC Exclusive Deal';

  return getRandomElement(triggers);
}

/**
 * Get business type specific strengths
 */
function getBusinessTypeStrengths(businessType: string): string[] {
  const type = businessType.toLowerCase();

  if (type.includes('restaurant') || type.includes('food')) return ['Premium', 'Fresh', 'Authentic'];
  if (type.includes('tech') || type.includes('software')) return ['Innovative', 'Cutting-edge', 'Advanced'];
  if (type.includes('health') || type.includes('medical')) return ['Trusted', 'Professional', 'Expert'];
  if (type.includes('finance') || type.includes('banking')) return ['Secure', 'Reliable', 'Trusted'];
  if (type.includes('real estate')) return ['Premium', 'Exclusive', 'Professional'];
  if (type.includes('legal')) return ['Expert', 'Trusted', 'Professional'];
  if (type.includes('education')) return ['Expert', 'Certified', 'Professional'];

  return ['Professional', 'Quality', 'Trusted'];
}

/**
 * Get default competitive advantage by business type
 */
function getDefaultAdvantage(businessType: string): string {
  const type = businessType.toLowerCase();

  if (type.includes('restaurant') || type.includes('food')) return 'Fresh, authentic ingredients';
  if (type.includes('tech') || type.includes('software')) return 'Cutting-edge technology';
  if (type.includes('health') || type.includes('medical')) return 'Expert medical care';
  if (type.includes('finance') || type.includes('banking')) return 'Secure financial solutions';
  if (type.includes('real estate')) return 'Premium property expertise';
  if (type.includes('legal')) return 'Expert legal guidance';

  return 'Professional excellence';
}

/**
 * Get default feature by business type
 */
function getDefaultFeature(businessType: string): string {
  const type = businessType.toLowerCase();

  if (type.includes('restaurant') || type.includes('food')) return 'farm-to-table freshness';
  if (type.includes('tech') || type.includes('software')) return 'advanced automation';
  if (type.includes('health') || type.includes('medical')) return 'personalized care';
  if (type.includes('finance') || type.includes('banking')) return 'secure transactions';
  if (type.includes('real estate')) return 'market expertise';

  return 'personalized service';
}

/**
 * Get default audience by business type
 */
function getDefaultAudience(businessType: string): string {
  const type = businessType.toLowerCase();

  if (type.includes('restaurant') || type.includes('food')) return 'food enthusiasts';
  if (type.includes('tech') || type.includes('software')) return 'forward-thinking businesses';
  if (type.includes('health') || type.includes('medical')) return 'health-conscious individuals';
  if (type.includes('finance') || type.includes('banking')) return 'smart investors';
  if (type.includes('real estate')) return 'property investors';

  return 'discerning clients';
}

/**
 * Extract outcome words from business context
 */
function extractOutcomeWords(description: string, competitiveAdvantages: string): string[] {
  const text = `${description} ${competitiveAdvantages}`.toLowerCase();
  const outcomes = ['success', 'growth', 'results', 'performance', 'efficiency', 'savings', 'profit', 'revenue'];
  const found = outcomes.filter(word => text.includes(word));

  return found.length > 0 ? found : ['success', 'results', 'growth'];
}

/**
 * Extract differentiators from competitive advantages
 */
function extractDifferentiators(competitiveAdvantages: string, businessType: string): string[] {
  if (competitiveAdvantages && competitiveAdvantages.length > 10) {
    const advantages = competitiveAdvantages.split(',').map(s => s.trim().split(' ')[0]);
    return advantages.slice(0, 3);
  }

  return getBusinessTypeStrengths(businessType);
}

/**
 * Get random element from array
 */
function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Create professional design prompt with 20 years of design + marketing expertise
 * Combines cultural awareness, psychology, and visual design mastery
 */
function createProfessionalDesignPrompt(imageText: string, platform: Platform, profile: BrandProfile, revoModel: RevoModelId): string {
  const businessName = profile.businessName || 'Your Business';
  const businessType = profile.businessType || 'Professional Services';
  const location = profile.location || '';
  const description = profile.description || '';
  const services = Array.isArray(profile.services) ? profile.services : [];

  // Extract business intelligence for design context
  const businessIntelligence = extractBusinessIntelligence(profile);

  // Get cultural context for design decisions
  const culturalContext = getCulturalContext(location);

  // Industry-specific design psychology
  const industryDesignPsychology = getIndustryDesignPsychology(businessType);

  // Platform-specific design requirements
  const platformRequirements = getPlatformDesignRequirements(platform);

  // Color psychology based on business type and culture
  const colorPsychology = getColorPsychology(businessType, location);

  // Typography psychology for conversion
  const typographyStrategy = getTypographyStrategy(businessType, platform);

  return `Create an exceptional, conversion-focused ${platform} design for ${businessName} that embodies 20 years of professional design and marketing expertise.

LANGUAGE REQUIREMENTS:
- ALL TEXT MUST BE IN ENGLISH ONLY
- NO Arabic, Hindi, Chinese, or other non-English characters
- NO transliterated text or mixed languages
- Use clear, professional English throughout

BUSINESS INTELLIGENCE & CONTEXT:
- Company: ${businessName} (${businessType})
- Primary Service: ${businessIntelligence.primaryService}
- Location: ${location}
- Target Audience: ${businessIntelligence.actualTargetAudience}
- Key Differentiator: ${businessIntelligence.realDifferentiator}
- Unique Value: ${businessIntelligence.mainValue}
- Competitive Advantage: ${businessIntelligence.realCompetitiveAdvantage}

TEXT TO INTEGRATE: "${imageText}"

BRAND-SPECIFIC DESIGN REQUIREMENTS:
- Must communicate: ${businessIntelligence.realCompetitiveAdvantage}
- Must highlight: ${businessIntelligence.uniqueFeature}
- Must appeal to: ${businessIntelligence.actualTargetAudience}
- Must convey: ${businessIntelligence.keyBenefit}

DESIGN PSYCHOLOGY & STRATEGY:
${industryDesignPsychology}

VISUAL HIERARCHY & COMPOSITION:
- Apply the golden ratio and rule of thirds for optimal visual flow
- Create clear focal points that guide the eye to key conversion elements
- Use strategic white space to enhance readability and premium feel
- Implement Z-pattern or F-pattern layout for maximum engagement

COLOR STRATEGY:
${colorPsychology}

TYPOGRAPHY MASTERY:
${typographyStrategy}

CULTURAL DESIGN ADAPTATION:
- ${culturalContext.localMarket} aesthetic preferences
- ${culturalContext.targetAudience} visual expectations
- Regional design trends and cultural symbols
- Local color associations and meanings

CONVERSION OPTIMIZATION:
- Design elements that create urgency and desire
- Visual cues that guide toward call-to-action
- Trust signals through professional presentation
- Emotional triggers through strategic imagery and layout

PLATFORM OPTIMIZATION:
${platformRequirements}

TECHNICAL EXCELLENCE:
- Aspect Ratio: 1:1 (perfect square)
- Resolution: Ultra-high quality, print-ready standards
- Text Clarity: Crystal clear, perfectly readable at all sizes
- Brand Consistency: Align with professional brand standards
- Mobile Optimization: Ensure perfect display on all devices

FINAL QUALITY STANDARDS:
This design must look like it was created by a top-tier creative agency specifically for ${businessName}. Every element should reflect their unique value proposition: "${businessIntelligence.realCompetitiveAdvantage}". The design should immediately communicate their expertise in ${businessIntelligence.primaryService} while appealing directly to ${businessIntelligence.actualTargetAudience}.

The final result should be a sophisticated, professional design that drives ${businessIntelligence.actualTargetAudience} to choose ${businessName} over competitors and take immediate action.

Make it absolutely irresistible for ${businessIntelligence.actualTargetAudience} and perfectly aligned with ${businessName}'s brand identity.`;
}

/**
 * Get industry-specific design psychology
 */
function getIndustryDesignPsychology(businessType: string): string {
  const type = businessType.toLowerCase();

  if (type.includes('restaurant') || type.includes('food') || type.includes('cafe')) {
    return `- Use warm, appetizing colors that stimulate hunger and comfort
- Incorporate food photography principles with rich textures
- Create cozy, inviting atmosphere through design elements
- Focus on sensory appeal and mouth-watering visual presentation`;
  }

  if (type.includes('tech') || type.includes('software') || type.includes('digital')) {
    return `- Employ clean, minimalist design with high-tech aesthetics
- Use gradients and modern geometric shapes
- Incorporate subtle tech-inspired elements and icons
- Focus on innovation, efficiency, and cutting-edge appeal`;
  }

  if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {
    return `- Use calming, trustworthy colors that convey safety and care
- Incorporate clean, sterile design elements
- Focus on professionalism, expertise, and patient comfort
- Use imagery that suggests health, vitality, and well-being`;
  }

  if (type.includes('finance') || type.includes('banking') || type.includes('investment')) {
    return `- Employ sophisticated, conservative design elements
- Use colors that convey stability, trust, and prosperity
- Incorporate subtle luxury elements and professional imagery
- Focus on security, growth, and financial success`;
  }

  if (type.includes('real estate') || type.includes('property')) {
    return `- Use aspirational imagery and luxury design elements
- Incorporate architectural lines and premium materials
- Focus on lifestyle, investment, and dream fulfillment
- Use colors that suggest stability, growth, and success`;
  }

  // Default professional services
  return `- Use professional, trustworthy design elements
- Incorporate subtle premium touches and quality indicators
- Focus on expertise, reliability, and professional excellence
- Use colors and imagery that convey competence and success`;
}

/**
 * Get platform-specific design requirements
 */
function getPlatformDesignRequirements(platform: Platform): string {
  switch (platform) {
    case 'Instagram':
      return `- Optimize for Instagram's visual-first environment
- Use bold, eye-catching elements that stand out in feeds
- Incorporate Instagram-native design trends and aesthetics
- Ensure design works perfectly in both feed and story formats`;

    case 'Facebook':
      return `- Design for Facebook's diverse, multi-generational audience
- Use clear, readable elements that work across age groups
- Incorporate social proof and community-focused elements
- Ensure design is engaging but not overwhelming`;

    case 'LinkedIn':
      return `- Employ professional, business-focused design elements
- Use conservative colors and sophisticated typography
- Incorporate industry-specific imagery and professional symbols
- Focus on credibility, expertise, and business value`;

    case 'Twitter':
      return `- Create concise, impactful design that communicates quickly
- Use bold typography and clear visual hierarchy
- Incorporate trending design elements and current aesthetics
- Ensure design is optimized for rapid consumption`;

    default:
      return `- Create versatile design that works across multiple platforms
- Use universal design principles and broad appeal
- Ensure scalability and readability across different contexts
- Focus on timeless, professional aesthetics`;
  }
}

/**
 * Get color psychology based on business type and location
 */
function getColorPsychology(businessType: string, location: string): string {
  const type = businessType.toLowerCase();
  const loc = location.toLowerCase();

  let baseColors = '';
  let culturalColors = '';

  // Business type color psychology
  if (type.includes('restaurant') || type.includes('food')) {
    baseColors = 'warm reds, oranges, and yellows to stimulate appetite and create warmth';
  } else if (type.includes('tech') || type.includes('digital')) {
    baseColors = 'modern blues, teals, and purples to convey innovation and trust';
  } else if (type.includes('health') || type.includes('medical')) {
    baseColors = 'calming blues, clean whites, and soft greens to suggest health and tranquility';
  } else if (type.includes('finance') || type.includes('banking')) {
    baseColors = 'sophisticated navy, gold, and silver to convey stability and prosperity';
  } else {
    baseColors = 'professional blues, grays, and accent colors to convey trust and competence';
  }

  // Cultural color adaptations
  if (loc.includes('dubai') || loc.includes('uae')) {
    culturalColors = 'Incorporate gold accents and luxury tones that resonate with UAE\'s premium market expectations';
  } else if (loc.includes('london') || loc.includes('uk')) {
    culturalColors = 'Use sophisticated, understated tones that align with British professional aesthetics';
  } else if (loc.includes('new york') || loc.includes('nyc')) {
    culturalColors = 'Employ bold, confident colors that match New York\'s dynamic business environment';
  } else {
    culturalColors = 'Use universally appealing professional color combinations';
  }

  return `- Primary Strategy: ${baseColors}
- Cultural Adaptation: ${culturalColors}
- Psychological Impact: Colors chosen to trigger specific emotional responses and buying behaviors
- Contrast Optimization: Ensure maximum readability and visual impact`;
}

/**
 * Get typography strategy for conversion
 */
function getTypographyStrategy(businessType: string, platform: Platform): string {
  const type = businessType.toLowerCase();

  let fontStrategy = '';
  let hierarchyStrategy = '';

  if (type.includes('luxury') || type.includes('premium')) {
    fontStrategy = 'Elegant serif or sophisticated sans-serif fonts that convey exclusivity and refinement';
  } else if (type.includes('tech') || type.includes('digital')) {
    fontStrategy = 'Modern, clean sans-serif fonts that suggest innovation and efficiency';
  } else if (type.includes('creative') || type.includes('design')) {
    fontStrategy = 'Unique, artistic fonts that showcase creativity while maintaining readability';
  } else {
    fontStrategy = 'Professional, highly readable fonts that convey trust and competence';
  }

  hierarchyStrategy = `- Primary Text: Bold, attention-grabbing headlines that create immediate impact
- Secondary Text: Clear, readable subheadings that support the main message
- Call-to-Action: Distinctive typography that stands out and drives action
- Supporting Text: Clean, professional fonts for additional information`;

  return `- Font Selection: ${fontStrategy}
- Visual Hierarchy: ${hierarchyStrategy}
- Readability: Optimized for ${platform} viewing conditions and mobile devices
- Conversion Focus: Typography choices designed to guide the eye and encourage action`;
}

(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/ai/flows/generate-creative-asset.ts
__turbopack_context__.s({});
"use turbopack no side effects";
;
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/data:0dd486 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4060851bba2dd643a600428bdf8a566b397fe38d6d":"generateCreativeAsset"},"src/ai/flows/generate-creative-asset.ts",""] */ __turbopack_context__.s({
    "generateCreativeAsset": (()=>generateCreativeAsset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var generateCreativeAsset = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("4060851bba2dd643a600428bdf8a566b397fe38d6d", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "generateCreativeAsset"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$0dd486__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$data$3a$0dd486__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/data:0dd486 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCreativeAsset": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["generateCreativeAsset"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$ai$2f$flows$2f$generate$2d$creative$2d$asset$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/ai/flows/generate-creative-asset.ts [app-client] (ecmascript) <exports>");
}}),
"[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// This file must be bundled in the app's client layer, it shouldn't be directly
// imported by the server.
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    callServer: null,
    createServerReference: null,
    findSourceMapURL: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    callServer: function() {
        return _appcallserver.callServer;
    },
    createServerReference: function() {
        return createServerReference;
    },
    findSourceMapURL: function() {
        return _appfindsourcemapurl.findSourceMapURL;
    }
});
const _appcallserver = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-call-server.js [app-client] (ecmascript)");
const _appfindsourcemapurl = __turbopack_context__.r("[project]/node_modules/next/dist/client/app-find-source-map-url.js [app-client] (ecmascript)");
const createServerReference = (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-server-dom-turbopack/client.js [app-client] (ecmascript)")).createServerReference; //# sourceMappingURL=action-client-wrapper.js.map
}}),
}]);

//# sourceMappingURL=_68a9230f._.js.map